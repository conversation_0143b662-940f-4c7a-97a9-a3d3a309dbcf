{"name": "new-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "staging:start": "next start -p 3030", "lint": "next lint", "postinstall": "patch-package"}, "dependencies": {"@badrap/bar-of-progress": "^0.1.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "@fullstory/browser": "^1.4.9", "@gsap/react": "^2.1.2", "@lottiefiles/dotlottie-react": "^0.12.2", "@next/font": "^13.0.4", "@rive-app/react-canvas": "^4.17.6", "@sentry/nextjs": "^6.18.0", "@svgr/webpack": "^6.2.1", "apexcharts": "^3.35.4", "cheerio": "^1.0.0-rc.12", "classnames": "^2.3.1", "colorthief": "^2.4.0", "csv": "^6.3.6", "date-fns": "^2.28.0", "dayjs": "^1.11.5", "emoji-picker-react": "^3.5.1", "fbemitter": "^3.0.0", "firebase": "^8.7.0", "formik": "^2.2.9", "g-sheets-api": "^2.2.0", "gsap": "^3.12.7", "hammerjs": "^2.0.8", "heic2any": "^0.0.3", "history": "^5.0.1", "iconsax-react": "^0.0.8", "lottie-react": "^2.4.1", "next": "^12.2.0", "next-inline-script": "^0.0.1", "next-intercom": "^0.1.4", "next-pwa": "^5.4.0", "preload-it": "^1.4.0", "react": "17.0.2", "react-apexcharts": "^1.4.0", "react-color-palette": "^7.1.0", "react-confetti": "^6.4.0", "react-date-range": "^1.4.0", "react-dom": "17.0.2", "react-google-recaptcha": "^3.1.0", "react-hot-toast": "^2.1.1", "react-instagram-embed": "^3.0.0", "react-paystack": "^4.0.3", "react-simple-pull-to-refresh": "^1.3.3", "react-swipeable": "^6.2.0", "react-tooltip": "^4.2.21", "react-typed": "^2.0.12", "react-youtube": "^10.1.0", "sass": "^1.69.7", "socket.io-client": "^4.5.4", "typed.js": "^2.0.12", "unfetch": "^4.2.0", "unstated-next": "^1.1.0", "url-loader": "^4.1.1", "yup": "^0.32.9"}, "devDependencies": {"@types/node": "^15.12.5", "@types/react": "^17.0.11", "autoprefixer": "^10.2.6", "eslint": "7.29.0", "eslint-config-next": "11.0.1", "file-loader": "^6.2.0", "font-loader": "^0.1.2", "patch-package": "^8.0.0", "postcss": "^8.3.5", "postinstall-postinstall": "^2.1.0", "sass-loader": "11.0.1", "tailwindcss": "^2.2.4", "typescript": "^4.3.4"}}