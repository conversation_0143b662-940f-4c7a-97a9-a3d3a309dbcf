export enum AFFILIATE_TYPES {
  INFLUENCER = "INFLUENCER",
  FRIEND = "FRIEND",
  AFFILIATE = "AFFILIATE",
}

export interface AffiliateInterface {
  id: string;
  name: string;
  email: string;
  phone: string;
  type: AFFILIATE_TYPES;
  slug: string;
  total_orders: number;
  total_customers: number;
  created_at: string;
  updated_at: string;
}

export interface AffiliateStatisticsInterface {
  total_affiliates: number;
  total_orders: number;
  total_customers: number;
  top_affiliate: string;
}
