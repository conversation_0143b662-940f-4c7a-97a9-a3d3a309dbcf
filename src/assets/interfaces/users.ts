import { StoreInterface } from "./stores";

export enum UserType {
  REGULAR = "REGULAR",
  THIRD_PARTY = "THIRD_PARTY",
}

export enum OnboardingStepsWithRewards {
  WATCH_SETUP_VIDEO = "WATCH_SETUP_VIDEO",
  UPLOAD_10_PRODUCTS = "UPLOAD_10_PRODUCTS",
  ENABLE_PUSH_NOTIFICATION = "ENABLE_PUSH_NOTIFICATION",
  COMPLETE_KYC = "COMPLETE_KYC",
  FIRST_ORDER_WITH_PAYMENT = "FIRST_ORDER_WITH_PAYMENT",
}

export interface SourceAd {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  gclid?: string;
}

export interface UserMeta {
  added_to_customer_io?: boolean;
  bigin_record_id?: string;
  has_watched_onboarding_video?: boolean;
}

export interface OnboardingSteps {
  community_joined: boolean;
  pwa_added: boolean;
  onboarding_call_booked: boolean;
  has_enabled_notification?: boolean;
  has_followed_socials?: boolean;
}

export interface OnboardingRewards {
  setup_video_credits_earned?: boolean;
  first_order_credits_earned?: boolean;
  kyc_credits_earned?: boolean;
  push_notification_credits_earned?: boolean;
  product_upload_credits_earned?: boolean;
}

export interface User {
  id: string;
  name: string;
  email: string;
  password: string;
  phone: string;
  plan?: string;
  email_verified: boolean;
  phone_verified: boolean;
  avatar: string;
  can_get_custom_link: Date | null;
  stores: string[] | StoreInterface[];
  primary_store: string | StoreInterface;
  subscription?: any;
  onboarding_steps: OnboardingSteps;
  onboarding_rewards: OnboardingRewards;
  email_verification_token?: string;
  phone_verification_token?: string;
  withdrawal_verification_token?: string;
  reset_password_token?: string;
  last_login?: Date;
  storesDisabled?: boolean;
  auto_debit_wallets?: boolean;
  reference?: string;
  referred_by?: string;
  source_ad?: SourceAd;
  meta?: UserMeta;
  type: UserType;
  created_at?: string;
}
