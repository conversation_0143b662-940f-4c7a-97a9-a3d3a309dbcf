import { CURRENCIES } from "./stores";

export type User = {
  id: number;
  name: string;
};

export interface PhoneInterface {
  code: string;
  digits: string;
}

export enum COUNTRIES {
  NG = "NG",
  GH = "GH",
  ZA = "ZA",
  KE = "KE",
}

export enum FILE_TYPES {
  ITEMS = "items",
  KYC = "kyc",
  STORES = "stores",
  HIGHLIGHTS = 'highlights'
}

export interface SortItemInterface {
  id: string;
  sort_index: number;
}

export enum MediaType {
  VIDEO = "video",
  IMAGE = "image",
  AUDIO = "audio",
}

export interface CountryInterface {
  name: string;
  currency: string;
  code: COUNTRIES;
  dial_code: string;
  emoji: string;
}

export type Media = {
  type: MediaType;
  src: string;
  name: string;
  lastModified: number;
  file: File | Blob;
  isUploading?: boolean;
  uploadProgress?: number;
  url?: string;
  thumbnail?: string;
  error?: boolean;
  meta?: any;
  key?: string;
  isPlaceholder?: boolean;
};

export type Image = Omit<Media, "type">;
export type Video = Omit<Media, "type">;

export interface Rewards {
  SIGNUP: {
    [key: string]: number;
  };
  SUBSCRIPTION: {
    [key: string]: number;
  };
  CASHOUT: {
    [key: string]: number;
  };
}

export interface CurrencyRates {
  [key: string]: {
    [key: string]: number;
  };
}

export enum DOMAIN_PURCHASE_STATUS {
  PENDING = "PENDING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}
export interface DomainPurchase {
  id: string;
  domain: string;
  store: string;
  status: DOMAIN_PURCHASE_STATUS;
  amount: number;
  currency: CURRENCIES;
  payment_id?: string;
  registration_id?: string;
  expires_at?: Date;
  nameservers?: string[];
  owner: string;
  meta?: Record<string, any>;
}

export type Phone = { code: string; digits: string };

export * from "./carts";
export * from "./products";
export * from "./stores";
export * from "./subscriptions";
export * from "./orders-customers";
export * from "./wallets";
export * from "./payments";
export * from "./chowbot";
