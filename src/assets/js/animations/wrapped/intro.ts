export const getIntroAnimation1 = function (tl: gsap.core.Timeline, rings: any[]) {
  tl.to(".part-2", { display: "none", opacity: 0, duration: 0 });

  for (let i = 0; i < rings.length; i++) {
    tl.set(`.c-ring-${i}`, {
      rotate: 0 + i * 45,
      scale: 0.1,
      opacity: 0,
    });
  }

  tl.to(".c-ring", {
    scale: 1.7,
    duration: 2,
    opacity: 1,
    ease: "expo.out",
  });

  for (let i = 0; i < rings.length; i++) {
    tl.to(
      `.c-ring-${i}`,
      {
        rotate: 360 * 2,
        duration: 2,
        ease: "expo.out",
      },
      "<"
    );
  }

  tl.to(
    `.header`,
    {
      y: "0",
      duration: 0.4,
      delay: 2,
      ease: "power1.out",
      opacity: 1,
    },
    "<"
  );

  for (let i = 0; i < 3; i++) {
    tl.to(
      `.text-row-${i + 1}`,
      {
        y: "0",
        delay: 0.1,
        duration: 0.5,
        ease: "power1.out",
        opacity: 1,
      },
      "<"
    );
  }

  tl.to(
    `.trinkets`,
    {
      duration: 0.5,
      delay: 0.8,
      ease: "power1.out",
      opacity: 1,
    },
    "<"
  );

  tl.to(
    `.header`,
    {
      y: "20",
      duration: 0.4,
      delay: 3,
      ease: "power1.out",
      opacity: 0,
    },
    "<"
  );

  for (let i = 0; i < 3; i++) {
    tl.to(
      `.text-row-${i + 1}`,
      {
        y: "0",
        delay: 0.1,
        duration: 0.5,
        ease: "power1.out",
        opacity: 0,
      },
      "<"
    );
  }

  tl.to(".part-1", { display: "none" });
};

export const getIntroAnimation2 = function (tl: gsap.core.Timeline, rings: any[], isShared?: boolean) {
  tl.to(".part-2", { display: "block", opacity: 1, duration: 0 })
    .to(".arrows", { rotate: 360 * 8, duration: 2, ease: "expo.out" })
    .to(".carret-text-1", { text: isShared ? "Here's my year" : "Here’s your", duration: 0.6, delay: 1.5 }, "<")
    .to(".carret-text-2", { text: isShared ? "in review, from Catlog!" : "year in review!", duration: 0.6 });
};
