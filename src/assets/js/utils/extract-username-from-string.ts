/**
 * Extracts the username from various social media handle formats.
 * Handles cases like:
 * - @username
 * - username
 * - https://twitter.com/username
 * - https://twitter.com/username?utm_source=xyz
 * - www.instagram.com/username/
 * - Random strings with spaces and special characters
 * - Preserves periods in usernames (like user.name)
 *
 * @param {string} input - The social media handle in any format
 * @param {Object} options - Optional configuration
 * @param {boolean} options.allowPeriods - Whether to allow periods in usernames (default: true)
 * @param {string} options.allowedChars - Additional characters to allow (default: '')
 * @return {string} - Just the username part
 */
export function extractUsername(input, setError) {
  // Return empty string for null/undefined/empty inputs
  if (!input) return "";

  // Convert to string in case a non-string is passed
  input = String(input).trim();

  // Remove @ prefix if present
  if (input.startsWith("@")) {
    input = input.substring(1);
  }

  // Handle URL formats by extracting the path component containing the username
  if (input.includes("://") || input.includes("www.") || /\.[a-z]{2,}\//.test(input)) {
    // Matches domain patterns like .com/ .org/ etc.

    // Parse URL to extract path components
    let urlParts = input.split("/");
    let username = "";

    // Find the likely username component
    for (let i = 0; i < urlParts.length; i++) {
      // Skip empty parts, protocol, and domain components
      if (
        !urlParts[i] ||
        urlParts[i].includes(":") ||
        urlParts[i].includes(".com") ||
        urlParts[i].includes(".org") ||
        urlParts[i].includes(".net") ||
        urlParts[i] === "www"
      ) {
        continue;
      }

      // Found a potential username
      username = urlParts[i];

      // Remove query parameters and hash fragments
      if (username.includes("?")) {
        username = username.split("?")[0];
      }
      if (username.includes("#")) {
        username = username.split("#")[0];
      }

      // If we found a non-empty username, stop searching
      if (username) break;
    }

    // If we found a username from the URL, use it
    if (username) {
      input = username;
    }
    // Otherwise, keep the original input and clean it below
  }

  // Clean the username by keeping only alphanumeric characters,
  // underscores, and optionally periods
  let allowedPattern = "[^a-zA-Z0-9_\\.]";

  // Create the regex from the pattern and use it to remove disallowed chars
  const cleaningRegex = new RegExp(allowedPattern, "g");
  input = input.replace(cleaningRegex, "");

  if (!input) {
    setError("Please enter a valid username");
  }

  return input;
}
