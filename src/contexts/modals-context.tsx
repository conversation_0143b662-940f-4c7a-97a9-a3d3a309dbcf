import React, { createContext, useContext, useState, useCallback } from "react";

interface ModalsContextType {
  toggleModal: (modalName: string) => void;
  isModalOpen: (modalName: string) => boolean;
}

const ModalsContext = createContext<ModalsContextType>({
  toggleModal: () => {},
  isModalOpen: () => false,
});

export const useModals = () => useContext(ModalsContext);

export const ModalsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [openModals, setOpenModals] = useState<Set<string>>(new Set());

  const toggleModal = useCallback((modalName: string) => {
    setOpenModals((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(modalName)) {
        newSet.delete(modalName);
      } else {
        newSet.add(modalName);
      }
      return newSet;
    });
  }, []);

  const isModalOpen = useCallback(
    (modalName: string) => {
      return openModals.has(modalName);
    },
    [openModals]
  );

  return <ModalsContext.Provider value={{ toggleModal, isModalOpen }}>{children}</ModalsContext.Provider>;
};

export default ModalsContext;
