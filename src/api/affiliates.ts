import { paramsFromObject } from "../assets/js/utils/functions";
import { request } from "./utils";
import {
  CreateAffiliateParams,
  DeleteAffiliateParams,
  GetAffiliateCustomersParams,
  GetAffiliateOrdersParams,
  GetAffiliateParams,
  GetAffiliatesParams,
  UpdateAffiliateParams,
} from "./interfaces/affiliates.interface";

const GetAffiliates = (data: GetAffiliatesParams) => {
  const params = paramsFromObject(data);
  return request(`affiliates?${params}`, "get");
};

const GetAffiliate = (data: GetAffiliateParams) => {
  return request(`affiliates/${data.id}`, "get");
};

const GetAffiliateAnalytics = () => {
  return request(`affiliates/analytics`, "get");
};

const GetAffiliateOrders = (data: GetAffiliateOrdersParams) => {
  const params = paramsFromObject(data);
  return request(`affiliates/${data.id}/orders?${params}`, "get");
};

const GetAffiliateCustomers = (data: GetAffiliateCustomersParams) => {
  const params = paramsFromObject(data);
  return request(`affiliates/${data.id}/customers?${params}`, "get");
};

const CreateAffiliate = (data: CreateAffiliateParams) => {
  return request("affiliates", "post", { data });
};

const UpdateAffiliate = (data: UpdateAffiliateParams) => {
  const affiliateId = data.id;
  const dataCopy = { ...data };
  delete dataCopy.id;

  return request(`affiliates/${affiliateId}`, "put", { data: dataCopy });
};

const DeleteAffiliate = (data: DeleteAffiliateParams) => {
  return request(`affiliates/${data.id}`, "delete");
};

export {
  GetAffiliates,
  GetAffiliate,
  GetAffiliateAnalytics,
  GetAffiliateOrders,
  GetAffiliateCustomers,
  CreateAffiliate,
  UpdateAffiliate,
  DeleteAffiliate,
};
