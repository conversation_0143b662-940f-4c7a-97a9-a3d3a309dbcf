import { paramsFromObject } from "@/assets/js/utils/functions";
import { InfoBlockInterface, StoreInterface } from "@/assets/interfaces";
import {
  CreateStoreParams,
  GetStoreByIdParams,
  CreateStoreCategoriesParams,
  GetStoreCategoriesParams,
  DeleteStoreCategoryParams,
  GetStoreBySlugParams,
  UpdateStoreParams,
  UpdateStoreLinkParams,
  UpdateDeliveryAreasParams,
  GetDeliveryAreasParams,
  UpdateCheckoutChannelsParams,
  CreateInviteParams,
  AcceptInviteParams,
  DeleteInviteParams,
  RemoveTeamMemberParams,
  UpdateTeamMemberParams,
  UpdateDirectCheckoutParams,
  UpdateStorePaymentMethodsParams,
  UpdateCurrenciesParams,
  UpdateStoreMaintenanceModeParams,
  UpdateSecurityPinParams,
  GetInstagramAccessTokenParams,
  GetInstagramMediaParams,
  GetInstagramAlbumMediaParams,
  GetMultipleInstagramAlbumMediaParams,
  GetProductTypesParams,
  UpdateBusinessCategoryParams,
  UpdateStoreColorParams,
  CreateBranchesParams,
  UpdateBranchesParams,
  UpdateChowdeckConfigParams,
  UpdateStoreAdditionalDetailsParams,
  ValidateSlugParams,
  GetSlugSuggestionsParams,
  DeleteTestimonialParams,
  CreateStoreInfoBlocksParams,
  UpdateStoreInfoBlocksParams,
  UpdateStoreAboutParams,
  UpdateStoreFaqParams,
  AssignItemsParams,
  UpdateStorefrontVersionParams,
} from "./interfaces/stores.interface";

import { request } from "./utils";

const ValidateSlug = (data: ValidateSlugParams) => {
  return request(`stores/validate-slug/${data.slug}`, "get");
};

const GetSlugSuggestion = (data: GetSlugSuggestionsParams) => {
  return request(`stores/suggest-slug/${data.store_name}`, "get");
};

const CreateStore = (data: CreateStoreParams) => {
  return request("stores", "post", { data });
};

const GetStoreById = (data: GetStoreByIdParams) => {
  return request(`stores/${data.id}`, "get");
};

const UpdateStoreDetails = (data: UpdateStoreParams) => {
  const storeId = data.id;
  const dataCopy = { ...data };
  delete dataCopy.id;

  return request(`stores/${storeId}`, "put", { data: dataCopy });
};

const CreateStoreCategories = (data: CreateStoreCategoriesParams) => {
  return request(`stores/${data.id}/categories`, "post", { data: data.categories });
};

const GetStoreCategories = (data: GetStoreCategoriesParams) => {
  return request(`stores/${data.id}/categories`, "get");
};

const DeleteStoreCategory = (data: DeleteStoreCategoryParams) => {
  return request(`stores/${data.id}/categories/${data.category_id}`, "delete");
};

const GetStoreBySlug = (data: GetStoreBySlugParams) => {
  return request(`stores/public/${data.slug}`, "get");
};

const UpdateStoreLink = (data: UpdateStoreLinkParams) => {
  const storeId = data.store;
  const dataCopy = { ...data };
  delete dataCopy.store;

  return request(`stores/${storeId}/slug`, "put", { data: dataCopy });
};

//delivery areas
const GetDeliveryAreas = (data: GetDeliveryAreasParams) => {
  return request(`stores/${data.store}/delivery-areas`, "get");
};

const UpdateDeliveryAreas = (data: UpdateDeliveryAreasParams) => {
  return request(`stores/${data.store}/delivery-areas`, "put", { data: data.data });
};

const UpdateCheckoutChannels = (data: UpdateCheckoutChannelsParams) => {
  return request(`stores/${data.store}/checkout-channels`, "put", { data: data.data });
};

//INVITES
const CreateInvite = (data: CreateInviteParams) => {
  return request(`stores/invites`, "post", { data });
};

const GetInvite = (data: { id: string }) => {
  return request(`stores/invites/${data.id}`, "get");
};

const AcceptInvite = (data: AcceptInviteParams) => {
  const id = data.id;
  const dataCopy = { ...data };
  delete dataCopy.id;

  return request(`stores/invites/${id}`, "put", { data: dataCopy });
};

const DeleteInvite = (data: DeleteInviteParams) => {
  return request(`stores/invites/${data.id}`, "delete");
};

//TEAMS
const GetTeamMembers = () => {
  return request("stores/teams/", "get");
};

const UpdateTeamMember = (data: UpdateTeamMemberParams) => {
  return request(`stores/teams/`, "put", { data: data });
};

const RemoveTeamMember = (data: RemoveTeamMemberParams) => {
  return request(`stores/teams/${data.id}`, "delete");
};

const UpdateDirectCheckout = (data: UpdateDirectCheckoutParams) => {
  const storeId = data.id;
  const dataCopy = { ...data };
  delete dataCopy.id;

  return request(`stores/${storeId}/direct-checkout`, "put", { data: dataCopy });
};

const UpdateStorePaymentMethods = (data: UpdateStorePaymentMethodsParams) => {
  const storeId = data.id;
  const dataCopy = { ...data };
  delete dataCopy.id;

  return request(`stores/${storeId}/payment-methods`, "put", { data: dataCopy });
};

const UpdateStoreCurrencies = (data: UpdateCurrenciesParams) => {
  const storeId = data.id;
  return request(`stores/${storeId}/currencies`, "put", { data: data.data });
};

const UpdateStoreMaintenanceMode = (data: UpdateStoreMaintenanceModeParams) => {
  const storeId = data.id;
  return request(`stores/${storeId}/maintenance-mode`, "put", { data: { state: data.state } });
};

const UpdateStorefrontVersion = (data: UpdateStorefrontVersionParams) => {
  return request(`stores/flags/storefront-version`, "put", { data });
};

const UpdateSecurityPin = (data: UpdateSecurityPinParams) => {
  const storeId = data.id;
  delete data.id;
  return request(`stores/${storeId}/security-pin`, "put", { data });
};

const GenerateInstagramAccessToken = (data: GetInstagramAccessTokenParams) => {
  const params = paramsFromObject(data);
  return request(`stores/ig/access-token?${params}`, "get");
};

const DisconnectInstagram = () => {
  return request(`stores/ig/disconnect`, "get");
};

const GetInstagramMedia = (data: GetInstagramMediaParams) => {
  const params = paramsFromObject(data);
  return request(`stores/ig/media?${params}`, "get");
};

const GetInstagramUser = () => {
  return request(`stores/ig/user`, "get");
};

const GetInstagramAlbumMedia = (data: GetInstagramAlbumMediaParams) => {
  return request(`stores/ig/album-media/${data.media_id}`, "get");
};

const GetMultipleInstagramAlbumMedia = (data: GetMultipleInstagramAlbumMediaParams) => {
  return request(`stores/ig/albums-media`, "post", { data });
};

const CheckInstagramToken = () => {
  return request(`stores/ig/check-token/`, "get");
};

const GetBusinessCategories = () => {
  return request("stores/business-categories", "get");
};

const GetProductTypes = (data: GetProductTypesParams) => {
  return request(`stores/business-categories/${data.category}`, "get");
};

const UpdateBusinessCategorization = (data: UpdateBusinessCategoryParams) => {
  return request(`stores/${data.id}/categorization`, "put", { data: data.business_category });
};

const UpdateStoreAdditionalDetails = (data: UpdateStoreAdditionalDetailsParams) => {
  return request(`stores/additional-details`, "put", { data: data.additional_details });
};

const UpdateStoreColor = (data: UpdateStoreColorParams) => {
  const { id, ...rest } = data;

  return request(`stores/${id}/color`, "put", { data: rest });
};

// const GetChowdeckItems = () => {
//   return request(`stores/chowdeck/menu-items`, "get");
// };

const GetChowdeckCategories = () => {
  return request(`stores/chowdeck/menu-categories`, "get");
};

const GetBranches = (data: { id: string }) => {
  return request(`stores/branches/${data.id}`, "get");
};

const CreateOrUpdateBranches = (data: UpdateBranchesParams) => {
  const { id, ...rest } = data;
  return id
    ? request(`stores/branches/${id}`, "post", { data: rest })
    : request(`stores/branches`, "post", { data: rest });
};

const DeleteBranches = (data: { id: string }) => {
  return request(`stores/branches/${data.id}`, "delete");
};

const GetMenuImage = (data: { storeId: string }) => {
  return request(`stores/${data.storeId}/menu-image`, "put");
};

const UpdateChowdeckConfiguration = (data: UpdateChowdeckConfigParams) => {
  const { storeId, ...rest } = data;
  return request(`stores/${storeId}/chowdeck-config`, "put", { data: rest });
};

const UpdateAutoCheckInConfiguration = (data) => {
  const { store_id, ...rest } = data;
  return request(`stores/${store_id}/auto-check-in`, "put", { data: rest });
};

const GetStoreWrappedData = (data: { store_slug: string }) => {
  return request(`store/year-wrap`, "get");
};
const GetStoreWrappedDataPublic = (data: { store_slug: string }) => {
  const { store_slug } = data;
  return request(`store/year-wrap/${store_slug}`, "get");
};

const GetWrappedImages = (data: { store_slug: string; cards: string }) => {
  return request(`stores/${data.store_slug}/get-wrapped-image?cards=${data.cards}`, "get");
};

// Domain management APIs
const AddDomain = (data: { domain: string }) => {
  return request(`stores/domains`, "post", { data });
};

const GetDomains = () => {
  return request(`stores/domains`, "get");
};

const RemoveDomain = (data: { id: string }) => {
  return request(`stores/domains/${data.id}`, "delete");
};

const GenerateSslCertificate = (data: { id: string }) => {
  return request(`stores/domains/${data.id}/generate-certificate`, "post");
};

const VerifyDomain = (data: { id: string }) => {
  return request(`stores/domains/${data.id}/verify`, "post");
};
const GetStoreAbout = () => {
  return request(`store/about`, "get");
};

const UpdateStoreAbout = (data: UpdateStoreAboutParams) => {
  return request(`store/about`, "put", { data });
};

const GetStoreFaqs = () => {
  return request(`store/faq-content`, "get");
};

const UpdateStoreFaqs = (data: UpdateStoreFaqParams) => {
  return request(`store/faq-content`, "put", { data });
};

const DeleteTestimonial = (data: DeleteTestimonialParams) => {
  return request(`store/testimonials/${data.id}`, "delete");
};

const GetStoreInfoBlocks = () => {
  return request(`store/info-block-content`, "get");
};

const CreateStoreInfoBlock = (data: InfoBlockInterface) => {
  return request(`store/info-blocks`, "post", { data });
};

const UpdateStoreInfoBlocks = (data: UpdateStoreInfoBlocksParams) => {
  return request(`store/info-blocks/${data.id}`, "put", { data: data.block });
};
const AssignItemsToInfoBlock = (data: AssignItemsParams) => {
  return request(`store/info-blocks/${data.id}/items`, "put", {
    data: {
      items: data.items
    }
  })
}
const DeleteStoreInfoBlock = (data: DeleteTestimonialParams) => {
  return request(`store/info-blocks/${data.id}`, "delete");
};

const CheckDomain = (data: { domain: string }) => {
  return request(`domains/check/${data.domain}`, "get");
};

const InitiateDomainPurchase = (data: { domain: string }) => {
  return request(`domains/purchase`, "post", { data });
};

const GetDomainPurchase = (data: { id: string }) => {
  return request(`domains/purchases/${data.id}`, "get");
};

export {
  CreateStore,
  GetStoreById,
  CreateStoreCategories,
  GetStoreCategories,
  DeleteStoreCategory,
  GetStoreBySlug,
  UpdateStoreDetails,
  UpdateStoreLink,
  GetDeliveryAreas,
  UpdateDeliveryAreas,
  UpdateCheckoutChannels,
  CreateInvite,
  GetInvite,
  AcceptInvite,
  GetTeamMembers,
  RemoveTeamMember,
  DeleteInvite,
  UpdateTeamMember,
  UpdateDirectCheckout,
  UpdateStorePaymentMethods,
  UpdateStoreCurrencies,
  UpdateStoreMaintenanceMode,
  UpdateSecurityPin,
  GenerateInstagramAccessToken,
  DisconnectInstagram,
  GetInstagramAlbumMedia,
  CheckInstagramToken,
  GetInstagramMedia,
  GetMultipleInstagramAlbumMedia,
  GetBusinessCategories,
  GetProductTypes,
  UpdateBusinessCategorization,
  UpdateStoreColor,
  // GetChowdeckItems,
  GetChowdeckCategories,
  GetBranches,
  CreateOrUpdateBranches,
  DeleteBranches,
  GetMenuImage,
  UpdateChowdeckConfiguration,
  GetInstagramUser,
  UpdateAutoCheckInConfiguration,
  UpdateStoreAdditionalDetails,
  GetStoreWrappedData,
  GetStoreWrappedDataPublic,
  GetWrappedImages,
  AddDomain,
  GetDomains,
  RemoveDomain,
  GenerateSslCertificate,
  VerifyDomain,
  CheckDomain,
  InitiateDomainPurchase,
  GetDomainPurchase,
  ValidateSlug,
  GetSlugSuggestion,
  GetStoreAbout,
  UpdateStoreAbout,
  DeleteTestimonial,
  GetStoreFaqs,
  UpdateStoreFaqs,
  GetStoreInfoBlocks,
  CreateStoreInfoBlock,
  UpdateStoreInfoBlocks,
  DeleteStoreInfoBlock,
  AssignItemsToInfoBlock,
  UpdateStorefrontVersion,
};
