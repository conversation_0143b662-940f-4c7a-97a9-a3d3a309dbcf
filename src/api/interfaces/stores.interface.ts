import {
  Branches,
  CURRENCIES,
  Category,
  CheckoutChannels,
  ChowdeckConfig,
  DeliveryArea,
  INPUT_TYPE,
  StoreAdditionalDetails,
  StoreFaq,
  StoreInterface,
  StorePaymentOptions,
  StorefrontVersion,
} from "../../assets/interfaces";

export interface CreateStoreParams {
  name: string;
  country: string;
  description: string;
  phone: string;
  store_type: string;
  logo?: string;
  copy_config?: boolean;
}

export interface GetStoreByIdParams {
  id: string;
}

export interface GetSlugSuggestionsParams {
  store_name: string;
}

export interface ValidateSlugParams {
  slug: string;
}

export interface UpdateStoreParams {
  id: string;
  name?: string;
  description?: string;
  phone?: string;
  country?: string;
  state?: string;
  delivery_locations?: string;
  address?: string;
  socials?: {
    twitter?: string;
    facebook?: string;
    instagram?: string;
  };
  logo?: string;
  hero_image?: string;
  configuration?: {
    view_modes?: {
      grid: boolean;
      card: boolean;
      horizontal: boolean;
      default: string;
    };
    fb_pixel?: string;
    custom_message?: string;
    enquiry_message?: string;
    sort_by_latest_products?: boolean;
    show_unavailable_products?: boolean;
    customer_pickup_enabled?: boolean;
    whatsapp_checkout_enabled?: boolean;
    facebook_pixel_enabled?: boolean;
    require_delivery_info?: boolean;
    payment_validates_order?: boolean;
    average_delivery_timeline?: string;
    send_menu_on_initiation?: boolean;
    menu_images?: string[];
    custom_checkout_form?: {
      name: string;
      type: INPUT_TYPE;
      is_required: boolean;
      is_enabled?: boolean;
      label?: string;
      options?: { value: string; price?: number }[];
    }[];
  };
  onboarding_steps?: {
    products_added?: boolean;
    link_added?: boolean;
  };
  pickup_address?: string;
  deliveries_enabled?: boolean;
  checkout_configuration?: {
    whatsapp_checkout_enabled?: boolean;
    require_delivery_info?: boolean;
    confirm_order_before_payment?: boolean;
  };
  extra_info?: any;
}

export interface UpdateStoreColorParams {
  id: string;
  color: string;
}

export interface UpdateStoreDetailsParams {
  id: string;
  name: string;
  description: string;
  phone: string;
  secondary_phone?: string;
  country?: string;
  state?: string;
  delivery_locations?: string;
  address?: string;
  socials: {
    twitter?: string;
    facebook?: string;
    instagram?: string;
    snapchat?: string;
    tiktok?: string;
  };
  extra_info?: {
    delivery_timeline?: string;
    production_timeline?: string;
    refund_policy?: string;
    extra_images_label?: string;
    extra_images?: string[];
  };
}

export interface UpdateStoreConfigParams {
  id: string;
  configuration: {
    view_modes?: {
      grid: boolean;
      card: boolean;
      horizontal: boolean;
      default: string;
    };
    custom_message?: string;
    enquiry_message?: string;
    sort_by_latest_products?: boolean;
    show_unavailable_products?: boolean;
    customer_pickup_enabled?: boolean;
    send_menu_on_initiation?: boolean;
    collect_order_notes?: boolean;
    pass_chowbot_fee_to_deliveries?: boolean;
    confirm_order_before_payment?: boolean;
  };
}

export interface UpdateStoreImagesParams {
  id: string;
  logo?: string;
  hero_image?: string;
}

export interface CreateStoreCategoriesParams {
  id: string;
  categories: Category[];
}

export interface GetStoreCategoriesParams {
  id?: string;
}

export interface DeleteStoreCategoryParams {
  id: string;
  category_id: string;
}

export interface GetStoreBySlugParams {
  slug: string;
}

export interface UpdateStoreLinkParams {
  store: string;
  slug: string;
}

export interface GetDeliveryAreasParams {
  store: string;
}

export interface UpdateDeliveryAreasParams {
  store: string;
  data: DeliveryArea[];
}

export interface UpdateCheckoutChannelsParams {
  store: string;
  data: CheckoutChannels;
}
export interface CreateInviteParams {
  role: string;
  email?: string;
  phone?: string;
}
export interface AcceptInviteParams {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  password?: string;
  recaptcha_token?: string;
}
export interface DeleteInviteParams {
  id: string;
}
export interface UpdateTeamMemberParams {
  user_id?: string;
  invite_id?: string;
  role: string;
}
export interface RemoveTeamMemberParams {
  id: string;
}

export interface UpdateCheckoutConfigParams {
  id: string;
  checkout_configuration: {
    whatsapp_checkout_enabled?: boolean;
    require_delivery_info?: boolean;
  };
}

export interface UpdateDirectCheckoutParams {
  id?: string;
  direct_checkout_enabled: boolean;
}

export interface UpdateStorePaymentMethodsParams {
  id?: string;
  payment_options: {
    enabled: boolean;
    type: string;
  }[];
  currency: string;
}

export interface UpdateCurrenciesParams {
  id?: string;
  data: {
    products: CURRENCIES;
    storefront: CURRENCIES[];
    storefront_default: CURRENCIES;
    rates: { [key: string]: number };
  };
}

export interface UpdateStoreMaintenanceModeParams {
  id?: string;
  state: boolean;
}

export interface UpdateStorefrontVersionParams {
  version: StorefrontVersion;
}

export interface UpdateSecurityPinParams {
  id: string;
  new_pin: string;
  current_pin?: string;
  password: string;
}

export interface GetInstagramAccessTokenParams {
  access_code: string;
  redirect_uri: string;
}
export interface GetInstagramMediaParams {
  pagination?: { next?: string; previous?: string; limit?: number };
}
export interface GetInstagramAlbumMediaParams {
  media_id: string;
}

export interface GetMultipleInstagramAlbumMediaParams {
  media_ids: string[];
}
export interface GetProductTypesParams {
  category: string;
}

export interface UpdateBusinessCategoryParams {
  id: string;
  business_category: StoreInterface["business_category"];
}

export interface UpdateStoreAdditionalDetailsParams {
  id: string;
  additional_details: StoreAdditionalDetails;
}

export interface UpdateChowdeckConfigParams extends ChowdeckConfig {
  storeId?: string;
}

export interface UpdateAutoCheckInConfigParams {
  store_id: string;
  enabled: boolean;
  message?: string;
  days?: number;
}

export interface CreateBranchesParams extends Omit<Branches, "id"> {}
export interface UpdateBranchesParams extends Branches {
  id?: string;
}

export interface DeleteTestimonialParams {
  id: string;
}

export interface CreateStoreInfoBlocksParams {
  title: string;
  content_type: "TEXT" | "IMAGES";
  text_content: string;
  image_content: string[];
  is_visible: boolean;
  tag?: string;
}
export interface UpdateStoreInfoBlocksParams {
  id: string;
  block: {
    title: string;
    content_type: "TEXT" | "IMAGES";
    text_content: string;
    image_content: string[];
    is_visible: boolean;
  };
}

export interface AssignItemsParams {
  id: string;
  items: string[];
}

export interface UpdateStoreAboutParams {
  about_us: {
    content: string;
    images: string[];
  };
  testimonials: {
    customer_name: string;
    content: string;
    source: string;
    is_visible: boolean;
  }[];
}
export interface DeleteStoreInfoBlockParams {
  id: string;
}

export interface UpdateStoreFaqParams {
  faqs: StoreFaq[];
}
