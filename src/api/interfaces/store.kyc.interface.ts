import { StoreFaq, StoreTestimonial } from "@/assets/interfaces";

export interface GetStorekYCParams {
  kyc: string;
}

export interface SavekYCBasicInfoParams {
  first_name: string;
  last_name: string;
}

export interface LookupBVNParams {
  bvn: string;
  dob: string;
}
export interface LookupPhoneParams {
  phone: string;
  dob: string;
}

export interface UpdateDobParams {
  dob: string;
}

export interface VerifyPhoneParams {
  token: string;
  phone: string;
}
export interface VerifyBVNParms {
  token: string;
  bvn: string;
}

export interface ResendBVNTokenParams {
  bvn: string;
}

export interface ResendPhoneTokenParams {
  phone: string;
}

export interface VerifyIDParams {
  id_type: string;
  id_number: string;
  selfie: string;
  photo_id: string;
}

export interface SaveAddressParams {
  address_line1: string;
  state: string;
  lga: string;
  city: string;
}