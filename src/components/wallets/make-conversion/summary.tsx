import React from "react";
import { FormikProps } from "formik";
import { amountFormat, toCurrency } from "../../../assets/js/utils/functions";
import { CURRENCIES } from "../../../assets/interfaces";
import { ConversionForm } from ".";

interface Props {
  form: FormikProps<ConversionForm>;
  conversion_rate: number;
  conversion_fee: number;
  destination_amount: number;
}

const ConversionSummary: React.FC<Props> = ({ form, conversion_rate, conversion_fee, destination_amount }) => {
  return (
    <div className="flex flex-col items-center">
      <figure className="h-16 w-16 sm:h-18 sm:w-18 rounded-full flex items-center justify-center bg-accent-red-500 text-white">
        {/* prettier-ignore */}
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
          <path d="M56.5324 47.9163C55.9825 52.3497 52.3491 55.983 47.9158 56.533C45.2325 56.8663 42.7325 56.133 40.7825 54.6997C39.6658 53.883 39.9325 52.1497 41.2658 51.7497C46.2825 50.233 50.2324 46.2663 51.7658 41.2497C52.1658 39.933 53.8991 39.6663 54.7158 40.7663C56.1325 42.733 56.8658 45.233 56.5324 47.9163Z" fill="currentColor"/>
          <path d="M36.6507 23.333C29.3007 23.333 23.334 29.2997 23.334 36.6497C23.334 43.9997 29.3007 49.9663 36.6507 49.9663C44.0007 49.9663 49.9673 43.9997 49.9673 36.6497C49.9506 29.2997 44.0007 23.333 36.6507 23.333ZM35.084 34.783L39.1007 36.183C40.5507 36.6997 41.2507 37.7163 41.2507 39.283C41.2507 41.083 39.8173 42.5663 38.0673 42.5663H37.9173V42.6497C37.9173 43.333 37.3507 43.8997 36.6673 43.8997C35.984 43.8997 35.4173 43.333 35.4173 42.6497V42.5497C33.5673 42.4663 32.084 40.9163 32.084 38.983C32.084 38.2997 32.6507 37.733 33.334 37.733C34.0173 37.733 34.584 38.2997 34.584 38.983C34.584 39.583 35.0173 40.0663 35.5507 40.0663H38.0507C38.434 40.0663 38.734 39.7163 38.734 39.283C38.734 38.6997 38.634 38.6663 38.2507 38.533L34.234 37.133C32.8007 36.633 32.084 35.6163 32.084 34.033C32.084 32.233 33.5173 30.7497 35.2673 30.7497H35.4173V30.683C35.4173 29.9997 35.984 29.433 36.6673 29.433C37.3507 29.433 37.9173 29.9997 37.9173 30.683V30.783C39.7673 30.8663 41.2507 32.4163 41.2507 34.3497C41.2507 35.033 40.684 35.5997 40.0007 35.5997C39.3173 35.5997 38.7507 35.033 38.7507 34.3497C38.7507 33.7497 38.3173 33.2663 37.784 33.2663H35.284C34.9007 33.2663 34.6007 33.6163 34.6007 34.0497C34.584 34.6163 34.684 34.6497 35.084 34.783Z" fill="currentColor"/>
        </svg>
      </figure>
      <h2 className="text-2lg sm:text-2xl mt-2.5 mb-5 font-display text-black text-center font-light">
        You're converting <br />
        <b className="font-bold">
          {toCurrency(form?.values.amount, form.values.source_currency)} ~{" "}
          {toCurrency(destination_amount, form.values.destination_currency)}
        </b>
      </h2>
      <dl className="w-full bg-grey-fields-100 rounded-lg p-3.75">
        <div className="flex justify-between items-center text-sm text-dark last:pb-0 pb-3.75">
          <div className="flex items-center">
            <figure>
              {/* prettier-ignore */}
              <svg className="bg-white rounded-full mr-1.5" width="28" height="28" viewBox="0 0 28 28" fill="none">
                <path d="M14.0024 20.6562C17.6767 20.6562 20.6552 17.6776 20.6552 14.0034C20.6552 10.3291 17.6767 7.35059 14.0024 7.35059C10.3282 7.35059 7.34961 10.3291 7.34961 14.0034C7.34961 17.6776 10.3282 20.6562 14.0024 20.6562Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M10.0918 8.68066L15.5338 14.136L15.5471 11.1089" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M17.9084 19.3188L12.4664 13.8701L12.4531 16.8905" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </figure>
            <dt>Amount You’re Converting</dt>
          </div>
          <span className="font-medium text-black-secondary">
            {toCurrency(form.values.amount, form.values.source_currency)}
          </span>
        </div>
        <div className="flex justify-between items-center text-sm text-dark last:pb-0 pb-3.75">
          <div className="flex items-center">
            <figure>
              {/* prettier-ignore */}
              <svg className="bg-white rounded-full mr-1.5" width="28" height="28" viewBox="0 0 28 28" fill="none">
                <path d="M14.0007 20.6663C17.6825 20.6663 20.6673 17.6816 20.6673 13.9997C20.6673 10.3178 17.6825 7.33301 14.0007 7.33301C10.3188 7.33301 7.33398 10.3178 7.33398 13.9997C7.33398 17.6816 10.3188 20.6663 14.0007 20.6663Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M14 11.667V15.667" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 14.333L14 16.333L16 14.333" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </figure>
            <dt>Amount You’re Receiving</dt>
          </div>
          <span className="font-medium text-black-secondary">
            {toCurrency(destination_amount, form.values.destination_currency)}
          </span>
        </div>
        <div className="flex justify-between items-center text-sm text-dark last:pb-0 pb-3.75">
          <div className="flex items-center">
            <figure>
              {/* prettier-ignore */}
              <svg className="bg-white rounded-full mr-1.5" width="28" height="28" viewBox="0 0 28 28" fill="none">
                <path d="M11 11.5605H15.9333C16.5267 11.5605 17 12.0405 17 12.6272V13.8072" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12.1267 10.4404L11 11.5605L12.1267 12.6871" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M17 16.44H12.0667C11.4733 16.44 11 15.96 11 15.3734V14.1934" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M15.873 17.5602L16.9997 16.4401L15.873 15.3135" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M14.0007 20.6663C17.6825 20.6663 20.6673 17.6816 20.6673 13.9997C20.6673 10.3178 17.6825 7.33301 14.0007 7.33301C10.3188 7.33301 7.33398 10.3178 7.33398 13.9997C7.33398 17.6816 10.3188 20.6663 14.0007 20.6663Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </figure>
            <dt>Conversion Rate</dt>
          </div>
          <span className="font-medium text-black-secondary">
            1 {form.values.source_currency} = {amountFormat(conversion_rate, 3)} {form.values.destination_currency}
          </span>
        </div>
        <div className="flex justify-between items-center text-sm text-dark last:pb-0 pb-3.75">
          <div className="flex items-center">
            <figure>
              {/* prettier-ignore */}
              <svg className="bg-white rounded-full mr-1.5" width="28" height="28" viewBox="0 0 28 28" fill="none">
                <path d="M11.7812 15.5536C11.7812 16.4136 12.4413 17.1069 13.2612 17.1069H14.9346C15.6479 17.1069 16.2279 16.5002 16.2279 15.7536C16.2279 14.9402 15.8746 14.6536 15.3479 14.4669L12.6612 13.5336C12.1346 13.3469 11.7812 13.0602 11.7812 12.2469C11.7812 11.5002 12.3612 10.8936 13.0746 10.8936H14.7479C15.5679 10.8936 16.2279 11.5869 16.2279 12.4469" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M14 10V18" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M14.0007 20.6663C17.6825 20.6663 20.6673 17.6816 20.6673 13.9997C20.6673 10.3178 17.6825 7.33301 14.0007 7.33301C10.3188 7.33301 7.33398 10.3178 7.33398 13.9997C7.33398 17.6816 10.3188 20.6663 14.0007 20.6663Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </figure>
            <dt>Conversion Fee</dt>
          </div>
          <span className="font-medium text-black-secondary">
            {toCurrency(conversion_fee, form.values.source_currency, false, 2)}
          </span>
        </div>
      </dl>
    </div>
  );
};

export default ConversionSummary;
