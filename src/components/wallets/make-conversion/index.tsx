import React, { useEffect, useState, useRef, use<PERSON>allback, useMemo } from "react";
import { CURRENCIES, Wallet } from "@/assets/interfaces";
import { amountFormat, getFieldvalues, millify, toCurrency } from "@/assets/js/utils/functions";
import { to<PERSON>ob<PERSON>, to<PERSON><PERSON><PERSON> } from "@/assets/js/utils/utils";
import useSteps from "@/components/hooks/useSteps";
import { AppBtn } from "@/components/ui/buttons";
import { InputWithAddon, SelectDropdown } from "@/components/ui/form-elements";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { FormikProps, useFormik } from "formik";
import { useRouter } from "next/router";
import * as Yup from "yup";
import { useFetcher, useRequest } from "@/api/utils";
import { GenerateQuoteParams, InitiateConversionParams } from "@/api/interfaces";
import { GenerateQuote, InitiateConversion } from "@/api";
import ConversionSummary from "./summary";
import EnterSecurityPin from "./enter-security-pin";
import SuccessAnimation from "@/components/ui/success-animation";
import ErrorLabel from "@/components/ui/error-label";
import { WalletBalances } from "@/contexts/wallet-context";
import { CURRENCY_FLAG_MAP, paymentsEnabledCurrencies } from "@/assets/js/utils/constants";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  initialWallet: Wallet;
  wallets: Wallet[];
  balances: WalletBalances["wallets"];
  debitBalance: (amount: number, wallet: string) => void;
  currency: CURRENCIES;
}

const MakeConversion: React.FC<Props> = ({
  show,
  toggle,
  wallets,
  initialWallet,
  balances,
  debitBalance,
  currency,
}) => {
  const router = useRouter();
  const { step, next, changeStep, isActive, previous, canNext } = useSteps(
    ["CONVERSION", "SUMMARY", "SECURITY_PIN", "SUCCESS"],
    0
  );

  const [error, setError] = useState("");
  const [debouncedAmount, setDebouncedAmount] = useState(0);

  // const [quoteData, setQuoteData] = useState(null);
  const [timeLeft, setTimeLeft] = useState(0);
  const debounceTimeoutRef = useRef(null);
  const countdownTimerRef = useRef(null);

  // let form: FormikProps<ConversionForm>;
  const storeCurrencies = useMemo(() => wallets.map((w) => w.currency), [wallets]);

  // Initialize the useRequest hook
  const initiateConversionReq = useRequest<InitiateConversionParams>(InitiateConversion);

  const form = useFormik<ConversionForm>({
    initialValues: {
      source_currency: initialWallet.currency,
      destination_currency: null,
      amount: 0,
      code: "",
      security_pin: "",
    },
    validationSchema: validationSchema(balances, step),
    onSubmit: async (values) => {
      switch (step) {
        case "CONVERSION":
          const insufficientFunds = toKobo(form.values.amount + quoteData.fee) > wallet?.balance;
          if (insufficientFunds) {
            setError("Insufficient funds for fee, please adjust the amount");
            return;
          }

          next();
          break;
        case "SUMMARY":
          next();
          break;
        case "SECURITY_PIN":
          const [res, err] = await initiateConversionReq.makeRequest({
            quote_reference: quoteData.reference,
            amount: values.amount,
            security_pin: values.security_pin,
            from_wallet: wallets.find((w) => w.currency === values.source_currency).id,
            to_wallet: wallets.find((w) => w.currency === values.destination_currency).id,
          });

          if (err && err.statusCode !== 412) {
            changeStep("CONVERSION");
            return;
          }

          if (!err) {
            debitBalance(Number(values.amount) + Number(quoteData.fee), wallet?.id);
            next();
          }

          break;
        case "SUCCESS":
          toggle(false);
          break;
      }
    },
  });

  const generateQuoteReq = useFetcher<GenerateQuoteParams>(
    GenerateQuote,
    {
      source_currency: form?.values?.source_currency,
      destination_currency: form?.values?.destination_currency,
      amount: debouncedAmount,
    },
    ["source_currency", "destination_currency", "amount"] //required values
  );

  const quoteData: ConversionQuote = generateQuoteReq.response?.data ?? {
    reference: "",
    rate: 0,
    fee: 0,
    amount_to_receive: 0,
    expire_at: "",
  };
  const isValidQuote = quoteData && quoteData.rate > 0 && quoteData.reference;

  const [wallet, destinationCurrencyOptions] = useMemo(() => {
    const wallet = wallets.find((w) => w.currency === (form?.values?.source_currency ?? initialWallet.currency));
    const newOptions = paymentsEnabledCurrencies
      .filter((quoteCurrency) => isPairSupported(form.values.source_currency, quoteCurrency))
      .map((currency) => ({
        text: `${CURRENCY_FLAG_MAP[currency]} ${currency}`,
        value: currency,
      }));

    if (!newOptions.find((option) => option.value === form.values.destination_currency)) {
      form.setFieldValue("destination_currency", newOptions.length > 0 ? newOptions[0].value : "");
    }

    return [wallet, newOptions];
  }, [form?.values]);

  useEffect(() => {
    form.setFieldValue("source_currency", initialWallet.currency);
  }, [initialWallet]);

  useEffect(() => {
    if (!show) {
      form.setValues({
        source_currency: initialWallet.currency,
        destination_currency: null,
        amount: 0,
        code: "",
        security_pin: "",
      });
      setError("");
      setTimeLeft(0);
      initiateConversionReq.clearResponse();
      generateQuoteReq.clearResponse();
      changeStep("CONVERSION");
    }
  }, [show]);

  // Fetch quote whenever amount or currency pairs change, with debouncing
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      if (!form.errors.amount) setDebouncedAmount(form.values.amount);
    }, 500);

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [form.values.amount, form.errors.amount]);

  useEffect(() => {
    if (countdownTimerRef.current) clearInterval(countdownTimerRef.current);

    if (!quoteData.expire_at) return;
    const timeLeftInSeconds = dayjs(quoteData.expire_at).diff(dayjs(), "second");
    setTimeLeft(timeLeftInSeconds);

    countdownTimerRef.current = setInterval(() => {
      setTimeLeft((prevTimeLeft) => {
        const timeLeft = prevTimeLeft - 1;
        if (timeLeft === 0) {
          clearInterval(countdownTimerRef.current);
          refetchQuote();

          return 0;
        }

        return timeLeft;
      });
    }, 1000);

    return () => {
      if (countdownTimerRef.current) clearInterval(countdownTimerRef.current);
    };
  }, [quoteData.reference, step]);

  const refetchQuote = () => {
    setTimeout(() => {
      if (step !== "CONVERSION") {
        return;
      }
      generateQuoteReq.makeRequest();
    }, 250); //waiting a few milliseconds helps prevent a bug where the setTimeLeft react state update retriggers
  };

  const insufficientFunds = toKobo(form.values.amount + quoteData.fee) > wallet?.balance;

  const stepCTAMap = {
    CONVERSION: {
      label: "Continue",
      disabled:
        !canNext ||
        generateQuoteReq.isLoading ||
        !quoteData ||
        !form.values.amount ||
        !form.values.destination_currency ||
        insufficientFunds,
      onClick: () => form.handleSubmit(),
      loading: generateQuoteReq.isLoading,
      loadingText: "Fetching Quote...",
    },
    SUMMARY: {
      label: "Convert",
      disabled: false,
      onClick: () => changeStep("SECURITY_PIN"),
      loading: false,
      loadingText: "",
    },
    SECURITY_PIN: {
      label: "Confirm Conversion",
      disabled: initiateConversionReq.isLoading,
      onClick: () => form.handleSubmit(),
      loading: initiateConversionReq.isLoading,
      loadingText: "Please wait...",
    },
    SUCCESS: {
      label: "Back to Dashboard",
      disabled: false,
      onClick: () => {
        toggle(false);
        // router.push("/dashboard");
      },
    },
  };

  return (
    <Modal
      {...{ show, toggle }}
      title={step === "SUMMARY" ? "Confirm Conversion" : step === "SUCCESS" ? "Conversion Processing" : "Convert"}
      size="midi"
    >
      <ModalBody className="min-h-[270px]">
        {isPastFourPmGMT && step === "CONVERSION" && (
          <div className="bg-grey-fields-100 text-dark text-1xs -mt-5 sm:-mt-6.25 -mx-5 sm:-mx-6.25 mb-5 sm:mb-6.25 px-3.5 py-2.5 border-t border-grey-border text-center font-medium">
            Conversions past 4pm GMT might take up to 24 hours to process
          </div>
        )}
        <ErrorLabel error={initiateConversionReq?.error?.message || generateQuoteReq?.error?.message || error} />
        {step === "CONVERSION" && (
          <div>
            <div className="-mt-5">
              <InputWithAddon
                label="Amount to convert"
                placeholder="Enter amount"
                type="number"
                {...getFieldvalues("amount", form)}
                inputMode="decimal"
                onChange={(e) => form.setFieldValue("amount", Number(e.target.value))}
                className="bg-white"
                childIsDropdown={true}
              >
                <SelectDropdown
                  label=""
                  options={storeCurrencies.map((currency) => ({
                    text: `${CURRENCY_FLAG_MAP[currency]} ${currency}`,
                    value: currency,
                  }))}
                  {...getFieldvalues("source_currency", form)}
                  isAddon={true}
                />
                <div className="flex items-center ml-auto py-1.25 px-2.5 bg-white rounded-15 absolute font-normal text-xxs top-1/2 transform -translate-y-1/2 right-3 text-black-placeholder">
                  Balance:
                  <span className="text-black-secondary font-medium inline-block ml-0.5">
                    {toCurrency(toNaira(wallet.balance), form?.values.source_currency)}
                  </span>
                </div>
              </InputWithAddon>
              {insufficientFunds && (
                <div className="text-accent-red-500 text-xs font-medium mt-1 flex items-center">
                  {/* prettier-ignore */}
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="mr-1">
                    <path
                      d="M7.018 3.55288L2.15392 11.6731C2.05363 11.8467 2.00057 12.0437 2 12.2442C1.99944 12.4447 2.0514 12.6419 2.15071 12.8162C2.25003 12.9904 2.39323 13.1356 2.56607 13.2373C2.73892 13.339 2.93538 13.3937 3.13592 13.3959H12.8641C13.0646 13.3937 13.2611 13.339 13.4339 13.2373C13.6068 13.1356 13.75 12.9904 13.8493 12.8162C13.9486 12.6419 14.0006 12.4447 14 12.2442C13.9994 12.0437 13.9464 11.8467 13.8461 11.6731L8.982 3.55288C8.87963 3.3841 8.73548 3.24456 8.56347 3.14772C8.39146 3.05088 8.1974 3 8 3C7.8026 3 7.60854 3.05088 7.43653 3.14772C7.26452 3.24456 7.12037 3.3841 7.018 3.55288V3.55288Z"
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path d="M8 6.50452V8.8016" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M8 11.0992H8.00718" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <span className="inline-block pt-1">Insufficient funds for fee</span>
                </div>
              )}
            </div>

            {quoteData && isValidQuote ? (
              <div className="bg-grey-fields-100 mt-4 border-grey-border border-opacity-50 divide-y divide-grey-border divide-opacity-50 rounded-lg">
                <div className="flex justify-between items-center p-4">
                  <span className="text-sm text-dark">Conversion Rate:</span>
                  <span className="text-sm text-black-secondary font-medium">
                    1.00 {form.values.source_currency} = {amountFormat(quoteData.rate, 3)}{" "}
                    {form.values.destination_currency}
                  </span>
                </div>
                <div className="flex justify-between items-center p-4">
                  <span className="text-sm text-dark">Conversion Fee:</span>
                  <span className="text-sm text-black-secondary font-medium">
                    {quoteData.fee} {form.values.source_currency}
                  </span>
                </div>
                <div className="flex justify-between items-center p-4">
                  <span className="text-sm text-dark">Quote Expires In:</span>
                  {generateQuoteReq.isLoading ? (
                    <span className="text-sm text-black-secondary font-medium">Refetching...</span>
                  ) : (
                    <span className="text-sm text-black-secondary font-medium">{timeLeft} seconds</span>
                  )}
                </div>
              </div>
            ) : generateQuoteReq.isLoading ? (
              <div className="bg-grey-fields-100 mt-4 rounded-lg p-4">
                <span className="text-sm text-dark">Fetching conversion rate...</span>
              </div>
            ) : null}

            <div>
              <InputWithAddon
                label="Amount you'll receive"
                placeholder=""
                type="number"
                inputMode="decimal"
                className="bg-white"
                childIsDropdown={true}
                readOnly={true}
                value={quoteData?.amount_to_receive}
                name="destination_amount"
              >
                <SelectDropdown
                  label=""
                  options={destinationCurrencyOptions}
                  {...getFieldvalues("destination_currency", form)}
                  // onChange={(e) => form.setFieldValue("destination_currency", e.target.value)}
                  isAddon={true}
                />
              </InputWithAddon>
            </div>
          </div>
        )}

        {step === "SUMMARY" && (
          <div>
            {/* Display summary of the conversion */}
            <ConversionSummary
              form={form}
              conversion_rate={quoteData?.rate}
              conversion_fee={quoteData?.fee}
              destination_amount={quoteData.amount_to_receive}
            />
          </div>
        )}

        {step === "SECURITY_PIN" && <EnterSecurityPin form={form} />}

        {step === "SUCCESS" && (
          <div className="flex flex-col items-center w-full py-8">
            <figure className="mb-3.5">
              <SuccessAnimation />
            </figure>
            <h2 className="text-center text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto !leading-tight font-light">
              Your conversion is <br /> <b>Processing</b>
            </h2>

            <p className="text-dark text-sm leading-tight text-center max-w-xs mx-auto mt-4">
              We're processing your conversion, you'll get notified once it's completed
            </p>
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <div className="flex items-center space-x-4 w-full">
          {isActive("SUMMARY") && (
            <AppBtn isBlock color="neutral" className="flex-1" onClick={previous} size="lg">
              Back
            </AppBtn>
          )}
          <AppBtn
            type="submit"
            isBlock
            className="flex-1"
            disabled={stepCTAMap[step].disabled}
            onClick={stepCTAMap[step].onClick}
            size="lg"
          >
            {stepCTAMap[step]?.loading ? stepCTAMap[step].loadingText : stepCTAMap[step].label}
          </AppBtn>
        </div>
      </ModalFooter>
    </Modal>
  );
};

const validationSchema = (balances: WalletBalances["wallets"], step: string) =>
  Yup.object().shape({
    amount: Yup.number()
      .typeError("Amount must be a number")
      .required("Amount is required")
      .min(1, "Please enter an amount greater than zero")
      .test("is-greater", "Insufficient funds", function (value) {
        const balance = balances.find((b) => b.currency === this.parent.source_currency).balance || 0;
        return balance >= value * 100;
      }),
    source_currency: Yup.string().required("Source currency is required"),
    destination_currency: Yup.string()
      .required("Destination currency is required")
      .test("is_pair_supported", "This currency pair is not supported", function (value) {
        return isPairSupported(this.parent.source_currency, value);
      }),
    security_pin:
      step === "SECURITY_PIN"
        ? Yup.string().required("Security PIN is required").min(6, "Security PIN must be at least 6 digits")
        : Yup.string(),
  });

export interface ConversionForm {
  source_currency: CURRENCIES;
  destination_currency: CURRENCIES;
  amount: number;
  code: string;
  security_pin: string;
}

export interface ConversionQuote {
  reference: string;
  rate: number;
  fee: number;
  amount_to_receive: number;
  expire_at: string;
}

const pairsToSkip = new Set([
  "NGN/GHS",
  "NGN/ZAR",
  "NGN/KES",
  "GHS/NGN",
  "GHS/KES",
  "GHS/ZAR",
  "KES/ZAR",
  "KES/GHS",
  "ZAR/NGN",
  "ZAR/KES",
  "ZAR/GHS",
]);

const unsupportedCurrencies = [];

const isPairSupported = (baseCurrency, quoteCurrency) => {
  if (baseCurrency === quoteCurrency) return false;
  if (unsupportedCurrencies.includes(baseCurrency) || unsupportedCurrencies.includes(quoteCurrency)) {
    return false;
  }
  const pair = `${baseCurrency}/${quoteCurrency}`;
  if (pairsToSkip.has(pair)) {
    return false;
  }
  return true;
};

const isPastFourPmGMT = dayjs().utc().isAfter(dayjs.utc().hour(16).minute(0).second(0).millisecond(0));

export default MakeConversion;
