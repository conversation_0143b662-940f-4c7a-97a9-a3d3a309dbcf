import React, { forwardRef, Ref, useEffect, useImper<PERSON><PERSON><PERSON><PERSON>, useState } from "react";
import { CreateTestPayment, GetStoreWallet, InitiateTestPayment } from "../../../api";
import { useFetcher, useRequest } from "../../../api/utils";
import { toCurrency } from "../../../assets/js/utils/functions";
import useSteps from "../../hooks/useSteps";
import authContext from "../../../contexts/auth-context";
import ErrorLabel from "../../ui/error-label";
import useSlider from "../../hooks/useSlider";
import { AccountInformation, COUNTRIES, CURRENCIES, PAYMENT_METHODS, Wallet } from "../../../assets/interfaces";
import useCopyClipboard from "../../hooks/useCopyClipboard";
import SuccessAnimation from "../../ui/success-animation";
import { to<PERSON><PERSON><PERSON> } from "../../../assets/js/utils/utils";
import { actionIsAllowed, SCOPES } from "../../../assets/js/utils/permissions";
import Head from "next/head";
import PayWithPaystack from "../../make-payments/pay-with-paystack";
import Portal from "../../portal";
import Radio from "../../ui/form-elements/radio";
import { useRouter } from "next/router";
import { COUNTRY_CURRENCY_MAP } from "@/assets/js/utils/constants";

interface Props {
  country: COUNTRIES;
  toggle?: (state: boolean) => void;
  setStep: (step: TEST_PAYMENT_STEPS) => void;
  setShowCTA: (state: boolean) => void;
  setCTADisabled: (state: boolean) => void;
  setIsLoading?: (state: boolean) => void;
  fromWidget?: boolean;
}

export enum TEST_PAYMENT_STEPS {
  INITIATING = "INITIATING",
  ACCOUNT_NUMBER = "ACCOUNT_NUMBER",
  WAITING = "WAITING",
  SUCCESS = "SUCCESS",
}

export type MakePaymentHandle = {
  cleanup: () => void;
  handleCTAClick: () => void;
};

const MakeTestPayment = forwardRef<MakePaymentHandle, Props>(
  ({ country, toggle, setCTADisabled, setShowCTA, setIsLoading, setStep, fromWidget }, ref) => {
    const { store, updateStore, userRole, user } = authContext.useContainer();
    const router = useRouter();
    const canViewBalance = actionIsAllowed({
      permission: SCOPES.WALLETS.CAN_VIEW_BALANCE,
      userRole,
    });
    const [error, setError] = useState("");
    const [balance, setBalance] = useState(0);
    const [selectedMethod, setSelectedMethod] = useState<PAYMENT_METHODS>(null);
    const { step, steps, stepIndex, changeStep } = useSteps<TEST_PAYMENT_STEPS>(Object.values(TEST_PAYMENT_STEPS), 0);
    const { next, slider, slide, previous, slides, currentSlide, switchSlides } = useSlider({
      slides: 2,
      sameWidthAsSlider: false,
      gap: 0,
    });
    const getStoreWalletReq = useFetcher(GetStoreWallet);
    const initiateTestPaymentReq = useRequest(CreateTestPayment);
    const payment = initiateTestPaymentReq?.response?.data ?? null;

    const wallet: Wallet = getStoreWalletReq?.response?.data ?? null;
    const account: AccountInformation = wallet?.accounts.length > 0 ? selectAccount(wallet.accounts) : null;
    const [copied, copyAccount] = useCopyClipboard(account?.account_number ?? "", { successDuration: 500 });
    const TEST_PAYMENT_AMOUNT = TEST_PAYMENT_AMOUNTS[country];

    useEffect(() => {
      if (step === TEST_PAYMENT_STEPS.ACCOUNT_NUMBER || step === TEST_PAYMENT_STEPS.WAITING) {
        switchSlides(step === TEST_PAYMENT_STEPS.ACCOUNT_NUMBER ? 0 : 1);
      }
    }, [step]);

    // useEffect(() => {
    //   if (!show) {
    //   }
    // }, [show]);

    useEffect(() => {
      if (initiateTestPaymentReq?.isLoading) {
        setIsLoading(true);
      } else setIsLoading(false);

      if (initiateTestPaymentReq?.isLoading || !selectedMethod) {
        setCTADisabled(true);
      } else setCTADisabled(false);
    }, [initiateTestPaymentReq?.isLoading, selectedMethod]);

    useEffect(() => {
      setStep(step);
      if (step === TEST_PAYMENT_STEPS.WAITING && !account) {
        setShowCTA(false);
      } else setShowCTA(true);
    }, [step, account]);

    useImperativeHandle(ref, () => ({
      cleanup: () => {
        initiateTestPaymentReq.clearResponse();
        changeStep(TEST_PAYMENT_STEPS.INITIATING);
        setSelectedMethod(null);
      },
      handleCTAClick: async () => {
        if (step === TEST_PAYMENT_STEPS.INITIATING) {
          if (!account && country === COUNTRIES.NG) {
            setError("Account details not found, please reload page");
            return;
          }

          if (selectedMethod !== PAYMENT_METHODS.PAYMENT_LINK) {
            const [res, err] = await initiateTestPaymentReq.makeRequest({
              currency: COUNTRY_CURRENCY_MAP[country],
              payment_method: selectedMethod,
            });

            if (res) {
              const paymentLink = res?.data?.payment_link;

              if (paymentLink) {
                window.open(paymentLink, "_blank");
              }

              const clientId = `TEST_PAYMENT.${store?.id}`;
              const evtSource = new EventSource(`${process.env.NEXT_PUBLIC_API_URL}/events?client_id=${clientId}`);

              evtSource.onmessage = (e) => {
                const data = JSON.parse(e.data);

                if (data.event === "TEST_PAYMENT_SUCCESSFUL" && data?.payload?.reference === res?.data?.reference) {
                  setBalance(data.balance);
                  changeStep(TEST_PAYMENT_STEPS.SUCCESS);
                  updateStore({ onboarding_steps: { ...store?.onboarding_steps, test_payment_made: true } });
                }
              };

              evtSource.onerror = (e) => {
                console.error("SSE error", e);
              };

              if (selectedMethod === PAYMENT_METHODS.DIRECT_TRANSFER) {
                changeStep(TEST_PAYMENT_STEPS.ACCOUNT_NUMBER);
              } else {
                changeStep(TEST_PAYMENT_STEPS.WAITING);
              }
            }
          }

          if (selectedMethod === PAYMENT_METHODS.PAYMENT_LINK) {
            router.push(`/payments/payment-links/create`);
          }

          return;
        }

        if (step === TEST_PAYMENT_STEPS.ACCOUNT_NUMBER || step === TEST_PAYMENT_STEPS.WAITING) {
          changeStep(
            step === TEST_PAYMENT_STEPS.ACCOUNT_NUMBER ? TEST_PAYMENT_STEPS.WAITING : TEST_PAYMENT_STEPS.ACCOUNT_NUMBER
          );
          return;
        }

        if (toggle) toggle(false);
      },
    }));

    if (!wallet) return null;

    return (
      <>
        {step === TEST_PAYMENT_STEPS.INITIATING && (
          <div>
            <div className="flex flex-col items-center border-b border-grey-border border-opacity-50">
              <figure className="flex items-center justify-center w-15 h-15 md:h-17.5 md:w-17.5 rounded-full bg-accent-green-500 mb-5">
                {/* prettier-ignore */}
                <svg width="50%" viewBox="0 0 30 30" fill="none">
                  <path d="M23.75 18.75C20.9875 18.75 18.75 20.9875 18.75 23.75C18.75 24.6875 19.0125 25.575 19.475 26.325C20.3375 27.775 21.925 28.75 23.75 28.75C25.575 28.75 27.1625 27.775 28.025 26.325C28.4875 25.575 28.75 24.6875 28.75 23.75C28.75 20.9875 26.5125 18.75 23.75 18.75ZM26.3375 23.2125L23.675 25.675C23.5 25.8375 23.2625 25.925 23.0375 25.925C22.8 25.925 22.5625 25.8375 22.375 25.65L21.1375 24.4125C20.775 24.05 20.775 23.45 21.1375 23.0875C21.5 22.725 22.1 22.725 22.4625 23.0875L23.0625 23.6875L25.0625 21.8375C25.4375 21.4875 26.0375 21.5125 26.3875 21.8875C26.7375 22.2625 26.7125 22.85 26.3375 23.2125Z" fill="white" />
                  <path d="M27.5 9.4375V10C27.5 10.6875 26.9375 11.25 26.25 11.25H3.75C3.0625 11.25 2.5 10.6875 2.5 10V9.425C2.5 6.5625 4.8125 4.25 7.675 4.25H22.3125C25.175 4.25 27.5 6.575 27.5 9.4375Z" fill="white" />
                  <path d="M2.5 14.375V20.575C2.5 23.4375 4.8125 25.75 7.675 25.75H15.5C16.225 25.75 16.85 25.1375 16.7875 24.4125C16.6125 22.5 17.225 20.425 18.925 18.775C19.625 18.0875 20.4875 17.5625 21.425 17.2625C22.9875 16.7625 24.5 16.825 25.8375 17.275C26.65 17.55 27.5 16.9625 27.5 16.1V14.3625C27.5 13.675 26.9375 13.1125 26.25 13.1125H3.75C3.0625 13.125 2.5 13.6875 2.5 14.375ZM10 21.5625H7.5C6.9875 21.5625 6.5625 21.1375 6.5625 20.625C6.5625 20.1125 6.9875 19.6875 7.5 19.6875H10C10.5125 19.6875 10.9375 20.1125 10.9375 20.625C10.9375 21.1375 10.5125 21.5625 10 21.5625Z" fill="white" />
                </svg>
              </figure>
              <div className="mb-5 flex flex-col items-center">
                <h1 className="flex flex-col items-center justify-center text-black text-xl lg:text-2xl font-bold">
                  Make Test Payment
                </h1>
              </div>
              <ErrorLabel error={initiateTestPaymentReq?.error?.message ?? error} perm={true} />
            </div>
            <div className="bg-grey-fields-100 border-grey-border mt-2.5 rounded-10 p-3 flex items-start mb-5">
              <figure className="h-9 w-9 rounded-full bg-white shadow-pill flex items-center justify-center flex-shrink-0">
                {/* prettier-ignore */}
                <svg width="60%" className="text-accent-yellow-500" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM11.25 8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V13C12.75 13.41 12.41 13.75 12 13.75C11.59 13.75 11.25 13.41 11.25 13V8ZM12.92 16.38C12.87 16.51 12.8 16.61 12.71 16.71C12.61 16.8 12.5 16.87 12.38 16.92C12.26 16.97 12.13 17 12 17C11.87 17 11.74 16.97 11.62 16.92C11.5 16.87 11.39 16.8 11.29 16.71C11.2 16.61 11.13 16.51 11.08 16.38C11.03 16.26 11 16.13 11 16C11 15.87 11.03 15.74 11.08 15.62C11.13 15.5 11.2 15.39 11.29 15.29C11.39 15.2 11.5 15.13 11.62 15.08C11.86 14.98 12.14 14.98 12.38 15.08C12.5 15.13 12.61 15.2 12.71 15.29C12.8 15.39 12.87 15.5 12.92 15.62C12.97 15.74 13 15.87 13 16C13 16.13 12.97 16.26 12.92 16.38Z" fill="currentColor"/>
                  </svg>
              </figure>
              <div className="ml-2.5 text-dark text-1xs text-left mr-2.5">
                Test payments made will be added to your wallet balance, you can withdraw anytime you want
              </div>
            </div>
            {COUNTRY_PAYMENT_METHOD_MAP[country]
              .filter((m) => (fromWidget ? m !== PAYMENT_METHODS.PAYMENT_LINK : true))
              .map((method, index) => {
                const methodDetails = getPaymentMethods(toCurrency(TEST_PAYMENT_AMOUNT, wallet?.currency))[method];
                return (
                  <div
                    className="flex items-start w-full p-3.75 cursor-pointer justify-between border-grey-fields-100 border rounded-15 mt-2.5"
                    onClick={() => setSelectedMethod(method)}
                    key={method}
                  >
                    <div className="flex items-start">
                      {methodDetails.icon}
                      <div className="flex-1">
                        <h5 className="font-display block text-black-secondary font-bold text-base leading-none">
                          {methodDetails.title}
                        </h5>
                        <span className="text-dark text-1xs">{methodDetails.description}</span>
                      </div>
                    </div>

                    <Radio
                      small
                      name="method"
                      value={method}
                      onChange={() => setSelectedMethod(method)}
                      chosen={selectedMethod}
                    ></Radio>
                  </div>
                );
              })}
          </div>
        )}
        {(step === TEST_PAYMENT_STEPS.ACCOUNT_NUMBER || step === TEST_PAYMENT_STEPS.WAITING) &&
          selectedMethod === PAYMENT_METHODS.DIRECT_TRANSFER && (
            <div>
              <div className="relative flex flex-col items-center border-b border-grey-border border-opacity-50 mb-3.75">
                <figure className="flex items-center justify-center w-15 h-15 md:h-17.5 md:w-17.5 bg-accent-red-500 rounded-full my-3.75">
                  {/* prettier-ignore */}
                  <svg width="50%" viewBox="0 0 30 30" fill="none">
                    <path d="M27.3998 20.9375C26.9873 24.2625 24.2623 26.9875 20.9373 27.4C18.9248 27.65 17.0498 27.1 15.5873 26.025C14.7498 25.4125 14.9498 24.1125 15.9498 23.8125C19.7123 22.675 22.6748 19.7 23.8248 15.9375C24.1248 14.95 25.4248 14.75 26.0373 15.575C27.0998 17.05 27.6498 18.925 27.3998 20.9375Z" fill="white" />
                    <path d="M12.4875 2.5C6.975 2.5 2.5 6.975 2.5 12.4875C2.5 18 6.975 22.475 12.4875 22.475C18 22.475 22.475 18 22.475 12.4875C22.4625 6.975 18 2.5 12.4875 2.5ZM11.3125 11.0875L14.325 12.1375C15.4125 12.525 15.9375 13.2875 15.9375 14.4625C15.9375 15.8125 14.8625 16.925 13.55 16.925H13.4375V16.9875C13.4375 17.5 13.0125 17.925 12.5 17.925C11.9875 17.925 11.5625 17.5 11.5625 16.9875V16.9125C10.175 16.85 9.0625 15.6875 9.0625 14.2375C9.0625 13.725 9.4875 13.3 10 13.3C10.5125 13.3 10.9375 13.725 10.9375 14.2375C10.9375 14.6875 11.2625 15.05 11.6625 15.05H13.5375C13.825 15.05 14.05 14.7875 14.05 14.4625C14.05 14.025 13.975 14 13.6875 13.9L10.675 12.85C9.6 12.475 9.0625 11.7125 9.0625 10.525C9.0625 9.175 10.1375 8.0625 11.45 8.0625H11.5625V8.0125C11.5625 7.5 11.9875 7.075 12.5 7.075C13.0125 7.075 13.4375 7.5 13.4375 8.0125V8.0875C14.825 8.15 15.9375 9.3125 15.9375 10.7625C15.9375 11.275 15.5125 11.7 15 11.7C14.4875 11.7 14.0625 11.275 14.0625 10.7625C14.0625 10.3125 13.7375 9.95 13.3375 9.95H11.4625C11.175 9.95 10.95 10.2125 10.95 10.5375C10.9375 10.9625 11.0125 10.9875 11.3125 11.0875Z" fill="white" />
                  </svg>
                </figure>
                <h1 className="flex flex-col items-center justify-center text-black text-2lg xl:text-2xl font-medium mb-3.75 xl:mb-5 !leading-snug">
                  <span className="block">Transfer</span>
                  <span className="font-bold">{toCurrency(TEST_PAYMENT_AMOUNT, wallet.currency)}</span>
                </h1>
              </div>
              <div className="bg-grey-fields-200 rounded-10 py-6.25">
                <ul className="grid w-full grid-cols-[100%,100%] overflow-hidden" ref={slider}>
                  <div className="flex flex-col items-center flex-1" ref={slide}>
                    <span className="bg-white text-xs font-semibold rounded-5 px-2.5 py-1.5 mb-2.5 text-primary-500">
                      {account.bank_name}
                    </span>
                    <div className="flex items-center mb-1.25">
                      <h1 className="text-2lg font-bold">{account.account_number}</h1>
                      <button
                        className="flex items-center justify-center text-primary-500 bg-primary-500 bg-opacity-5 rounded-5 p-1.25 ml-1.25"
                        onClick={() => copyAccount(account.account_number)}
                      >
                        {/* prettier-ignore */}
                        <svg width="14" height="15" viewBox="0 0 14 15" fill="none">
                      <path d="M6.47533 13.7709H4.02533C1.74449 13.7709 0.729492 12.7559 0.729492 10.475V8.02502C0.729492 5.74419 1.74449 4.72919 4.02533 4.72919H6.47533C8.75616 4.72919 9.77116 5.74419 9.77116 8.02502V10.475C9.77116 12.7559 8.75616 13.7709 6.47533 13.7709ZM4.02533 5.60419C2.21699 5.60419 1.60449 6.21669 1.60449 8.02502V10.475C1.60449 12.2834 2.21699 12.8959 4.02533 12.8959H6.47533C8.28366 12.8959 8.89616 12.2834 8.89616 10.475V8.02502C8.89616 6.21669 8.28366 5.60419 6.47533 5.60419H4.02533Z" fill="currentColor" />
                      <path d="M9.97533 10.2709H9.33366C9.09449 10.2709 8.89616 10.0725 8.89616 9.83335V8.02502C8.89616 6.21669 8.28366 5.60419 6.47533 5.60419H4.66699C4.42783 5.60419 4.22949 5.40585 4.22949 5.16669V4.52502C4.22949 2.24419 5.24449 1.22919 7.52533 1.22919H9.97533C12.2562 1.22919 13.2712 2.24419 13.2712 4.52502V6.97502C13.2712 9.25585 12.2562 10.2709 9.97533 10.2709ZM9.77116 9.39585H9.97533C11.7837 9.39585 12.3962 8.78335 12.3962 6.97502V4.52502C12.3962 2.71669 11.7837 2.10419 9.97533 2.10419H7.52533C5.71699 2.10419 5.10449 2.71669 5.10449 4.52502V4.72919H6.47533C8.75616 4.72919 9.77116 5.74419 9.77116 8.02502V9.39585Z" fill="currentColor" />
                    </svg>
                      </button>
                    </div>
                    <span className="text-sm">{account?.account_name}</span>
                  </div>
                  <div className="flex flex-col items-center flex-1">
                    <span className="bg-white text-xs font-semibold rounded-5 px-2.5 py-1.5 text-primary-500">
                      Waiting for Transfer
                    </span>
                    <span className="inline-block text-dark text-xs text-center mt-1.5 mb-2.5 max-w-[220px]">
                      We&apos;re waiting to receive your transfer. This can take some minutes
                    </span>
                    <div className="mt-2.5 bg-white h-1.5 w-full max-w-[200px] rounded-10 overflow-hidden">
                      <div className="bg-accent-green-500 h-full rounded-10 w-1/4 transfer-waiting-loader"></div>
                    </div>
                  </div>
                </ul>
              </div>
            </div>
          )}
        {step === TEST_PAYMENT_STEPS.WAITING && selectedMethod !== PAYMENT_METHODS.DIRECT_TRANSFER && (
          <div className="py-20 flex flex-col items-center justify-center">
            <div className="spinner spinner--md text-primary-500"></div>
            <span className="text-sm font-semibold text-black-secondary inline-block mt-2.5">
              Waiting for payment...
            </span>
          </div>
        )}
        {step === TEST_PAYMENT_STEPS.SUCCESS && (
          <div>
            <div className="flex flex-col items-center py-4 w-full border-b border-grey-divider">
              <SuccessAnimation />
              <h1 className="text-xl sm:text-2lg md:text-2xl font-bold text-center mt-5 mb-2.5 !leading-tight">
                Your payment <br /> was successful
              </h1>
            </div>
            <div className="mt-4">
              <div className="bg-grey-fields-100 rounded-10 py-6.25">
                <div className="flex flex-col items-center flex-1">
                  <span className="bg-white text-xs font-semibold rounded-5 px-2.5 py-1.5 text-primary-500">
                    Wallet Balance
                  </span>
                  <div className="mt-2.5">
                    <h1 className="text-xl font-bold">
                      {canViewBalance
                        ? toCurrency(toNaira(Number(balance)), wallet?.currency)
                        : `${wallet?.currency} ******`}
                    </h1>
                  </div>
                  <div className="flex items-center mt-3 bg-white rounded-20 py-1 pl-1 pr-2">
                    {/* prettier-ignore */}
                    <svg className="w-4 text-accent-green-500" viewBox="0 0 20 20" fill="none">
                        <path d="M14 6L6 14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        <path d="M14.1667 14.1667H5.83341V5.83341"  stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"  />
                      </svg>
                    <span className="text-1xs text-black-secondary inline-block ml-1">
                      Recieved <b className="font-semibold"> {toCurrency(TEST_PAYMENT_AMOUNT, wallet?.currency)}</b>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        <Head>
          <script src="https://js.paystack.co/v1/inline.js" async></script>
        </Head>
        <Portal>
          {payment && payment?.payment_method === PAYMENT_METHODS.PAYSTACK && (
            <PayWithPaystack
              paymentData={{
                email: user?.email || "<EMAIL>",
                amount: payment?.amount_with_charge,
                reference: payment?.reference,
                publicKey: payment?.public_key,
                currency: payment?.currency,
              }}
              onClose={() => {}}
              callback={() => {
                changeStep(TEST_PAYMENT_STEPS.WAITING);
              }}
            />
          )}
        </Portal>
      </>
    );
  }
);

function selectAccount(accounts: Wallet["accounts"]) {
  return accounts.find((account) => account.provider === "PAYAZA") || accounts[0];
}

export const CTALabels = {
  [TEST_PAYMENT_STEPS.INITIATING]: "Get Started",
  [TEST_PAYMENT_STEPS.ACCOUNT_NUMBER]: "I have made the transfer",
  [TEST_PAYMENT_STEPS.WAITING]: "Show account number",
  [TEST_PAYMENT_STEPS.SUCCESS]: "Done",
};

const TEST_PAYMENT_AMOUNTS = {
  [COUNTRIES.NG]: 100,
  [COUNTRIES.GH]: 5,
  [COUNTRIES.KE]: 10,
  [COUNTRIES.ZA]: 5,
};

const COUNTRY_PAYMENT_METHOD_MAP = {
  [COUNTRIES.NG]: [PAYMENT_METHODS.PAYSTACK, PAYMENT_METHODS.DIRECT_TRANSFER, PAYMENT_METHODS.PAYMENT_LINK],
  [COUNTRIES.GH]: [PAYMENT_METHODS.PAYSTACK, PAYMENT_METHODS.PAYMENT_LINK],
  [COUNTRIES.ZA]: [PAYMENT_METHODS.PAYSTACK, PAYMENT_METHODS.PAYMENT_LINK],
  [COUNTRIES.KE]: [PAYMENT_METHODS.STARTBUTTON, PAYMENT_METHODS.PAYMENT_LINK],
};

const getPaymentMethods = (amountToPay: string) => ({
  [PAYMENT_METHODS.PAYSTACK]: {
    title: "Test with Paystack",
    description: `Make a test payment of ${amountToPay} via Paystack`,
    icon: (
      <figure className="bg-grey-fields-100 shadow-card rounded-full mr-2.5 h-8 w-8 text-accent-orange-500 flex items-center justify-center">
        {/* prettier-ignore */}
        <svg width="50%" viewBox="0 0 20 20" fill="none">
          <path d="M17.9221 0H1.03895C0.467516 0 0 0.476179 0 1.05821V2.96296C0 3.54498 0.467516 4.02116 1.03895 4.02116H17.8701C18.4416 4.02116 18.9091 3.54498 18.9091 2.96296V1.05821C18.961 0.476179 18.4935 0 17.9221 0ZM17.9221 10.6349H1.03895C0.467516 10.6349 0 11.1111 0 11.6931V13.5979C0 14.1799 0.467516 14.6561 1.03895 14.6561H17.8701C18.4416 14.6561 18.9091 14.1799 18.9091 13.5979V11.6931C18.961 11.1111 18.4935 10.6349 17.9221 10.6349ZM10.5455 15.9788H1.03895C0.467516 15.9788 0 16.455 0 17.037V18.9418C0 19.5238 0.467516 20 1.03895 20H10.5455C11.1169 20 11.5844 19.5238 11.5844 18.9418V17.037C11.5844 16.455 11.1169 15.9788 10.5455 15.9788ZM18.961 5.34392H1.03895C0.467516 5.34392 0 5.8201 0 6.40209V8.30688C0 8.88887 0.467516 9.36508 1.03895 9.36508H18.961C19.5325 9.36508 20 8.88887 20 8.30688V6.40209C20 5.8201 19.5325 5.34392 18.961 5.34392Z" fill="#0BA4DB"/>
        </svg>
      </figure>
    ),
  },
  [PAYMENT_METHODS.DIRECT_TRANSFER]: {
    title: "Test with Transfers",
    description: `Test your business account by sending ${amountToPay}`,
    icon: (
      <figure className="bg-grey-fields-100 shadow-card rounded-full mr-2.5 h-8 w-8 text-accent-red-500 flex items-center justify-center">
        {/* prettier-ignore */}
        <svg width="60%" viewBox="0 0 24 24" fill="none"><path d="M22 19V22H2V19C2 18.45 2.45 18 3 18H21C21.55 18 22 18.45 22 19Z" fill="currentColor" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M7 11H5V18H7V11Z" fill="currentColor"/>
          <path d="M11 11H9V18H11V11Z" fill="currentColor"/>
          <path d="M15 11H13V18H15V11Z" fill="currentColor"/>
          <path d="M19 11H17V18H19V11Z" fill="currentColor"/>
          <path d="M23 22.75H1C0.59 22.75 0.25 22.41 0.25 22C0.25 21.59 0.59 21.25 1 21.25H23C23.41 21.25 23.75 21.59 23.75 22C23.75 22.41 23.41 22.75 23 22.75Z" fill="currentColor"/>
          <path d="M21.37 5.74984L12.37 2.14984C12.17 2.06984 11.83 2.06984 11.63 2.14984L2.63 5.74984C2.28 5.88984 2 6.29984 2 6.67984V9.99984C2 10.5498 2.45 10.9998 3 10.9998H21C21.55 10.9998 22 10.5498 22 9.99984V6.67984C22 6.29984 21.72 5.88984 21.37 5.74984ZM12 8.49984C11.17 8.49984 10.5 7.82984 10.5 6.99984C10.5 6.16984 11.17 5.49984 12 5.49984C12.83 5.49984 13.5 6.16984 13.5 6.99984C13.5 7.82984 12.83 8.49984 12 8.49984Z" fill="currentColor"/>
        </svg>
      </figure>
    ),
  },
  [PAYMENT_METHODS.MOMO]: {
    title: "Test with Mobile Money",
    description: `Pay ${amountToPay} via Mobile Money`,
    icon: (
      <figure className="bg-grey-fields-100 shadow-card rounded-full mr-2.5 h-8 w-8 text-accent-orange-500 flex items-center justify-center">
        {/* prettier-ignore */}
        <svg width="60%" viewBox="0 0 110 110" fill="none" >
          <path d="M47.7578 61.5084H60.7745L62.2411 48.4917H49.2245L47.7578 61.5084Z" fill="currentColor"/>
          <path d="M74.2035 9.1665H35.7952C19.1118 9.1665 9.16602 19.1123 9.16602 35.7957V74.1582C9.16602 90.8873 19.1118 100.833 35.7952 100.833H74.1577C90.841 100.833 100.787 90.8873 100.787 74.204V35.7957C100.833 19.1123 90.8868 9.1665 74.2035 9.1665ZM86.2577 48.4915H68.9785L67.5118 61.554H82.9577C84.791 61.554 86.3035 63.0665 86.3035 64.8998C86.3035 66.7332 84.791 68.2457 82.9577 68.2457H66.7785L64.8993 85.0207C64.716 86.7165 63.2493 87.9998 61.5535 87.9998C61.416 87.9998 61.3243 87.9998 61.1868 87.9998C59.3535 87.8165 58.0243 86.1207 58.2077 84.2873L59.9952 68.2457H46.9785L45.0993 85.0207C44.916 86.7165 43.4493 87.9998 41.7535 87.9998C41.616 87.9998 41.5243 87.9998 41.3868 87.9998C39.5535 87.8165 38.2243 86.1207 38.4077 84.2873L40.1952 68.2457H23.741C21.9077 68.2457 20.3952 66.7332 20.3952 64.8998C20.3952 63.0665 21.9077 61.554 23.741 61.554H41.0202L42.4868 48.4915H27.041C25.2077 48.4915 23.6952 46.979 23.6952 45.1457C23.6952 43.3123 25.2077 41.7998 27.041 41.7998H43.2202L45.0993 25.0248C45.2827 23.1915 46.9785 21.8623 48.8118 22.0457C50.6452 22.229 51.9743 23.9248 51.791 25.7582L50.0035 41.7998H63.0202L64.8993 25.0248C65.1285 23.1915 66.7785 21.8623 68.6118 22.0457C70.4452 22.229 71.7743 23.9248 71.591 25.7582L69.8035 41.7998H86.3493C88.1827 41.7998 89.6952 43.3123 89.6952 45.1457C89.6952 46.979 88.091 48.4915 86.2577 48.4915Z" fill="currentColor"/>
        </svg>
      </figure>
    ),
  },
  [PAYMENT_METHODS.PAYMENT_LINK]: {
    title: "Test with payment links",
    description: `Make a test payment by creating a payment link`,
    icon: (
      <figure className="bg-grey-fields-100 shadow-card rounded-full mr-2.5 h-8 w-8 text-accent-orange-500 flex items-center justify-center">
        {/* prettier-ignore */}
        <svg width="60%" viewBox="0 0 24 24" fill="none">
          <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM8.18 16.77C8.16 16.77 8.13 16.77 8.11 16.77C7.14 16.68 6.23 16.23 5.55 15.51C3.95 13.83 3.95 11.1 5.55 9.42L7.74 7.12C8.52 6.3 9.57 5.84 10.69 5.84C11.81 5.84 12.86 6.29 13.64 7.12C15.24 8.8 15.24 11.53 13.64 13.21L12.55 14.36C12.26 14.66 11.79 14.67 11.49 14.39C11.19 14.1 11.18 13.63 11.46 13.33L12.55 12.18C13.61 11.07 13.61 9.26 12.55 8.16C11.56 7.12 9.82 7.12 8.82 8.16L6.63 10.46C5.57 11.57 5.57 13.38 6.63 14.48C7.06 14.94 7.64 15.22 8.25 15.28C8.66 15.32 8.96 15.69 8.92 16.1C8.89 16.48 8.56 16.77 8.18 16.77ZM18.45 14.59L16.26 16.89C15.48 17.71 14.43 18.17 13.31 18.17C12.19 18.17 11.14 17.72 10.36 16.89C8.76 15.21 8.76 12.48 10.36 10.8L11.45 9.65C11.74 9.35 12.21 9.34 12.51 9.62C12.81 9.91 12.82 10.38 12.54 10.68L11.45 11.83C10.39 12.94 10.39 14.75 11.45 15.85C12.44 16.89 14.18 16.9 15.18 15.85L17.37 13.55C18.43 12.44 18.43 10.63 17.37 9.53C16.94 9.07 16.36 8.79 15.75 8.73C15.34 8.69 15.04 8.32 15.08 7.91C15.12 7.5 15.48 7.19 15.9 7.24C16.87 7.34 17.78 7.78 18.46 8.5C20.05 10.17 20.05 12.91 18.45 14.59Z" fill="currentColor"/>
        </svg>
      </figure>
    ),
  },
});

MakeTestPayment.displayName = "MakeTestPayment";
export default MakeTestPayment;
