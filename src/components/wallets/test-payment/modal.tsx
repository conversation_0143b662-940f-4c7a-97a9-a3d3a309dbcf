import React, { useEffect, useRef, useState } from "react";
import { AppBtn } from "../../ui/buttons";
import Modal, { ModalBody, ModalFooter } from "../../ui/modal";
import { COUNTRIES } from "../../../assets/interfaces";
import MakeTestPayment, { CTALabels, MakePaymentHandle, TEST_PAYMENT_STEPS } from "./make-payment";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  country?: COUNTRIES;
}

const MakeTestPaymentModal: React.FC<Props> = ({ show, toggle, country }) => {
  const makePaymentRef = useRef<MakePaymentHandle>(null);

  const [step, setStep] = useState(TEST_PAYMENT_STEPS.INITIATING);
  const [showCTA, setShowCTA] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCTADisabled, setCTADisabled] = useState(false);

  useEffect(() => {
    if (!show) {
      makePaymentRef.current?.cleanup();
    }
  }, [show]);

  return (
    <>
      <Modal {...{ show, toggle }} title="Make Test Payment" size="midi">
        <ModalBody>
          <MakeTestPayment
            ref={makePaymentRef}
            {...{ setCTADisabled, setShowCTA, setIsLoading, setStep, country, toggle, fromWidget: true }}
          />
        </ModalBody>
        {showCTA ? (
          <ModalFooter>
            <AppBtn size="lg" isBlock onClick={makePaymentRef.current?.handleCTAClick} disabled={isCTADisabled}>
              {isLoading ? "Please wait..." : CTALabels[step]}
            </AppBtn>
          </ModalFooter>
        ) : null}
      </Modal>
    </>
  );
};

export default MakeTestPaymentModal;
