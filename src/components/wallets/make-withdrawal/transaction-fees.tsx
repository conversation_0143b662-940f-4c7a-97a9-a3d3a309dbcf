import React, { useMemo } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, PLAN_TYPE, WITHDRAWAL_FEE_TYPES } from "../../../assets/interfaces";
import { enumToHumanFriendly, getUserCountry, millify, toCurrency } from "../../../assets/js/utils/functions";
import { toNaira } from "../../../assets/js/utils/utils";
import Modal, { ModalBody } from "../../ui/modal";
import TabToggle from "../../ui/tab-toggle";
import Table, { TableBody, TableCell, TableHead, TableHeadItem, TableRow } from "../../ui/table";
import { GetTransactionFees } from "@/api/wallets";
import { useFetcher } from "@/api/utils";
import ErrorBox from "@/components/ui/error";
import AppBtn from "@/components/ui/buttons/app-btn";
import authContext from "@/contexts/auth-context";

interface TransactionFee {
  [key: string]: {
    percentage: number;
    cap: number;
  };
}

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  currency: CURRENCIES;
}

const TransactionFeesModal: React.FC<Props> = ({ show, toggle, currency }) => {
  const { response, isLoading, error, makeRequest } = useFetcher(GetTransactionFees, { currency });

  const fees = useMemo(() => {
    const allFees = (response?.data ?? {}) as TransactionFee;

    if (allFees[PLAN_TYPE.KITCHEN]) {
      delete allFees[PLAN_TYPE.KITCHEN];
    }

    return allFees;
  }, [response?.data]);

  return (
    <Modal {...{ show, toggle }} title="Transaction Fees" size="midi">
      <ModalBody>
        {isLoading && (
          <div className="py-12 flex items-center justify-center">
            <div className="spinner spinner--md text-primary-500"></div>
          </div>
        )}

        {!isLoading && (!fees || error) && (
          <ErrorBox title="Couldn't fetch fees" message="Something went wrong, please try again later">
            <AppBtn onClick={() => makeRequest()} size="sm" className="mt-3.75">
              Try again
            </AppBtn>
          </ErrorBox>
        )}

        {!isLoading && fees && (
          <>
            <span className="text-sm text-dark block mb-3.5">
              We charge a small processing fee every time you get paid, to help keep Catlog affordable.
              <br />
              <br />
              *The cap is the maximum amount of money you will ever be charged for a transaction.
            </span>
            <Table>
              <TableHead>
                <TableHeadItem>Plan</TableHeadItem>
                <TableHeadItem>Percentage</TableHeadItem>
                <TableHeadItem>Cap</TableHeadItem>
              </TableHead>
              <TableBody>
                {Object.entries(fees).map(([key, value], index) => (
                  <TableRow key={index}>
                    <TableCell>{enumToHumanFriendly(key)} Plan</TableCell>
                    <TableCell className="font-medium">{value.percentage}%</TableCell>
                    <TableCell className="font-medium">{toCurrency(toNaira(value.cap), currency)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </>
        )}
      </ModalBody>
    </Modal>
  );
};

export default TransactionFeesModal;
