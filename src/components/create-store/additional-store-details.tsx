import React, { useMemo } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { InputField, SelectDropdown, TextArea } from "../ui/form-elements";
import { AppBtn } from "../ui/buttons";
import { useFetcher, useRequest } from "../../api/utils";
import { getFieldvalues } from "../../assets/js/utils/functions";
import ErrorLabel from "../ui/error-label";
import { GetBusinessCategories, UpdateStoreAdditionalDetails, UpdateStoreDetails } from "@/api";
import { FILE_TYPES, StoreAdditionalDetails, StoreInterface } from "../../assets/interfaces";
import { UpdateStoreAdditionalDetailsParams } from "../../api/interfaces";
import authContext from "../../contexts/auth-context";
import router from "next/router";
import { extractUsername } from "@/assets/js/utils/extract-username-from-string";
import ImagePickerHorizontal from "../ui/form-elements/image-picker-horizontal";
import classNames from "classnames";

const StoreAdditionalDetailsForm: React.FC = () => {
  const { updateStore, user, store } = authContext.useContainer();
  const getBusinessCategoriesReq = useFetcher(GetBusinessCategories);
  const { makeRequest, error, response, isLoading } =
    useRequest<UpdateStoreAdditionalDetailsParams>(UpdateStoreAdditionalDetails);
  const businessCategories: string[] = getBusinessCategoriesReq?.response?.data ?? [];

  const categoriesOptions = useMemo(() => {
    return businessCategories.map((k) => ({ text: k, value: k }));
  }, [businessCategories]);

  const form = useFormik<StoreAdditionalDetails>({
    initialValues: {
      logo: store?.logo ?? "",
      description: store?.description ?? "",
      business_category: "",
      business_type: "",
      social_media_platform: "instagram",
      social_media_username: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      const [response, error] = await makeRequest({ additional_details: values, id: store.id });
      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors(error.fields);
        }
      } else {
        updateStore({ ...response.data });
        router.push("/setup/success");
      }
    },
  });

  const handleUserNameInputs = (e: React.ChangeEvent<HTMLInputElement>) => {
    const cleanedUsername = extractUsername(e.target.value, (error) =>
      form.setFieldError("social_media_username", error)
    );
    form.setFieldValue("social_media_username", cleanedUsername);
  };

  return (
    <>
      <form onSubmit={form.handleSubmit} className="w-full max-w-sm mx-auto mt-10">
        <ErrorLabel error={error?.message} />

        {/* Business Type */}
        <SelectDropdown
          label="What does your business do?"
          options={[
            { value: BUSINESS_TYPES.PHYSICAL, text: "I sell physical products (e.g. food, clothes)" },
            { value: BUSINESS_TYPES.DIGITAL, text: "I sell digital products (e.g. ebooks, courses)" },
            { value: BUSINESS_TYPES.PROPERTIES, text: "I sell properties (e.g. houses, cars)" },
            { value: BUSINESS_TYPES.SERVICE, text: "I'm service-based (e.g. graphic design, hair salon)" },
            { value: BUSINESS_TYPES.OTHERS, text: "Others" },
          ]}
          {...getFieldvalues("business_type", form)}
        />

        {/* Business Categorization */}
        <SelectDropdown
          label="Which category best describes your business?"
          options={categoriesOptions}
          hasSearch
          searchLabel="Search Categories"
          {...getFieldvalues("business_category", form)}
        />

        <div className="w-full h-px bg-grey-divider mt-3.75"></div>
        <h3 className="break-words text-base text-black font-bold !leading-tight mt-3.75">
          What's your business social media account?
        </h3>
        <div className="flex items-center space-x-3.5 mt-3.75">
          {Object.keys(socialIcons).map((platform, index) => (
            <div
              key={index}
              className={classNames(
                "flex items-center justify-center h-12.5 w-12.5 rounded-10 border cursor-pointer transition-all ease-out duration-200",
                {
                  "bg-white": form.values.social_media_platform === platform,
                  "bg-grey-fields-100 bg-opacity-50 border-grey-border border-opacity-50 text-grey-border-dark":
                    form.values.social_media_platform !== platform,
                }
              )}
              onClick={() => form.setFieldValue("social_media_platform", platform)}
              style={
                form.values.social_media_platform === platform
                  ? { borderColor: socialIcons[platform].color, color: socialIcons[platform].color }
                  : {}
              }
            >
              <div className="w-1/2">{socialIcons[platform].icon}</div>
            </div>
          ))}
        </div>

        {/* Social Media Username */}
        <InputField
          label={`${
            form?.values?.social_media_platform
              ? socialMediaPlatforms[form.values.social_media_platform]
              : "Social Media"
          } Username`}
          {...getFieldvalues("social_media_username", form)}
          onChange={(e) => handleUserNameInputs(e)}
        />

        {!store?.description && (
          <TextArea
            label="Describe your business - what you sell"
            maxLength={150}
            {...getFieldvalues("description", form)}
          />
        )}

        {!store?.logo && (
          <ImagePickerHorizontal
            className="mt-3.5"
            title="Upload business Logo - Optional"
            type={FILE_TYPES.STORES}
            saveImage={(url) => form.setFieldValue("logo", url)}
          />
        )}

        {/* Submit Button */}
        <AppBtn color="primary" isBlock className="mt-5" disabled={isLoading} type="submit" size="lg">
          {isLoading ? "Saving..." : "Proceed"}
        </AppBtn>
      </form>
    </>
  );
};

enum BUSINESS_TYPES {
  PHYSICAL = "PHYSICAL",
  DIGITAL = "DIGITAL",
  PROPERTIES = "PROPERTIES",
  SERVICE = "SERVICE",
  OTHERS = "OTHERS",
}

const socialMediaPlatforms = {
  instagram: "Instagram",
  facebook: "Facebook",
  snapchat: "Snapchat",
  twitter: "Twitter",
  tiktok: "TikTok",
};

const socialIcons = {
  instagram: {
    icon:
      // prettier-ignore
      <svg width="100%" viewBox="0 0 24 24" fill="none">
      <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM12 15.88C9.86 15.88 8.12 14.14 8.12 12C8.12 9.86 9.86 8.12 12 8.12C14.14 8.12 15.88 9.86 15.88 12C15.88 14.14 14.14 15.88 12 15.88ZM17.92 6.88C17.87 7 17.8 7.11 17.71 7.21C17.61 7.3 17.5 7.37 17.38 7.42C17.26 7.47 17.13 7.5 17 7.5C16.73 7.5 16.48 7.4 16.29 7.21C16.2 7.11 16.13 7 16.08 6.88C16.03 6.76 16 6.63 16 6.5C16 6.37 16.03 6.24 16.08 6.12C16.13 5.99 16.2 5.89 16.29 5.79C16.52 5.56 16.87 5.45 17.19 5.52C17.26 5.53 17.32 5.55 17.38 5.58C17.44 5.6 17.5 5.63 17.56 5.67C17.61 5.7 17.66 5.75 17.71 5.79C17.8 5.89 17.87 5.99 17.92 6.12C17.97 6.24 18 6.37 18 6.5C18 6.63 17.97 6.76 17.92 6.88Z" fill="currentColor"/>
    </svg>,
    color: "#9B4F96",
  },
  twitter: {
    icon:
      // prettier-ignore
      <svg width="100%" viewBox="0 0 24 24" fill="none">
      <path d="M22 5.79826C21.2483 6.12435 20.4534 6.33992 19.64 6.43826C20.4982 5.92558 21.1413 5.11904 21.45 4.16826C20.6436 4.64832 19.7608 4.98655 18.84 5.16826C18.2245 4.50083 17.405 4.05655 16.5098 3.90511C15.6147 3.75366 14.6945 3.90361 13.8938 4.33145C13.093 4.75928 12.4569 5.44079 12.0852 6.26909C11.7135 7.0974 11.6273 8.02565 11.84 8.90826C10.2094 8.82578 8.61444 8.40121 7.15865 7.66212C5.70287 6.92303 4.41885 5.88595 3.39 4.61826C3.02914 5.24842 2.83952 5.96208 2.84 6.68826C2.83872 7.36264 3.00422 8.02687 3.32176 8.62182C3.63929 9.21677 4.09902 9.72397 4.66 10.0983C4.00798 10.0805 3.36989 9.90556 2.8 9.58826V9.63826C2.80489 10.5831 3.13599 11.4973 3.73731 12.2262C4.33864 12.9551 5.17326 13.4539 6.1 13.6383C5.74326 13.7468 5.37288 13.8041 5 13.8083C4.74189 13.8052 4.48442 13.7818 4.23 13.7383C4.49391 14.5511 5.00462 15.2614 5.69107 15.7704C6.37753 16.2794 7.20558 16.5618 8.06 16.5783C6.6172 17.7135 4.83588 18.3331 3 18.3383C2.66574 18.3394 2.33174 18.3193 2 18.2783C3.87443 19.4885 6.05881 20.131 8.29 20.1283C9.82969 20.1442 11.3571 19.8533 12.7831 19.2723C14.2091 18.6914 15.505 17.8321 16.5952 16.7448C17.6854 15.6574 18.548 14.3636 19.1326 12.9392C19.7172 11.5147 20.012 9.98798 20 8.44826C20 8.27826 20 8.09826 20 7.91826C20.7847 7.33307 21.4615 6.61568 22 5.79826Z" fill="currentColor"/>
    </svg>,
    color: "#1DA1F2",
  },
  snapchat: {
    icon:
      // prettier-ignore
      <svg width="100%" viewBox="0 0 24 24" fill="none">
      <path fillRule="evenodd" clipRule="evenodd" d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM17.31 15.31C17.02 15.47 16.78 15.54 16.58 15.59C16.41 15.63 16.27 15.67 16.15 15.75C16.02 15.83 16.01 15.96 15.99 16.08C15.97 16.19 15.96 16.28 15.88 16.34C15.79 16.4 15.62 16.41 15.39 16.42C15.09 16.44 14.71 16.45 14.25 16.59C14.03 16.67 13.83 16.79 13.63 16.91C13.23 17.16 12.78 17.43 11.99 17.43C11.21 17.43 10.76 17.16 10.36 16.91C10.16 16.78 9.96001 16.66 9.73001 16.58C9.28001 16.44 8.89 16.42 8.59 16.41C8.36 16.4 8.20001 16.39 8.10001 16.33C8.02001 16.28 8.00999 16.18 7.98999 16.07C7.96999 15.95 7.95999 15.82 7.82999 15.74C7.70999 15.66 7.55999 15.62 7.39999 15.57C7.19999 15.52 6.96001 15.46 6.67001 15.3C6.33001 15.11 6.53001 15 6.64001 14.94C8.57001 14.01 8.87 12.57 8.88 12.46C8.88 12.45 8.88 12.45 8.88 12.44C8.9 12.32 8.90999 12.22 8.76999 12.09C8.67999 12 8.37 11.8 8.12 11.64C8.00999 11.56 7.91001 11.5 7.85001 11.47C7.57001 11.27 7.45001 11.08 7.54001 10.83C7.60001 10.66 7.76001 10.6 7.92001 10.6C7.97001 10.6 8.03001 10.61 8.07001 10.62C8.25001 10.66 8.43 10.73 8.59 10.79C8.7 10.84 8.78999 10.87 8.85999 10.89C8.87999 10.9 8.91001 10.9 8.92001 10.9C9.02001 10.9 9.05001 10.85 9.04001 10.75C9.04001 10.72 9.04 10.69 9.03 10.66C9.01 10.31 8.97999 9.70001 9.01999 9.14001C9.09999 8.30001 9.37 7.87001 9.69 7.51001C9.85 7.33001 10.58 6.56 11.97 6.56C13.37 6.56 14.09 7.33 14.25 7.5C14.57 7.87 14.84 8.29001 14.92 9.14001C14.97 9.73001 14.93 10.36 14.9 10.71V10.74C14.89 10.85 14.92 10.89 15.02 10.89C15.04 10.89 15.06 10.89 15.08 10.88C15.15 10.86 15.24 10.83 15.35 10.79C15.5 10.73 15.68 10.66 15.87 10.62C15.92 10.6 15.97 10.6 16.02 10.6C16.19 10.6 16.34 10.67 16.4 10.84C16.49 11.08 16.37 11.27 16.09 11.47C16.04 11.51 15.94 11.58 15.82 11.64C15.58 11.8 15.26 12 15.17 12.09C15.03 12.22 15.04 12.32 15.06 12.44C15.06 12.45 15.06 12.46 15.06 12.46C15.08 12.57 15.38 14.01 17.3 14.94C17.45 15 17.65 15.12 17.31 15.31Z" fill="currentColor"/>
    </svg>,
    color: "#FFCC00",
  },
  facebook: {
    icon:
      // prettier-ignore
      <svg width="100%" viewBox="0 0 24 24" fill="none">
      <path d="M22 16.19C22 19.83 19.83 22 16.19 22H15C14.45 22 14 21.55 14 21V15.23C14 14.96 14.22 14.73 14.49 14.73L16.25 14.7C16.39 14.69 16.51 14.59 16.54 14.45L16.89 12.54C16.92 12.36 16.78 12.19 16.59 12.19L14.46 12.22C14.18 12.22 13.96 12 13.95 11.73L13.91 9.28C13.91 9.12 14.04 8.98001 14.21 8.98001L16.61 8.94C16.78 8.94 16.91 8.81001 16.91 8.64001L16.87 6.23999C16.87 6.06999 16.74 5.94 16.57 5.94L13.87 5.98001C12.21 6.01001 10.89 7.37 10.92 9.03L10.97 11.78C10.98 12.06 10.76 12.28 10.48 12.29L9.28 12.31C9.11 12.31 8.98001 12.44 8.98001 12.61L9.01001 14.51C9.01001 14.68 9.14 14.81 9.31 14.81L10.51 14.79C10.79 14.79 11.01 15.01 11.02 15.28L11.11 20.98C11.12 21.54 10.67 22 10.11 22H7.81C4.17 22 2 19.83 2 16.18V7.81C2 4.17 4.17 2 7.81 2H16.19C19.83 2 22 4.17 22 7.81V16.19Z" fill="currentColor"/>
    </svg>,
    color: "#1877F2",
  },
  tiktok: {
    icon:
      // prettier-ignore
      <svg width="100%" viewBox="0 0 24 24" fill="none">
        <path d="M20.4006 7.10156C20.3006 7.00156 20.2006 7.00156 20.0006 7.00156C19.7006 7.00156 19.5006 7.00156 19.2006 6.90156C17.5006 6.50156 16.3006 5.20156 16.2006 3.50156V3.10156C16.2006 2.80156 16.0006 2.60156 15.7006 2.60156H12.6006C12.3006 2.60156 12.1006 2.80156 12.1006 3.10156V15.6016C12.1006 16.8016 11.1006 17.7016 10.0006 17.7016C9.30056 17.7016 8.70056 17.4016 8.30056 16.9016C7.60056 16.0016 7.80056 14.6016 8.70056 13.9016C9.10056 13.6016 9.50056 13.6016 10.3006 13.6016C10.4006 13.6016 10.6006 13.6016 10.7006 13.5016C10.8006 13.4016 10.9006 13.3016 10.9006 13.1016V9.80156C10.9006 9.60156 10.7006 9.30156 10.5006 9.30156C8.90056 9.10156 7.30056 9.50156 6.10056 10.5016C5.00056 11.5016 4.20056 12.9016 4.00056 14.6016C3.70056 16.5016 4.40056 18.5016 5.70056 19.8016C6.90056 21.1016 8.60056 21.7016 10.2006 21.7016C11.8006 21.7016 13.4006 21.1016 14.6006 19.9016C15.8006 18.7016 16.5006 17.1016 16.5006 15.4016V9.90156C17.7006 10.5016 19.0006 10.9016 20.3006 10.9016C20.6006 10.9016 20.8006 10.7016 20.8006 10.4016V7.40156C20.6006 7.30156 20.5006 7.20156 20.4006 7.10156Z" fill="currentColor"/>
      </svg>,
    color: "#000000",
  },
};

const validationSchema = Yup.object({
  logo: Yup.string().optional(),
  description: Yup.string().required("Please describe your business"),
  business_type: Yup.string().required("Please select your business type"),
  // monthly_orders: Yup.string().required("Please select your monthly orders"),
  business_category: Yup.string().required("Please select your business category"),
  social_media_platform: Yup.string().required("Please select a social media platform"),
  social_media_username: Yup.string().required("Please enter your social media username"),
});

export default StoreAdditionalDetailsForm;
