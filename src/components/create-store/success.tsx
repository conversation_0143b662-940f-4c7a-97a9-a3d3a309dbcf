import { useEffect } from "react";
import { useState } from "react";
import Confetti from "react-confetti";
// const Confetti: any = (...args) => {
//   return <></>;
// };
import { subdomainStoreLink, toAppUrl } from "../../assets/js/utils/functions";
import authContext from "../../contexts/auth-context";
import useCopyClipboard from "../hooks/useCopyClipboard";
import { AppBtn } from "../ui/buttons";

const CreateStoreSuccess = () => {
  const [hasDocument, setHasDocument] = useState(false);
  const { store, user } = authContext.useContainer();
  const [copied, copy] = useCopyClipboard(subdomainStoreLink(store.slug, true), {
    successDuration: 1000,
  });

  useEffect(() => {
    setHasDocument(document !== undefined && document.body ? true : false);
  }, []);

  return (
    <div className="grid w-full h-full place-items-center">
      <div className="flex flex-col items-center w-full">
        <figure className="h-16 w-16 sm:h-20 sm:w-20 rounded-full bg-success flex items-center justify-center anim-pulse mb-5 sm:mb-7.5">
          <svg className="w-6.25 sm:w-8 -mt-1.5" viewBox="0 0 32 35" fill="none">
            <path
              d="M4.18766 22.4105L13.7894 29.9576L28.4843 11.882"
              stroke="white"
              strokeWidth="5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="anim-draw-checkmark"
            />
          </svg>
        </figure>
        <h2 className="text-black text-center text-lg sm:text-xl lg:text-2lg font-semibold max-w-[270px] mb-6.25">
          Your store <b>{store.name}</b> was created successfully!
        </h2>
        <span className="text-sm text-dark font-semibold">Here&apos;s your store link, tap to copy!</span>
        <div
          className="flex items-center bg-primary-80 px-6 sm:px-12 py-2.5 rounded-lg mt-3 text-primary-500 cursor-pointer"
          onClick={() => copy()}
        >
          <svg viewBox="0 0 10 10" fill="none" className="mr-1.5 h-3.5">
            <path
              d="M8.33333 3.75H4.58333C4.1231 3.75 3.75 4.1231 3.75 4.58333V8.33333C3.75 8.79357 4.1231 9.16667 4.58333 9.16667H8.33333C8.79357 9.16667 9.16667 8.79357 9.16667 8.33333V4.58333C9.16667 4.1231 8.79357 3.75 8.33333 3.75Z"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M2.08337 6.25016H1.66671C1.44569 6.25016 1.23373 6.16237 1.07745 6.00608C0.921171 5.8498 0.833374 5.63784 0.833374 5.41683V1.66683C0.833374 1.44582 0.921171 1.23385 1.07745 1.07757C1.23373 0.921293 1.44569 0.833496 1.66671 0.833496H5.41671C5.63772 0.833496 5.84968 0.921293 6.00596 1.07757C6.16224 1.23385 6.25004 1.44582 6.25004 1.66683V2.0835"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-sm sm:text-1sm inline-block pt-1 font-semibold">
            {copied ? "Copied!" : subdomainStoreLink(store.slug)}
          </span>
        </div>
        <AppBtn color="accent" href="/pick-plan" className="max-w-xs sm:max-w-sm w-full mt-15" isBlock>
          Proceed
        </AppBtn>
      </div>
      {hasDocument && (
        <Confetti
          width={document.body.clientWidth}
          height={document.body.clientHeight}
          numberOfPieces={500}
          recycle={false}
          style={{ zIndex: 200 }}
        />
      )}
    </div>
  );
};

export default CreateStoreSuccess;
