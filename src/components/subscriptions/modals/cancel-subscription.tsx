import React from "react";
import { AppBtn } from "../../ui/buttons";
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "../../ui/modal";
import ErrorLabel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";
import { RequestInterface, useRequest } from "@/api/utils";
import { CancelSubscriptionToggle } from "@/api";
import { SubscriptionInterface } from "@/assets/interfaces";
import { useRouter } from "next/router";

interface Props {
  show: boolean;
  subscription: SubscriptionInterface;
  setSubscription: (state: SubscriptionInterface) => void;
  toggle: (state: boolean) => void;
  isCanceling: boolean;
  toggleSubscription: VoidFunction;
  request: RequestInterface<any>;
}

const CancelSubscriptionModal: React.FC<Props> = ({
  show,
  subscription,
  setSubscription,
  toggle,
  isCanceling,
  toggleSubscription,
  request,
}) => {
  const { isLoading, error } = request;

  return (
    <Modal {...{ show, toggle }} title={isCanceling ? "Cancel Subscription" : "Resume Subscription"} size="sm">
      <ModalBody>
        <ErrorLabel error={error?.message} />
        {/* <SuccessLabel message={successMessage} /> */}
        <div className="text-center flex flex-col items-center">
          <figure
            className={`h-12.5 w-12.5 sm:h-15 sm:w-15 ${
              isCanceling ? "bg-accent-red-500" : "bg-accent-green-500"
            } m-auto rounded-full flex items-center justify-center text-white`}
          >
            {/* SVG Icon */}
            {isCanceling ? (
              // Cancel Icon
              <svg width="65%" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM15.36 14.3C15.65 14.59 15.65 15.07 15.36 15.36C15.21 15.51 15.02 15.58 14.83 15.58C14.64 15.58 14.45 15.51 14.3 15.36L12 13.06L9.7 15.36C9.55 15.51 9.36 15.58 9.17 15.58C8.98 15.58 8.79 15.51 8.64 15.36C8.35 15.07 8.35 14.59 8.64 14.3L10.94 12L8.64 9.7C8.35 9.41 8.35 8.93 8.64 8.64C8.93 8.35 9.41 8.35 9.7 8.64L12 10.94L14.3 8.64C14.59 8.35 15.07 8.35 15.36 8.64C15.65 8.93 15.65 9.41 15.36 9.7L13.06 12L15.36 14.3Z"
                  fill="currentColor"
                />
              </svg>
            ) : (
              // Resume Icon
              <svg width="65%" viewBox="0 0 24 24" fill="none">
                <path
                  d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM12 18.58C8.83 18.58 6.25 16 6.25 12.83C6.25 9.72 8.73 7.19 11.81 7.08L11.44 6.65C11.17 6.34 11.2 5.86 11.51 5.59C11.82 5.32 12.3 5.35 12.57 5.66L14.23 7.56C14.25 7.58 14.26 7.6 14.28 7.62C14.32 7.68 14.36 7.75 14.38 7.82C14.39 7.86 14.4 7.9 14.41 7.94C14.43 8.03 14.42 8.13 14.4 8.22C14.38 8.3 14.35 8.38 14.31 8.46C14.3 8.48 14.28 8.5 14.27 8.52C14.23 8.58 14.17 8.63 14.11 8.67C14.11 8.67 14.1 8.67 14.1 8.68L12.16 10.1C11.82 10.34 11.36 10.27 11.11 9.94C10.87 9.61 10.94 9.14 11.27 8.89L11.68 8.59C9.48 8.75 7.74 10.59 7.74 12.83C7.74 15.17 9.65 17.08 11.99 17.08C14.33 17.08 16.24 15.17 16.24 12.83C16.24 11.99 15.99 11.17 15.53 10.47C15.3 10.13 15.39 9.66 15.74 9.43C16.09 9.2 16.55 9.29 16.78 9.64C17.41 10.59 17.75 11.69 17.75 12.83C17.75 16 15.17 18.58 12 18.58Z"
                  fill="currentColor"
                />
              </svg>
            )}
          </figure>
          <p className="font-body text-1sm sm:text-base font-medium mt-3.75 max-w-[195px] sm:max-w-[270px]">
            {isCanceling
              ? "Are you sure you want to cancel your subscription?"
              : "Are you sure you want to resume your subscription?"}
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color={isCanceling ? "danger" : "success"} onClick={toggleSubscription}>
          {isLoading ? "Updating..." : isCanceling ? "Yes, Cancel Subscription" : "Yes, Resume Subscription"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default CancelSubscriptionModal;
