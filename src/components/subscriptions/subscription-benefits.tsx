import Modal, { ModalBody } from "../ui/modal";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
}
const SubscriptionBenefits: React.FC<Props> = ({ show, toggle }) => {
  return (
    <Modal {...{ show, toggle }} title="Subscription Benefits" size="midi">
      <ModalBody>
        <div className={`overflow-hidden `}>
          <ul className="flex flex-col">
            {subscriptionBenefits.map((feature, i) => (
              <li
                className="flex items-center border-b last:border-none border-grey-border border-opacity-50 px-2 py-3.5 first:pt-0"
                key={i}
              >
                <svg width="13" viewBox="0 0 11 8" fill="none" className="text-accent-green-500">
                  <path
                    d="M10 1L3.8125 7L1 4.27273"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="inline-block ml-2.5 text-black-secondary text-sm sm:text-1sm">{feature}</span>
              </li>
            ))}
          </ul>
        </div>
      </ModalBody>
    </Modal>
  );
};

const subscriptionBenefits = [
  "Fully functional online store",
  "Business Bank Account [NG]",
  "SEO to help customers find you from Google",
  "Multiple payment methods for your customers",
  "Automated records of all your orders & customers",
  "Professional invoices & receipts generator",
  "Easy to run sales with discounts & coupons",
  "Multiple Delivery Options [NG & GH]",
];

export default SubscriptionBenefits;
