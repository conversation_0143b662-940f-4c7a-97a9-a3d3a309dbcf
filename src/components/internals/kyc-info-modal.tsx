import React from "react";
import { ApproveBvn, ApproveKyc, RejectKyc } from "../../api/internals";
import { useRequest } from "../../api/utils";
import { KYCInfo, KYC_STATUSES, KycStore } from "../../assets/interfaces";
import { toAppUrl } from "../../assets/js/utils/functions";
import { useModals } from "../hooks/useModals";
import { statusBadgeColorMap } from "../payments/kyc/kyc-options";
import Portal from "../portal";
import Badge from "../ui/badge";
import { AppBtn } from "../ui/buttons";
import Modal, { ModalBody, ModalFooter } from "../ui/modal";
import { toast } from "../ui/toast";
import ApproveKycModal from "./approve-kyc-modal";
import RejectKycModal from "./reject-kyc-modal";
import ApproveBvnModal from "./approve-bvn-modal";
import LazyImage from "@/components/lazy-image";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  kycInfo: KYCInfo;
  update: (id: string, data: KYCInfo) => void;
}

const KycInfoModal: React.FC<Props> = ({ show, toggle, kycInfo, update }) => {
  const store = kycInfo?.store as KycStore;
  const { modals, toggleModal } = useModals(["approve", "reject", "approve_bvn"]);
  const approveKycReq = useRequest(ApproveKyc);
  const approveBvnReq = useRequest(ApproveBvn);
  const rejectKycReq = useRequest(RejectKyc);

  const approveKyc = async () => {
    toggleModal("approve");
    const [res, err] = await approveKycReq.makeRequest({ id: kycInfo.id });

    if (res) {
      update(kycInfo.id, { ...kycInfo, status: KYC_STATUSES.APPROVED });
      toast.success({
        title: "Approval successful",
        message: "This KYC information has been approved successfully.",
      });
      toggle(false);
      return;
    }

    toast.error({
      title: "Something went wrong",
      message: "Couldn't approve Kyc information",
    });
  };

  const approveBvn = async () => {
    toggleModal("approve_bvn");
    const [res, err] = await approveBvnReq.makeRequest({ id: kycInfo.id });

    if (res) {
      update(kycInfo.id, { ...kycInfo, bvn: res.data?.bvn, address: res?.data?.address });
      toast.success({
        title: "Approval successful",
        message: "This BVN has been approved successfully.",
      });
      toggle(false);
      return;
    }

    toast.error({
      title: "Something went wrong",
      message: "Couldn't approve BVN",
    });
  };

  const rejectKyc = async (message: string) => {
    toggleModal("reject");
    const [res, err] = await rejectKycReq.makeRequest({ id: kycInfo.id, message });

    if (res) {
      update(kycInfo.id, { ...kycInfo, status: KYC_STATUSES.DENIED });
      toast.success({
        title: "Rejection successful",
        message: "This KYC information has been rejected successfully.",
      });
      toggle(false);
      return;
    }

    toast.error({
      title: "Something went wrong",
      message: "Couldn't reject Kyc information",
    });
  };

  if (!kycInfo) return null;

  return (
    <>
      <Modal title="Kyc Information" {...{ show, toggle }} size="midi">
        <ModalBody>
          <ul className="flex flex-col w-full space-y-2.5">
            <li className="flex items-center justify-between text-sm text-dark w-full">
              <span>Name:</span>
              <span className="font-medium">
                {kycInfo?.first_name} {kycInfo?.last_name}
              </span>
            </li>
            <li className="flex items-center justify-between text-sm text-dark w-full">
              <span>Status:</span>
              <span className="font-medium">
                <Badge color={statusBadgeColorMap[kycInfo?.status]} text={kycInfo?.status} />
              </span>
            </li>
            <li className="flex items-center justify-between text-sm text-dark w-full">
              <span>Store:</span>
              <span className="font-medium">
                <a
                  // href={`http://wa.me/${store?.store?.phone?.replace("-", "")}`}
                  href={toAppUrl(store?.slug)}
                  target="_blank"
                  rel="noreferrer"
                  className="underline"
                >
                  {store?.name}
                </a>
              </span>
            </li>
            <li className="flex items-center justify-between text-sm text-dark w-full">
              <span>Store Phone:</span>
              <span className="font-medium">
                <a
                  href={`http://wa.me/${store?.phone?.replace("-", "")}`}
                  target="_blank"
                  rel="noreferrer"
                  className="underline"
                >
                  {store?.phone?.replace("-", "")}
                </a>
              </span>
            </li>
            {kycInfo?.bvn && (
              <li className="flex items-center justify-between text-sm text-dark w-full">
                <span>BVN:</span>
                <span className="font-medium">{kycInfo?.bvn}</span>
              </li>
            )}
            {!kycInfo?.bvn && (
              <li className="flex items-center justify-between text-sm text-dark w-full">
                <span>BVN Token:</span>
                <span className="font-medium">{kycInfo?.bvn_token}</span>
              </li>
            )}
            {!kycInfo?.bvn && kycInfo?.proposed_bvn?.bvn && (
              <li className="mt-2.5 pt-3.75 border-t border-grey-border">
                <ul className="flex flex-col w-full space-y-2.5">
                  <li className="flex items-center justify-between text-sm text-dark w-full">
                    <span>Proposed Bvn:</span>
                    <span className="font-medium">{kycInfo?.proposed_bvn?.bvn}</span>
                  </li>
                  <li className="flex items-center justify-between text-sm text-dark w-full">
                    <span>Bvn Name:</span>
                    <span className="font-medium">{kycInfo?.proposed_bvn?.name}</span>
                  </li>
                  <li className="flex items-center justify-between text-sm text-dark w-full">
                    <span>Bvn Phone:</span>
                    <span className="font-medium">{kycInfo?.proposed_bvn?.phone}</span>
                  </li>
                </ul>
              </li>
            )}
            {kycInfo?.phone && (
              <li className="flex items-center justify-between text-sm text-dark w-full">
                <span>BVN Phone:</span>
                <span className="font-medium">{kycInfo?.phone}</span>
              </li>
            )}
            {kycInfo?.identity?.type && (
              <li className="flex items-center justify-between text-sm text-dark w-full">
                <span>Id Type:</span>
                <span className="font-medium">
                  <Badge text={kycInfo?.identity.type} />
                </span>
              </li>
            )}
            {kycInfo?.address?.address_line1 && (
              <li className="flex items-center justify-between text-sm text-dark w-full">
                <span>Street Address:</span>
                <span className="font-medium">{kycInfo?.address.address_line1}</span>
              </li>
            )}

            {kycInfo?.address?.state && (
              <li className="flex items-center justify-between text-sm text-dark w-full">
                <span>State:</span>
                <span className="font-medium">{kycInfo?.address.state}</span>
              </li>
            )}
            {kycInfo?.address?.city && (
              <li className="flex items-center justify-between text-sm text-dark w-full">
                <span>City:</span>
                <span className="font-medium">{kycInfo?.address.city}</span>
              </li>
            )}

            {kycInfo?.identity?.url && (
              <li className="flex flex-col text-lg text-black-secondary font-bold w-full font-display">
                <span className="font-bold">Id Image</span>
                <figure className="w-full mt-2.5">
                  <LazyImage src={kycInfo?.identity?.url} alt="" className="w-full" />
                </figure>
              </li>
            )}
            {kycInfo?.identity?.selfie && (
              <li className="flex flex-col text-lg text-black-secondary font-bold w-full font-display">
                <span className="font-bold">Selfie</span>
                <figure className="w-full mt-2.5">
                  <LazyImage src={kycInfo?.identity?.selfie} alt="" className="w-full" />
                </figure>
              </li>
            )}
          </ul>
        </ModalBody>
        {kycInfo.status === "PENDING" && (
          <ModalFooter>
            <div className="flex items-center space-x-2.5 w-full">
              <AppBtn
                color="danger"
                size="md"
                className="w-full flex-1"
                onClick={() => toggleModal("reject")}
                disabled={approveKycReq?.isLoading || rejectKycReq.isLoading}
              >
                Reject
              </AppBtn>
              <AppBtn
                color="success"
                size="md"
                className="w-full flex-1"
                onClick={() => toggleModal("approve")}
                disabled={approveKycReq?.isLoading || rejectKycReq.isLoading}
              >
                {approveKycReq?.isLoading ? "Approving..." : "Approve"}
              </AppBtn>
            </div>
          </ModalFooter>
        )}

        {kycInfo?.proposed_bvn?.bvn && !kycInfo?.bvn && (
          <ModalFooter>
            <div className="flex items-center space-x-2.5 w-full">
              <AppBtn
                color="success"
                size="md"
                className="w-full flex-1"
                onClick={() => toggleModal("approve_bvn")}
                disabled={approveBvnReq?.isLoading}
              >
                {approveBvnReq?.isLoading ? "Approving..." : "Approve BVN"}
              </AppBtn>
            </div>
          </ModalFooter>
        )}
      </Modal>
      <Portal>
        <ApproveKycModal
          show={modals.approve.show}
          toggle={() => toggleModal("approve")}
          approve={approveKyc}
          title=""
        />
        <ApproveBvnModal
          show={modals.approve_bvn.show}
          toggle={() => toggleModal("approve_bvn")}
          approve={approveBvn}
          title=""
        />
        <RejectKycModal
          show={modals.reject.show}
          toggle={() => toggleModal("reject")}
          reject={(m: string) => rejectKyc(m)}
          title=""
        />
      </Portal>
    </>
  );
};

export default KycInfoModal;
