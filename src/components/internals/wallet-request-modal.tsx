import React from "react";
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "../ui/modal";
import Portal from "../portal";
import { AppBtn } from "../ui/buttons";
import { toast } from "../ui/toast";
import { useModals } from "../hooks/useModals";
import { useRequest } from "../../api/utils";
import Badge from "../ui/badge";
import { statusBadgeColorMap } from "../payments/kyc/kyc-options";
import { WalletRequest } from "@/assets/interfaces";
import { ApproveWalletRequest, RejectWalletRequest } from "@/api/internals";
import ApproveKycModal from "./approve-kyc-modal";
import RejectKycModal from "./reject-kyc-modal";
import { toAppUrl } from "@/assets/js/utils/functions";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  walletRequest: WalletRequest | null;
  update: (id: string, data: WalletRequest) => void;
}

const WalletRequestModal: React.FC<Props> = ({ show, toggle, walletRequest, update }) => {
  const { modals, toggleModal } = useModals(["approve", "reject"]);
  const approveReq = useRequest(ApproveWalletRequest);
  const rejectReq = useRequest(RejectWalletRequest);

  if (!walletRequest) return null;

  const handleApprove = async () => {
    toggleModal("approve");
    const [res, err] = await approveReq.makeRequest({ id: walletRequest.id });
    if (res) {
      toast.success({
        title: "Approved!",
        message: "This wallet request was approved successfully.",
      });
      update(walletRequest.id, { ...walletRequest, status: "APPROVED" });
      toggle(false);
    } else {
      toast.error({
        title: "Approval Failed",
        message: "Could not approve wallet request.",
      });
    }
  };

  const getSocialLink = (key: string, handle: string) => {
    switch (key) {
      case "twitter":
        return `https://twitter.com/${handle}`;
      case "instagram":
        return `https://instagram.com/${handle}`;
      case "facebook":
        return `https://facebook.com/${handle}`;
      case "whatsapp":
        return `https://wa.me/${handle.replace(/\D/g, "")}`; // e.g. remove non-digits for phone numbers
      default:
        return "#";
    }
  };

  const handleReject = async (message: string) => {
    toggleModal("reject");
    const [res, err] = await rejectReq.makeRequest({ id: walletRequest.id, message });
    if (res) {
      toast.success({
        title: "Rejected!",
        message: "This wallet request was rejected.",
      });
      update(walletRequest.id, { ...walletRequest, status: "REJECTED" });
      toggle(false);
    } else {
      toast.error({
        title: "Rejection Failed",
        message: "Could not reject wallet request.",
      });
    }
  };

  return (
    <>
      <Modal title="Wallet Request Info" show={show} toggle={toggle} size="midi">
        <ModalBody>
          <ul className="space-y-2.5 text-sm text-dark">
            <li className="flex items-center justify-between">
              <span>Request ID:</span>
              <span>{walletRequest.id}</span>
            </li>
            <li className="flex items-center justify-between">
              <span>Status:</span>
              <Badge color={statusBadgeColorMap[walletRequest.status]} text={walletRequest.status} />
            </li>
            <li className="flex items-center justify-between">
              <span>Store Name:</span>
              <span>
                <a href={toAppUrl(walletRequest.store.slug)} target="_blank" rel="noreferrer" className="underline">
                  {walletRequest.store.name}
                </a>
              </span>
            </li>
            <li className="flex items-center justify-between">
              <span>Store Phone:</span>
              <span>
                <a
                  href={`https://wa.me/${walletRequest.store.phone.replace("-", "")}`}
                  target="_blank"
                  rel="noreferrer"
                  className="underline"
                >
                  {walletRequest.store.phone}
                </a>
              </span>
            </li>
            <li className="flex items-center justify-between text-sm text-dark w-full">
              <span>Store Socials:</span>
              <div className="font-medium flex flex-col items-end text-right">
                {Object.entries(walletRequest.store.socials).map(([key, value]) =>
                  value ? (
                    <a
                      key={key}
                      href={getSocialLink(key, value)} // or do the same switch logic inline
                      target="_blank"
                      rel="noreferrer"
                      className="underline"
                    >
                      {key}: {value}
                    </a>
                  ) : null
                )}
              </div>
            </li>
            <li className="flex items-center justify-between">
              <span>Requested Currencies:</span>
              <span>{walletRequest.requested_currencies.join(", ")}</span>
            </li>
            <li className="flex items-center justify-between">
              <span>Collect from Abroad?</span>
              <span>{walletRequest.collect_payments_from_abroad ? "Yes" : "No"}</span>
            </li>
            <li className="flex items-center justify-between">
              <span>Current Payment Method:</span>
              <span>{walletRequest.current_payment_method}</span>
            </li>
            {walletRequest.plans_to_get_customers_abroad && (
              <li className="flex items-center justify-between">
                <span>Plans to get Customers Abroad:</span>
                <span>{walletRequest.plans_to_get_customers_abroad}</span>
              </li>
            )}
            <li className="flex items-center justify-between">
              <span>Reason:</span>
              <span className="w-1/2 text-right">{walletRequest.reason}</span>
            </li>
            {walletRequest.admin_notes && (
              <li className="flex items-center justify-between">
                <span>Admin Notes:</span>
                <span className="w-1/2 text-right">{walletRequest.admin_notes}</span>
              </li>
            )}
          </ul>
        </ModalBody>

        {walletRequest.status === "PENDING" && (
          <ModalFooter>
            <div className="flex items-center space-x-2.5 w-full">
              <AppBtn
                color="danger"
                size="md"
                className="w-full flex-1"
                onClick={() => toggleModal("reject")}
                disabled={rejectReq.isLoading}
              >
                {rejectReq.isLoading ? "Rejecting..." : "Reject"}
              </AppBtn>
              <AppBtn
                color="success"
                size="md"
                className="w-full flex-1"
                onClick={() => toggleModal("approve")}
                disabled={approveReq.isLoading}
              >
                {approveReq.isLoading ? "Approving..." : "Approve"}
              </AppBtn>
            </div>
          </ModalFooter>
        )}
      </Modal>
      <Portal>
        <ApproveKycModal
          show={modals.approve.show}
          toggle={() => toggleModal("approve")}
          approve={handleApprove}
          isWalletRequest={true}
        />
        <RejectKycModal
          show={modals.reject.show}
          toggle={() => toggleModal("reject")}
          reject={(m: string) => handleReject(m)}
        />
      </Portal>
    </>
  );
};

export default WalletRequestModal;
