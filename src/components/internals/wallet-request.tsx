import classNames from "classnames";
import React, { useEffect, useState } from "react";
import { useFetcher } from "../../api/utils";
import { GetAdminWalletRequests } from "../../api/internals";
import { AppBtn } from "../ui/buttons";
import Table, { TableBody, TableCell, TableHead, TableHeadItem, TableRow } from "../ui/table";
import Portal from "../portal";
import { useModals } from "../hooks/useModals";
import usePagination from "../hooks/usePagination";
import SearchBar from "../ui/search-bar";
import Badge from "../ui/badge";
import { statusBadgeColorMap } from "../payments/kyc/kyc-options";
import { toAppUrl } from "../../assets/js/utils/functions";
import { WalletRequest } from "@/assets/interfaces";
import WalletRequestModal from "./wallet-request-modal";
import { GetWalletRequestParams } from "@/api/interfaces/internals";
import { FilterOption } from "./stores";
import FilterDropdown from "../ui/filter-dropdown";

interface SearchProps {
  value: string;
  search: (query: string) => void;
}

const SearchBarX: React.FC<SearchProps> = ({ value, search }) => {
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (value !== searchQuery) {
      setSearchQuery(value);
    }
  }, [value]);

  return (
    <div className="flex items-center">
      <SearchBar {...{ searchQuery, setSearchQuery, placeholder: `Search` }} />
      <AppBtn size="md" className="ml-2.5" onClick={() => search(searchQuery)}>
        Search
      </AppBtn>
    </div>
  );
};

const InternalsWalletRequests = () => {
  // local state
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("");
  const [filterData, setFilterData] = useState<{ country?: string; status?: string; verification_method?: string }>({});
  const [selectedRequest, setSelectedData] = useState<WalletRequest | null>(null);
  const { modals, toggleModal } = useModals(["info", "confirm"]);
  const PER_PAGE = 200;
  const { currentPage, goNext, goPrevious } = usePagination();

  // fetcher
  const { isLoading, error, response } = useFetcher<GetWalletRequestParams>(GetAdminWalletRequests, {
    pagination: { page: currentPage, per_page: PER_PAGE },
    filter: (() => {
      const data = {};

      if (filterData.country) data["country"] = filterData.country;
      if (filterData.status) data["status"] = filterData.status;
      if (filterData.verification_method) data["verification_method"] = filterData.verification_method;
      if (searchQuery) data["search"] = searchQuery;

      return data;
    })(),
  });

  // if you want, you can store the data in local state
  const [walletRequests, setWalletRequests] = useState<WalletRequest[]>([]);

  useEffect(() => {
    if (response?.data) {
      setWalletRequests(response.data);
    }
  }, [response]);

  const optionIsSelected = (o: FilterOption) => filterData[o.key] === o.value;
  const toggleOption = (o: FilterOption) => {
    const { key, value, label } = o;
    const filterDataCopy = { ...filterData };

    if (filterDataCopy[key] === value) {
      filterDataCopy[key] = undefined;
    } else {
      filterDataCopy[key] = value;
    }

    setFilterData(filterDataCopy);
  };

  const openSelection = (data: WalletRequest) => {
    setSelectedData(data);
    toggleModal("info");
  };

  const updateSelection = (id: string, data: WalletRequest) => {
    // e.g. after approval, you can update the local array
    setWalletRequests((prev) => {
      const index = prev.findIndex((w) => w.id === id);
      if (index === -1) return prev;
      const updated = [...prev];
      updated[index] = data;
      return updated;
    });
  };

  if (isLoading) {
    return <p>Loading wallet requests...</p>;
  }

  if (error) {
    return <p className="py-10 text-red-500 text-center">Something went wrong!</p>;
  }

  if (!walletRequests || walletRequests.length === 0) {
    return (
      <div className="py-10 flex items-center flex-col text-center">
        <span className="text-gray-500 text-sm inline-block">No wallet requests to show</span>
        <AppBtn
          className="mt-2.5"
          onClick={() => {
            setSearchQuery("");
            if (currentPage > 1) goPrevious();
          }}
        >
          Go Back
        </AppBtn>
      </div>
    );
  }

  return (
    <div className="p-5 sm:p-8">
      {/* top bar - search */}
      <div className="flex items-center justify-end mb-6">
        <SearchBarX search={(query) => setSearchQuery(query)} value={searchQuery} />
      </div>
      <div className="bg-black py-3.5 w-full flex items-center justify-between px-5 mb-5 rounded-10">
        <div className="flex items-center flex-wrap space-x-2.5">
          {filterOptions.map((o, index) => (
            <button
              className={classNames(
                "inline-flex items-center font-semibold text-sm px-3 py-2 rounded-10 transition-all ease-out duration-150",
                {
                  "text-white bg-white bg-opacity-10 hover:bg-opacity-30": !optionIsSelected(o),
                  "text-black bg-white": optionIsSelected(o),
                }
              )}
              onClick={() => toggleOption(o)}
              key={index}
            >
              {o.label}
            </button>
          ))}
        </div>
        <FilterDropdown items={sortOptions} label="Sort By" onSave={setSortBy} />
      </div>

      {/* table */}
      <Table>
        <TableHead>
          <TableHeadItem>Date Created</TableHeadItem>
          <TableHeadItem>ID</TableHeadItem>
          <TableHeadItem>Store</TableHeadItem>
          <TableHeadItem>Socials</TableHeadItem>
          <TableHeadItem>Phone</TableHeadItem>
          <TableHeadItem>Requested Currencies</TableHeadItem>
          <TableHeadItem>Status</TableHeadItem>
          <TableHeadItem>Action</TableHeadItem>
        </TableHead>

        <TableBody>
          {walletRequests.map((req) => (
            <TableRow key={req.id}>
              <TableCell>{new Date(req.created_at).toDateString()}</TableCell>
              <TableCell>{req.id}</TableCell>
              <TableCell>
                <a href={toAppUrl(req.store.slug)} target="_blank" rel="noreferrer" className="underline">
                  {req.store.name}
                </a>
              </TableCell>
              <TableCell>
                {Object.entries(req.store.socials).map(([key, value]) => {
                  // If the store’s socials entry is blank, skip it
                  if (!value) return null;

                  // A tiny helper for each social link – can expand as needed
                  let socialBase = "";
                  switch (key) {
                    case "twitter":
                      socialBase = "https://twitter.com/";
                      break;
                    case "instagram":
                      socialBase = "https://instagram.com/";
                      break;
                    case "facebook":
                      socialBase = "https://facebook.com/";
                      break;
                    case "whatsapp":
                      // For WhatsApp, you might want to do wa.me, or something else
                      socialBase = "https://wa.me/";
                      break;
                  }

                  return (
                    <div key={key}>
                      <a href={socialBase + value} target="_blank" rel="noreferrer" className="underline">
                        {key}: {value}
                      </a>
                    </div>
                  );
                })}
              </TableCell>
              <TableCell>
                <a
                  href={`http://wa.me/${req.store.phone.replace("-", "")}`}
                  target="_blank"
                  rel="noreferrer"
                  className="underline"
                >
                  {req.store.phone?.replace("-", "")}
                </a>
              </TableCell>
              <TableCell>{req.requested_currencies?.join(", ")}</TableCell>
              <TableCell>
                <Badge color={statusBadgeColorMap[req.status]} text={req.status} />
              </TableCell>
              <TableCell>
                <AppBtn size="sm" onClick={() => openSelection(req)}>
                  View
                </AppBtn>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* pagination */}
      <div className="mt-5 flex mb-16">
        <span className="text-sm text-dark pt-1">
          Showing{" "}
          <b>
            {PER_PAGE * (currentPage - 1) + 1} - {PER_PAGE * (currentPage - 1) + walletRequests.length}
          </b>{" "}
          of <b>{response?.total_wallet_requests}</b> requests
        </span>
        <div className="ml-auto flex items-center space-x-2.5">
          {currentPage > 1 && (
            <AppBtn color="neutral" size="sm" onClick={goPrevious}>
              Previous
            </AppBtn>
          )}
          {/* Use something like response?.next_page if your backend tells you there's a next page */}
          {walletRequests.length === PER_PAGE && (
            <AppBtn color="neutral" size="sm" onClick={goNext}>
              Next
            </AppBtn>
          )}
        </div>
      </div>

      {/* details modal */}
      <Portal>
        <WalletRequestModal
          show={modals.info.show}
          toggle={() => toggleModal("info")}
          walletRequest={selectedRequest}
          update={updateSelection}
        />
      </Portal>
    </div>
  );
};

export default InternalsWalletRequests;

const sortOptions = [
  {
    label: "By Date Created",
    value: "DATE_CREATED",
  },
];

const filterOptions: FilterOption[] = [
  {
    label: "Approved",
    value: "APPROVED",
    key: "status",
  },
  {
    label: "Rejected",
    value: "REJECTED",
    key: "status",
  },
  {
    label: "Pending",
    value: "PENDING",
    key: "status",
  },
];
