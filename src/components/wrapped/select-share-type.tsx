import { RoundActionBtn } from "../ui/buttons";

interface Props {
  onSelect: (type: ShareType) => void
}

export enum ShareType {
  SINGLE = "single",
  MULTIPLE = "multiple",
  ALL = "all",
}
const SelectShareType: React.FC<Props> = ({onSelect}) => {
  return (
    <div className="bg-grey-fields-200 rounded-[20px] overflow-hidden">
      {data.map((d, i) => (
        <button key={i} onClick={()=> onSelect(d.type)} className="flex items-center  justify-between w-full hover:bg-grey-fields-100 p-2.5 border-b border-grey-divider last:border-none">
          <div className="flex gap-2.5 items-center">
            {d.icon}
            <span className="text-sm text-black"> {d.title} </span>
          </div>
          <RoundActionBtn className="!bg-white">
            {/* prettier-ignore */}
            <svg className="text-primary-400" width="16" height="16" fill="none">
                <path d="M1 7H13" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 1L13 7L7 13" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </RoundActionBtn>
        </button>
      ))}
    </div>
  );
};
export default SelectShareType;

const data = [
  {
    icon: (
      <div className="p-2.5 w-[fit-content] h-[fit-content] rounded-full flex items-center justify-center bg-accent-green-100 bg-opacity-30">
        {/* prettier-ignore */}
        <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
            <path d="M8.75 13.9062H6.25C4.1875 13.9062 2.94375 13.4937 2.7125 11.4C2.675 11.1187 2.65625 10.7812 2.65625 10.3125V4.6875C2.65625 4.21875 2.675 3.875 2.71875 3.58125C2.94375 1.50625 4.1875 1.09375 6.25 1.09375H8.75C10.8125 1.09375 12.0562 1.50625 12.2875 3.6C12.325 3.88125 12.3438 4.21875 12.3438 4.6875V10.3125C12.3438 10.7812 12.325 11.125 12.2812 11.4188C12.0562 13.4938 10.8125 13.9062 8.75 13.9062ZM6.25 2.03125C4.18125 2.03125 3.7875 2.45 3.64375 3.7C3.6125 3.9625 3.59375 4.2625 3.59375 4.6875V10.3125C3.59375 10.7375 3.6125 11.0375 3.64375 11.2812C3.78125 12.55 4.18125 12.9688 6.25 12.9688H8.75C10.8188 12.9688 11.2125 12.55 11.3562 11.3C11.3938 11.0375 11.4062 10.7375 11.4062 10.3125V4.6875C11.4062 4.2625 11.3875 3.9625 11.3562 3.71875C11.2188 2.45 10.8188 2.03125 8.75 2.03125H6.25Z" fill="#39B588"/>
            <path d="M1.25 12.6562C0.99375 12.6562 0.78125 12.4438 0.78125 12.1875V2.8125C0.78125 2.55625 0.99375 2.34375 1.25 2.34375C1.50625 2.34375 1.71875 2.55625 1.71875 2.8125V12.1875C1.71875 12.4438 1.50625 12.6562 1.25 12.6562Z" fill="#39B588"/>
            <path d="M13.75 12.3438C13.4938 12.3438 13.2812 12.1313 13.2812 11.875V2.5C13.2812 2.24375 13.4938 2.03125 13.75 2.03125C14.0063 2.03125 14.2188 2.24375 14.2188 2.5V11.875C14.2188 12.1313 14.0063 12.3438 13.75 12.3438Z" fill="#39B588"/>
        </svg>
      </div>
    ),
    title: "Share this “Card”",
    type: ShareType.SINGLE
  },
  {
    icon: (
      <div className="p-2.5 w-[fit-content] h-[fit-content] rounded-full flex items-center justify-center bg-accent-orange-100 bg-opacity-30">
        {/* prettier-ignore */}
        <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
            <path d="M10.6875 0.78125H8.0625C5.61875 0.78125 4.53125 1.86875 4.53125 4.3125V5C4.53125 5.25625 4.74375 5.46875 5 5.46875H6.9375C8.875 5.46875 9.53125 6.125 9.53125 8.0625V10C9.53125 10.2563 9.74375 10.4688 10 10.4688H10.6875C13.1313 10.4688 14.2188 9.38125 14.2188 6.9375V4.3125C14.2188 1.86875 13.1313 0.78125 10.6875 0.78125ZM5.46875 4.53125V4.3125C5.46875 2.375 6.125 1.71875 8.0625 1.71875H10.6875C12.625 1.71875 13.2812 2.375 13.2812 4.3125V6.9375C13.2812 8.875 12.625 9.53125 10.6875 9.53125H10.4688V8.0625C10.4688 5.61875 9.38125 4.53125 6.9375 4.53125H5.46875Z" fill="#FF8159"/>
            <path d="M6.9375 4.53125H4.3125C1.86875 4.53125 0.78125 5.61875 0.78125 8.0625V10.6875C0.78125 13.1313 1.86875 14.2188 4.3125 14.2188H6.9375C9.38125 14.2188 10.4688 13.1313 10.4688 10.6875V8.0625C10.4688 5.61875 9.38125 4.53125 6.9375 4.53125ZM4.3125 13.2812C2.375 13.2812 1.71875 12.625 1.71875 10.6875V8.0625C1.71875 6.125 2.375 5.46875 4.3125 5.46875H6.9375C8.875 5.46875 9.53125 6.125 9.53125 8.0625V10.6875C9.53125 12.625 8.875 13.2812 6.9375 13.2812H4.3125Z" fill="#FF8159"/>
            <path d="M5.0187 11.0625C4.89995 11.0625 4.7812 11.0187 4.68745 10.925L3.4687 9.70625C3.28745 9.525 3.28745 9.225 3.4687 9.04375C3.64995 8.8625 3.94995 8.8625 4.1312 9.04375L5.0187 9.93125L7.1187 7.83125C7.29995 7.65 7.59995 7.65 7.7812 7.83125C7.96245 8.0125 7.96245 8.3125 7.7812 8.49375L5.3437 10.925C5.2562 11.0125 5.13745 11.0625 5.0187 11.0625Z" fill="#FF8159"/>
        </svg>
      </div>
    ),
    title: "Select multiple cards to share",
    type: ShareType.MULTIPLE
  },
  {
    icon: (
      <div className="p-2.5 w-[fit-content] h-[fit-content] rounded-full flex items-center justify-center bg-accent-red-100 bg-opacity-30">
        {/* prettier-ignore */}
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
        <path d="M4.31673 3.68663L9.26923 2.0358C11.4917 1.29497 12.6992 2.5083 11.9642 4.7308L10.3134 9.6833C9.20506 13.0141 7.38506 13.0141 6.27673 9.6833L5.78673 8.2133L4.31673 7.7233C0.985895 6.61497 0.985895 4.8008 4.31673 3.68663Z" stroke="#BF0637" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M5.89746 7.96245L7.98579 5.86829" stroke="#BF0637" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </div>
    ),
    title: "Share all cards",
    type: ShareType.ALL
  },
];


