import classNames from "classnames";

interface Props {
  currentProgress: number;
  currentCard: number;
  cardTotal: number;
}
const CardProgress: React.FC<Props> = ({ currentCard, cardTotal, currentProgress }) => {
  return (
    <div className="flex z-20 items-center gap-1.25 w-full absolute top-0 left-0 p-5 pb-8 black-gradient" >
      {new Array(cardTotal).fill(0).map((_, index) => (
        <div key={index} className={classNames("flex-1  h-1 rounded-full bg-white bg-opacity-30 overflow-hidden")}>
          <div
            className="bg-white w-full h-full"
            style={{ width: index === currentCard ? `${currentProgress}%` : index < currentCard ? "100%" : "0px" }}
          ></div>
        </div>
      ))}
    </div>
  );
};
export default CardProgress;
