import { useEffect, useState } from "react";
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, ModalProps } from "../ui/modal";
import SelectShareType, { ShareType } from "./select-share-type";
import SelectWrappedCards from "./select-cards";
import SelectShareMethod from "./select-method";
import { set } from "date-fns";
import { wrappedCardData } from "@/assets/js/utils/constants";
import { useRequest } from "@/api/utils";
import { GetWrappedImages } from "@/api";
import { useLocalObject } from "../hooks/useLocalState";
import usePreventFirstRun from "../hooks/usePreventFirstRun";

enum Steps {
  TYPE = "type",
  SELECT = "select",
  METHOD = "method",
}

interface Props extends ModalProps {
  currentCard: string;
  store_slug: string;
}

const ShareWrappedCardsModal: React.FC<Props> = ({ show, toggle, currentCard, store_slug }) => {
  const request = useRequest(GetWrappedImages);
  const [step, setStep] = useState(Steps.TYPE);
  const [type, setType] = useState(ShareType.SINGLE);
  const [selectedCards, setSelectedCards] = useState<string[]>();
  const [cardImages, setCardImages] = useState<{ [key: string]: string } | null>(null);

  useEffect(() => {
    const locallyStoredCardUrls = localStorage.getItem("cardImagesUrl");

    if (!locallyStoredCardUrls) {
      getCardImageUrls();
      return;
    }

    setCardImages(JSON.parse(locallyStoredCardUrls));
  }, []);

  const getCardImageUrls = async () => {
    const cards = wrappedCardData.filter((c) => c.shareable && !c.is_private).map((c) => c.key);
    const cardsKey = cards.join(",");
    const [res, err] = await request.makeRequest({ cards: cardsKey, store_slug });

    if (res) {
      const images = res.data;
      const imagesObj = {};

      for (let index = 0; index < images.length; index++) {
        const element = images[index];
        imagesObj[cards[index]] = element;
      }

      setCardImages(imagesObj);
      localStorage.setItem("cardImagesUrl", JSON.stringify(imagesObj));
      return;
    }

    return { error: "Error downloading images" };
  };

  const handleSelectType = (type: ShareType) => {
    setType(type);
    if (type === ShareType.MULTIPLE) {
      setStep(Steps.SELECT);
    } else {
      setStep(Steps.METHOD);
    }

    if (type === ShareType.SINGLE) {
      setSelectedCards([currentCard ?? "summary"]);
      setStep(Steps.METHOD);
    }

    if (type === ShareType.ALL) {
      setSelectedCards(wrappedCardData.filter((card) => card.shareable && !card.is_private).map((card) => card.key));
      setStep(Steps.METHOD);
    }
  };

  const handleSelect = (cards: string[]) => {
    setSelectedCards(cards);
    setStep(Steps.METHOD);
  };

  const _toggle = (state: boolean) => {
    toggle(state);
    setStep(Steps.TYPE);
  };

  const components = {
    [Steps.TYPE]: <SelectShareType onSelect={handleSelectType} />,
    [Steps.SELECT]: <SelectWrappedCards onSelectCards={handleSelect} />,
    [Steps.METHOD]: <SelectShareMethod selectedCards={selectedCards} cardImages={cardImages} storeSlug={store_slug} />,
  };

  const titles = {
    [Steps.TYPE]: "What would you like to share?",
    [Steps.SELECT]: "Select Multiple Cards to Share",
    [Steps.METHOD]: "How would you like share?",
  };

  return (
    <Modal title={titles[step]} show={show} toggle={_toggle} size="midi">
      <ModalBody>{components[step]}</ModalBody>
    </Modal>
  );
};
export default ShareWrappedCardsModal;
