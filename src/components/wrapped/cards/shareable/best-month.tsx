import { YearWrappedResponse } from "@/assets/interfaces";
import { COUNTRY_CURRENCY_MAP } from "@/assets/js/utils/constants";
import dayjs from "dayjs";

interface Props {
  data: Partial<YearWrappedResponse>;
}
const BestMonthShareCard: React.FC<Props> = ({ data }) => {
  const month = dayjs().set("month", data.month_with_highest_orders??2).format("MMMM");
  return (
    <div className="h-full relative" style={{ aspectRatio: "16:9" }}>
      <img
        className="w-full h-full abs-center"
        src="https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/orders_best_month.png"
        alt=""
      />

      <div className=" text-white relative z-10 text-center top-[130px] px-[50px] h-[1000px] w-full">
        <div className="flex flex-col gap-[45px] items-center justify-center w-full h-full">
          <div className="w-[fit-content] max-w-[90%] rounded-3xl flex justify-center text-[80px] font-display font-semibold min-w-[60%] py-6.25 bg-[#D9FFE6] text-[#38D771]">
            {month}
          </div>
          <h4 className="text-[50px] l font-semibold">was my best-selling <br/> month in 2024!</h4>
        </div>
      </div>
    </div>
  );
};
export default BestMonthShareCard;
