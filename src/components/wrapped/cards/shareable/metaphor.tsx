import { YearWrappedResponse } from "@/assets/interfaces";

interface Props {
  data: Partial<YearWrappedResponse>;
}
const MetaphorShareCard: React.FC<Props> = ({ data }) => {
  const images = {
    lily: "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/lily.png",
    "oak tree": "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/oak.png",
    "iroko tree": "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/iroko.png",
    bamboo: "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/bamboo.png",
  };

  return (
    <div className="h-full relative" style={{ aspectRatio: "16:9" }}>
      <img className="w-full h-full abs-center" src={images[data.metaphor.toLowerCase() ?? "lily"]} alt="" />
    </div>
  );
};
export default MetaphorShareCard;
