import { CURRENCIES, YearWrappedResponse } from "@/assets/interfaces";
import { COUNTRY_CURRENCY_MAP } from "@/assets/js/utils/constants";
import { toNaira } from "@/assets/js/utils/utils";

interface Props {
  data: Partial<YearWrappedResponse>;
}
const PaymentsProcessedShareCard: React.FC<Props> = ({ data }) => {
  return (
    <div className="h-full relative" style={{ aspectRatio: "16:9" }}>
      <img
        className="w-full h-full abs-center"
        src="https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/Total_Payment_Processed.png"
        alt=""
      />

      <div className="text-white relative z-10 text-center top-[130px] px-[50px] h-[1000px] w-full">
        <div className="flex flex-col gap-[45px] items-center justify-center w-full h-full">
          <h4 className="text-[50px] l font-semibold">My business processed</h4>
          <div className="w-[fit-content] rounded-3xl flex justify-center text-[80px] font-display font-semibold min-w-[60%] py-6.25 bg-[#D9FFE6] text-[#38D771]">
            {data?.store_currency ?? "NGN"}{" "}
            {data.store_currency === CURRENCIES.NGN
              ? toNaira(data.total_payments_volume).toLocaleString()
              : data.total_payments_volume.toLocaleString()}
          </div>
          <h4 className="text-[50px] l font-semibold ">on Catlog in 2024!</h4>
        </div>
      </div>
    </div>
  );
};
export default PaymentsProcessedShareCard;
