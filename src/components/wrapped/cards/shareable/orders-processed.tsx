import { YearWrappedResponse } from "@/assets/interfaces";
import { COUNTRY_CURRENCY_MAP } from "@/assets/js/utils/constants";

interface Props {
  data: Partial<YearWrappedResponse>;
}
const OrdersProcessedShareCard: React.FC<Props> = ({ data }) => {
  return (
    <div className="h-full relative" style={{ aspectRatio: "16:9" }}>
      <img
        className="w-full h-full abs-center"
        src="https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/Top_order_Location_6.png"
        alt=""
      />

      <div className=" relative z-10 text-center top-[130px] px-[50px] h-[1000px] w-full">
        <div className="flex flex-col gap-[45px] items-center justify-center w-full h-full">
          <h4 className="text-[50px] l font-semibold text-black-secondary">I earned</h4>A
          <div className="w-[fit-content] rounded-3xl flex justify-center text-[80px] font-display font-semibold min-w-[60%] py-6.25 bg-[#6955D1] text-white">
            {data?.store_currency??"NGN"} {data?.total_order_volume?.toLocaleString()??'1000'}
          </div>
          <h4 className="text-[50px] l font-semibold text-black-secondary">in revenue from 2,000 <br/> orders in 2024!</h4>
        </div>
      </div>
    </div>
  );
};
export default OrdersProcessedShareCard;
