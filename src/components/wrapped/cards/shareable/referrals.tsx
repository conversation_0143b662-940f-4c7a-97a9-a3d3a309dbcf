import { YearWrappedResponse } from "@/assets/interfaces";

interface Props {
  data: Partial<YearWrappedResponse>;
}
const ReferralsShareCard: React.FC<Props> = ({ data }) => {
  return (
    <div className="h-full relative" style={{ aspectRatio: "16:9" }}>
      <img
        className="w-full h-full abs-center"
        src="https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/Total_Referrals.png"
        alt=""
      />

      <div className=" text-white relative z-10 text-center top-[130px] px-[50px] h-[1000px] w-full">
        <div className="flex flex-col gap-[45px] items-center justify-center w-full h-full">
          <h4 className="text-[50px] l font-semibold">I referred</h4>
          <div className="w-[fit-content] max-w-[90%] px-10 rounded-3xl flex justify-center text-[80px] font-display font-semibold min-w-[60%] py-6.25 bg-[#705FCC]">
            {data.no_of_referrals ?? "0"}
          </div>
          <h4 className="text-[50px] l font-semibold">
            {" "}
            {data.no_of_referrals === 1 ? "Person" : "People"} to Catlog in 2024.
          </h4>
        </div>
      </div>
    </div>
  );
};
export default ReferralsShareCard;
