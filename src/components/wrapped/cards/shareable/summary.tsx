import { YearWrappedResponse } from "@/assets/interfaces";
import { millify } from "@/assets/js/utils/functions";
import { toNaira } from "@/assets/js/utils/utils";
import dayjs from "dayjs";

interface Props {
  data: Partial<YearWrappedResponse>;
}
const SummaryShareCard: React.FC<Props> = ({ data }) => {
  const month = dayjs()
    .set("month", data.month_with_highest_orders ?? 2)
    .format("MMMM");

  return (
    <div className="h-full relative" style={{ aspectRatio: "16:9" }}>
      <img
        className="w-full h-full abs-center"
        src="https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/My_Year_in_Review.png"
        alt=""
      />
      <div className="text-white relative z-10 text-center top-[130px] px-[50px] h-[1360px] w-full">
        <div className="w-full h-full text-[90px]   overflow-hidden text-card rounded-[40px] bg-white ">
          <div className="font-display font-semibold w-full h-full flex flex-col p-10 items-center justify-between">
            <h2 className="py-5  text-[#5644B3]">Your Year In Review</h2>
            <div className="w-full h-full flex flex-col items-center justify-end">
              <div className="data-card data-card-1 h-[215px] flex items-center px-[5%] justify-between bg-[#5644B3] w-full rounded-t-8">
                <span className="text-[25px] text-white">TOTAL STORE VISITS</span>
                <h3 className="text-[#F2B700]"> {data.no_of_store_visits?.toLocaleString() ?? "0"} </h3>
              </div>

              <div className="data-card data-card-2 h-[215px] flex items-center px-[5%] justify-between bg-[#E5DFFF] w-full">
                <h3 className="text-[#5644B3]"> {data.total_order_count?.toLocaleString() ?? "0"} </h3>
                <span className="text-[25px] text-black-secondary uppercase">TOTAL ORDERS</span>
              </div>

              <div className="data-card data-card-3 h-[215px] flex items-center px-[5%] justify-between bg-[#38D771] w-full">
                <span className="text-[25px] text-white uppercase text-left">PAYMENTS</span>
                <h3 className="text-white text-right leading-none tracking-tighter">
                  {" "}
                  {data.store_currency} {millify(toNaira(data?.total_payments_volume ?? 0))}{" "}
                </h3>
              </div>

              <div className="data-card data-card-4 h-[215px] flex items-center px-[5%] justify-between bg-[#FFF1F4] w-full">
                <h3 className="text-[#DB577A] text-left leading-none elipsis-2">{data?.top_product?.name ?? "-"}</h3>
                <span className="text-[25px] text-black-secondary text-right  uppercase">TOP PRODUCT</span>
              </div>

              <div className="data-card data-card-5 h-[215px] flex items-center px-[5%] justify-between bg-[#F2B700] w-full rounded-b-8">
                <span className="text-[25px] text-white  uppercase text-left">BEST MONTH</span>
                <h3 className="text-white text-right leading-[1.8rem]"> {month} </h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default SummaryShareCard;
