import { YearWrappedResponse } from "@/assets/interfaces";

interface Props {
  data: Partial<YearWrappedResponse>;
}
const TopLocationShareCard: React.FC<Props> = ({ data }) => {
  return (
    <div className="h-full relative" style={{ aspectRatio: "16:9" }}>
      <img
        className="w-full h-full abs-center"
        src="https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/Top_order_Location_6.png"
        alt=""
      />

      <div className=" text-white relative z-10 text-center top-[130px] px-[50px] h-[1000px] w-full">
        <h3 className="z-10 relative text-[100px] top-[290px] leading-none max-w-[70%] mx-auto font-semibold">
          {" "}
          {data.top_orders_location ?? "Lagos"}{" "}
        </h3>
        <div className="w-[fit-content] mx-auto">
          <h4 className="text-black font-bold relative top-[380px] text-left text-[30px]">Gave me</h4>
          <h3 className="text-[#624FC3] text-[100px] relative font-bold top-[350px]">
            {" "}
            {data.no_of_orders_for_location ?? "2000"}{" "}
          </h3>
          <h4 className="text-black font-bold relative top-[320px] text-right text-[30px]">orders in 2024</h4>
        </div>

        <h4 className="text-black mx-auto left-0 right-0 font-bold absolute bottom-[100px] text-[35px]">
          In 2024, my biggest fans were in{" "}
          <span className="text-[#624FC3]">{data.top_orders_location ?? "Lagos"}!</span>
        </h4>
      </div>
    </div>
  );
};
export default TopLocationShareCard;
