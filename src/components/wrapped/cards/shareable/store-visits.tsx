import { YearWrappedResponse } from "@/assets/interfaces";

interface Props {
  data: Partial<YearWrappedResponse>;
}
const StoreVisitsShareCard: React.FC<Props> = ({ data }) => {
  return (
    <div className="h-full relative" style={{ aspectRatio: "16:9" }}>
      <img
        className="w-full h-full abs-center"
        src="https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/shareable/Total_Store_Visits.png"
        alt=""
      />

      <div className=" relative z-10 top-[130px] px-[50px] h-[1000px] w-full">
        <div className="flex flex-col items-center justify-center w-full h-full">
          <div className="w-[fit-content] rounded-3xl flex justify-center text-[80px] font-display font-semibold min-w-[60%] py-6.25 bg-[#6955D1] text-[#FFBE0B]">
            {data.no_of_store_visits??'1000'}
          </div>
          <h4 className="text-[70px] leading-none mt-[45px] font-display max-w-[680px] font-semibold text-white">
          Customers visited my Catlog store in 2024!
          </h4>
        </div>
      </div>
    </div>
  );
};
export default StoreVisitsShareCard;
