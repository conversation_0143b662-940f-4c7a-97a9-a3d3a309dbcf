import { useMemo, useState } from "react";
import { AppBtn } from "../ui/buttons";
import Checkbox from "../ui/form-elements/checkbox";
import Bg from "./cards/big-moves-bg";
import { wrappedCardData } from "@/assets/js/utils/constants";
import { WrappedData } from "@/pages/wrapped/[slug]";

interface Props {
  onSelectCards: (keys: string[]) => void;
}
const SelectWrappedCards: React.FC<Props> = ({ onSelectCards }) => {
  const [checked, setChecked] = useState<string[]>([]);

  const data = useMemo<WrappedData[]>(() => {
    const data = wrappedCardData.filter((c) => c.shareable);
    return data;
  }, []);

  const handleToggleCardSelect = (key: string) => {
    const newChecked = [...checked];
    if (newChecked.includes(key)) {
      newChecked.splice(newChecked.indexOf(key), 1);
    } else {
      newChecked.push(key);
    }
    setChecked(newChecked);
  };

  return (
    <div>
      <div className="flex gap-2.5 items-center rounded-xl bg-grey-fields-100 p-3.75">
        {/* prettier-ignore */}
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M17.1 2H12.9C9.81693 2 8.37099 3.09409 8.06975 5.73901C8.00673 6.29235 8.465 6.75 9.02191 6.75H11.1C15.3 6.75 17.25 8.7 17.25 12.9V14.9781C17.25 15.535 17.7077 15.9933 18.261 15.9303C20.9059 15.629 22 14.1831 22 11.1V6.9C22 3.4 20.6 2 17.1 2Z" fill="#FF8159"/>
            <path d="M11.1 8H6.9C3.4 8 2 9.4 2 12.9V17.1C2 20.6 3.4 22 6.9 22H11.1C14.6 22 16 20.6 16 17.1V12.9C16 9.4 14.6 8 11.1 8ZM12.29 13.65L8.58 17.36C8.44 17.5 8.26 17.57 8.07 17.57C7.88 17.57 7.7 17.5 7.56 17.36L5.7 15.5C5.42 15.22 5.42 14.77 5.7 14.49C5.98 14.21 6.43 14.21 6.71 14.49L8.06 15.84L11.27 12.63C11.55 12.35 12 12.35 12.28 12.63C12.56 12.91 12.57 13.37 12.29 13.65Z" fill="#FF8159"/>
        </svg>
        <span className="text-sm text-black"> {checked.length} Cards Selected</span>
      </div>

      <div className="max-w-full overflow-x-auto mt-2.5">
        <div className="flex gap-2.5 h-[300px]">
          {data.map((card, index) => (
            <div
              onClick={() => handleToggleCardSelect(card.key)}
              key={index}
              className="w-[170px] overflow-hidden cursor-pointer hover:opacity-90 duration-200 h-full rounded-xl bg-grey-ash flex-shrink-0 relative"
            >
              <div className="w-full absolute left-0 top-0 h-full">{Bg(card.colors.fill1, card.colors.fill2)}</div>
              <div
                style={{ color: card.colors.textColor }}
                className="flex relative z-10 h-full w-full font-bold flex-col items-center justify-center"
              >
                <h4 className="tracking-tight text-lg">{card.name}</h4>
              </div>
              <Checkbox className="absolute bottom-2.5 right-2.5" round checked={checked.includes(card.key)} name="" />
            </div>
          ))}
        </div>
      </div>
      <AppBtn disabled={checked.length === 0} onClick={() => onSelectCards(checked)} className="mt-5" isBlock>
        Share
      </AppBtn>
    </div>
  );
};
export default SelectWrappedCards;
