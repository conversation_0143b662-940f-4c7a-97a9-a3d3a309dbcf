import { toAppUrl, toBase64 } from "@/assets/js/utils/functions";
import useCopyClipboard from "../hooks/useCopyClipboard";
import { wrappedCardData } from "@/assets/js/utils/constants";
import { useRequest } from "@/api/utils";
import { GetWrappedImages } from "@/api";
import { downloadImages } from "@/assets/js/utils/utils";
import { toast } from "../ui/toast";
import useShare from "../hooks/useShare";

interface Props {
  storeSlug: string;
  selectedCards: string[];
  cardImages: { [key: string]: string };
}

const SelectShareMethod: React.FC<Props> = ({ storeSlug, selectedCards, cardImages }) => {
  const request = useRequest(GetWrappedImages);
  const [copy, copyToClipboard] = useCopyClipboard("", {
    successDuration: 1000,
  });

  const { share } = useShare({
    title: "Catlog Chronicles",
    text: "✨ Checkout how my business did in 2024, on my Catlog Chronicles",
    images: [],
  });

  const handleSelectMethod = (action: (typeof data)[number]["action"]) => {
    const share_data = toBase64(JSON.stringify({ cards: selectedCards, type: "multiple" }));
    const link = `${toAppUrl(`wrapped/${storeSlug}`, true, false)}?share_data=${share_data}`;

    const shareText = `✨ Checkout how my business did in 2024, on my Catlog Chronicles: ${link}`;

    switch (action) {
      case "copy":
        copyToClipboard(link);
        break;
      case "whatsapp":
        window.open(`https://wa.me/?text=${shareText}`);
        break;
      case "x":
        window.open(`https://twitter.com/intent/tweet?text=${shareText} @catlogshop #CatlogChronicles24`, "_blank");
        break;
      case "socials":
        handleShare(link);
        break;
      case "download":
        handleDownload();
        break;
      default:
        break;
    }
  };

  const getImagesFromCache = (cardKeys: string[]) => {
    if (!cardImages) return null;
    const images = cardKeys.map((key) => cardImages[key]);
    return images;
  };

  const handleShare = async (link: string) => {
    const toastOpts = {
      loading: {
        title: "Creating images",
        message: "Please wait...",
      },
      success: {
        title: "Successful",
        message: "Shared successfully!",
      },
      error: {
        title: "Processing Images",
        message: "Please try again in one minute...",
        actionText: "Retry",
        actionFunc: () => processShare(),
      },
    };

    async function processShare() {
      try {
        const images = getImagesFromCache(selectedCards);

        if (!images || images.length === 0) throw "Processing images, try again in one minute...";

        try {
          await share({
            text: `✨ Checkout how my business did in 2024, on my Catlog Chronicles: ${link}`,
            images,
            title: "Catlog Chronicles",
          });
        } catch (e) {
          throw e;
        }
      } catch (e) {
        throw e;
      }
    }

    toast.promise(() => processShare(), toastOpts);
  };

  const handleDownload = async () => {
    const toastOpts = {
      loading: {
        title: "Downloading images",
        message: "Please wait...",
      },
      success: {
        title: "Successful",
        message: "Images downloaded successfully!",
      },
      error: {
        title: "Processing Images",
        message: "Please try again in one minute...",
        actionText: "Retry",
        actionFunc: () => processDownload(),
      },
    };

    async function processDownload() {
      try {
        const images = getImagesFromCache(selectedCards);

        if (!images || images.length === 0) throw "Processing images, try again in one minute...";

        if (images) {
          try {
            await downloadImages(images);
          } catch (e) {
            throw e;
          }
        } else throw "Something went wrong";
      } catch (e) {
        throw e;
      }
    }

    toast.promise(() => processDownload(), toastOpts);
  };

  return (
    <div className="bg-grey-fields-200 rounded-[20px] overflow-hidden">
      {data.map((d, i) => (
        <button
          key={i}
          onClick={() => handleSelectMethod(d.action)}
          className="flex items-center  justify-between w-full hover:bg-grey-fields-100 p-2.5 border-b border-grey-divider last:border-none"
        >
          <div className="flex gap-2.5 items-center">
            {d.icon}
            <span className="text-sm text-black"> {d.title} </span>
          </div>
        </button>
      ))}
    </div>
  );
};
export default SelectShareMethod;

const data = [
  {
    icon: (
      <div className="p-2.5 w-[fit-content] h-[fit-content] rounded-full flex items-center justify-center bg-primary-100 bg-opacity-60">
        {/* prettier-ignore */}
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" >
        <path d="M11.3334 8.93329V10.9333C11.3334 13.6 10.2667 14.6666 7.60004 14.6666H5.06671C2.40004 14.6666 1.33337 13.6 1.33337 10.9333V8.39996C1.33337 5.73329 2.40004 4.66663 5.06671 4.66663H7.06671" stroke="#7E76DD" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M11.3333 8.93329H9.19998C7.59998 8.93329 7.06665 8.39996 7.06665 6.79996V4.66663L11.3333 8.93329Z" stroke="#7E76DD" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M7.73328 1.33337H10.3999" stroke="#7E76DD" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M4.66663 3.33337C4.66663 2.22671 5.55996 1.33337 6.66663 1.33337H8.41329" stroke="#7E76DD" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M14.6667 5.33337V9.46004C14.6667 10.4934 13.8267 11.3334 12.7933 11.3334" stroke="#7E76DD" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M14.6666 5.33337H12.6666C11.1666 5.33337 10.6666 4.83337 10.6666 3.33337V1.33337L14.6666 5.33337Z" stroke="#7E76DD" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </div>
    ),
    title: "Copy Link",
    action: "copy",
  },
  {
    icon: (
      <div className="p-2.5 w-[fit-content] h-[fit-content] rounded-full flex items-center justify-center bg-accent-red-100 bg-opacity-30">
        {/* prettier-ignore */}
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" >
        <path d="M6.00004 14.6667H10C13.3334 14.6667 14.6667 13.3334 14.6667 10V6.00004C14.6667 2.66671 13.3334 1.33337 10 1.33337H6.00004C2.66671 1.33337 1.33337 2.66671 1.33337 6.00004V10C1.33337 13.3334 2.66671 14.6667 6.00004 14.6667Z" stroke="#BF0637" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M6 7.67334L8 9.67334L10 7.67334" stroke="#BF0637" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8 9.6733V4.33997" stroke="#BF0637" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M4 11.0066C6.59333 11.8733 9.40667 11.8733 12 11.0066" stroke="#BF0637" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </div>
    ),
    title: "Download As Image(s)",
    action: "download",
  },
  {
    icon: (
      <div className="p-2.5 w-[fit-content] h-[fit-content] rounded-full flex items-center justify-center bg-accent-orange-100 bg-opacity-30">
        {/* prettier-ignore */}
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" >
        <path d="M8.00029 4.57678C7.32323 4.57678 6.66138 4.77755 6.09843 5.15371C5.53547 5.52986 5.09671 6.0645 4.83761 6.69002C4.57851 7.31554 4.51072 8.00384 4.6428 8.66789C4.77489 9.33193 5.10093 9.9419 5.57968 10.4207C6.05843 10.8994 6.6684 11.2254 7.33244 11.3575C7.99649 11.4896 8.68479 11.4218 9.31031 11.1627C9.93583 10.9036 10.4705 10.4649 10.8466 9.9019C11.2228 9.33895 11.4235 8.6771 11.4235 8.00004C11.4236 7.55049 11.335 7.10534 11.163 6.69001C10.991 6.27467 10.7388 5.89729 10.4209 5.57941C10.103 5.26153 9.72566 5.00938 9.31032 4.83734C8.89499 4.66531 8.44984 4.57677 8.00029 4.57678ZM8.00029 10.2224C7.56074 10.2224 7.13106 10.0921 6.76559 9.84788C6.40012 9.60367 6.11527 9.25658 5.94706 8.85049C5.77885 8.4444 5.73485 7.99755 5.8206 7.56644C5.90635 7.13534 6.11802 6.73935 6.42883 6.42854C6.73964 6.11773 7.13564 5.90607 7.56674 5.82032C7.99784 5.73457 8.4447 5.77859 8.85079 5.9468C9.25688 6.11501 9.60396 6.39986 9.84816 6.76534C10.0924 7.13081 10.2227 7.56049 10.2227 8.00004C10.2227 8.2919 10.1653 8.58091 10.0536 8.85056C9.94192 9.12021 9.77822 9.36522 9.57184 9.57159C9.36547 9.77796 9.12046 9.94166 8.85081 10.0533C8.58116 10.165 8.29215 10.2225 8.00029 10.2224Z" fill="#F35508"/>
        <path d="M14.6266 5.2515C14.6157 4.69848 14.511 4.15132 14.3169 3.63333C14.1493 3.18858 13.8869 2.78562 13.548 2.45235C13.2152 2.11312 12.8123 1.85067 12.3676 1.68337C11.8496 1.48923 11.3024 1.38453 10.7494 1.37373C10.0385 1.34137 9.81129 1.33337 8.00084 1.33337C6.19038 1.33337 5.96296 1.34088 5.25174 1.37373C4.69872 1.38466 4.15156 1.48936 3.63357 1.68337C3.1887 1.85082 2.78569 2.11324 2.4526 2.45235C2.11317 2.78518 1.85072 3.18827 1.68366 3.63333C1.48966 4.15132 1.38496 4.69848 1.37402 5.2515C1.34116 5.96231 1.33362 6.18959 1.33362 8.00004C1.33362 9.81049 1.34116 10.0377 1.37401 10.7485C1.38494 11.3016 1.48965 11.8488 1.68366 12.3668C1.85077 12.8118 2.11322 13.2149 2.4526 13.5477C2.78585 13.8866 3.18882 14.149 3.63357 14.3167C4.15158 14.5106 4.69872 14.6153 5.25174 14.6263C5.96296 14.6586 6.18983 14.6667 8.00028 14.6667C9.81074 14.6667 10.038 14.6592 10.7488 14.6263C11.3018 14.6154 11.849 14.5107 12.367 14.3167C12.8099 14.1454 13.2122 13.8835 13.548 13.5477C13.8838 13.2119 14.1457 12.8097 14.3169 12.3668C14.5112 11.8488 14.6159 11.3016 14.6266 10.7485C14.6589 10.0372 14.6664 9.81049 14.6664 8.00004C14.6664 6.18959 14.6589 5.96231 14.6266 5.2515ZM13.4273 10.6942C13.4221 11.117 13.3446 11.5357 13.1979 11.9323C13.087 12.2198 12.917 12.4808 12.6991 12.6987C12.4811 12.9166 12.22 13.0864 11.9324 13.1972C11.536 13.3442 11.1172 13.4218 10.6944 13.4265C9.99165 13.4588 9.78058 13.4653 8.00024 13.4653C6.2199 13.4653 6.00942 13.4588 5.30691 13.4265C4.88412 13.4217 4.46533 13.3441 4.06886 13.1972C3.77929 13.0902 3.5173 12.9198 3.30203 12.6985C3.08042 12.4838 2.90999 12.2219 2.80336 11.9323C2.65638 11.5358 2.5788 11.117 2.57403 10.6942C2.5417 9.99149 2.53526 9.78043 2.53526 8.00009C2.53526 6.21975 2.54226 6.00927 2.57403 5.30595C2.57936 4.88323 2.65692 4.46453 2.80336 4.06795C2.91037 3.77838 3.08075 3.51639 3.30203 3.30113C3.51692 3.07939 3.77902 2.90895 4.06886 2.80246C4.46531 2.65549 4.88412 2.57792 5.30691 2.57313C6.00962 2.5408 6.2207 2.53436 8.00024 2.53436C9.77979 2.53436 9.99106 2.54135 10.6944 2.57313C11.1171 2.57845 11.5358 2.65601 11.9324 2.80246C12.2201 2.9133 12.4813 3.08317 12.6992 3.30114C12.9172 3.5191 13.0871 3.78032 13.1979 4.06796C13.3449 4.4644 13.4225 4.88319 13.4273 5.30596C13.4596 6.00929 13.466 6.21979 13.466 8.00009C13.466 9.7804 13.4596 9.99089 13.4273 10.6942H13.4273Z" fill="#F35508"/>
        <path d="M11.5594 3.64136H11.5591C11.347 3.6414 11.1436 3.72569 10.9936 3.87571C10.8436 4.02572 10.7594 4.22917 10.7594 4.44129C10.7594 4.65341 10.8437 4.85684 10.9937 5.00683C11.1437 5.15682 11.3471 5.24108 11.5593 5.24108C11.7714 5.24108 11.9748 5.15682 12.1248 5.00683C12.2748 4.85684 12.3591 4.65341 12.3591 4.44129C12.3591 4.22917 12.2749 4.02572 12.1249 3.87571C11.975 3.72569 11.7715 3.6414 11.5594 3.64136Z" fill="#F35508"/>
        </svg>
      </div>
    ),
    title: "Share to Socials",
    action: "socials",
  },
  {
    icon: (
      <div className="p-2.5 w-[fit-content] h-[fit-content] rounded-full flex items-center justify-center bg-accent-yellow-100 bg-opacity-30">
        {/* prettier-ignore */}
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" >
        <path d="M1.33334 2.33329C2.14546 3.32896 3.15965 4.14094 4.30888 4.71555C5.4581 5.29016 6.7162 5.61433 8.00001 5.66663C7.82667 4.95996 7.81334 3.03996 9.16001 2.23996C9.70744 1.8801 10.345 1.68142 11 1.66663C11.4274 1.66759 11.85 1.75693 12.2412 1.92902C12.6324 2.10111 12.9838 2.35224 13.2733 2.66663C13.975 2.51727 14.6496 2.26095 15.2733 1.90663C15.0544 2.62209 14.5798 3.23195 13.94 3.61996C14.5505 3.56713 15.1489 3.41865 15.7133 3.17996C15.2885 3.81291 14.7507 4.36209 14.1267 4.79996C14.6 9.04663 10.7933 14.3266 5.38 14.3266C3.62423 14.3362 1.89787 13.8759 0.380005 12.9933C1.20755 13.0963 2.04738 13.0313 2.84923 12.8022C3.65108 12.5731 4.39847 12.1846 5.04667 11.66C4.32951 11.6177 3.63815 11.3773 3.04961 10.9653C2.46107 10.5533 1.99847 9.98602 1.71334 9.32663C2.38 9.53996 3.01334 9.64663 3.38 9.32663C2.63099 9.13604 1.963 8.70987 1.4745 8.11094C0.985993 7.51202 0.702806 6.772 0.666672 5.99996C0.886924 6.21531 1.14753 6.38506 1.43354 6.49946C1.71954 6.61387 2.02533 6.67067 2.33334 6.66663C1.66251 6.19406 1.18956 5.49066 1.00505 4.6911C0.820539 3.89155 0.937428 3.05203 1.33334 2.33329Z" fill="#EF940F"/>
        </svg>
      </div>
    ),
    title: "Share on Twitter",
    action: "x",
  },
  {
    icon: (
      <div className="p-2.5 w-[fit-content] h-[fit-content] rounded-full flex items-center justify-center bg-accent-green-100 bg-opacity-30">
        {/* prettier-ignore */}
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" >
        <path d="M14.6533 7.60663C14.4266 3.73997 10.9133 0.759975 6.86665 1.42664C4.07999 1.88664 1.84666 4.14663 1.41333 6.93329C1.15999 8.54663 1.49334 10.0733 2.22001 11.3333L1.62666 13.54C1.49333 14.04 1.95332 14.4933 2.44665 14.3533L4.61999 13.7533C5.60666 14.3333 6.75999 14.6666 7.99332 14.6666C11.7533 14.6666 14.8733 11.3533 14.6533 7.60663ZM11.2533 10.48C11.1933 10.6 11.12 10.7133 11.0266 10.82C10.86 11 10.68 11.1333 10.48 11.2133C10.28 11.3 10.06 11.34 9.82666 11.34C9.48666 11.34 9.11999 11.26 8.73999 11.0933C8.35332 10.9266 7.97334 10.7066 7.59334 10.4333C7.20667 10.1533 6.84666 9.83996 6.5 9.49996C6.15333 9.1533 5.84665 8.78662 5.56665 8.40662C5.29331 8.02662 5.07333 7.64663 4.91333 7.26663C4.75332 6.88663 4.67333 6.51997 4.67333 6.1733C4.67333 5.94664 4.71333 5.72664 4.79333 5.52664C4.87333 5.31997 5.00001 5.13331 5.18001 4.96664C5.39334 4.75331 5.62666 4.65331 5.87333 4.65331C5.96666 4.65331 6.05998 4.6733 6.14664 4.7133C6.23331 4.7533 6.31333 4.8133 6.37333 4.89997L7.14664 5.99329C7.20664 6.07996 7.25332 6.1533 7.27998 6.22663C7.31332 6.29996 7.32666 6.36663 7.32666 6.43329C7.32666 6.51329 7.3 6.5933 7.25333 6.6733C7.20667 6.7533 7.14665 6.83329 7.06665 6.91329L6.81331 7.17996C6.77331 7.21996 6.76 7.25997 6.76 7.3133C6.76 7.33997 6.76664 7.36663 6.77331 7.3933C6.78664 7.41996 6.79334 7.43997 6.8 7.45997C6.86 7.5733 6.96665 7.71329 7.11332 7.88662C7.26665 8.05996 7.42668 8.23996 7.60001 8.41329C7.78001 8.59329 7.95334 8.75329 8.13334 8.90662C8.30667 9.05329 8.45331 9.1533 8.56665 9.2133C8.58665 9.21997 8.60666 9.23329 8.62666 9.23995C8.65333 9.25329 8.68 9.2533 8.71333 9.2533C8.77333 9.2533 8.81335 9.2333 8.85335 9.1933L9.10664 8.93997C9.19331 8.8533 9.27334 8.7933 9.34667 8.7533C9.42667 8.70663 9.5 8.67996 9.58667 8.67996C9.65333 8.67996 9.72 8.6933 9.79333 8.72663C9.86666 8.75996 9.94665 8.79997 10.0266 8.85997L11.1333 9.64663C11.22 9.70663 11.28 9.77997 11.32 9.85997C11.3533 9.94664 11.3733 10.0266 11.3733 10.12C11.3333 10.2333 11.3067 10.36 11.2533 10.48Z" fill="#39B588"/>
        </svg>
      </div>
    ),
    title: "Share on Whatsapp",
    action: "whatsapp",
  },
] as const;
