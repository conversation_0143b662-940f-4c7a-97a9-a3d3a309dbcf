interface Props {
  pause: VoidFunction;
  back: VoidFunction;
  next: VoidFunction;
}
const CardControls: React.FC<Props> = ({ back, next, pause }) => {
  return (
    <>
      <div className="h-full z-[99] absolute left-0 w-1/3 ripplex cursor-pointer" onClick={back}></div>
      <div className="h-full z-[99] absolute right-0 w-1/3 ripplex cursor-pointer" onClick={next}></div>
      <div className="z-[98] absolute left-0 top-0 w-full h-[45%] ripplex cursor-pointer" onClick={pause}></div>
      <div className="z-[98] absolute left-0 bottom-0 w-full h-[45%] ripplex cursor-pointer" onClick={pause}></div>
    </>
  );
};
export default CardControls;
