import React, { useEffect, useState } from "react";
import { useModals } from "../hooks/useModals";
import { AppBtn } from "../ui/buttons";
import Table, { TableHead, TableHeadItem, TableBody } from "../ui/table";
import AffiliateDetailsModal from "./modals/affiliate";
import EditAffiliateModal from "./modals/edit-affiliate";
import DeleteAffiliateModal from "./modals/delete-affiliate";
import AddAffiliateModal from "./modals/add-affiliate";
import { AffiliateInterface } from "../../assets/interfaces/affiliates.interface";
import AffiliateItem from "./affiliate-item";
import AffiliatesHeader from "./affiliates-header";
import useSearchParams from "../hooks/useSearchParams";
import usePagination from "../hooks/usePagination";
import useScreenSize from "../hooks/useScreenSize";
import { AffiliateItemMobile } from "./affiliate-item-mobile";
import ContentState from "../ui/content-state";
import ClearSearch from "../clear-search";
import Pagination from "../ui/pagination";
import { useFetcher } from "../../api/utils";
import { GetAffiliates } from "../../api/affiliates";
import { GetAffiliatesParams } from "../../api/interfaces/affiliates.interface";
import AffiliateOrdersList from "./modals/affiliate-orders";
import AffiliateCustomersList from "./modals/affiliate-customers";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import authContext from "@/contexts/auth-context";

const AffiliatesList: React.FC = () => {
  const PER_PAGE = 10;
  const { subscription } = authContext.useContainer();
  const { currentPage, goNext, goPrevious, setPage } = usePagination();
  const [affiliates, setAffiliates] = useState<AffiliateInterface[]>([]);
  const [selected, setSelected] = useState<AffiliateInterface | null>(null);
  const { modals, toggleModal } = useModals([
    "affiliate_details",
    "edit_details",
    "delete_details",
    "add_affiliate",
    "orders_details",
    "customers_details",
  ]);
  const { isSmall } = useScreenSize();

  const { search = null } = useSearchParams(["search"]);
  const { response, error, isLoading, makeRequest } = useFetcher<GetAffiliatesParams>(GetAffiliates, {
    page: currentPage,
    per_page: PER_PAGE,
    filter: (() => {
      const filter = {};
      const s = new URLSearchParams(window.location.search).get("search");

      if (s ?? search) {
        filter["search"] = s ?? search;
      }

      return filter;
    })(),
  });

  const pageNotReady = isLoading || error || !response?.data || affiliates.length < 1;

  useEffect(() => {
    setAffiliates(response?.data.data || []);
  }, [response]);

  const showDetails = (data: AffiliateInterface, type: "affiliate" | "edit" | "delete" | "orders" | "customers") => {
    setSelected(data);

    toggleModal(`${type}_details`);
  };

  const updateAffiliate = (details: AffiliateInterface) => {
    const affiliatesCopy = [...affiliates];
    const affiliateIndex = affiliatesCopy.findIndex((a) => a.id === details.id);

    if (affiliateIndex > -1) {
      affiliatesCopy[affiliateIndex] = details;
      setAffiliates(affiliatesCopy);
    }
  };

  const removeAffiliate = (details: AffiliateInterface) => {
    const affiliatesCopy = [...affiliates];
    const affiliateIndex = affiliatesCopy.findIndex((a) => a.id === details.id);

    if (affiliateIndex > -1) {
      affiliatesCopy.splice(affiliateIndex, 1);
      setAffiliates(affiliatesCopy);
    }
  };

  const addAffiliate = (affiliate: AffiliateInterface) => {
    const affiliatesCopy = [...affiliates];
    affiliatesCopy.unshift(affiliate);
    setAffiliates(affiliatesCopy);
  };

  return (
    <div className="pt-5 sm:pt-7.5 lg:pt-8.75">
      <AffiliatesHeader toggleModal={toggleModal} />
      <ClearSearch search={search} />
      {pageNotReady && (
        <ContentState
          loadingText="Loading Affiliates..."
          isLoading={isLoading}
          isEmpty={affiliates?.length < 1}
          emptyIcon={
            // prettier-ignore
            <svg width="45%" viewBox="0 0 24 24" fill="none">
              <path opacity="0.4" d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.96 11.48 12.04 11.48 12.1 11.49C12.12 11.49 12.13 11.49 12.15 11.49C12.16 11.49 12.16 11.49 12.17 11.49C14.73 11.4 16.74 9.32 16.75 6.75C16.75 4.13 14.62 2 12 2Z" fill="#AAAAAA"/>
              <path d="M17.08 14.1499C14.29 12.2899 9.74002 12.2899 6.93002 14.1499C5.66002 14.9999 4.96002 16.1499 4.96002 17.3799C4.96002 18.6099 5.66002 19.7499 6.92002 20.5899C8.32002 21.5299 10.16 21.9999 12 21.9999C13.84 21.9999 15.68 21.5299 17.08 20.5899C18.34 19.7399 19.04 18.5999 19.04 17.3599C19.03 16.1299 18.34 14.9899 17.08 14.1499Z" fill="#AAAAAA"/>
            </svg>
          }
          title="No Affiliates to show"
          description="Create a new affiliate"
          errorMessage="We couldn't load your affiliates, click on the button to retry"
          errorTitle="Fetching affiliates failed"
          error={error}
          errorAction={
            <AppBtn size="sm" onClick={() => makeRequest()}>
              Reload Affiliates
            </AppBtn>
          }
        >
          <AppBtn size="sm" onClick={() => toggleModal("add_affiliate")}>
            Create an affiliate
          </AppBtn>
        </ContentState>
      )}
      {!pageNotReady && (
        <>
          <div className="mt-5">
            {!isSmall && (
              <Table>
                <TableHead>
                  <TableHeadItem>Name</TableHeadItem>
                  <TableHeadItem>Email</TableHeadItem>
                  <TableHeadItem>Phone</TableHeadItem>
                  <TableHeadItem className="hidden sm:table-cell">Type</TableHeadItem>
                  <TableHeadItem className="hidden sm:table-cell">Orders</TableHeadItem>
                  <TableHeadItem>Options</TableHeadItem>
                </TableHead>
                <TableBody>
                  {affiliates.map((affiliate, index) => (
                    <AffiliateItem {...{ affiliate, showDetails }} key={index} />
                  ))}
                </TableBody>
              </Table>
            )}

            {isSmall && (
              <ul>
                {affiliates.map((a, index) => (
                  <AffiliateItemMobile
                    key={index}
                    index={index}
                    onAction={(action: "affiliate" | "edit" | "delete") => showDetails(a, action)}
                    data={a}
                  />
                ))}
              </ul>
            )}
            <Pagination
              {...{
                setPage,
                currentPage,
                length: affiliates?.length,
                data: response?.data,
                goNext,
                goPrevious,
                label: "affiliates",
                per_page: PER_PAGE,
              }}
            />
          </div>

          {selected && (
            <AffiliateDetailsModal
              show={modals.affiliate_details.show}
              toggle={() => toggleModal("affiliate_details")}
              affiliate={selected}
              takeAction={(type: "edit" | "delete" | "orders" | "customers") => showDetails(selected, type)}
            />
          )}

          {selected && (
            <EditAffiliateModal
              show={modals.edit_details.show}
              toggle={() => toggleModal("edit_details")}
              affiliate={selected}
              updateAffiliate={updateAffiliate}
            />
          )}

          {selected && (
            <DeleteAffiliateModal
              show={modals.delete_details.show}
              toggle={() => toggleModal("delete_details")}
              affiliate={selected}
              removeAffiliate={removeAffiliate}
            />
          )}
        </>
      )}

      <AddAffiliateModal
        show={modals.add_affiliate.show}
        toggle={() => toggleModal("add_affiliate")}
        addAffiliate={addAffiliate}
      />

      <AffiliateOrdersList
        show={modals.orders_details.show}
        toggle={() => toggleModal("orders_details")}
        affiliateId={selected?.id}
      />

      <AffiliateCustomersList
        show={modals.customers_details.show}
        toggle={() => toggleModal("customers_details")}
        affiliateId={selected?.id}
      />
    </div>
  );
};

export default AffiliatesList;
