import React from "react";
import { AppBtn } from "../../ui/buttons";
import ErrorLabel from "../../ui/error-label";
import Mo<PERSON>, { <PERSON>dalBody, ModalFooter } from "../../ui/modal";
import SuccessLabel from "../../ui/success-label";
import { AffiliateInterface } from "../../../assets/interfaces/affiliates.interface";
import { useRequest } from "../../../api/utils";
import { DeleteAffiliate } from "../../../api/affiliates";
import { DeleteAffiliateParams } from "../../../api/interfaces/affiliates.interface";

interface Props {
  show: boolean;
  toggle: (status: boolean) => void;
  affiliate: AffiliateInterface;
  removeAffiliate: (affiliate: AffiliateInterface) => void;
}

const DeleteAffiliateModal: React.FC<Props> = ({ show, toggle, affiliate, removeAffiliate }) => {
  const { makeRequest, isLoading, error, response } = useRequest<DeleteAffiliateParams>(DeleteAffiliate);

  const handleItemDelete = async () => {
    const [res, err] = await makeRequest({ id: affiliate.id });

    if (!err) {
      toggle(false);
      removeAffiliate(affiliate);
    }
  };

  return (
    <Modal {...{ show, toggle }} title="Delete Affiliate" size="sm">
      <ModalBody>
        <ErrorLabel error={error?.message} />
        <SuccessLabel message={response ? "Affiliate deleted successfully!" : ""} />
        <div className="text-center py-3.5">
          <h4 className="text-black text-base font-semibold">Do you want to delete this affiliate?</h4>
          <p className="text-sm text-grey-subtext mt-1 max-w-xs mx-auto">
            This affiliate would be completely removed from the list and all records of orders and customers associated
            with this affiliate would be lost.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="danger" onClick={handleItemDelete} disabled={isLoading} size="lg">
          {isLoading ? "Deleting..." : "Delete Affiliate"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default DeleteAffiliateModal;
