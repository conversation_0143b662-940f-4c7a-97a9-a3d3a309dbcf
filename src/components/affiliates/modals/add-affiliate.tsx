import { useFormik } from "formik";
import React, { useEffect } from "react";
import { AppBtn } from "../../ui/buttons";
import <PERSON><PERSON>, { <PERSON>dalBody, ModalFooter } from "../../ui/modal";
import * as Yup from "yup";
import { InputField, PhoneInput, SelectDropdown } from "../../ui/form-elements";
import { enumToHumanFriendly, getFieldvalues, phoneObjectToString } from "../../../assets/js/utils/functions";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import { AFFILIATE_TYPES, AffiliateInterface } from "../../../assets/interfaces/affiliates.interface";
import { phoneValidation } from "../../../assets/js/utils/common-validations";
import { useRequest } from "../../../api/utils";
import { CreateAffiliate } from "../../../api/affiliates";
import { CreateAffiliateParams } from "../../../api/interfaces/affiliates.interface";

interface Props {
  show: boolean;
  toggle: (status: boolean) => void;
  addAffiliate: (affiliate: AffiliateInterface) => void;
}

const AddAffiliateModal: React.FC<Props> = ({ show, toggle, addAffiliate }) => {
  const { isLoading, makeRequest, error, response } = useRequest<CreateAffiliateParams>(CreateAffiliate);
  const form = useFormik({
    initialValues: {
      email: "",
      phone: {
        code: "+234",
        digits: "",
      },
      name: "",
      type: AFFILIATE_TYPES.INFLUENCER,
    },
    onSubmit: async (values) => {
      const [response, error] = await makeRequest({
        ...values,
        phone: phoneObjectToString(values.phone),
      });

      if (response) {
        //update affiliate details
        addAffiliate(response?.data);

        setTimeout(() => {
          toggle(false);
        }, 2000);
      }
    },
    validationSchema,
  });

  useEffect(() => {
    if (!show) {
      form.resetForm();
    }
  }, [show]);

  return (
    <Modal {...{ show, toggle }} title={`Add Affiliate`} size="midi" bgClose={false}>
      <form className="flex flex-col flex-auto overflow-hidden" onSubmit={form.handleSubmit}>
        <ModalBody>
          <ErrorLabel error={error?.message ?? null} />
          <SuccessLabel message={response ? "Affiliate added successfully!" : null} />
          <InputField label="Name" {...getFieldvalues("name", form)} />
          <PhoneInput label="Phone" {...getFieldvalues("phone", form)} />
          <InputField label="Email" {...getFieldvalues("email", form)} />
          <SelectDropdown
            label="Affiliate Type"
            placeholder="Select Affiliate Type"
            options={Object.values(AFFILIATE_TYPES).map((type) => ({
              value: type,
              text: enumToHumanFriendly(type),
            }))}
            {...getFieldvalues("type", form)}
          />
        </ModalBody>
        <ModalFooter>
          <AppBtn isBlock type="submit" size="lg" disabled={isLoading}>
            {isLoading ? "Adding affiliate..." : "Save Affiliate"}
          </AppBtn>
        </ModalFooter>
      </form>
    </Modal>
  );
};

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  email: Yup.string().email("Please enter a valid email").required("Email is required"),
  phone: phoneValidation(),
  type: Yup.string().required("Type is required"),
});

export default AddAffiliateModal;
