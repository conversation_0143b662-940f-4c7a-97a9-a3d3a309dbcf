import React, { useEffect, useState } from "react";
import { useModals } from "../../hooks/useModals";
import Modal, { ModalBody } from "../../ui/modal";
import { OrderInterface } from "../../../assets/interfaces";
import LazyImage from "../../lazy-image";
import { getActualOrderItem, getProductNameText } from "../../../assets/js/utils/utils";
import { toCurrency } from "../../../assets/js/utils/functions";
import { useFetcher } from "../../../api/utils";
import { GetAffiliateOrders } from "../../../api/affiliates";
import { GetAffiliateOrdersParams } from "../../../api/interfaces/affiliates.interface";
import ContentState from "../../ui/content-state";
import { AppBtn } from "../../ui/buttons";
import OrderDetailsModal from "../../orders/modals/order";
import { useRouter } from "next/router";
interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  affiliateId: string;
}

const AffiliateOrdersList: React.FC<Props> = ({ show, toggle, affiliateId }) => {
  const { modals, toggleModal } = useModals(["order"]);
  const [order, setOrder] = useState<string | null>(null);
  // const [orders, setOrders] = useState<OrderInterface[]>([]);

  const router = useRouter();
  const { response, error, isLoading, makeRequest } = useFetcher<GetAffiliateOrdersParams>(
    GetAffiliateOrders,
    {
      id: affiliateId,
      page: 1,
      per_page: 10,
    },
    ["id"]
  );

  const orders: OrderInterface[] = response?.data ?? [];

  const openOrder = (orderId: string) => {
    setOrder(orderId);
    toggleModal("order");
  };

  return (
    <>
      <Modal {...{ show, toggle }} title="Affiliate Orders" bgClose={false} size="midi">
        <ModalBody className="relative !pt-0">
          {isLoading || error || !orders.length ? (
            <ContentState
              loadingText="Loading orders..."
              isLoading={isLoading}
              isEmpty={!orders.length}
              emptyIcon={
                // prettier-ignore
                <svg width="45%" viewBox="0 0 24 24" fill="none">
                  <path opacity="0.4" d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.96 11.48 12.04 11.48 12.1 11.49C12.12 11.49 12.13 11.49 12.15 11.49C12.16 11.49 12.16 11.49 12.17 11.49C14.73 11.4 16.74 9.32 16.75 6.75C16.75 4.13 14.62 2 12 2Z" fill="#AAAAAA"/>
                  <path d="M17.08 14.1499C14.29 12.2899 9.74002 12.2899 6.93002 14.1499C5.66002 14.9999 4.96002 16.1499 4.96002 17.3799C4.96002 18.6099 5.66002 19.7499 6.92002 20.5899C8.32002 21.5299 10.16 21.9999 12 21.9999C13.84 21.9999 15.68 21.5299 17.08 20.5899C18.34 19.7399 19.04 18.5999 19.04 17.3599C19.03 16.1299 18.34 14.9899 17.08 14.1499Z" fill="#AAAAAA"/>
                </svg>
              }
              title="No orders to show"
              description="This affiliate has no orders yet"
              errorMessage="We couldn't load the orders, click on the button to retry"
              errorTitle="Fetching orders failed"
              error={error}
              errorAction={
                <AppBtn size="sm" onClick={() => makeRequest()}>
                  Reload Orders
                </AppBtn>
              }
            />
          ) : (
            <div>
              {orders.map((order: OrderInterface) => {
                return (
                  <li
                    key={order.id}
                    className="flex items-start justify-between border-b border-grey-border border-opacity-50 last:border-0 py-3.75 cursor-pointer"
                    onClick={() => openOrder(order.id)}
                    role="button"
                  >
                    <div className="flex items-center flex-1 overflow-hidden mr-2">
                      <figure className="h-11.25 w-11.25 rounded-8 flex-shrink-0 flex items-center justify-center overflow-hidden border border-grey-border border-opacity-20 relative">
                        <LazyImage
                          src={getActualOrderItem(order?.items?.[0])?.images[0]}
                          alt="product image"
                          className="h-full w-full object-cover"
                          loaderClasses="rounded-5"
                        />
                      </figure>
                      <div className="flex flex-col font-medium ml-2.5 overflow-hidden">
                        <span className="text-dark text-sm block w-full overflow-hidden overflow-ellipsis whitespace-nowrap mb-0.5">
                          {getProductNameText(order?.items)}
                        </span>
                        <span className="inline-block mt-0.5 text-1xs">
                          {toCurrency(order.total_amount, order?.currency)}
                        </span>
                      </div>
                    </div>
                    <button
                      className="flex items-center cursor-pointer select-none text-primary-500"
                      onClick={() => router.push(`/orders/${order.id}`)}
                    >
                      <span className={`font-semibold text-1xs sm:text-sm`}>See order</span>
                      {/* prettier-ignore */}
                      <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M7.33301 14.8892L15.1112 7.11099"
                          stroke="currentColor"
                          strokeWidth="1.75"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M15.1104 14.8892L15.1104 7.11099L7.33218 7.11099"
                          stroke="currentColor"
                          strokeWidth="1.75"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>
                  </li>
                );
              })}
            </div>
          )}
        </ModalBody>
      </Modal>
    </>
  );
};

export default AffiliateOrdersList;
