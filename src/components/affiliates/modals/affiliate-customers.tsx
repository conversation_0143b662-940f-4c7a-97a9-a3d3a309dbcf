import React, { useEffect, useState } from "react";
import { useModals } from "../../hooks/useModals";
import Modal, { ModalBody } from "../../ui/modal";
import { CustomerInterface } from "../../../assets/interfaces";
import { useFetcher } from "../../../api/utils";
import { GetAffiliateCustomers } from "../../../api/affiliates";
import { GetAffiliateCustomersParams } from "../../../api/interfaces/affiliates.interface";
import ContentState from "../../ui/content-state";
import { AppBtn } from "../../ui/buttons";
import { humanFriendlyDate, removeCountryCode, toCurrency } from "../../../assets/js/utils/functions";
import CustomerDetailsModal from "@/components/orders/modals/customer";
import StoreLogo from "@/components/ui/store-logo";
import Badge from "@/components/ui/badge";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  affiliateId: string;
}

const AffiliateCustomersList: React.FC<Props> = ({ show, toggle, affiliateId }) => {
  const { modals, toggleModal } = useModals(["customer"]);
  const [customer, setCustomer] = useState<CustomerInterface | null>(null);

  const { response, error, isLoading, makeRequest } = useFetcher<GetAffiliateCustomersParams>(
    GetAffiliateCustomers,
    {
      id: affiliateId,
      page: 1,
      per_page: 10,
    },
    ["id"]
  );

  const customers: CustomerInterface[] = response?.data ?? [];

  const openCustomer = (customer: CustomerInterface) => {
    setCustomer(customer);
    toggleModal("customer");
  };

  return (
    <>
      <Modal {...{ show, toggle }} title="Affiliate Customers" bgClose={false} size="midi">
        <ModalBody className="relative !pt-0">
          {isLoading || error || !customers.length ? (
            <ContentState
              loadingText="Loading customers..."
              isLoading={isLoading}
              isEmpty={!customers.length}
              emptyIcon={
                // prettier-ignore
                <svg width="45%" viewBox="0 0 24 24" fill="none">
                  <path opacity="0.4" d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.96 11.48 12.04 11.48 12.1 11.49C12.12 11.49 12.13 11.49 12.15 11.49C12.16 11.49 12.16 11.49 12.17 11.49C14.73 11.4 16.74 9.32 16.75 6.75C16.75 4.13 14.62 2 12 2Z" fill="#AAAAAA"/>
                  <path d="M17.08 14.1499C14.29 12.2899 9.74002 12.2899 6.93002 14.1499C5.66002 14.9999 4.96002 16.1499 4.96002 17.3799C4.96002 18.6099 5.66002 19.7499 6.92002 20.5899C8.32002 21.5299 10.16 21.9999 12 21.9999C13.84 21.9999 15.68 21.5299 17.08 20.5899C18.34 19.7399 19.04 18.5999 19.04 17.3599C19.03 16.1299 18.34 14.9899 17.08 14.1499Z" fill="#AAAAAA"/>
                </svg>
              }
              title="No customers to show"
              description="This affiliate has no customers yet"
              errorMessage="We couldn't load the customers, click on the button to retry"
              errorTitle="Fetching customers failed"
              error={error}
              errorAction={
                <AppBtn size="sm" onClick={() => makeRequest()}>
                  Reload Customers
                </AppBtn>
              }
            />
          ) : (
            <div>
              {customers.map((customer: CustomerInterface) => {
                return (
                  <li
                    key={customer.id}
                    className="flex items-start justify-between border-b border-grey-border border-opacity-50 last:border-0 py-3.75 cursor-pointer"
                    onClick={() => openCustomer(customer)}
                    role="button"
                  >
                    <div className="flex items-center flex-1 overflow-hidden mr-2">
                      <StoreLogo
                        logo={null}
                        storeName={customer.name ?? "-"}
                        className="h-11.25 w-11.25 text-xl font-bold"
                      />
                      <div className="flex flex-col font-medium ml-2.5 overflow-hidden">
                        <span className="text-dark text-sm block w-full overflow-hidden overflow-ellipsis whitespace-nowrap mb-0.5">
                          {customer.name}
                        </span>
                        <span className="inline-block mt-0.5 text-1xs">{removeCountryCode(customer.phone)}</span>
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className="text-1xs text-grey-subtext">
                        <Badge text={`Added: ${humanFriendlyDate(customer.created_at)}`} />
                      </span>
                    </div>
                  </li>
                );
              })}
            </div>
          )}
        </ModalBody>
      </Modal>
      {customer && (
        <CustomerDetailsModal show={modals.customer.show} toggle={() => toggleModal("customer")} customer={customer} />
      )}
    </>
  );
};

export default AffiliateCustomersList;
