import React from "react";
import AppBtn from "../../ui/buttons/app-btn";
import <PERSON><PERSON>, { <PERSON>dal<PERSON><PERSON>, ModalFooter } from "../../ui/modal";
import ContentWithCopy from "@/components/ui/content-with-copy";
import { AffiliateInterface } from "@/assets/interfaces/affiliates.interface";
import { enumToHumanFriendly, removeCountryCode, resolvePhone } from "@/assets/js/utils/functions";
import { getWhatsappLink } from "@/assets/js/utils/utils";
import { discountDetailsIcons } from "@/components/products/discounts/modals/discount-details";
import { deliveryPageIcons } from "@/pages/deliveries/[slug]";
import authContext from "@/contexts/auth-context";
import Badge from "@/components/ui/badge";

interface Props {
  affiliate: AffiliateInterface;
  show: boolean;
  toggle: (status: boolean) => void;
  takeAction: (type: "edit" | "delete" | "orders" | "customers") => void;
}

const AffiliateDetailsModal: React.FC<Props> = ({ affiliate, show, toggle, takeAction }) => {
  const { storeLink } = authContext.useContainer();
  const stats = [
    {
      icon:
        // prettier-ignore
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none" className="text-accent-red-500">
          <path d="M21.0521 8.14583L13.0312 12.7917C12.7083 12.9792 12.3021 12.9792 11.9687 12.7917L3.9479 8.14583C3.37498 7.8125 3.22915 7.03125 3.66665 6.54167C3.96873 6.19792 4.31248 5.91667 4.67706 5.71875L10.3229 2.59375C11.5312 1.91667 13.4896 1.91667 14.6979 2.59375L20.3437 5.71875C20.7083 5.91667 21.0521 6.20833 21.3541 6.54167C21.7708 7.03125 21.625 7.8125 21.0521 8.14583Z" fill="currentColor"/>
          <path d="M11.9063 14.7292V21.8333C11.9063 22.625 11.1042 23.1458 10.3959 22.8021C8.25008 21.75 4.6355 19.7813 4.6355 19.7813C3.36466 19.0625 2.323 17.25 2.323 15.7604V10.3854C2.323 9.56251 3.18758 9.04167 3.89591 9.44792L11.3855 13.7917C11.698 13.9896 11.9063 14.3438 11.9063 14.7292Z" fill="currentColor"/>
          <path d="M13.0938 14.7292V21.8333C13.0938 22.625 13.8958 23.1458 14.6042 22.8021C16.75 21.75 20.3646 19.7813 20.3646 19.7813C21.6354 19.0625 22.6771 17.25 22.6771 15.7604V10.3854C22.6771 9.56251 21.8125 9.04167 21.1042 9.44792L13.6146 13.7917C13.3021 13.9896 13.0938 14.3438 13.0938 14.7292Z" fill="currentColor"/>
        </svg>,
      label: "Total Orders",
      value: affiliate.total_orders,
      color: "",
    },
    {
      icon:
        // prettier-ignore
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none" className="text-accent-orange-500">
          <path d="M12.5007 2.08331C9.77148 2.08331 7.55273 4.30206 7.55273 7.03123C7.55273 9.70831 9.64648 11.875 12.3757 11.9687C12.459 11.9583 12.5423 11.9583 12.6048 11.9687C12.6257 11.9687 12.6361 11.9687 12.6569 11.9687C12.6673 11.9687 12.6673 11.9687 12.6777 11.9687C15.3444 11.875 17.4382 9.70831 17.4486 7.03123C17.4486 4.30206 15.2298 2.08331 12.5007 2.08331Z" fill="currentColor"/>
          <path d="M17.791 14.7396C14.8848 12.8021 10.1452 12.8021 7.2181 14.7396C5.89518 15.625 5.16602 16.8229 5.16602 18.1041C5.16602 19.3854 5.89518 20.5729 7.20768 21.4479C8.66602 22.4271 10.5827 22.9166 12.4993 22.9166C14.416 22.9166 16.3327 22.4271 17.791 21.4479C19.1035 20.5625 19.8327 19.375 19.8327 18.0833C19.8223 16.8021 19.1035 15.6146 17.791 14.7396Z" fill="currentColor"/>
        </svg>,
      label: "Total Customers",
      value: affiliate.total_customers,
      color: "",
    },
  ];

  return (
    <>
      <Modal show={show} toggle={toggle} title="Affiliate Details" size="md">
        <ModalBody>
          <div className="flex items-center text-dark">
            <div className="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center text-white text-xl font-bold">
              {affiliate.name.charAt(0).toUpperCase()}
            </div>
            <div className="ml-4">
              <h5 className="text-lg font-bold text-black -mb-1 font-display">{affiliate.name}</h5>
              <span className="text-grey-subtext text-1xs">
                <b className="font-medium">Affiliate Link: </b>
                <ContentWithCopy text={`${storeLink}?ref=${affiliate.slug}`}>
                  <span className="text-primary-500 font-medium">{`${storeLink}?ref=${affiliate.slug}`}</span>
                </ContentWithCopy>
              </span>
            </div>
          </div>

          <div className="w-full rounded-15 border card-shadow border-grey-divider grid lg:gap-3 grid-cols-2 my-5">
            {stats.map(({ icon, label, color, value }, index) => (
              <div
                className={`flex items-center w-full py-4 px-3 ${
                  index === 1 ? "border-grey-divider border-l pl-3" : ""
                }`}
                key={index}
              >
                <figure
                  className={`h-11 w-11 sm:h-11.5 sm:w-11.5 rounded-full flex items-center justify-center flex-shrink-0 bg-grey-fields-100 text-grey-subtext`}
                >
                  {icon}
                </figure>
                <div className="ml-2.5">
                  <h4
                    className={`text-base sm:text-lg mb-0.5 font-display !leading-none font-bold mt-0.5 ${
                      value === 0 ? "text-placeholder" : "text-black"
                    }`}
                  >
                    {value}
                  </h4>
                  <span className="text-xs md:text-1xs text-dark inline-block !leading-none">{label}</span>
                </div>
              </div>
            ))}
          </div>

          <div className="text-sm text-dark">
            <div className="flex justify-between mb-4">
              <div className="flex items-center">
                <figure className="h-7.5 w-7.5 rounded-full bg-grey-fields-100 flex items-center justify-center text-dark">
                  <svg width="50%" viewBox="0 0 15 15" fill="none">
                    <path
                      d="M13.7312 11.4562C13.7312 11.6812 13.6812 11.9125 13.575 12.1375C13.4687 12.3625 13.3313 12.575 13.15 12.775C12.8438 13.1125 12.5062 13.3563 12.125 13.5125C11.75 13.6688 11.3438 13.75 10.9063 13.75C10.2688 13.75 9.5875 13.6 8.86875 13.2938C8.15 12.9875 7.43125 12.575 6.71875 12.0563C6 11.5313 5.31875 10.95 4.66875 10.3062C4.025 9.65625 3.44375 8.975 2.925 8.2625C2.4125 7.55 2 6.8375 1.7 6.13125C1.4 5.41875 1.25 4.7375 1.25 4.0875C1.25 3.6625 1.325 3.25625 1.475 2.88125C1.625 2.5 1.8625 2.15 2.19375 1.8375C2.59375 1.44375 3.03125 1.25 3.49375 1.25C3.66875 1.25 3.84375 1.2875 4 1.3625C4.1625 1.4375 4.30625 1.55 4.41875 1.7125L5.86875 3.75625C5.98125 3.9125 6.0625 4.05625 6.11875 4.19375C6.175 4.325 6.20625 4.45625 6.20625 4.575C6.20625 4.725 6.1625 4.875 6.075 5.01875C5.99375 5.1625 5.875 5.3125 5.725 5.4625L5.25 5.95625C5.18125 6.025 5.15 6.10625 5.15 6.20625C5.15 6.25625 5.15625 6.3 5.16875 6.35C5.1875 6.4 5.20625 6.4375 5.21875 6.475C5.33125 6.68125 5.525 6.95 5.8 7.275C6.08125 7.6 6.38125 7.93125 6.70625 8.2625C7.04375 8.59375 7.36875 8.9 7.7 9.18125C8.025 9.45625 8.29375 9.64375 8.50625 9.75625C8.5375 9.76875 8.575 9.7875 8.61875 9.80625C8.66875 9.825 8.71875 9.83125 8.775 9.83125C8.88125 9.83125 8.9625 9.79375 9.03125 9.725L9.50625 9.25625C9.6625 9.1 9.8125 8.98125 9.95625 8.90625C10.1 8.81875 10.2437 8.775 10.4 8.775C10.5187 8.775 10.6438 8.8 10.7812 8.85625C10.9187 8.9125 11.0625 8.99375 11.2188 9.1L13.2875 10.5687C13.45 10.6812 13.5625 10.8125 13.6312 10.9687C13.6937 11.125 13.7312 11.2812 13.7312 11.4562Z"
                      stroke="currentColor"
                      strokeMiterlimit="10"
                    />
                  </svg>
                </figure>
                <span className="ml-2">Phone Number</span>
              </div>
              <div className="flex items-center">
                <span className="text-black font-medium">{removeCountryCode(affiliate.phone)}</span>
                <div className="flex items-center border-grey-border space-x-1.5 ml-2.5">
                  <a
                    className="h-4 w-4 text-placeholder"
                    href={getWhatsappLink(affiliate.phone.replace("+", ""))}
                    target="_blank"
                    rel="noreferrer"
                  >
                    <svg width="100%" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M6.9 20.6C8.4 21.5 10.2 22 12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 13.8 2.5 15.5 3.3 17L2.44044 20.306C2.24572 21.0549 2.93892 21.7317 3.68299 21.5191L6.9 20.6Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeMiterlimit="10"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M16.5 14.8485C16.5 15.0105 16.4639 15.177 16.3873 15.339C16.3107 15.501 16.2116 15.654 16.0809 15.798C15.86 16.041 15.6167 16.2165 15.3418 16.329C15.0714 16.4415 14.7784 16.5 14.4629 16.5C14.0033 16.5 13.512 16.392 12.9937 16.1715C12.4755 15.951 11.9572 15.654 11.4434 15.2805C10.9251 14.9025 10.4339 14.484 9.9652 14.0205C9.501 13.5525 9.08187 13.062 8.70781 12.549C8.33826 12.036 8.04081 11.523 7.82449 11.0145C7.60816 10.5015 7.5 10.011 7.5 9.543C7.5 9.237 7.55408 8.9445 7.66224 8.6745C7.77041 8.4 7.94166 8.148 8.18052 7.923C8.46895 7.6395 8.78443 7.5 9.11793 7.5C9.24412 7.5 9.37031 7.527 9.48297 7.581C9.60015 7.635 9.70381 7.716 9.78493 7.833L10.8305 9.3045C10.9116 9.417 10.9702 9.5205 11.0108 9.6195C11.0513 9.714 11.0739 9.8085 11.0739 9.894C11.0739 10.002 11.0423 10.11 10.9792 10.2135C10.9206 10.317 10.835 10.425 10.7268 10.533L10.3843 10.8885C10.3348 10.938 10.3122 10.9965 10.3122 11.0685C10.3122 11.1045 10.3167 11.136 10.3257 11.172C10.3393 11.208 10.3528 11.235 10.3618 11.262C10.4429 11.4105 10.5826 11.604 10.7809 11.838C10.9837 12.072 11.2 12.3105 11.4344 12.549C11.6778 12.7875 11.9121 13.008 12.151 13.2105C12.3853 13.4085 12.5791 13.5435 12.7323 13.6245C12.7549 13.6335 12.7819 13.647 12.8135 13.6605C12.8495 13.674 12.8856 13.6785 12.9261 13.6785C13.0028 13.6785 13.0613 13.6515 13.1109 13.602L13.4534 13.2645C13.5661 13.152 13.6743 13.0665 13.7779 13.0125C13.8816 12.9495 13.9852 12.918 14.0979 12.918C14.1835 12.918 14.2737 12.936 14.3728 12.9765C14.472 13.017 14.5756 13.0755 14.6883 13.152L16.18 14.2095C16.2972 14.2905 16.3783 14.385 16.4279 14.4975C16.473 14.61 16.5 14.7225 16.5 14.8485Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeMiterlimit="10"
                      />
                    </svg>
                  </a>
                  <a className="h-4 w-4 text-placeholder ml-1.25" href={`tel:${resolvePhone(affiliate.phone)}`}>
                    <svg width="100%" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M21.97 18.33C21.97 18.69 21.89 19.06 21.72 19.42C21.55 19.78 21.33 20.12 21.04 20.44C20.55 20.98 20.01 21.37 19.4 21.62C18.8 21.87 18.15 22 17.45 22C16.43 22 15.34 21.76 14.19 21.27C13.04 20.78 11.89 20.12 10.75 19.29C9.6 18.45 8.51 17.52 7.47 16.49C6.44 15.45 5.51 14.36 4.68 13.22C3.86 12.08 3.2 10.94 2.72 9.81C2.24 8.67 2 7.58 2 6.54C2 5.86 2.12 5.21 2.36 4.61C2.6 4 2.98 3.44 3.51 2.94C4.15 2.31 4.85 2 5.59 2C5.87 2 6.15 2.06 6.4 2.18C6.66 2.3 6.89 2.48 7.07 2.74L9.39 6.01C9.57 6.26 9.7 6.49 9.79 6.71C9.88 6.92 9.93 7.13 9.93 7.32C9.93 7.56 9.86 7.8 9.72 8.03C9.59 8.26 9.4 8.5 9.16 8.74L8.4 9.53C8.29 9.64 8.24 9.77 8.24 9.93C8.24 10.01 8.25 10.08 8.27 10.16C8.3 10.24 8.33 10.3 8.35 10.36C8.53 10.69 8.84 11.12 9.28 11.64C9.73 12.16 10.21 12.69 10.73 13.22C11.27 13.75 11.79 14.24 12.32 14.69C12.84 15.13 13.27 15.43 13.61 15.61C13.66 15.63 13.72 15.66 13.79 15.69C13.87 15.72 13.95 15.73 14.04 15.73C14.21 15.73 14.34 15.67 14.45 15.56L15.21 14.81C15.46 14.56 15.7 14.37 15.93 14.25C16.16 14.11 16.39 14.04 16.64 14.04C16.83 14.04 17.03 14.08 17.25 14.17C17.47 14.26 17.7 14.39 17.95 14.56L21.26 16.91C21.52 17.09 21.7 17.3 21.81 17.55C21.91 17.8 21.97 18.05 21.97 18.33Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeMiterlimit="10"
                      />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <div className="flex justify-between mb-4">
              <div className="flex items-center">
                <figure className="h-7.5 w-7.5 rounded-full bg-grey-fields-100 flex items-center justify-center text-dark">
                  <svg width="50%" viewBox="0 0 15 15" fill="none">
                    <path
                      d="M10.625 12.8125H4.375C2.5 12.8125 1.25 11.875 1.25 9.6875V5.3125C1.25 3.125 2.5 2.1875 4.375 2.1875H10.625C12.5 2.1875 13.75 3.125 13.75 5.3125V9.6875C13.75 11.875 12.5 12.8125 10.625 12.8125Z"
                      stroke="currentColor"
                      strokeMiterlimit="10"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M10.625 5.625L8.66875 7.1875C8.025 7.7 6.96875 7.7 6.325 7.1875L4.375 5.625"
                      stroke="currentColor"
                      strokeMiterlimit="10"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </figure>
                <span className="ml-2">Email Address</span>
              </div>
              <span className="text-black mt-2 font-medium">{affiliate.email}</span>
            </div>

            <div className="flex justify-between mb-4">
              <div className="flex items-center">
                <figure className="h-7.5 w-7.5 rounded-full bg-grey-fields-100 flex items-center justify-center text-dark">
                  <svg width="50%" viewBox="0 0 15 15" fill="none">
                    <path
                      d="M7.5 13.75C10.8848 13.75 13.625 11.0098 13.625 7.625C13.625 4.24023 10.8848 1.5 7.5 1.5C4.11523 1.5 1.375 4.24023 1.375 7.625C1.375 11.0098 4.11523 13.75 7.5 13.75Z"
                      stroke="currentColor"
                      strokeMiterlimit="10"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M7.5 4.125V7.625L10 9.125"
                      stroke="currentColor"
                      strokeMiterlimit="10"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </figure>
                <span className="ml-2">Type</span>
              </div>
              <span className="text-black mt-2 font-medium">
                <Badge text={enumToHumanFriendly(affiliate.type)} />
              </span>
            </div>

            {affiliate.total_orders > 0 && (
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <figure className="h-7.5 w-7.5 rounded-full bg-grey-fields-100 flex items-center justify-center text-dark">
                    {discountDetailsIcons.products}
                  </figure>

                  <span className="ml-2 text-gray-600 text-1xs sm:text-sm">Orders</span>
                </div>
                <button className="flex items-center cursor-pointer select-none" onClick={() => takeAction("orders")}>
                  <span className={`mr-2 font-semibold text-primary-500 text-1xs sm:text-sm`}>
                    {affiliate.total_orders} Order{affiliate.total_orders > 1 ? "s" : ""}
                  </span>
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 14 15" fill="none" className="w-3">
                    <path d="M1 7.5L13 7.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M7 13.5L13 7.5L7 1.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            )}

            {affiliate.total_customers > 0 && (
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <figure className="h-7.5 w-7.5 rounded-full bg-grey-fields-100 flex items-center justify-center text-dark">
                    {deliveryPageIcons.contact}
                  </figure>

                  <span className="ml-2 text-gray-600 text-1xs sm:text-sm">Customers</span>
                </div>
                <button
                  className="flex items-center cursor-pointer select-none"
                  onClick={() => takeAction("customers")}
                >
                  <span className={`mr-2 font-semibold text-primary-500 text-1xs sm:text-sm`}>
                    {affiliate.total_customers} Customer{affiliate.total_customers > 1 ? "s" : ""}
                  </span>
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 14 15" fill="none" className="w-3">
                    <path d="M1 7.5L13 7.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M7 13.5L13 7.5L7 1.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            )}
          </div>
        </ModalBody>

        <ModalFooter>
          <div className="flex w-full space-x-3.75">
            <AppBtn
              color="neutral"
              onClick={() => takeAction("delete")}
              size="lg"
              className="flex-1 text-accent-red-500"
              isBlock
            >
              Delete
            </AppBtn>
            <AppBtn color="primary" onClick={() => takeAction("edit")} size="lg" className="flex-1" isBlock>
              Edit Affiliate
            </AppBtn>
          </div>
        </ModalFooter>
      </Modal>
    </>
  );
};

export default AffiliateDetailsModal;
