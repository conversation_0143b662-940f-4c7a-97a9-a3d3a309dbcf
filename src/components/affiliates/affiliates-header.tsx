import React, { useRef, useState } from "react";
import { SCOPES } from "../../assets/js/utils/permissions";
import useClickOutside from "../hooks/useClickOutside";
import useFluxState from "../hooks/useFluxState";
import useScreenSize from "../hooks/useScreenSize";
import useSearchParams from "../hooks/useSearchParams";
import AppSearchBar from "../ui/app-search-bar";
import { AppBtn } from "../ui/buttons";
import Can from "../ui/can";

interface Props {
  toggleModal: (key: string) => void;
}

const AffiliatesHeader: React.FC<Props> = ({ toggleModal }) => {
  const [fullViewOnMobile, setFullViewOnMobile] = useState(false);
  const { search = null } = useSearchParams(["search"]);
  const { width } = useScreenSize();
  const [searchQuery, setSearchQuery] = useFluxState(search ?? "", [search]);
  const searchBar = useRef(null);

  useClickOutside(searchBar, () => {
    setFullViewOnMobile(false);
  });

  return (
    <div className="flex items-center justify-between mb-3.75">
      <div className="flex items-center justify-between mb-3.75 w-full">
        <h4
          className={`text-base sm:text-lg text-black font-semibold ${fullViewOnMobile ? "hidden md:block" : "block"}`}
        >
          All Affiliates
        </h4>
        <div
          ref={searchBar}
          className={`flex items-stretch space-x-2 ${fullViewOnMobile ? " flex-1 md:flex-none" : ""}`}
        >
          <AppSearchBar
            {...{
              placeholder: `Search Affiliates`,
              searchQuery,
              setSearchQuery,
              fullViewOnMobile,
              setFullViewOnMobile,
            }}
          />
          <Can data={{ permission: SCOPES.CUSTOMERS.UPDATE_CUSTOMERS }}>
            <div className={`pl-2 ${fullViewOnMobile ? "hidden md:block" : "block"}`}>
              {width > 800 && (
                <AppBtn size="md" onClick={() => toggleModal("add_affiliate")}>
                  Add Affiliate
                </AppBtn>
              )}
              {width <= 800 && (
                <AppBtn size="md" onClick={() => toggleModal("add_affiliate")} className="!rounded-full !p-0 !h-9 !w-9">
                  {/* prettier-ignore */}
                  <svg width={17} height={17} viewBox="0 0 17 17" fill="none" >
                    <path d="M4.25 8.5h8.5M8.5 12.75v-8.5" stroke="#fff" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </AppBtn>
              )}
            </div>
          </Can>
        </div>
      </div>
    </div>
  );
};

export default AffiliatesHeader;
