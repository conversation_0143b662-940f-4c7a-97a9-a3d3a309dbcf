import { GetLatestWalletRequest } from "@/api";
import { useFetcher } from "@/api/utils";
import { CURRENCIES } from "@/assets/interfaces";
import { paymentsEnabledCurrencies, WHATSAPP_LINK } from "@/assets/js/utils/constants";
import InternationalPaymentRequestModal from "@/components/dashboard/wallets/modals/international-payments-modal";
import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import Badge from "@/components/ui/badge";
import { AppBtn } from "@/components/ui/buttons";
import dayjs from "dayjs";
import React from "react";

interface Props {
  storeEnabledCurrencies: CURRENCIES[];
}

const ManageStoreCurrencies: React.FC<Props> = ({ storeEnabledCurrencies }) => {
  const { modals, toggleModal } = useModals(["request_currencies"]);

  const getLatestWalletRequest = useFetcher(GetLatestWalletRequest);
  const hasPendingRequest = getLatestWalletRequest?.response?.data?.status === "PENDING";
  const hasRejectedRequest = getLatestWalletRequest?.response?.data?.status === "REJECTED";
  const daysFromLastRequest = getLatestWalletRequest?.response
    ? dayjs().diff(dayjs(getLatestWalletRequest?.response?.data?.created_at), "day")
    : 1;

  const disabledCurrencies = paymentsEnabledCurrencies.filter((c) => !storeEnabledCurrencies.includes(c));

  return (
    <div className="bg-grey-fields-100 rounded-15 bg-opacity-70 divide-y divide-grey-border divide-opacity-50 mb-7.5">
      <div className="flex p-3.75 flex-col">
        <h4 className="font-display text-black-secondary font-bold text-1sm sm:text-base">Enabled Currencies</h4>
        <div className="mt-2.5 flex items-center space-x-2.5">
          {storeEnabledCurrencies.map((currency) => (
            <Badge key={currency} greyBg={false} color="green" size="md" text={currency} />
          ))}
        </div>
      </div>
      {disabledCurrencies.length > 0 && (
        <div className="p-3.75">
          <div className="flex flex-col">
            <span className="text-dark font-semibold text-1xs sm:text-sm block">Other Available Currencies</span>
            <div className="mt-2.5 flex items-center space-x-2.5">
              {disabledCurrencies.map((currency) => (
                <Badge key={currency} greyBg={false} color="dark" size="sm" text={currency} />
              ))}
            </div>
          </div>

          <div className="flex flex-col items-center mt-3.5">
            {getLatestWalletRequest?.response && !hasRejectedRequest && (
              <AppBtn
                color="white"
                size="sm"
                className="!text-1xs font-semibold border border-grey-divider ml-auto"
                isBlock
                onClick={() => toggleModal("request_currencies")}
                disabled={hasPendingRequest}
              >
                {hasPendingRequest ? "Request Pending" : "Enable Currencies"}
                {hasPendingRequest ? (
                  //prettier-ignore
                  <svg className="ml-1 w-4" viewBox="0 0 24 24" fill="none">
            <path d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M15.71 15.18L12.61 13.33C12.07 13.01 11.63 12.24 11.63 11.61V7.51001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
                ) : (
                  //prettier-ignore
                  <svg className="ml-1 w-4" viewBox="0 0 16 17" fill="none">
            <path d="M3.33301 8.5H12.6663" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 3.83301L12.6667 8.49967L8 13.1663" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
                )}
              </AppBtn>
            )}

            {hasRejectedRequest && daysFromLastRequest < 30 && (
              <div className="flex-col flex">
                <div className="text-xs font-medium text-accent-red-500 flex items-start">
                  {/* prettier-ignore */}
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="mr-1.5">
                <path d="M12 9V14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12.0001 21.41H5.94005C2.47005 21.41 1.02005 18.93 2.70005 15.9L5.82006 10.28L8.76006 5.00003C10.5401 1.79003 13.4601 1.79003 15.2401 5.00003L18.1801 10.29L21.3001 15.91C22.9801 18.94 21.5201 21.42 18.0601 21.42H12.0001V21.41Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M11.9945 17H12.0035" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
                  Your last request wallet request was rejected, please wait {30 - daysFromLastRequest} more days to
                  make a new request
                </div>
                <div className="flex flex-col bg-white w-full py-2 px-2.5 rounded-8 mt-2.5">
                  <h6 className="font-bold text-black-secondary text-sm font-display">Reason:</h6>
                  <span className="text-sm text-dark">{getLatestWalletRequest?.response?.data?.admin_notes}</span>
                </div>
                <AppBtn
                  color="white"
                  size="sm"
                  className="!text-1xs font-semibold border border-grey-divider mt-1.5"
                  isBlock
                  href={WHATSAPP_LINK}
                >
                  Contact Support
                </AppBtn>
              </div>
            )}

            <Portal>
              <InternationalPaymentRequestModal
                show={modals.request_currencies.show}
                toggle={() => toggleModal("request_currencies")}
              />
            </Portal>
          </div>
        </div>
      )}
    </div>
  );
};

export default ManageStoreCurrencies;
