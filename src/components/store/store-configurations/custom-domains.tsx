import { useState, useEffect, useRef } from "react";
import { AppBtn } from "../../ui/buttons";
import { useRequest } from "../../../api/utils";
import { FormikProps } from "formik";
import { ConfigurationForm } from "./index";
import { GetDomains, AddDomain, RemoveDomain, GenerateSslCertificate, VerifyDomain } from "../../../api/stores";
import Portal from "@/components/portal";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { useModals } from "@/components/hooks/useModals";
import ErrorLabel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";
import ContentCopy from "../custom-domains/content-copy";
import Table, { TableBody, TableHead, TableHeadItem } from "@/components/ui/table";
import authContext from "../../../contexts/auth-context";
import { actionIsAllowed, SCOPES } from "../../../assets/js/utils/permissions";
import ErrorBox from "../../ui/error";

interface DomainData {
  domain: string;
  verified: boolean;
  verification_code: string;
  store_id: string;
  created_at: string;
  updated_at: string;
  id: string;
  certificate_issued: boolean;
  verified_at?: string;
  certificate_issued_at?: string;
}

interface Props {
  error?: any;
  response?: any;
  form: FormikProps<ConfigurationForm>;
}

// SVG Icons
const InfoIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="12" y1="16" x2="12" y2="12"></line>
    <line x1="12" y1="8" x2="12.01" y2="8"></line>
  </svg>
);

const CheckIcon = ({ size = 14 }) => (
  <svg width={size} height={size} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.5 11.8828C9.25 11.8828 11.5 9.63281 11.5 6.88281C11.5 4.13281 9.25 1.88281 6.5 1.88281C3.75 1.88281 1.5 4.13281 1.5 6.88281C1.5 9.63281 3.75 11.8828 6.5 11.8828Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.375 6.88277L5.79 8.29777L8.625 5.46777"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const WarningIcon = ({ size = 14 }) => (
  <svg width={size} height={size} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.5 5.38281V7.88281" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M6.50021 11.5875H3.47021C1.73521 11.5875 1.01021 10.3475 1.85021 8.83246L3.41021 6.02246L4.88021 3.38246C5.77021 1.77746 7.23021 1.77746 8.12021 3.38246L9.59021 6.02746L11.1502 8.83746C11.9902 10.3525 11.2602 11.5925 9.53021 11.5925H6.50021V11.5875Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path d="M6.49707 9.38281H6.50156" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

const DeleteIcon = ({ size = 20 }) => (
  <svg width={size} height={size} viewBox="0 0 15 15" fill="none">
    <path
      d="M12.75 3.87077C10.8075 3.67827 8.85333 3.5791 6.905 3.5791C5.75 3.5791 4.595 3.63743 3.44 3.7541L2.25 3.87077"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.45825 3.2823L5.58659 2.51814C5.67992 1.96397 5.74992 1.5498 6.73575 1.5498H8.26409C9.24992 1.5498 9.32575 1.9873 9.41325 2.52397L9.54159 3.2823"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.4958 5.71484L11.1167 11.589C11.0525 12.5048 11 13.2165 9.37248 13.2165H5.62748C3.99998 13.2165 3.94748 12.5048 3.88332 11.589L3.50415 5.71484"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.52588 10.0078H8.46838"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.04175 7.6748H8.95841"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CustomDomains: React.FC<Props> = ({ error, response }) => {
  const [domains, setDomains] = useState<DomainData[]>([]);
  const [loading, setLoading] = useState(false);
  const [newDomain, setNewDomain] = useState("");
  const [message, setMessage] = useState({ type: "", text: "" });
  const [domainToDelete, setDomainToDelete] = useState<{ id: string; name: string } | null>(null);
  const [processingDomains, setProcessingDomains] = useState<{
    verifying: string[];
    generatingSSL: string[];
    removing: string[];
  }>({
    verifying: [],
    generatingSSL: [],
    removing: [],
  });
  const messageTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { subscription } = authContext.useContainer();
  const { modals, toggleModal } = useModals(["delete_domain"]);

  const serverIp = process.env.NEXT_PUBLIC_SERVER_IP || "**************";

  const addDomainReq = useRequest(AddDomain);
  const removeDomainReq = useRequest(RemoveDomain);
  const generateSslReq = useRequest(GenerateSslCertificate);
  const verifyDomainReq = useRequest(VerifyDomain);

  let canManageCustomDomains = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_CUSTOM_DOMAINS,
  });

  const fetchDomains = async () => {
    setLoading(true);
    try {
      const [response, error] = await GetDomains();
      if (error) {
        setMessage({
          type: "error",
          text: typeof error.message === "string" ? error.message : "Failed to fetch domains",
        });
      } else {
        setDomains(response.data || []);
      }
    } catch (err) {
      setMessage({ type: "error", text: "An unexpected error occurred" });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDomains();
  }, []);

  const scrollToTop = () => {
    const element = document.querySelector(".store-config");
    if (element) {
      element.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  // Clear messages after a delay
  useEffect(() => {
    if (message.text) {
      // Clear any existing timeout
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }

      // Set new timeout to clear the message after 5 seconds
      messageTimeoutRef.current = setTimeout(() => {
        setMessage({ type: "", text: "" });
      }, 5000);
    }

    // Cleanup on unmount
    return () => {
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
    };
  }, [message]);

  const handleAddDomain = async (e) => {
    e.preventDefault();
    const trimmedDomain = newDomain.trim();
    if (!trimmedDomain) return;

    const domainRegex = /^(?!:\/\/)(?=.{1,255}$)((.{1,63}\.){1,127}(?![0-9]*$)[a-z0-9-]+\.?)$/i;
    if (!domainRegex.test(trimmedDomain)) {
      setMessage({ type: "error", text: "Please enter a valid domain name" });
      return;
    }

    setMessage({ type: "", text: "" });

    try {
      const [response, error] = await addDomainReq.makeRequest({ domain: trimmedDomain });
      if (error) {
        setMessage({
          type: "error",
          text: typeof error.message === "string" ? error.message : "Failed to add domain",
        });
      } else {
        setMessage({ type: "success", text: "Domain added successfully" });
        setNewDomain("");
        await fetchDomains();
      }
    } catch (err) {
      setMessage({ type: "error", text: "An unexpected error occurred" });
    }
  };

  const openDeleteModal = (domainId: string, domainName: string) => {
    setDomainToDelete({ id: domainId, name: domainName });
    toggleModal("delete_domain");
  };

  const handleRemoveDomain = async () => {
    if (!domainToDelete) return;

    try {
      setProcessingDomains((prev) => ({
        ...prev,
        removing: [...prev.removing, domainToDelete.id],
      }));

      const [response, error] = await removeDomainReq.makeRequest({ id: domainToDelete.id });
      if (error) {
        setMessage({
          type: "error",
          text: typeof error.message === "string" ? error.message : "Failed to remove domain",
        });
      } else {
        setMessage({ type: "success", text: "Domain removed successfully" });
        await fetchDomains();
      }
    } catch (err) {
      setMessage({ type: "error", text: "An unexpected error occurred" });
    } finally {
      setProcessingDomains((prev) => ({
        ...prev,
        removing: prev.removing.filter((id) => id !== domainToDelete.id),
      }));
      toggleModal("delete_domain");
      setDomainToDelete(null);
    }

    scrollToTop();
  };

  const handleGenerateSSL = async (domainId: string) => {
    try {
      setProcessingDomains((prev) => ({
        ...prev,
        generatingSSL: [...prev.generatingSSL, domainId],
      }));

      const [response, error] = await generateSslReq.makeRequest({ id: domainId });
      if (error) {
        setMessage({
          type: "error",
          text: typeof error.message === "string" ? error.message : "Failed to generate SSL certificate",
        });
      } else {
        setMessage({ type: "success", text: "SSL certificate generation initiated" });
        await fetchDomains();
      }
    } catch (err) {
      setMessage({ type: "error", text: "An unexpected error occurred" });
    } finally {
      setProcessingDomains((prev) => ({
        ...prev,
        generatingSSL: prev.generatingSSL.filter((id) => id !== domainId),
      }));
    }
    scrollToTop();
  };

  const handleVerifyDomain = async (domainId: string) => {
    try {
      setProcessingDomains((prev) => ({
        ...prev,
        verifying: [...prev.verifying, domainId],
      }));

      const [response, error] = await verifyDomainReq.makeRequest({ id: domainId });
      if (error) {
        setMessage({
          type: "error",
          text: typeof error.message === "string" ? error.message : "Failed to verify domain",
        });
      } else {
        if (response.data?.verified) {
          setMessage({
            type: "success",
            text: typeof response.message === "string" ? response.message : "Domain verified successfully",
          });
        } else {
          setMessage({
            type: "error",
            text: typeof response.message === "string" ? response.message : "Domain verification failed",
          });
        }
        await fetchDomains();
      }
    } catch (err) {
      setMessage({ type: "error", text: "An unexpected error occurred" });
    } finally {
      setProcessingDomains((prev) => ({
        ...prev,
        verifying: prev.verifying.filter((id) => id !== domainId),
      }));
    }
    scrollToTop();
  };

  if (!canManageCustomDomains) {
    return (
      <ErrorBox
        title="Upgrade required"
        message="Please upgrade to business plus plan to use custom domains on your store"
      >
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  return (
    <>
      <div className="mb-8 md:mb-10">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-yellow-500 text-white m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg width="50%" viewBox="0 0 31 30" fill="none">
          <path d="M27.7375 25.95L26.8375 25.05C27.3 24.35 27.575 23.5 27.575 22.6C27.575 20.125 25.575 18.125 23.1 18.125C20.625 18.125 18.625 20.125 18.625 22.6C18.625 25.075 20.625 27.075 23.1 27.075C24.0125 27.075 24.85 26.8 25.55 26.3375L26.45 27.2375C26.625 27.4125 26.8625 27.5 27.0875 27.5C27.325 27.5 27.55 27.4125 27.725 27.2375C28.0875 26.875 28.0875 26.3 27.7375 25.95Z" fill="currentColor" />
          <path d="M3.0377 18.2871C3.0377 18.3246 3.0127 18.3746 3.0127 18.4121C4.1627 20.7121 6.0377 22.5996 8.3377 23.7371C8.3752 23.7371 8.42519 23.7121 8.4627 23.7121C8.03769 22.2621 7.7127 20.7746 7.4752 19.2871C5.9752 19.0371 4.4877 18.7121 3.0377 18.2871Z" fill="currentColor" />
          <path d="M24.3376 8.0373C23.1626 5.5748 21.1751 3.5873 18.7251 2.4248C19.1751 3.9123 19.5501 5.4373 19.8001 6.96231C21.3251 7.21231 22.8501 7.5748 24.3376 8.0373Z" fill="currentColor" />
          <path d="M2.9126 8.03789C4.4126 7.58789 5.9376 7.21289 7.4626 6.96289C7.7126 5.47539 8.0251 4.00039 8.4501 2.55039C8.4126 2.55039 8.3626 2.52539 8.3251 2.52539C5.9751 3.68789 4.0626 5.65039 2.9126 8.03789Z" fill="currentColor" />
          <path d="M17.7751 6.7C17.4751 5.075 17.1001 3.45 16.5626 1.875C16.5376 1.7875 16.5376 1.7125 16.5251 1.6125C15.6001 1.3875 14.6251 1.25 13.6251 1.25C12.6126 1.25 11.6501 1.3875 10.7126 1.625C10.7001 1.7125 10.7126 1.7875 10.6876 1.8875C10.1626 3.4625 9.7751 5.075 9.4751 6.7C12.2376 6.4 15.0126 6.4 17.7751 6.7Z" fill="currentColor" />
          <path d="M7.2 8.97461C5.5625 9.27461 3.9625 9.66211 2.375 10.1871C2.2875 10.2121 2.2125 10.2121 2.125 10.2246C1.8875 11.1496 1.75 12.1246 1.75 13.1246C1.75 14.1371 1.8875 15.0996 2.125 16.0371C2.2125 16.0496 2.2875 16.0371 2.3875 16.0621C3.9625 16.5871 5.575 16.9746 7.2125 17.2746C6.9 14.5121 6.9 11.7371 7.2 8.97461Z" fill="currentColor" />
          <path d="M25.1251 10.2246C25.0376 10.2246 24.9626 10.2121 24.8626 10.1871C23.2876 9.66211 21.6626 9.27461 20.0376 8.97461C20.3501 11.7371 20.3501 14.5121 20.0376 17.2621C21.6626 16.9621 23.2876 16.5871 24.8626 16.0496C24.9501 16.0246 25.0251 16.0371 25.1251 16.0246C25.3501 15.0871 25.5001 14.1246 25.5001 13.1121C25.5001 12.1246 25.3626 11.1621 25.1251 10.2246Z" fill="currentColor" />
          <path d="M9.4751 19.5496C9.7751 21.1871 10.1501 22.7996 10.6876 24.3746C10.7126 24.4621 10.7001 24.5371 10.7126 24.6371C11.6501 24.8621 12.6126 24.9996 13.6251 24.9996C14.6251 24.9996 15.6001 24.8621 16.5251 24.6246C16.5376 24.5371 16.5376 24.4621 16.5626 24.3621C17.0876 22.7871 17.4751 21.1746 17.7751 19.5371C16.4001 19.6871 15.0126 19.7996 13.6251 19.7996C12.2376 19.7996 10.8501 19.6996 9.4751 19.5496Z" fill="currentColor" />
          <path d="M9.1875 8.6875C8.8125 11.6375 8.8125 14.6125 9.1875 17.575C12.1375 17.95 15.1125 17.95 18.075 17.575C18.45 14.625 18.45 11.65 18.075 8.6875C15.1125 8.3125 12.1375 8.3125 9.1875 8.6875Z" fill="currentColor" />
        </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Custom Domains
        </h2>
        <span className="block text-center text-1xs text-grey-subtext mt-2">
          Change your website name or replace the default <br /> domain with any other custom domain.
        </span>
      </div>

      <div className="space-y-6">
        <ErrorLabel error={message?.text && message?.type == "error" ? message?.text : ""} />
        <SuccessLabel message={message?.text && message?.type !== "error" ? message?.text : ""} />

        <div className="bg-grey-fields-200 rounded-10 border border-grey-border border-opacity-50">
          <div className="p-4 border-b border-grey-border border-opacity-50">
            <div className="flex items-center">
              <h2 className="text-lg font-bold text-black">How to set up a custom domain</h2>
            </div>
          </div>

          <div className="p-4">
            <div className="text-black-muted">
              <ol className="text-sm list-decimal pl-4 space-y-2">
                <li>Add your domain here</li>
                <li>
                  Add an <strong>A record</strong> in your domain provider's DNS settings that points to:
                  <ContentCopy text={serverIp} />
                </li>
                <li>
                  After adding the A record, click the <strong>Verify Domain</strong> button
                </li>
              </ol>
              <div className="w-full flex items-start mt-3.75 bg-accent-yellow-pastel text-black-muted text-1xs font-normal p-2 rounded-10">
                <svg width="15%" height={18} viewBox="0 0 19 18" fill="none">
                  <path
                    d="M9.5 17.0625C5.0525 17.0625 1.4375 13.4475 1.4375 9C1.4375 4.5525 5.0525 0.9375 9.5 0.9375C13.9475 0.9375 17.5625 4.5525 17.5625 9C17.5625 13.4475 13.9475 17.0625 9.5 17.0625ZM9.5 2.0625C5.675 2.0625 2.5625 5.175 2.5625 9C2.5625 12.825 5.675 15.9375 9.5 15.9375C13.325 15.9375 16.4375 12.825 16.4375 9C16.4375 5.175 13.325 2.0625 9.5 2.0625Z"
                    fill="currentColor"
                  />
                  <path
                    d="M9.5 10.3125C9.1925 10.3125 8.9375 10.0575 8.9375 9.75V6C8.9375 5.6925 9.1925 5.4375 9.5 5.4375C9.8075 5.4375 10.0625 5.6925 10.0625 6V9.75C10.0625 10.0575 9.8075 10.3125 9.5 10.3125Z"
                    fill="currentColor"
                  />
                  <path
                    d="M9.5 12.7511C9.4025 12.7511 9.305 12.7286 9.215 12.6911C9.125 12.6536 9.0425 12.6011 8.9675 12.5336C8.9 12.4586 8.8475 12.3836 8.81 12.2861C8.7725 12.1961 8.75 12.0986 8.75 12.0011C8.75 11.9036 8.7725 11.8061 8.81 11.7161C8.8475 11.6261 8.9 11.5436 8.9675 11.4686C9.0425 11.4011 9.125 11.3486 9.215 11.3111C9.395 11.2361 9.605 11.2361 9.785 11.3111C9.875 11.3486 9.9575 11.4011 10.0325 11.4686C10.1 11.5436 10.1525 11.6261 10.19 11.7161C10.2275 11.8061 10.25 11.9036 10.25 12.0011C10.25 12.0986 10.2275 12.1961 10.19 12.2861C10.1525 12.3836 10.1 12.4586 10.0325 12.5336C9.9575 12.6011 9.875 12.6536 9.785 12.6911C9.695 12.7286 9.5975 12.7511 9.5 12.7511Z"
                    fill="currentColor"
                  />
                </svg>
                <span>
                  DNS changes can take up to 24-48 hours to propagate. If verification fails, please wait and try again
                  later.
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-grey-fields-200 rounded-10 border border-grey-border border-opacity-50">
          <div className="p-4 border-b border-grey-border border-opacity-50">
            <h3 className="text-lg font-bold">Domains</h3>
          </div>

          <div className="p-4">
            <h2 className="text-sm font-bold text-black">New Custom Domain</h2>
            <p className="text-1xs mt-1 mb-3 text-black-muted">
              Enter an existing domain name to connect your custom domain
            </p>

            <form
              onSubmit={handleAddDomain}
              className="flex gap-2"
              onKeyDown={(e) => e.key === "Enter" && e.preventDefault()}
            >
              <div className="flex-1">
                <input
                  type="text"
                  value={newDomain}
                  onChange={(e) => setNewDomain(e.target.value)}
                  placeholder="yourdomain.com"
                  className="w-full px-4 py-3.5 border border-grey-border border-opacity-50 rounded-10 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                />
              </div>
              <AppBtn type="submit" size="md" disabled={addDomainReq.isLoading} className="px-4 text-sm">
                {addDomainReq.isLoading ? "Adding..." : "Add Domain"}
              </AppBtn>
            </form>

            {loading ? (
              <div className="flex flex-col items-center py-5">
                <div className="spinner spinner--md text-primary-500"></div>
                <span className="text-sm text-black-placeholder mt-2 inline-block font-medium">Loading Domains</span>
              </div>
            ) : domains.length === 0 ? (
              <div className="text-center py-4 text-gray-500">No custom domains added yet.</div>
            ) : (
              <div className="flex flex-col gap-y-3 mt-3.75">
                {domains.map((domainData) => (
                  <div
                    key={domainData.id}
                    className="bg-white border border-grey-border border-opacity-50 py-3 px-3 rounded-10 flex items-center justify-between"
                  >
                    <div className="flex flex-col gap-2">
                      <div className="text-xs text-black-muted">
                        <span className="font-bold inline-block whitespace-nowrap overflow-ellipsis overflow-hidden w-full">
                          {domainData.domain}
                        </span>
                        <br />
                        <span className="flex">
                          {domainData.verified ? (
                            <>
                              <span className="text-green-600 mr-1">
                                <CheckIcon />
                              </span>
                              <span className="text-green-600">Verified</span>
                            </>
                          ) : (
                            <>
                              <span className="text-yellow-600 mr-1">
                                <WarningIcon />
                              </span>
                              <span className="text-yellow-600">Pending verification</span>
                            </>
                          )}
                        </span>
                      </div>
                      <div className="text-xs text-black-muted flex items-center gap-1">
                        {domainData.verified && (
                          <span>
                            {" "}
                            {domainData.verified && domainData.certificate_issued ? "SSL Active" : "SSL Pending"}
                          </span>
                        )}
                        {domainData.verified && <span>•</span>}
                        <span>{new Date(domainData.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex gap-1 flex-end h-full items-center">
                      {!domainData.verified && (
                        <AppBtn
                          type="button"
                          size="sm"
                          color="neutral"
                          onClick={() => handleVerifyDomain(domainData.id)}
                          disabled={processingDomains.verifying.includes(domainData.id)}
                          className="px-4 text-sm ml-auto"
                        >
                          <span className="text-xs">
                            {processingDomains.verifying.includes(domainData.id) ? "Verifying..." : "Verify Domain"}
                          </span>
                        </AppBtn>
                      )}
                      {domainData.verified && !domainData.certificate_issued && (
                        <button
                          onClick={() => handleGenerateSSL(domainData.id)}
                          disabled={processingDomains.generatingSSL.includes(domainData.id)}
                          className="mr-2 px-2 py-1 text-xs bg-white border border-green-600 text-green-600 rounded hover:bg-green-50 min-w-[90px]"
                        >
                          {processingDomains.generatingSSL.includes(domainData.id) ? "Generating..." : "Generate SSL"}
                        </button>
                      )}
                      <button
                        onClick={() => openDeleteModal(domainData.id, domainData.domain)}
                        disabled={processingDomains.removing.includes(domainData.id)}
                        className="text-red-500 p-2 ml-[6px] bg-grey-fields-200 rounded-full hover:text-red-600"
                        title="Remove domain"
                      >
                        <DeleteIcon />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <Portal>
          <Modal
            show={modals.delete_domain.show}
            toggle={() => toggleModal("delete_domain")}
            title="Remove Domain"
            size="sm"
            className="z-[1000]"
          >
            <form className="flex flex-col flex-auto overflow-hidden">
              <ModalBody>
                <div className="text-center">
                  <p className="text-sm text-grey-subtext mt-1 max-w-[360px] mx-auto">
                    Are you sure you want to remove <strong>{domainToDelete?.name}</strong>? This action cannot be
                    undone. Any SSL certificates associated with this domain will be revoked.
                  </p>
                </div>
              </ModalBody>
              <ModalFooter>
                <div className="grid grid-cols-2 gap-3 w-full">
                  <AppBtn size="lg" onClick={() => toggleModal("delete_domain")} color="neutral">
                    Cancel
                  </AppBtn>
                  <AppBtn
                    size="lg"
                    onClick={handleRemoveDomain}
                    disabled={
                      removeDomainReq?.isLoading || processingDomains.removing.includes(domainToDelete?.id || "")
                    }
                    color="danger"
                  >
                    {removeDomainReq?.isLoading || processingDomains.removing.includes(domainToDelete?.id || "")
                      ? "Removing..."
                      : "Yes, Remove"}
                  </AppBtn>
                </div>
              </ModalFooter>
            </form>
          </Modal>
        </Portal>
      </div>
    </>
  );
};

export default CustomDomains;
