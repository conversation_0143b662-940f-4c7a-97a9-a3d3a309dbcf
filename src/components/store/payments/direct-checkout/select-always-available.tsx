import { useEffect, useState } from "react";
import { EditItemQuantityParams, ItemQuantityParams } from "../../../../api/interfaces/items.interface";
import { RequestInterface } from "../../../../api/utils";
import { Category, ProductItemInterface } from "../../../../assets/interfaces";
import { getItemThumbnail } from "../../../../assets/js/utils/utils";
import authContext from "../../../../contexts/auth-context";
import useFluxState from "../../../hooks/useFluxState";
import LazyImage from "../../../lazy-image";
import { AppBtn } from "../../../ui/buttons";
import ErrorLabel from "../../../ui/error-label";
import Checkbox from "../../../ui/form-elements/checkbox";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "../../../ui/modal";
import MultiSelectDropdown from "../../../ui/multi-select-dropdown";
import SearchBar from "../../../ui/search-bar";

type HideableProduct = ProductItemInterface & { isVisible?: boolean };
interface SelectAvailableItemProps {
  items: HideableProduct[];
  nextStep: (itemsToUpdate?: ItemQuantityParams[]) => void;
  updateItems: (items: ProductItemInterface[]) => void;
  updateRequest: RequestInterface<EditItemQuantityParams>;
}
const SelectAvailableItems: React.FC<SelectAvailableItemProps> = ({ items, nextStep, updateItems, updateRequest }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const { categories } = authContext.useContainer();
  const selectedAll = items.every((i) => i.is_always_available);
  const { makeRequest, isLoading, error, response } = updateRequest;

  useEffect(() => {
    if (items.length === 0) {
      nextStep();
    }
  }, []);

  useEffect(() => {
    if (searchQuery === "") {
      updateItems(items.map((i) => ({ ...i, isVisible: true })));
      return;
    }

    searchQuery.trim();
    const splitQueries = searchQuery.split(" ");

    const filteredItems = items.map((item) => {
      let match = false;
      splitQueries.map((query) => {
        if (item.name.toLocaleLowerCase().includes(query)) match = true;
      });
      if (match) return { ...item, isVisible: true };
      return { ...item, isVisible: false };
    });
    updateItems(filteredItems);
  }, [searchQuery]);

  const selectAll = () => {
    const itemsCopy = [...items];

    if (!selectedAll) {
      itemsCopy.forEach((item, index) => {
        itemsCopy[index] = { ...item, is_always_available: true };
      });
    } else {
      itemsCopy.forEach((item, index) => {
        itemsCopy[index] = { ...item, is_always_available: false };
      });
    }
    updateItems(itemsCopy);
  };

  const categoryDropdownItems = getCategoryDropdownItems(categories);

  const selectCategories = (categories: string[]) => {
    const itemsCopy = [...items];
    itemsCopy.forEach((item, index) => {
      itemsCopy[index] = { ...item, is_always_available: item?.category && categories.includes(String(item.category)) };
    });
    updateItems(itemsCopy);
  };

  const toggleItem = (index: number) => {
    const itemsCopy = [...items];
    const item = itemsCopy[index];
    itemsCopy[index] = { ...item, is_always_available: !Boolean(item.is_always_available) };
    updateItems(itemsCopy);
  };

  const goNext = async () => {
    const itemsToUpdate = items
      .filter((i) => i.is_always_available)
      .map((i) => ({
        id: i.id,
        is_always_available: true,
      }));

    nextStep(itemsToUpdate);
  };

  return (
    <>
      <ModalBody className="!pt-0">
        <div className="sticky top-0 z-50 bg-white pt-4 pb-1">
          <ErrorLabel error={error && error.message} />
          <h2 className="font-bold text-black text-base sm:text-lg mb-3.75">
            Which of these items are always available?
          </h2>
          <SearchBar {...{ searchQuery, setSearchQuery }} />

          <div className="flex items-center justify-between mt-2.5">
            <div className="flex items-center w-[fit-content]">
              <Checkbox
                onChange={selectAll}
                checked={selectedAll}
                name="select_all"
                className="mr-1"
                id="select_all"
                small
                round
              ></Checkbox>
              <span className="text-sm text-dark font-medium inline-block ml-1">Select All</span>
            </div>
            {categoryDropdownItems.length > 0 && (
              <MultiSelectDropdown label="Select by Category" items={categoryDropdownItems} onSave={selectCategories} />
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-5 mt-2">
          {items.map(
            (item, index) =>
              item.isVisible && (
                <div
                  key={index}
                  className="w-fit flex items-center py-4 pr-4 border-b-[1px] border-gray-100 last:border-0 cursor-pointer"
                  onClick={() => toggleItem(index)}
                >
                  <figure className=" flex-shrink-0 h-[34px] w-[34px] rounded-md overflow-hidden mr-3 relative">
                    <LazyImage
                      src={getItemThumbnail(item)}
                      className="h-full w-full object-cover rounded-md relative z-10"
                      alt={item.name}
                    />
                  </figure>
                  <span className="w-[60%] text-sm sm:text-1sm mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis text-black-secondary">
                    {item.name}
                  </span>
                  <Checkbox
                    onChange={() => toggleItem(index)}
                    checked={item.is_always_available}
                    id={item.id}
                    name={item.name}
                    className="ml-auto"
                    round
                    small
                  />
                </div>
              )
          )}
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock onClick={goNext} size="lg">
          {isLoading ? "Updating..." : "Next"}
        </AppBtn>
      </ModalFooter>
    </>
  );
};

const getCategoryDropdownItems = (categories: Category[]) => {
  return categories.map((category) => {
    return {
      label: `${category.emoji}  ${category.name}`,
      value: category.id,
    };
  });
};

export default SelectAvailableItems;
