import { INPUT_TYPE } from "@/assets/interfaces/stores";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalProps } from "@/components/ui/modal";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import * as yup from "yup";
import CustomFormFieldDetails from "./custom-form-field-details";
import SelectComponent from "./select-component";
import { ComponentFormValues, FormComponent, formComponents, getValidationSchema } from "./add-form-component";

interface Props extends ModalProps {
  value?: ComponentFormValues;
  onSubmit: (values: ComponentFormValues) => void;
}
const EditFormComponentModal: React.FC<Props> = ({ show, toggle, value, onSubmit }) => {
  const [selectedComponent] = useState<FormComponent>(
    formComponents.find((c) => c.type?.toString() === value?.type?.toString())
  );

  const form = useFormik<ComponentFormValues>({
    initialValues: {
      type: value?.type ?? null,
      name: value?.name ?? "",
      is_required: value?.is_required ?? true,
      label: value?.label ?? "",
      options: value?.options ?? [{ value: "", price: 0 }],
    },
    onSubmit,
    validationSchema: getValidationSchema(selectedComponent?.hasOptions),
  });

  const hasPricedOptions =
    selectedComponent?.hasOptions && form.values.options?.some((o) => o.price.toString() !== "0");

  return (
    <form onSubmit={form.handleSubmit} className="">
      <Modal title={"Edit form component"} show={show} toggle={toggle} size="md">
        <ModalBody noPadding>
          {selectedComponent && (
            <CustomFormFieldDetails {...{ selectedComponent, form, defaultShowPrice: hasPricedOptions }} />
          )}
        </ModalBody>
        <ModalFooter>
          <AppBtn isBlock disabled={form.isValid === false} type="submit" size="lg">
            Continue
          </AppBtn>
        </ModalFooter>
      </Modal>
    </form>
  );
};
export default EditFormComponentModal;
