import { getFieldvalues, getProductsCurrency, toCurrency } from "@/assets/js/utils/functions";
import { InputField } from "@/components/ui/form-elements";
import Toggle from "@/components/ui/toggle";
import { FormikProps } from "formik";
import { ComponentFormValues, FormComponent } from "./add-form-component";
import ErrorLabel from "@/components/ui/error-label";
import { useState } from "react";
import { RoundActionBtn } from "@/components/ui/buttons";
import LazyImage from "@/components/lazy-image";

interface ComponentDetailsProps {
  selectedComponent?: FormComponent;
  form: FormikProps<ComponentFormValues>;
  defaultShowPrice?: boolean;
}
const CustomFormFieldDetails: React.FC<ComponentDetailsProps> = ({
  selectedComponent,
  form,
  defaultShowPrice = false,
}) => {
  const [showPrice, setShowPrice] = useState(defaultShowPrice);
  const addOption = () => {
    form.setFieldValue(
      "options",
      form.values.options?.length > 0
        ? [...form.values.options, { value: "", price: 0 }]
        : [
            { value: "", price: 0 },
            { value: "", price: 0 },
          ]
    );
  };

  const deleteOption = (index: number) => {
    form.setFieldValue(
      "options",
      form.values.options.filter((_, i) => i !== index)
    );
  };

  const handleFormLabelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    form.setFieldValue("label", e.target.value);
    form.setFieldValue("name", e.target.value.toLowerCase().replace(/\s/g, "_"));
  };

  const handleShowPricesChange = (state: boolean) => {
    if (!state) {
      form.setFieldValue(
        "options",
        form.values.options.map((o) => ({ ...o, price: 0 }))
      );
    }

    setShowPrice(state);
  };

  return (
    <div className="px-5 pt-5 pb-10">
      <div className="">
        <LazyImage src={selectedComponent.img} className="w-full rounded-md h-[225px] object-cover" />
      </div>
      <h3 className="mt-5 text-black-secondary font-medium ">Add {selectedComponent.name} details</h3>

      <div className="mb-5">
        <InputField
          label={`${selectedComponent.name} label`}
          {...getFieldvalues("label", form)}
          onChange={handleFormLabelChange}
        />
        <div className="flex items-center justify-between mt-2.5">
          <p className="text-sm text-black-muted">Option has prices</p>
          <Toggle intialState={showPrice} onChange={(state) => handleShowPricesChange(state)} />
        </div>
      </div>
      {selectedComponent.hasOptions && (
        <div className="">
          <h3 className="text-black-secondary text-lg font-bold my-3.75">Options</h3>
          {form?.errors?.options && typeof form.errors.options === "string" && (
            <ErrorLabel error={form?.errors?.options} perm={true} />
          )}
          {form.values.options.map((option, index) => (
            <ComponentOption
              {...{
                form,
                selectedComponent,
                index,
                showPrice,
                handleDelete: () => deleteOption(index),
                canDelete: form.values.options?.length > 2,
              }}
              key={index}
            />
          ))}
          <button
            className="mt-5 flex items-center text-primary-500 text-sm font-semibold"
            onClick={addOption}
            type="button"
          >
            {/* prettier-ignore */}
            <svg viewBox="0 0 14 14" fill="none" className="w-4 mr-1">
              <path d="M7 2.91675V11.0834" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2.91675 7H11.0834" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Add Option
          </button>
        </div>
      )}
      <div className="flex items-center justify-between mt-5 pt-1.25">
        <p className="text-sm text-black-muted">Require customers to fill this field</p>
        <Toggle intialState={form.values.is_required} onChange={(state) => form.setFieldValue("is_required", state)} />
      </div>
    </div>
  );
};

interface ComponentOptionProps {
  selectedComponent?: FormComponent;
  form: FormikProps<ComponentFormValues>;
  canDelete?: boolean;
  handleDelete: () => void;
  // update: (data: { value?: string; price?: string }) => void;
  showPrice?: boolean;
  index?: number;
}

const ComponentOption = (props: ComponentOptionProps) => {
  const { selectedComponent, form, index, showPrice, handleDelete, canDelete } = props;
  const [showSummary, setShowSummary] = useState(false);

  const option = form.values.options[index];

  const handleInputBlur = () => {
    if (option.value && (showPrice ? option.price.toString() : true)) {
      setShowSummary(true);
    }
  };

  const handlePriceChange = (text: string) => {
    // Remove any non-numeric characters and ensure it's not negative
    const numericValue = text.replace(/[^0-9]/g, "");
    form.setFieldValue(`options.${index}.price`, numericValue);
  };

  if (showSummary) {
    return (
      <div className="bg-grey-fields-100 rounded-10 border border-grey-divider px-3.5 py-2.5 flex items-center justify-between mt-5">
        <div className="flex items-center flex-1 mr-5">
          <span className="flex-1 inline-flex text-sm text-dark">{form.values.options[index].value}</span>
          {showPrice && (
            <span className="flex-1 inline-flex text-sm text-black-secondary font-medium">
              {toCurrency(Number(form.values.options[index]?.price ?? 0))}
            </span>
          )}
        </div>
        <div className="flex space-x-2 items-center">
          <RoundActionBtn icon="edit" size="sm" onClick={() => setShowSummary(false)} white grey={false} />
          {canDelete && <RoundActionBtn icon="delete" size="sm" onClick={handleDelete} white grey={false} />}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col sm:flex-row sm:items-start space-y-3 sm:space-y-0 sm:space-x-3 mt-5">
      <InputField
        name="label"
        label={`Option ${index + 1}`}
        {...getFieldvalues(`options.${index}.value`, form)}
        className="flex-1"
        onBlur={handleInputBlur}
      />
      {showPrice && (
        <div className="flex-1">
          <InputField
            {...getFieldvalues(`options.${index}.price`, form)}
            onChange={(e) => handlePriceChange(e.target.value)}
            inputMode="numeric"
            type="number"
            min={0}
            label={`Option price (${getProductsCurrency()})`}
            onBlur={handleInputBlur}
          />
        </div>
      )}
    </div>
  );
};

export default CustomFormFieldDetails;
