import LazyImage from "@/components/lazy-image";
import classNames from "classnames";
import { FormComponent, formComponents } from "./add-form-component";
interface SelectComponentProps {
  selectedComponent?: FormComponent;
  setSelectedComponent: (type: FormComponent) => void;
}
const SelectComponent: React.FC<SelectComponentProps> = ({ setSelectedComponent, selectedComponent }) => {
  return (
    <div className="grid grid-cols-2 gap-2.5 p-2.5 pt-5">
      {formComponents.map((component, idx) => (
        <div
          key={idx}
          className={classNames(
            "cursor-pointer rounded-xl border border-grey-divider p-2.5 duration-200 hover:bg-grey-fields-100 hover:bg-opacity-50",
            {
              "border-primary-500": selectedComponent?.type === component.type,
              "hover:border-grey-outline": selectedComponent?.type !== component.type,
            }
          )}
          onClick={() => setSelectedComponent(component)}
        >
          <LazyImage src={component.img} className="w-full rounded-md" />
          <h3 className="mt-1.25 text-black-secondary text-sm">{component.name}</h3>
        </div>
      ))}
    </div>
  );
};

export default SelectComponent;
