import { INPUT_TYPE } from "@/assets/interfaces/stores";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalProps } from "@/components/ui/modal";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import * as yup from "yup";
import CustomFormFieldDetails from "./custom-form-field-details";
import SelectComponent from "./select-component";

interface Props extends ModalProps {
  value?: ComponentFormValues;
  onSubmit: (values: ComponentFormValues) => void;
}
const AddFormComponentModal: React.FC<Props> = ({ show, toggle, value, onSubmit }) => {
  const [step, setStep] = useState<"select" | "details" | "success">("select");
  const [selectedComponent, setSelectedComponent] = useState<FormComponent>();

  const form = useFormik<ComponentFormValues>({
    initialValues: {
      type: value?.type ?? null,
      name: value?.name ?? "",
      is_required: value?.is_required ?? true,
      label: value?.label ?? "",
      options: value?.options ?? [{ value: "", price: 0 }],
    },
    onSubmit: (values) => {
      if (step === "select") {
        setStep("details");
        return;
      }
      toggle(false);
      onSubmit(values);
      setStep("select");
    },
    validationSchema: step === "details" ? getValidationSchema(selectedComponent?.hasOptions) : undefined,
  });

  useEffect(() => {
    if (!show) {
      form.resetForm();
    }
  }, [show]);

  useEffect(() => {
    if (selectedComponent) {
      form.setFieldValue("type", selectedComponent.type);
    }
  }, [selectedComponent]);

  return (
    <form onSubmit={form.handleSubmit} className="">
      <Modal title={"Add form component"} show={show} toggle={toggle} size="md">
        <ModalBody noPadding>
          {step === "select" && <SelectComponent {...{ setSelectedComponent, selectedComponent }} />}
          {step === "details" && <CustomFormFieldDetails {...{ selectedComponent, form }} />}
        </ModalBody>
        <ModalFooter>
          <AppBtn isBlock disabled={step === "select" ? !selectedComponent : !form.isValid} type="submit" size="lg">
            Continue
          </AppBtn>
        </ModalFooter>
      </Modal>
    </form>
  );
};
export default AddFormComponentModal;

export const getValidationSchema = (hasOptions: boolean) => {
  return yup.object().shape({
    name: yup.string().required(),
    label: yup.string().required(),
    options: hasOptions
      ? yup
          .array()
          .of(
            yup.object().shape({
              value: yup.string().required("Option value is required"),
              price: yup.number().nullable(),
            })
          )
          .min(2, "At least two options are required")
      : null,
  });
};

export type ComponentFormValues = {
  type: INPUT_TYPE;
  name: string;
  is_required: boolean;
  label?: string;
  options?: { value: string; price?: number }[];
};

export type FormComponent = {
  name: string;
  type: INPUT_TYPE;
  img: string;
  hasOptions: boolean;
};

export const formComponents: FormComponent[] = [
  {
    name: "Text input",
    type: INPUT_TYPE.TEXT,
    img: "/images/form-components/text-input.png",
    hasOptions: false,
  },
  {
    name: "Number input",
    type: INPUT_TYPE.NUMBER,
    img: "/images/form-components/number-input.png",
    hasOptions: false,
  },
  {
    name: "Dropdown",
    type: INPUT_TYPE.DROPDOWN,
    img: "/images/form-components/dropdown.png",
    hasOptions: true,
  },
  {
    name: "Radio input",
    type: INPUT_TYPE.RADIO,
    img: "/images/form-components/radio-group.png",
    hasOptions: true,
  },
  {
    name: "Text area",
    type: INPUT_TYPE.TEXTAREA,
    img: "/images/form-components/text-area.png",
    hasOptions: false,
  },
];
