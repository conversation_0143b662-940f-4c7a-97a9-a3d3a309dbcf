import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import { AppBtn } from "@/components/ui/buttons";
import { ChowbotSetupPageProps } from "@/pages/get-started/chowbot";
import classNames from "classnames";
import { useFormik } from "formik";
import React, { useState } from "react";
import * as Yup from "yup";
import { UpdateStoreParams } from "../../../api/interfaces/stores.interface";
import { UpdateStoreDetails } from "../../../api/stores";
import { useRequest } from "../../../api/utils";
import { StoreInterface } from "../../../assets/interfaces";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import AddFormComponentModal, { ComponentFormValues } from "./add-form-component";
import FormPreview from "./custom-form-preview";
import EditFormComponentModal from "./edit-form-component";

interface Props extends ChowbotSetupPageProps {
  store: StoreInterface;
  updateStore: (stores: Partial<StoreInterface>) => void;
}

export type CustomCheckoutFormItem = StoreInterface["configuration"]["custom_checkout_form"][0];

const CustomCheckoutForm: React.FC<Props> = ({ store, updateStore }) => {
  const updateStoreReq = useRequest<UpdateStoreParams>(UpdateStoreDetails);
  const { modals, toggleModal } = useModals(["add", "edit"]);
  const [selectedComponentIndex, setSelectedComponentIndex] = useState<number | null>(null);

  const form = useFormik<{ components: CustomCheckoutFormItem[] }>({
    initialValues: {
      components: store.configuration?.custom_checkout_form ?? [],
    },
    onSubmit: async (values) => {
      const [res] = await updateStoreReq.makeRequest({
        id: store.id,
        configuration: { ...store.configuration, custom_checkout_form: values.components },
      });

      if (res) {
        updateStore({ configuration: { ...store.configuration, custom_checkout_form: values.components } });
      }
    },
    validationSchema: Yup.object().shape({}),
  });

  const hasFormItems = form.values.components.length > 0;

  const addNewFormField = (values: ComponentFormValues) => {
    form.setFieldValue("components", [...form.values.components, { ...values, options: values.options }]);
  };

  const handleEdit = (index: number) => {
    setSelectedComponentIndex(index);
    toggleModal("edit");
  };

  const handleRemove = (index: number) => {
    form.setFieldValue(
      "components",
      form.values.components.filter((_, i) => i !== index)
    );
    form.submitForm();
  };

  const saveEdit = (values: ComponentFormValues) => {
    form.setFieldValue(
      "components",
      form.values.components.map((c, i) => (i === selectedComponentIndex ? { ...values, options: values.options } : c))
    );
    toggleModal("edit");
  };
  const selectedFormField = form.values.components[selectedComponentIndex];

  return (
    <div className="">
      <div className="mb-3.75">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 text-white m-auto flex items-center justify-center">
          {
            // prettier-ignore
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" >
              <path d="M22 15V9C22 7.34 20.66 6 19 6H5C3.34 6 2 7.34 2 9V15C2 16.66 3.34 18 5 18H19C20.66 18 22 16.66 22 15Z" fill="currentColor"/>
              <path d="M5.92923 19.5H18.0692C18.3892 19.5 18.6192 19.79 18.5592 20.1C18.2892 21.58 17.4192 22 15.3292 22H8.66923C6.56923 22 5.70923 21.58 5.43923 20.1C5.37923 19.79 5.60923 19.5 5.92923 19.5Z" fill="currentColor"/>
              <path d="M8.66923 2H15.3292C17.4292 2 18.2892 2.42 18.5592 3.9C18.6192 4.21 18.3792 4.5 18.0692 4.5H5.92923C5.60923 4.5 5.37923 4.21 5.43923 3.9C5.70923 2.42 6.56923 2 8.66923 2Z" fill="currentColor"/>
            </svg>
          }
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Checkout Form
        </h2>
        <span className="block m-auto text-center text-sm text-grey-subtext max-w-[290px]">
          Create a custom form for customers at checkout
        </span>
      </div>

      <div className="w-full mx-auto max-w-[450px]">
        <form onSubmit={form.handleSubmit} className="w-full">
          <div className="mt-2.5">
            <ErrorLabel error={updateStoreReq?.error?.message ?? null} />
            <SuccessLabel message={updateStoreReq.response ? "Checkout options updated successfully!" : null} />

            <div
              className={classNames(
                "bg-grey-divider bg-opacity-50 rounded-3xl pt-10 pb-15 flex flex-col items-center",
                { hidden: hasFormItems }
              )}
            >
              {/* prettier-ignore */}
              <svg className="mb-2.5 text-grey-subtext" width="24" height="24" viewBox="0 0 24 24" fill="none" >
                <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 8V13" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M11.9945 16H12.0035" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>

              <span className="text-1xs text-grey-subtext text-center w-full block">
                No form items found <br /> Click here to add form items
              </span>
              <AppBtn onClick={() => toggleModal("add")} size="sm" className="mt-3.75">
                + Add a form item
              </AppBtn>
            </div>

            <div className={classNames({ hidden: !hasFormItems })}>
              <FormPreview editField={handleEdit} removeField={handleRemove} formValues={form?.values?.components} />
              <button
                className="mt-3.75 flex items-center text-primary-500 text-sm font-semibold ml-auto"
                onClick={() => toggleModal("add")}
                type="button"
              >
                {/* prettier-ignore */}
                <svg viewBox="0 0 14 14" fill="none" className="w-4 mr-1">
                  <path d="M7 2.91675V11.0834" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M2.91675 7H11.0834" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Add New Field
              </button>
              <div className="flex flex-col gap-3.75 mt-7.5 justify-between">
                <AppBtn type="submit" className="" size="lg">
                  Save changes
                </AppBtn>
              </div>
            </div>
          </div>
        </form>
      </div>
      <Portal>
        <AddFormComponentModal onSubmit={addNewFormField} show={modals.add.show} toggle={() => toggleModal("add")} />
        {selectedFormField && (
          <EditFormComponentModal
            value={{ ...selectedFormField, options: selectedFormField?.options }}
            onSubmit={saveEdit}
            show={modals.edit.show}
            toggle={() => toggleModal("edit")}
          />
        )}
      </Portal>
    </div>
  );
};

export default CustomCheckoutForm;
