import { InputField, SelectDropdown, TextArea } from "@/components/ui/form-elements";
import Checkbox from "@/components/ui/form-elements/checkbox";
import React, { useState } from "react";
import { INPUT_TYPE } from "../../../assets/interfaces";
import { CustomCheckoutFormItem } from "./custom-checkout-form";
import Badge from "@/components/ui/badge";
import classNames from "classnames";
import { toCurrency } from "@/assets/js/utils/functions";

interface FormPreviewProps {
  formValues: CustomCheckoutFormItem[];
  removeField: (idx: number) => void;
  editField: (idx: number) => void;
}
const FormPreview: React.FC<FormPreviewProps> = ({ formValues, removeField, editField }) => {
  const [currentPreviewComponent, setCurrentPreviewComponent] = useState<number | null>(null);
  const getPreviewComponent = (component: CustomCheckoutFormItem) => {
    switch (component.type) {
      case INPUT_TYPE.TEXT:
        return <InputField label={component.label} name={component.name} />;
      case INPUT_TYPE.NUMBER:
        return <InputField type="number" label={component.label} name={component.name} />;
      case INPUT_TYPE.TEXTAREA:
        return <TextArea label={component.label} name={component.name} onBlur={() => null} />;
      case INPUT_TYPE.DROPDOWN:
        return (
          <SelectDropdown
            label={component.label}
            name={component.name}
            options={component.options.map((o) => ({
              text: o.value + (Number(o.price) > 0 ? ` (${toCurrency(Number(o.price))})` : ""),
              value: o.value,
            }))}
          />
        );
      case INPUT_TYPE.RADIO:
        return (
          <div>
            <h3 className="font-semibold text-sm text-black-muted capitalize"> {component.label} </h3>
            <div className="flex items-center gap-2.5 mt-2.5 flex-wrap">
              {component.options.map((o, i) => (
                <div key={i} className="flex items-center gap-2.5">
                  <Checkbox round name={component.name} checked={i === 0} />
                  <label className="text-sm text-black-muted" htmlFor={o.value}>
                    {o.value + (Number(o.price) > 0 ? ` (${toCurrency(Number(o.price))})` : o.value)}
                  </label>
                </div>
              ))}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="flex flex-col items-center w-full ">
      <div className="flex items-center justify-between w-full">
        <h2 className="text-black-muted font-semibold"></h2>
      </div>
      <div className="w-full mt-3.75 rounded-2xl space-y-2.5">
        {formValues.map((c, i) => (
          <div className="border-grey-divider border rounded-xl p-3.5 w-full" key={i}>
            <div key={i} className=" flex items-center justify-between ">
              {/* {getPreviewComponent(c)} */}
              <div className="flex-1 flex gap-2.5 items-center">
                <span className="text-sm capitalize text-black">{c.label}</span>
                <Badge greyBg text={c.type} />
              </div>
              <div className="pl-5 flex gap-2.5 items-center">
                <button onClick={() => editField(i)} type="button" className="p-2.5 rounded-full bg-grey-fields-100">
                  {/* prettier-ignore */}
                  <svg className="w-3.75 text-black-muted"  viewBox="0 0 24 24" fill="none" >
                  <path d="M13.2655 5.13928L5.05547 13.8293C4.74547 14.1593 4.44547 14.8093 4.38547 15.2593L4.01547 18.4993C3.88547 19.6693 4.72547 20.4693 5.88547 20.2693L9.10547 19.7193C9.55547 19.6393 10.1855 19.3093 10.4955 18.9693L18.7055 10.2793C20.1255 8.77928 20.7655 7.06928 18.5555 4.97928C16.3555 2.90928 14.6855 3.63928 13.2655 5.13928Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 6.48169C12.43 9.24169 14.67 11.3517 17.45 11.6317" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                <button onClick={() => removeField(i)} type="button" className="p-2.5 rounded-full bg-grey-fields-100">
                  {/* prettier-ignore */}
                  <svg className="w-3.75 text-black-muted" viewBox="0 0 24 24" fill="none">
                    <path d="M21 5.97998C17.67 5.64998 14.32 5.47998 10.98 5.47998C9 5.47998 7.02 5.57998 5.04 5.77998L3 5.97998" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8.5 4.97L8.72 3.66C8.88 2.71 9 2 10.69 2H13.31C15 2 15.13 2.75 15.28 3.67L15.5 4.97" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M18.85 9.14001L18.2 19.21C18.09 20.78 18 22 15.21 22H8.79002C6.00002 22 5.91002 20.78 5.80002 19.21L5.15002 9.14001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M10.33 16.5H13.66" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M9.5 12.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
            <div className="w-full mt-1.25">
              <button
                type="button"
                onClick={() => setCurrentPreviewComponent(i === currentPreviewComponent ? -1 : i)}
                className="text-1xs text-primary-500 flex gap-1.25 items-center "
              >
                Show Preview
                {/* prettier-ignore */}
                <svg className={classNames("text-primary-500 w-3.75 duration-200",{"rotate-180": currentPreviewComponent === i})} viewBox="0 0 24 24" fill="none"><path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path></svg>
              </button>
              <div
                className={classNames("mt-2.5 duration-200", {
                  "max-h-0  overflow-hidden": currentPreviewComponent !== i,
                  "max-h-[500px] ": currentPreviewComponent === i,
                })}
              >
                {getPreviewComponent(c)}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FormPreview;
