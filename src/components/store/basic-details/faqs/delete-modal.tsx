import { AppBtn } from "@/components/ui/buttons";
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON>ooter } from "@/components/ui/modal";
import { ModalBody } from "@/components/ui/modal";
import { UpdateStoreFaqParams } from "@/api/interfaces";
import { UpdateStoreFaqs } from "@/api/stores";
import { FormikProps } from "formik";
import { UpdateStoreFaqDetails } from "@/assets/interfaces/stores";
import { useRequest } from "@/api/utils";
import ErrorLabel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";

interface DelProps {
  show: boolean;
  toggle: (status: boolean) => void;
  selected: number;
  form: FormikProps<UpdateStoreFaqDetails>;
}

const DeleteFaqModal: React.FC<DelProps> = ({ show, toggle, selected, form }) => {
  const { makeRequest, isLoading, error, response } = useRequest<UpdateStoreFaqParams>(UpdateStoreFaqs);

  const handleItemDelete = async () => {
    const updatedFaqs = [...form.values.faqs.slice(0, selected), ...form.values.faqs.slice(selected + 1)];

    const [res, err] = await makeRequest({
      faqs: updatedFaqs,
    });
    if (!err && res?.data) {
      toggle(false);
      form.setValues(res.data); // Update the form values
      form.setFormikState((prevState) => ({
        ...prevState,
        initialValues: res.data, // Sync initialValues with the updated values
      }));
    }
  };

  return (
    <Modal {...{ show, toggle }} title="Delete FAQ" size="sm">
      <ModalBody>
        <ErrorLabel error={error?.message} />
        <SuccessLabel message={response?.message ? "Testimonial deleted successfully!" : ""} />
        <div className="text-center py-3.5">
          <h4 className="text-black text-base font-semibold">Do you want to delete this FAQ?</h4>
          <p className="text-sm text-grey-subtext mt-1 max-w-xs mx-auto">
            This FAQ would be completly removed from the list.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="danger" onClick={handleItemDelete} disabled={isLoading} size="lg">
          Delete Faq
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default DeleteFaqModal;
