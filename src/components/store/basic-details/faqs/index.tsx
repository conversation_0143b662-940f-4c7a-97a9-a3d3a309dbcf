import React, { useEffect, useRef, useState } from "react";
import * as Yup from "yup";
import { arrayToInputValues, getFieldvalues } from "../../../../assets/js/utils/functions";
import ErrorLabel from "../../../ui/error-label";
import { InputField, SelectDropdown, TextArea } from "../../../ui/form-elements";
import SuccessLabel from "../../../ui/success-label";
import { FormikProps, useFormik } from "formik";
import { AppBtn, RoundActionBtn } from "@/components/ui/buttons";
import { useFetcher, useRequest } from "@/api/utils";
import { GetStoreFaqs, UpdateStoreFaqs } from "@/api";
import authContext from "@/contexts/auth-context";
import { StoreFaq, UpdateStoreFaqDetails } from "@/assets/interfaces";
import ContentState from "@/components/ui/content-state";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "@/components/ui/modal";
import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import { UpdateStoreFaqParams } from "@/api/interfaces";
import useClickOutside from "@/components/hooks/useClickOutside";
import { FAQForm, FAQSummary } from "./faq-item";
import DeleteFaqModal from "./delete-modal";

interface Props {}
export const Faqs: React.FC<Props> = ({}) => {
  const { store, updateStore } = authContext.useContainer();
  const [accordion, setAccordion] = useState([]);
  const [editing, setEditing] = useState([]);
  const { modals, toggleModal } = useModals(["delete"]);
  const [selected, setSelected] = useState<{
    delete: number;
  }>({
    delete: null,
  });

  const updateFaq = useRequest(UpdateStoreFaqs);

  const form = useFormik<UpdateStoreFaqDetails>({
    initialValues: {
      faqs: store?.faqs ?? [],
    },
    validationSchema,
    onSubmit: async (values) => {
      const { makeRequest } = updateFaq;
      const [response, error] = await makeRequest({
        faqs: values.faqs,
      });

      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors({ ...error.fields });
        }
      } else {
        form.setValues(response.data); // Update the form values
        form.setFormikState((prevState) => ({
          ...prevState,
          initialValues: response.data, // Sync initialValues with the updated values
        }));
      }
      //scroll to top so response message shows
      const formEl = document.querySelector("#basic-details-form");
      formEl.scrollTo(0, 0);
    },
  });

  const creatEmptyFaq = () => {
    const faqs = [...form.values.faqs];
    faqs.push({
      id: "",
      answer: "",
      question: "",
      created_at: "",
      is_visible: true,
    });
    form.setFieldValue("faqs", faqs);
  };

  const removeFaq = (e: React.MouseEvent<HTMLButtonElement>, index: number) => {
    e.preventDefault();
    const faqs = [...form.values.faqs];
    faqs.splice(index, 1);
    form.setFieldValue("faqs", faqs);
  };

  const toggleAccordion = (index: number) => {
    const accordionCopy = [...accordion];

    accordionCopy[index] = !accordion[index];
    setAccordion(accordionCopy);
  };

  const toggleEditing = (index: number, toggle: boolean = false) => {
    if (toggle) {
      toggleAccordion(index);
    }
    const editingCopy = [...editing];

    editingCopy[index] = !editing[index];
    setEditing(editingCopy);
  };
  const deleteFaq = (id: number) => {
    setSelected((prev) => ({
      ...prev,
      delete: id,
    }));
    toggleModal("delete");
  };

  useEffect(() => {
    if (updateFaq.response?.data) {
      setEditing([]);
      setAccordion([]);
    }
  }, [updateFaq?.response]);

  return (
    <>
      <div className="mb-7.5">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg width={31} height={30} viewBox="0 0 31 30" fill="none">
            <path d="M21.75 3.03711H9.25C5.5 3.03711 3 5.53711 3 9.28711V16.7871C3 20.5371 5.5 23.0371 9.25 23.0371V25.6996C9.25 26.6996 10.3625 27.2996 11.1875 26.7371L16.75 23.0371H21.75C25.5 23.0371 28 20.5371 28 16.7871V9.28711C28 5.53711 25.5 3.03711 21.75 3.03711ZM15.5 18.2496C14.975 18.2496 14.5625 17.8246 14.5625 17.3121C14.5625 16.7996 14.975 16.3746 15.5 16.3746C16.025 16.3746 16.4375 16.7996 16.4375 17.3121C16.4375 17.8246 16.025 18.2496 15.5 18.2496ZM17.075 13.0621C16.5875 13.3871 16.4375 13.5996 16.4375 13.9496V14.2121C16.4375 14.7246 16.0125 15.1496 15.5 15.1496C14.9875 15.1496 14.5625 14.7246 14.5625 14.2121V13.9496C14.5625 12.4996 15.625 11.7871 16.025 11.5121C16.4875 11.1996 16.6375 10.9871 16.6375 10.6621C16.6375 10.0371 16.125 9.52461 15.5 9.52461C14.875 9.52461 14.3625 10.0371 14.3625 10.6621C14.3625 11.1746 13.9375 11.5996 13.425 11.5996C12.9125 11.5996 12.4875 11.1746 12.4875 10.6621C12.4875 8.99961 13.8375 7.64961 15.5 7.64961C17.1625 7.64961 18.5125 8.99961 18.5125 10.6621C18.5125 12.0871 17.4625 12.7996 17.075 13.0621Z" fill="white" />
          </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Frequently Asked Questions
        </h2>
      </div>
      <ErrorLabel error={updateFaq?.error?.message} />
      <SuccessLabel
        message={updateFaq?.response?.data?.message ? "Frequently asked questions updated successfully!" : null}
      />
      <div className="mb-5">
        <>
          <div className="py-3.75">
            {form?.values.faqs.map((item, index) => (
              <div key={index} className="mb-3.75 flex flex-col">
                {item.id === "" ? (
                  <FAQForm
                    index={index}
                    form={form}
                    removeFaq={removeFaq}
                    toggleAccordion={toggleAccordion}
                    toggleEditing={toggleEditing}
                  />
                ) : (
                  <FAQSummary
                    faq={item}
                    index={index}
                    {...{ toggleEditing, accordion, editing, toggleAccordion, form, deleteAction: deleteFaq }}
                  />
                )}
              </div>
            ))}

            <div className="flex flex-col items-center gap-y-3 mb-3">
              <AppBtn color="neutral" type="button" size="md" className="w-full" onClick={() => creatEmptyFaq()}>
                + Add a new Question
              </AppBtn>

              <AppBtn
                color="primary"
                size="lg"
                className="w-full mt-3.75"
                disabled={!form.dirty || updateFaq.isLoading}
                onClick={() => form.submitForm()}
              >
                Save Updates
              </AppBtn>
            </div>
          </div>
        </>
      </div>

      <Portal>
        {selected.delete !== null && (
          <DeleteFaqModal
            show={modals?.delete.show}
            toggle={() => toggleModal("delete")}
            selected={selected.delete}
            form={form}
          />
        )}
      </Portal>
    </>
  );
};

const validationSchema = Yup.object().shape({
  faqs: Yup.array().of(
    Yup.object().shape({
      question: Yup.string().required("Question is required"),
      answer: Yup.string().required("Answer is required"),
    })
  ),
});
