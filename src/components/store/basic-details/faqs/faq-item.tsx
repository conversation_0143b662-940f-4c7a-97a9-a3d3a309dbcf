import { Text<PERSON>rea } from "@/components/ui/form-elements";
import { UpdateStoreFaqDetails } from "@/assets/interfaces/stores";
import { StoreFaq } from "@/assets/interfaces/stores";
import { getFieldvalues, getRandString } from "@/assets/js/utils/functions";
import useClickOutside from "@/components/hooks/useClickOutside";
import { RoundActionBtn } from "@/components/ui/buttons";
import { InputField } from "@/components/ui/form-elements";
import { FormikProps } from "formik";
import React, { useRef } from "react";

interface FAQFormProps {
  index: number;
  form: FormikProps<UpdateStoreFaqDetails>;
  removeFaq: (e: React.MouseEvent<HTMLButtonElement>, index: number) => void;
  toggleAccordion: (index: number) => void;
  toggleEditing: (index: number, toggle?: boolean) => void;
}

export const FAQForm: React.FC<FAQFormProps> = ({ index, form, removeFaq, toggleAccordion, toggleEditing }) => {
  const faqForm = useRef(null);
  useClickOutside(faqForm, () => {
    if (form.values.faqs[index].question && form.values.faqs[index].answer) {
      toggleAccordion(index);
      toggleEditing(index, false);
      form.setFieldValue(`faqs.${index}.id`, getRandString(10));
    }
  });

  return (
    <div className="relative" ref={faqForm}>
      <div className="flex gap bg-grey-fields-100 items-center rounded-15 relative">
        <div className="flex items-center gap-2 justify-center h-full w-2" />
        <div className="border rounded-15 border-gray-border border-opacity-50 bg-white px-3 py-3 sm:py-5 sm:px-4 flex-1 my-0">
          <div className="flex flex-col gap-y-1">
            <div className="flex gap-2 items-center">
              <InputField
                className="flex-1"
                label="Question: eg. What is your return policy?"
                {...getFieldvalues(`faqs.${index}.question`, form)}
              />
              <RoundActionBtn
                icon="delete"
                color="danger"
                className="text-accent-red-500"
                onClick={(e) => removeFaq(e, index)}
                size="md"
              />
            </div>
            <TextArea label="Your Answer" {...getFieldvalues(`faqs.${index}.answer`, form)} />
          </div>
        </div>
      </div>
    </div>
  );
};

interface FAQSummaryProps {
  faq: StoreFaq;
  index: number;
  accordion: boolean[];
  editing: boolean[];
  toggleAccordion: (index: number) => void;
  toggleEditing: (index: number, toggle?: boolean) => void;
  form: FormikProps<UpdateStoreFaqDetails>;
  deleteAction: (id: number) => void;
}

export const FAQSummary: React.FC<FAQSummaryProps> = ({
  faq,
  index,
  toggleEditing,
  editing,
  accordion,
  toggleAccordion,
  form,
  deleteAction,
}) => {
  const faqBox = useRef(null);
  useClickOutside(faqBox, () => {
    if (editing[index]) {
      toggleEditing(index, false);
    }
  });

  return (
    <div className="relative" ref={faqBox}>
      <div className="flex gap bg-grey-fields-100 items-center rounded-15 relative">
        <div className="flex items-center gap-2 justify-center h-full">
          {/* prettier-ignore */}
          <svg width={24} height={24} viewBox="0 0 18 18" fill="none" className="text-black-placeholder mx-2" >
            <path d="M11.5 14.5C12.052 14.5 12.5 14.052 12.5 13.5L12.5 13C12.5 12.448 12.052 12 11.5 12L11 12C10.448 12 10 12.448 10 13L10 13.5C10 14.052 10.448 14.5 11 14.5L11.5 14.5Z" fill="currentColor" />
            <path d="M11.5 10.25C12.052 10.25 12.5 9.802 12.5 9.25L12.5 8.75C12.5 8.198 12.052 7.75 11.5 7.75L11 7.75C10.448 7.75 10 8.198 10 8.75L10 9.25C10 9.802 10.448 10.25 11 10.25L11.5 10.25Z" fill="currentColor" />
            <path d="M12.5 5C12.5 5.552 12.052 6 11.5 6L11 6C10.448 6 10 5.552 10 5L10 4.5C10 3.948 10.448 3.5 11 3.5L11.5 3.5C12.052 3.5 12.5 3.948 12.5 4.5L12.5 5Z" fill="currentColor" />
            <path d="M7 14.5C7.552 14.5 8 14.052 8 13.5L8 13C8 12.448 7.552 12 7 12L6.5 12C5.948 12 5.5 12.448 5.5 13L5.5 13.5C5.5 14.052 5.948 14.5 6.5 14.5L7 14.5Z" fill="currentColor" />
            <path d="M8 9.25C8 9.802 7.552 10.25 7 10.25L6.5 10.25C5.948 10.25 5.5 9.802 5.5 9.25L5.5 8.75C5.5 8.198 5.948 7.75 6.5 7.75L7 7.75C7.552 7.75 8 8.198 8 8.75L8 9.25Z" fill="currentColor" />
            <path d="M7 6C7.552 6 8 5.552 8 5L8 4.5C8 3.948 7.552 3.5 7 3.5L6.5 3.5C5.948 3.5 5.5 3.948 5.5 4.5L5.5 5C5.5 5.552 5.948 6 6.5 6L7 6Z" fill="currentColor" />
          </svg>
        </div>
        <div className="border rounded-15 border-gray-border border-opacity-50 bg-white px-3 py-3 sm:py-5 sm:px-4 flex-1 my-0 w-full overflow-hidden">
          <div className="flex justify-between w-full">
            <div className="flex gap-1 items-center flex-1 mr-3.5 overflow-hidden">
              <button
                type="button"
                className={`transition-all duration-100 ease-out transform flex-shrink-0 ${
                  !accordion[index] ? "" : "rotate-90"
                }`}
                onClick={(e) => toggleAccordion(index)}
              >
                {/* prettier ignore */}
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path
                    d="M7.5 15L12.5 10L7.5 5"
                    stroke="#656565"
                    strokeWidth="1.6"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
              <h6 className="text-1sm font-medium text-black-secondary overflow-hidden overflow-ellipsis whitespace-nowrap pt-0.5">
                {faq.question}
              </h6>
            </div>
            <div className="flex gap-1 items-center">
              <RoundActionBtn
                icon="edit"
                className="text-black-secondary"
                onClick={() => toggleEditing(index, !accordion[index])}
              />
              <RoundActionBtn icon="delete" className="text-accent-red-500" onClick={() => deleteAction(index)} />
            </div>
          </div>
          {accordion[index] && (
            <div className="flex flex-col gap-y-1 pt-3.75">
              {editing[index] && (
                <div className="flex gap-2 items-center">
                  <InputField
                    className="flex-1"
                    label="Question: eg. What is your return policy?"
                    {...getFieldvalues(`faqs.${index}.question`, form)}
                  />
                  {!faq.id && (
                    <RoundActionBtn
                      icon="delete"
                      color="danger"
                      className="text-accent-red-500"
                      onClick={(e) => true}
                      size="md"
                    />
                  )}
                </div>
              )}
              {editing[index] && <TextArea label="Answer" {...getFieldvalues(`faqs.${index}.answer`, form)} />}
              {!editing[index] && (
                <div className="rounded-10 p-3 bg-grey-fields-100 border border-grey-border border-opacity-50">
                  <p className="text-xs sm:text-sm text-black-placeholder">{faq.answer}</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
