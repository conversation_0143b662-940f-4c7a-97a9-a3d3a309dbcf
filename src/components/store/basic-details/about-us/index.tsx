import React, { useEffect, useRef, useState } from "react";
import { useFormik } from "formik";
import { useModals } from "@/components/hooks/useModals";
import { useRequest } from "@/api/utils";
import useImageUploads from "@/components/hooks/useImageUploads";
import authContext from "@/contexts/auth-context";
import { UpdateStoreAbout } from "@/api";
import { FILE_TYPES, Image } from "@/assets/interfaces";
import { handleImageSelectionFromFile } from "@/assets/js/utils/functions";
import { AppBtn, RoundActionBtn } from "@/components/ui/buttons";
import ErrorLabel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";
import { InputField, SelectDropdown, TextArea } from "@/components/ui/form-elements";
import LazyImage from "@/components/lazy-image";
import Portal from "@/components/portal";
import { getFieldvalues } from "@/assets/js/utils/functions";
import { TestimonialForm, TestimonialPreview, testimonialSources } from "./customer-testimonial";
import { DeleteTestimonialModal } from "./delete-modal";
import * as Yup from "yup";

export const AboutUs: React.FC<{}> = () => {
  const { store } = authContext.useContainer();
  const [accordion, setAccordion] = useState([]);
  const [editing, setEditing] = useState([]);
  const { modals, toggleModal } = useModals(["delete"]);
  const [selected, setSelected] = useState<{
    delete: number;
  }>({
    delete: null,
  });

  const updateAbout = useRequest(UpdateStoreAbout);

  const form = useFormik({
    enableReinitialize: true,
    initialValues: {
      about_us: {
        content: store?.about_us?.content ?? "",
        images: store?.about_us?.images ?? [],
      },
      testimonials: store?.testimonials ?? [],
    },
    validationSchema,
    onSubmit: async (values) => {
      const { makeRequest } = updateAbout;
      const [response, error] = await makeRequest({
        about_us: values.about_us,
        testimonials: values.testimonials.map(({ id, created_at, ...rest }) => rest),
      });

      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors({ ...error.fields });
        }
      } else {
        form.setValues(response.data);
        form.setFormikState((prevState) => ({
          ...prevState,
          initialValues: response.data,
        }));
      }

      const formEl = document.querySelector("#basic-details-form");
      formEl.scrollTo(0, 0);
    },
  });

  const [images, setImages] = useState<Image[]>(
    form.values.about_us.images.map((url) => ({
      url,
      file: null,
      isUploading: false,
      uploadProgress: 100,
      name: "",
      src: "",
      lastModified: 0,
    }))
  );

  const creatEmptyTestimonial = () => {
    const testimonials = [...form.values.testimonials];
    testimonials.push({
      id: "",
      customer_name: "",
      content: "",
      source: "",
      created_at: "",
      is_visible: true,
    });
    form.setFieldValue("testimonials", testimonials);
  };

  const removeTestimonial = (e: React.MouseEvent<HTMLButtonElement>, index: number) => {
    e.preventDefault();
    const testimonials = [...form.values.testimonials];
    testimonials.splice(index, 1);
    form.setFieldValue("testimonials", testimonials);
  };

  const toggleAccordion = (index: number) => {
    const accordionCopy = [...accordion];
    accordionCopy[index] = !accordion[index];
    setAccordion(accordionCopy);
  };

  const toggleEditing = (index: number, toggle: boolean = false) => {
    if (toggle) {
      toggleAccordion(index);
    }
    const editingCopy = [...editing];
    editingCopy[index] = !editing[index];
    setEditing(editingCopy);
  };

  const deleteTestimonial = (id: number) => {
    setSelected((prev) => ({
      ...prev,
      delete: id,
    }));
    toggleModal("delete");
  };

  useEffect(() => {
    form.setFieldValue(
      "about_us.images",
      images.map((img) => img.url)
    );
  }, [images]);

  useEffect(() => {
    if (store?.about_us?.images) {
      const formattedImages = store.about_us.images.map((url) => ({
        url,
        file: null,
        isUploading: false,
        uploadProgress: 100,
        name: "",
        src: "",
        lastModified: 0,
      }));
      setImages(formattedImages);
      form.setFieldValue("about_us.images", store?.about_us?.images);
    }
  }, [store?.about_us]);

  useEffect(() => {
    if (updateAbout.response?.data) {
      setEditing([]);
      setAccordion([]);
    }
  }, [updateAbout?.response]);

  useImageUploads(images, FILE_TYPES.ITEMS, setImages);

  const imagePicker = useRef<HTMLInputElement>(null);
  const removeImage = (e: React.MouseEvent<HTMLButtonElement>, index: number) => {
    e.preventDefault();
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  return (
    <>
      <div className="mb-7.5" id="#store-about-form">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-red-500 m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg width={31} height={30} viewBox="0 0 31 30">
            <path d="M15.5 15C18.9518 15 21.75 12.2018 21.75 8.75C21.75 5.29822 18.9518 2.5 15.5 2.5C12.0482 2.5 9.25 5.29822 9.25 8.75C9.25 12.2018 12.0482 15 15.5 15Z" fill="white" />
            <path d="M15.5002 18.125C9.2377 18.125 4.1377 22.325 4.1377 27.5C4.1377 27.85 4.4127 28.125 4.7627 28.125H26.2377C26.5877 28.125 26.8627 27.85 26.8627 27.5C26.8627 22.325 21.7627 18.125 15.5002 18.125Z" fill="white" />
          </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          About Us
        </h2>
      </div>
      <ErrorLabel error={updateAbout?.error?.message} />
      <SuccessLabel message={updateAbout?.response?.message ? "Store About updated successfully!" : null} />
      <>
        <div className="mb-3.75 border-b border-grey-border pb-7.5">
          <h3 className="font-bold text-base sm:text-[17px] text-black-secondary">
            Tell your customers more about your business:
          </h3>
          <TextArea label="Your business story" {...getFieldvalues("about_us.content", form)} />

          <h3 className="font-bold text-base sm:text-[17px] text-black-secondary mt-3.75">Supporting Images:</h3>
          <p className="text-1xs text-dark">Pictures of you, your team, your store, etc.</p>
          <div className="mt-3.75 grid items-start w-full grid-cols-[repeat(auto-fit,60px)] sm:grid-cols-[repeat(auto-fit,90px)] gap-2.5">
            <input
              type="file"
              ref={imagePicker}
              name="product-images"
              multiple
              accept="image/*,.heic"
              id="product-images"
              className="hidden"
              onChange={(e) =>
                handleImageSelectionFromFile({
                  e,
                  images: images,
                  saveImages: (newImages: Image[]) => setImages(newImages),
                })
              }
            />

            {images.map(({ name, src, url, isUploading, uploadProgress, error }, index) => (
              <div key={index}>
                <figure className="w-16 h-16 sm:w-[90px] sm:h-[90px] relative group" key={index}>
                  {url ? (
                    <>
                      <LazyImage
                        src={url}
                        alt={name}
                        className="w-full h-full object-cover rounded-10 border border-grey-fields-100"
                        loaderClasses="rounded-10"
                      />
                    </>
                  ) : (
                    <div className="h-full w-full rounded-10 bg-grey-loader animate-pulse flex items-center justify-center">
                      <div className="spinner spinner--sm"></div>
                    </div>
                  )}
                  <button
                    className="h-6 w-6 bg-black bg-opacity-80 flex items-center justify-center absolute transform -top-1 -right-1 rounded-full transition-all opacity-0 group-hover:opacity-100 z-30"
                    onClick={(e) => removeImage(e, index)}
                    type="button"
                  >
                    {/* prettier-ignore */}
                    <svg width="12" height="12" viewBox="0 0 15 15" fill="none">
                      <path d="M11.25 3.75L3.75 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M3.75 3.75L11.25 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                </figure>
                {isUploading && (
                  <div
                    className={`mt-1.5 h-1 w-16 sm:w-[90px] rounded-10 overflow-hidden ${
                      error ? "bg-accent-red-500 bg-opacity-10" : "bg-grey-divider"
                    }`}
                  >
                    <div
                      className={`h-full transition-all duration-200 ease-out ${
                        error ? "bg-accent-red-500" : "bg-accent-green-500"
                      }`}
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                )}
              </div>
            ))}

            <button
              className="no-outline w-16 h-16 sm:w-[90px] sm:h-[90px] rounded-10 border border-dashed border-grey-subtext text-grey-subtext hover:border-primary-500 hover:text-primary-500 transition-all flex items-center justify-center"
              onClick={() => imagePicker.current.click()}
              type="button"
            >
              {/* prettier-ignore */}
              <svg width="20" viewBox="0 0 30 30" fill="none">
                <path d="M15 6.25V23.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6.24976 15H23.7498" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
          </div>
        </div>

        <h3 className="font-bold text-base sm:text-[17px] text-black-secondary mt-3.75">Customer Testimonials</h3>
        <div className="py-3.75">
          {form.values.testimonials &&
            form.values.testimonials.map((item, index) => (
              <div key={index} className="mb-3.75">
                {item.id === "" ? (
                  <TestimonialForm
                    index={index}
                    form={form}
                    removeTestimonial={removeTestimonial}
                    toggleEditing={toggleEditing}
                    toggleAccordion={toggleAccordion}
                  />
                ) : (
                  <TestimonialPreview
                    testimonial={item}
                    index={index}
                    {...{ toggleEditing, accordion, editing, toggleAccordion, form, deleteAction: deleteTestimonial }}
                  />
                )}
              </div>
            ))}

          <div className="flex flex-col items-center gap-y-3 mb-3">
            <AppBtn color="neutral" size="md" className="w-full" onClick={() => creatEmptyTestimonial()}>
              + Add a new Testimonial
            </AppBtn>

            <AppBtn
              color="primary"
              size="lg"
              className="w-full"
              disabled={!form.dirty || updateAbout.isLoading}
              onClick={() => form.submitForm()}
            >
              Save Updates
            </AppBtn>
          </div>
        </div>
      </>

      <Portal>
        {selected.delete !== null && (
          <DeleteTestimonialModal
            show={modals?.delete.show}
            toggle={() => toggleModal("delete")}
            selected={selected.delete}
            form={form}
          />
        )}
      </Portal>
    </>
  );
};

const validationSchema = Yup.object().shape({
  about_us: Yup.object().shape({
    content: Yup.string().required("About Us content is required"),
    images: Yup.array().of(Yup.string().url("Each image must be a valid URL")).min(1, "At least one image is required"),
  }),
  testimonials: Yup.array().of(
    Yup.object().shape({
      customer_name: Yup.string().required("Customer name is required"),
      content: Yup.string().required("Testimonial content is required"),
      source: Yup.string().required("Testimonial source is required"),
    })
  ),
});
