import React from "react";
import { useRequest } from "@/api/utils";
import { UpdateStoreAboutParams } from "@/api/interfaces";
import { UpdateStoreAbout } from "@/api";
import Modal, { <PERSON>dalBody, ModalFooter } from "@/components/ui/modal";
import ErrorLabel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";
import { AppBtn } from "@/components/ui/buttons";
import { FormikProps } from "formik";
import { StoreAboutDetails } from "@/assets/interfaces";

export interface DeleteModalProps {
  show: boolean;
  toggle: (status: boolean) => void;
  selected: number;
  form: FormikProps<StoreAboutDetails>;
}

export const DeleteTestimonialModal: React.FC<DeleteModalProps> = ({ show, toggle, selected, form }) => {
  const { makeRequest, isLoading, error, response } = useRequest<UpdateStoreAboutParams>(UpdateStoreAbout);

  const handleItemDelete = async () => {
    const updatedTestimonials = [
      ...form.values.testimonials.slice(0, selected),
      ...form.values.testimonials.slice(selected + 1),
    ];

    const [res, err] = await makeRequest({
      about_us: form.values.about_us,
      testimonials: updatedTestimonials,
    });
    if (!err && res?.data) {
      toggle(false);
      form.setValues(res.data);
      form.setFormikState((prevState) => ({
        ...prevState,
        initialValues: res.data,
      }));
    }
  };

  return (
    <Modal {...{ show, toggle }} title="Delete Testimonial" size="sm">
      <ModalBody>
        <ErrorLabel error={error?.message} />
        <SuccessLabel message={response?.message ? "Testimonial deleted successfully!" : ""} />
        <div className="text-center py-3.5">
          <h4 className="text-black text-base font-semibold">Do you want to delete this testimonial?</h4>
          <p className="text-sm text-grey-subtext mt-1 max-w-xs mx-auto">
            This Testimonial would be completly removed from the list.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="danger" onClick={handleItemDelete} disabled={isLoading} size="lg">
          Delete Testimonial
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};
