import { useFormik } from "formik";
import * as Yup from "yup";
import { UpdateStoreDetailsParams } from "../../../api/interfaces";
import { UpdateStoreDetails } from "../../../api";
import { RequestInterface, useRequest } from "../../../api/utils";
import { optionalPhoneValidation, phoneValidation } from "../../../assets/js/utils/common-validations";
import getStates from "../../../assets/js/utils/get-states";
import useTabs from "../../../components/hooks/useTabs";
import { SocialDetails } from "./social-details";
import { LocationDetails } from "./location-details";
import { BasicDetails } from "./basic-details-content";
import { AppBtn } from "../../../components/ui/buttons";
import authContext from "../../../contexts/auth-context";
import { StoreCategory } from "./category";
import { ExtraInfo } from "./extra-info";
import { convertImageUrlsToImages } from "@/assets/js/utils/utils";
import { AboutUs } from "./about-us";
import { Faqs } from "./faqs";

interface Props {
  activeTab: string;
  updateStoreReq: RequestInterface<UpdateStoreDetailsParams>;
}

const BasicDetailsSettings: React.FC<Props> = ({ activeTab, updateStoreReq }) => {
  const { store, updateStore } = authContext.useContainer();
  const { isLoading, response, makeRequest, error, clearResponse } = updateStoreReq;
  const country = store?.country;
  const states = getStates(country?.code);

  const form = useFormik({
    initialValues: {
      id: store?.id ?? "",
      name: store?.name ?? "",
      phone: {
        digits: store?.phone.split("-")[1],
        code: store?.phone.split("-")[0] ?? "+234",
      },
      secondary_phone: {
        digits: store?.secondary_phone?.split("-")[1],
        code: store?.secondary_phone ? store?.secondary_phone.split("-")[0] : "+234",
      },
      description: store?.description ?? "",
      state: store?.state ?? "",
      address: store?.address ?? "",
      delivery_locations: store?.delivery_locations ?? "",
      socials: {
        facebook: store?.socials?.facebook || "",
        instagram: store?.socials?.instagram || "",
        twitter: store?.socials?.twitter || "",
        snapchat: store?.socials?.snapchat || "",
        tiktok: store?.socials?.tiktok || "",
      },
      extra_info: {
        // production_timeline: store?.extra_info?.production_timeline || "",
        delivery_timeline: store?.extra_info?.delivery_timeline
          ? {
              count: store?.extra_info?.delivery_timeline.split(" ")[0]?.toLowerCase(),
              unit: store?.extra_info?.delivery_timeline.split(" ")[1]?.toLowerCase(),
            }
          : { count: "", unit: "" },
        refund_policy: store?.extra_info?.refund_policy || "",
        images: convertImageUrlsToImages(store?.extra_info?.images ?? []),
        images_label: store?.extra_info?.images_label || "",
      },
    },
    validationSchema,
    onSubmit: async (values) => {
      const phone = `${values.phone.code}-${values.phone.digits}`;
      const secondary_phone = Boolean(values?.secondary_phone?.digits?.trim())
        ? `${values.secondary_phone.code}-${values.secondary_phone.digits}`
        : "";
      const extra_info = values.extra_info
        ? { ...values.extra_info, images: values.extra_info?.images?.map((i) => i.url) }
        : undefined;

      const [response, error] = await makeRequest({
        ...values,
        phone,
        secondary_phone,
        extra_info: {
          ...extra_info,
          delivery_timeline: `${extra_info.delivery_timeline.count} ${extra_info.delivery_timeline.unit}`.trim(),
        },
      });

      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors({ ...error.fields });
        }
      } else {
        updateStore({ ...response.data });
      }

      //scroll to top so response message shows
      const formEl = document.querySelector("#basic-details-form");
      formEl.scrollTo(0, 0);
    },
  });

  const tabComponents = {
    store_basic_details: (
      <BasicDetails {...{ form, country, error, response, usesChowbot: store?.flags?.uses_chowbot }} />
    ),
    store_location: <LocationDetails {...{ states, form, error, response }} />,
    social_links: <SocialDetails {...{ form, error, response }} />,
    extra_info: <ExtraInfo {...{ form, error, response }} />,
  };

  if (activeTab === "business_category") {
    return (
      <div className="pt-10 sm:pt-11.25 w-full mx-auto max-w-sm" id="basic-details-form">
        <StoreCategory {...{ store, updateStore }}></StoreCategory>
      </div>
    );
  }

  if (activeTab === "about_us") {
    return (
      <div className="pt-10 sm:pt-11.25 w-full mx-auto max-w-md" id="basic-details-form">
        <AboutUs />
      </div>
    );
  }

  if (activeTab === "faqs") {
    return (
      <div className="pt-10 sm:pt-11.25 w-full mx-auto max-w-md" id="basic-details-form">
        <Faqs {...{ form, error, response }}></Faqs>
      </div>
    );
  }

  return (
    <div className="pt-10 sm:pt-11.25">
      <form onSubmit={form.handleSubmit} className="w-full mx-auto max-w-sm pb-32">
        {tabComponents[activeTab]}

        <AppBtn isBlock className="mt-7.5" disabled={isLoading} type="submit" size="lg">
          {isLoading ? "Updating..." : "Update store details"}
        </AppBtn>
      </form>
    </div>
  );
};

export const StoreSettingsBreadCrumb = () => {
  return (
    <div className="border-b border-grey-outline border-opacity-40 py-3 px-4 sticky top-0 z-10">
      <AppBtn size="sm" color="neutral" href="/my-store" className="inline-flex">
        {/* prettier-ignore */}
        <svg width="16" viewBox="0 0 24 24" fill="none" className="mr-1">
        <path d="M19 12H5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M12 19L5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
        Back to my store
      </AppBtn>
    </div>
  );
};

export default BasicDetailsSettings;
const validationSchema = Yup.object().shape({
  name: Yup.string().required("Store name is required"),
  phone: phoneValidation(),
  secondary_phone: optionalPhoneValidation,
  description: Yup.string().max(150, "Description cannot be more than 150 characters"),
  address: Yup.string(),
  state: Yup.string(),
  delivery_locations: Yup.string(),
  socials: Yup.object().shape({
    facebook: Yup.string().min(3, "Username should be at least 3 characters"),
    twitter: Yup.string().min(3, "Username should be at least 3 characters"),
    instagram: Yup.string().min(3, "Username should be at least 3 characters"),
  }),
  // delivery_timeline: Yup.string(),
  // production_timeline: Yup.string(),
  refund_policy: Yup.string().max(150, "Description cannot be more than 150 characters"),
  extra_info_label: Yup.string(),
});
