import React from "react";
import { arrayToInputValues, getFieldvalues } from "../../../assets/js/utils/functions";
import ErrorLabel from "../../ui/error-label";
import { InputField, SelectDropdown, TextArea } from "../../ui/form-elements";
import SuccessLabel from "../../ui/success-label";
import DataAccordion from "@/components/ui/data-accordion";
import PickProductMedia from "@/components/products/pick-product-media";
import PickSizeChartImages from "./pick-size-chart-images";
import { FormikProps } from "formik";

interface Props {
  form: FormikProps<any>;
  error: any;
  response: any;
}
export const ExtraInfo: React.FC<Props> = ({ form, error, response }) => {
  return (
    <>
      <div className="mb-12.5">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-red-500 m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg width="30" viewBox="0 0 24 24" fill="none">
            <path d="M15.7997 2.21048C15.3897 1.80048 14.6797 2.08048 14.6797 2.65048V6.14048C14.6797 7.60048 15.9197 8.81048 17.4297 8.81048C18.3797 8.82048 19.6997 8.82048 20.8297 8.82048C21.3997 8.82048 21.6997 8.15048 21.2997 7.75048C19.8597 6.30048 17.2797 3.69048 15.7997 2.21048Z" fill="white"/>
            <path d="M20.5 10.19H17.61C15.24 10.19 13.31 8.26 13.31 5.89V3C13.31 2.45 12.86 2 12.31 2H8.07C4.99 2 2.5 4 2.5 7.57V16.43C2.5 20 4.99 22 8.07 22H15.93C19.01 22 21.5 20 21.5 16.43V11.19C21.5 10.64 21.05 10.19 20.5 10.19ZM11.5 17.75H7.5C7.09 17.75 6.75 17.41 6.75 17C6.75 16.59 7.09 16.25 7.5 16.25H11.5C11.91 16.25 12.25 16.59 12.25 17C12.25 17.41 11.91 17.75 11.5 17.75ZM13.5 13.75H7.5C7.09 13.75 6.75 13.41 6.75 13C6.75 12.59 7.09 12.25 7.5 12.25H13.5C13.91 12.25 14.25 12.59 14.25 13C14.25 13.41 13.91 13.75 13.5 13.75Z" fill="white"/>
          </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Store Extra Info
        </h2>
      </div>
      <ErrorLabel error={error?.message ?? null} />
      <SuccessLabel message={response ? "Store updated successfully!" : null} />
      {/* <InputField label="Production Timeline" {...getFieldvalues("extra_info.production_timeline", form)} /> */}

      <div className="mb-5">
        <h3 className="font-bold text-base sm:text-[17px] text-black-secondary">Average Delivery Timeline:</h3>
        <div className="flex items-center space-x-3 mt-3.75">
          <InputField
            label="Timeline"
            type="number"
            {...getFieldvalues("extra_info.delivery_timeline.count", form)}
            className="flex-1"
          />
          <div className="flex-1">
            <SelectDropdown
              label="Duration"
              options={durationOptions}
              {...getFieldvalues("extra_info.delivery_timeline.unit", form)}
            />
          </div>
        </div>
      </div>
      <h3 className="font-bold text-base sm:text-[17px] text-black-secondary">Return & Refund Policy:</h3>
      <TextArea label="Policy" {...getFieldvalues("extra_info.refund_policy", form)} />
      <DataAccordion title="Info Images" className="mt-5" isClosed>
        <InputField
          label="Title (e.g. Size Chart)"
          className="mb-3.75 !mt-5"
          {...getFieldvalues("extra_info.images_label", form)}
        />
        <PickSizeChartImages
          images={form.values.extra_info.images}
          saveImages={(images) => form.setFieldValue("extra_info.images", images)}
        />
      </DataAccordion>
    </>
  );
};

const durationOptions = [
  {
    value: "minute(s)",
    text: "Minute(s)",
  },
  {
    value: "hour(s)",
    text: "Hour(s)",
  },
  {
    value: "day(s)",
    text: "Day(s)",
  },
  {
    value: "week(s)",
    text: "Week(s)",
  },
  {
    value: "month(s)",
    text: "Month(s)",
  },
];
