import { GetDomainPurchase } from "@/api";
import { useFetcher, useRequest } from "@/api/utils";
import { DOMAIN_PURCHASE_STATUS, DomainPurchase } from "@/assets/interfaces";
import { WHATSAPP_LINK } from "@/assets/js/utils/constants";
import { ErrorAnimation } from "@/components/error.animation";
import { AppBtn } from "@/components/ui/buttons";
import SuccessAnimation from "@/components/ui/success-animation";
import ErrorIcon from "../../../assets/icons/statuses/error.svg";
import React from "react";

interface Props {
  domainPurchaseId: string;
}

const DomainPurchaseStatus: React.FC<Props> = ({ domainPurchaseId }) => {
  const getDomainPurchaseStatusReq = useFetcher(GetDomainPurchase, { id: domainPurchaseId }, ["id"]);
  const domainPurchase: DomainPurchase = getDomainPurchaseStatusReq.response?.data ?? null;

  if (getDomainPurchaseStatusReq.isLoading) {
    return (
      <div className="flex items-center flex-col py-12">
        <div className="spinner spinner--md text-primary-500"></div>
        <span className="text-sm text-black-muted mt-2.5">Checking purchase status...</span>
      </div>
    );
  }

  if (
    getDomainPurchaseStatusReq.error ||
    !domainPurchase ||
    domainPurchase.status !== DOMAIN_PURCHASE_STATUS.COMPLETED
  ) {
    return (
      <>
        <div className="flex flex-col items-center text-center">
          <figure className="w-[90px] h-[90px] md:w-25 md:h-25 rounded-full bg-opacity-10 flex items-center justify-center">
            <ErrorIcon />
          </figure>
          <h2 className="text-2xl sm:text-3lg lg:text-3xl max-w-[290px] text-black-500 mt-3.75 sm:mt-5 font-bold">
            We couldn't <br /> buy your domain
          </h2>
        </div>
        <div className="mt-5 sm:mt-7.5 bg-grey-fields-100 rounded-10">
          <div className="px-3.75 py-3 border-b border-grey-border border-opacity-50 text-1xs font-semibold text-accent-red-500">
            Error we received:
          </div>
          <div className="p-3.75 text-dark text-sm">
            {
              "We could't fetch or buy your domain, this could be due to a downtime with our providers, we'll automatically retry and notify you when it's done, you can also contact support"
            }
          </div>
        </div>

        <div className="mt-3 flex items-center space-x-2.5">
          <AppBtn
            className="flex-1"
            isBlock
            size="lg"
            color="primary"
            href={`${WHATSAPP_LINK}&text=Hi, My domain purchase was not successful, the purchase ID is: ${domainPurchase?.id}`}
          >
            Contact Support
          </AppBtn>
          {/* <AppBtn className="flex-1" isBlock size="lg" onClick={retry} disabled={initiateRequest?.isLoading}>
            {initiateRequest?.isLoading ? "Retrying..." : "Try Again"}
          </AppBtn> */}
        </div>
      </>
    );
  }

  return (
    <>
      <div className="flex flex-col items-center text-center">
        <SuccessAnimation />
        <h2 className="text-2xl sm:text-3lg lg:text-3xl max-w-[290px] text-black-500 mt-5 sm:mt-7.5">
          Your <b>domain</b> has been successfully purchased
        </h2>
      </div>

      <div className="rounded-15 bg-grey-light mt-7.5">
        <div className="flex gap-3.75 p-3.75 border-b border-grey-divider relative">
          <h3 className="text-base sm:text-lg lg:text-xl font-bold font-display text-primary-500">
            {domainPurchase?.domain}
          </h3>
        </div>
        <div className="p-2.5">
          <p className="text-sm text-dark">
            Your domain will be automatically linked to your store shortly, please check back in a few minutes
          </p>
        </div>
      </div>

      <AppBtn className="flex-1 mt-20" isBlock size="lg" href="/my-store/store-configuration?tab=custom_domains">
        Go to Domains
      </AppBtn>
    </>
  );
};

export default DomainPurchaseStatus;
