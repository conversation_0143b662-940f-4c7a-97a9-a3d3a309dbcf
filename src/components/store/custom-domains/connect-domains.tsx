import { useState, useEffect, useRef } from "react";
import { AppBtn } from "../../ui/buttons";
import { useRequest } from "../../../api/utils";
import { FormikProps } from "formik";
import { GetDomains, AddDomain, RemoveDomain, GenerateSslCertificate, VerifyDomain } from "../../../api/stores";
import Portal from "@/components/portal";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { useModals } from "@/components/hooks/useModals";
import ErrorLabel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";
import ContentCopy from "../custom-domains/content-copy";
import Table, { TableBody, TableHead, TableHeadItem } from "@/components/ui/table";
import authContext from "../../../contexts/auth-context";
import { actionIsAllowed, SCOPES } from "../../../assets/js/utils/permissions";
import ErrorBox from "../../ui/error";
import { InputField } from "@/components/ui/form-elements";
import { useRouter } from "next/router";

interface DomainData {
  domain: string;
  verified: boolean;
  verification_code: string;
  store_id: string;
  created_at: string;
  updated_at: string;
  id: string;
  certificate_issued: boolean;
  verified_at?: string;
  certificate_issued_at?: string;
}

interface Props {}

export const ConnectDomains: React.FC<Props> = () => {
  const router = useRouter();
  const [newDomain, setNewDomain] = useState("");
  const [error, setError] = useState("");
  const { subscription } = authContext.useContainer();

  const serverIp = process.env.NEXT_PUBLIC_SERVER_IP || "**************";

  const addDomainReq = useRequest(AddDomain);

  let canManageCustomDomains = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_CUSTOM_DOMAINS,
  });

  const handleAddDomain = async (e) => {
    e.preventDefault();
    const trimmedDomain = newDomain.trim();
    if (!trimmedDomain) return;

    const domainRegex = /^(?!:\/\/)(?=.{1,255}$)((.{1,63}\.){1,127}(?![0-9]*$)[a-z0-9-]+\.?)$/i;
    if (!domainRegex.test(trimmedDomain)) {
      setError("Please enter a valid domain name");
      return;
    }

    const [response, error] = await addDomainReq.makeRequest({ domain: trimmedDomain });
    if (response) {
      setTimeout(() => {
        router.push("/my-store/configurations?tab=custom_domains");
      }, 3000);
    }
  };

  if (!canManageCustomDomains) {
    return (
      <ErrorBox
        title="Upgrade required"
        message="Please upgrade to business plus plan to use custom domains on your store"
      >
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  return (
    <>
      <div className="mb-8 md:mb-10">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-orange-500 text-white m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg  width="50%" viewBox="0 0 31 30" fill="none">
            <path d="M21.8752 2.5H16.6252C12.6749 2.5 10.8748 3.9369 10.5618 7.42556C10.512 7.98025 10.9677 8.4375 11.5246 8.4375H14.3752C19.6252 8.4375 22.0627 10.875 22.0627 16.125V18.9756C22.0627 19.5325 22.5199 19.9882 23.0746 19.9384C26.5633 19.6254 28.0002 17.8253 28.0002 13.875V8.625C28.0002 4.25 26.2502 2.5 21.8752 2.5Z" fill="white" />
            <path d="M14.375 10H9.125C4.75 10 3 11.75 3 16.125V21.375C3 25.75 4.75 27.5 9.125 27.5H14.375C18.75 27.5 20.5 25.75 20.5 21.375V16.125C20.5 11.75 18.75 10 14.375 10ZM15.8625 17.0625L11.225 21.7C11.05 21.875 10.825 21.9625 10.5875 21.9625C10.35 21.9625 10.125 21.875 9.95 21.7L7.625 19.375C7.275 19.025 7.275 18.4625 7.625 18.1125C7.975 17.7625 8.5375 17.7625 8.8875 18.1125L10.575 19.8L14.5875 15.7875C14.9375 15.4375 15.5 15.4375 15.85 15.7875C16.2 16.1375 16.2125 16.7125 15.8625 17.0625Z" fill="white" />
          </svg>
        </figure>
        <p className="text-center font-display text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Connect An
        </p>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto !leading-tight">
          Existing Domain
        </h2>
      </div>

      <div className="space-y-6">
        {/* <ErrorLabel error={message?.text && message?.type == "error" ? message?.text : ""} />
        <SuccessLabel message={message?.text && message?.type !== "error" ? message?.text : ""} /> */}

        <ErrorLabel error={error ?? addDomainReq.error?.message ?? "Couldn't add domain, please try again."} />
        <SuccessLabel
          message={addDomainReq?.response ? "Domain added successfully, redirecting in 3 seconds..." : ""}
        />

        <div className="bg-grey-fields-200 rounded-10 border border-grey-border border-opacity-50">
          <div className="p-4 border-b border-grey-border border-opacity-50">
            <div className="flex items-center">
              <h2 className="text-lg font-bold text-black">How add an existing domain</h2>
            </div>
          </div>

          <div className="p-4">
            <div className="text-black-muted">
              <ol className="text-sm list-decimal pl-4 space-y-2">
                <li>Add your domain here</li>
                <li>
                  Add an <strong>A record</strong> in your domain provider's DNS settings that points to:
                  <ContentCopy text={serverIp} />
                </li>
                <li>
                  After adding the A record, click the <strong>Verify Domain</strong> button
                </li>
              </ol>
              <div className="w-full flex items-start mt-3.75 bg-accent-yellow-pastel text-black-muted text-1xs font-normal p-2 rounded-10">
                {/* prettier-ignore */}
                <svg width="15%" height={18} viewBox="0 0 19 18" fill="none">
                  <path d="M9.5 17.0625C5.0525 17.0625 1.4375 13.4475 1.4375 9C1.4375 4.5525 5.0525 0.9375 9.5 0.9375C13.9475 0.9375 17.5625 4.5525 17.5625 9C17.5625 13.4475 13.9475 17.0625 9.5 17.0625ZM9.5 2.0625C5.675 2.0625 2.5625 5.175 2.5625 9C2.5625 12.825 5.675 15.9375 9.5 15.9375C13.325 15.9375 16.4375 12.825 16.4375 9C16.4375 5.175 13.325 2.0625 9.5 2.0625Z" fill="currentColor" />
                  <path d="M9.5 10.3125C9.1925 10.3125 8.9375 10.0575 8.9375 9.75V6C8.9375 5.6925 9.1925 5.4375 9.5 5.4375C9.8075 5.4375 10.0625 5.6925 10.0625 6V9.75C10.0625 10.0575 9.8075 10.3125 9.5 10.3125Z" fill="currentColor" />
                  <path d="M9.5 12.7511C9.4025 12.7511 9.305 12.7286 9.215 12.6911C9.125 12.6536 9.0425 12.6011 8.9675 12.5336C8.9 12.4586 8.8475 12.3836 8.81 12.2861C8.7725 12.1961 8.75 12.0986 8.75 12.0011C8.75 11.9036 8.7725 11.8061 8.81 11.7161C8.8475 11.6261 8.9 11.5436 8.9675 11.4686C9.0425 11.4011 9.125 11.3486 9.215 11.3111C9.395 11.2361 9.605 11.2361 9.785 11.3111C9.875 11.3486 9.9575 11.4011 10.0325 11.4686C10.1 11.5436 10.1525 11.6261 10.19 11.7161C10.2275 11.8061 10.25 11.9036 10.25 12.0011C10.25 12.0986 10.2275 12.1961 10.19 12.2861C10.1525 12.3836 10.1 12.4586 10.0325 12.5336C9.9575 12.6011 9.875 12.6536 9.785 12.6911C9.695 12.7286 9.5975 12.7511 9.5 12.7511Z" fill="currentColor" />
                </svg>
                <span>
                  DNS changes can take up to 24-48 hours to propagate. If verification fails, please wait and try again
                  later.
                </span>
              </div>
            </div>
          </div>
          <div className="p-4  border-t border-grey-border border-opacity-50">
            <h2 className="text-sm font-bold text-black">New Custom Domain</h2>
            <p className="text-1xs mt-1 mb-3 text-black-muted">
              Enter an existing domain name to connect your custom domain
            </p>

            <form
              onSubmit={handleAddDomain}
              className="flex gap-2"
              onKeyDown={(e) => e.key === "Enter" && e.preventDefault()}
            >
              <div className="flex-1">
                <input
                  type="text"
                  name="domain"
                  value={newDomain}
                  onChange={(e) => {
                    setNewDomain(e.target.value);
                    setError("");
                  }}
                  placeholder="yourdomain.com"
                  className="w-full px-4 py-3.5 border border-grey-border border-opacity-50 rounded-10 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                />
              </div>
              <AppBtn type="submit" size="md" disabled={addDomainReq.isLoading} className="px-4 text-sm">
                {addDomainReq.isLoading ? "Adding..." : "Add Domain"}
              </AppBtn>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default ConnectDomains;
