import React from "react";
import { DomainData } from "./custom-domains";
import <PERSON><PERSON>, { <PERSON>dal<PERSON>ody, ModalFooter } from "@/components/ui/modal";
import { AppBtn } from "@/components/ui/buttons";
import { RequestInterface } from "@/api/utils";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  domain: DomainData;
  removeDomainReq: RequestInterface<any>;
  removeDomain: () => void;
}

const ConfirmDomainDelete: React.FC<Props> = ({ show, toggle, domain, removeDomainReq, removeDomain }) => {
  return (
    <Modal show={show} toggle={toggle} title="Remove Domain" size="sm" className="z-[1000]">
      <form className="flex flex-col flex-auto overflow-hidden">
        <ModalBody>
          <div className="text-center">
            <p className="text-sm text-grey-subtext mt-1 max-w-[360px] mx-auto">
              Are you sure you want to remove <strong>{domain?.domain}</strong>? This action cannot be undone. Any SSL
              certificates associated with this domain will be revoked.
            </p>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="grid grid-cols-2 gap-3 w-full">
            <AppBtn size="lg" onClick={() => toggle(false)} color="neutral">
              Cancel
            </AppBtn>
            <AppBtn size="lg" onClick={removeDomain} disabled={removeDomainReq?.isLoading} color="danger">
              {removeDomainReq?.isLoading ? "Removing..." : "Yes, Remove"}
            </AppBtn>
          </div>
        </ModalFooter>
      </form>
    </Modal>
  );
};

export default ConfirmDomainDelete;
