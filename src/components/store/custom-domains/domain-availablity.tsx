import React from "react";
import DomainCard from "./domain-card";

const DomainAvailability: React.FC<{
  domain: string;
  price?: number;
  currency?: string;
  available: boolean;
  onBuy?: VoidFunction;
  actionDisabled?: boolean;
  selectedDomain?: string;
}> = ({ domain, price, currency, available, onBuy, actionDisabled, selectedDomain }) => {
  if (available) {
    return (
      <DomainCard
        className="mt-3.75"
        domain={domain}
        price={price!}
        currency={currency!}
        available={available}
        main
        onBuy={onBuy}
        actionDisabled={actionDisabled}
        selectedDomain={selectedDomain}
      />
    );
  }

  return (
    <div className="bg-red-500 bg-opacity-10 py-3 px-3 rounded-10 flex items-center justify-between mt-3.75">
      <div className="flex items-center gap-1 w-full">
        {/* prettier ignore */}
        <svg className="w-5" viewBox="0 0 23 22" fill="none">
          <path
            d="M11.4997 1.83301C6.44884 1.83301 2.33301 5.94884 2.33301 10.9997C2.33301 16.0505 6.44884 20.1663 11.4997 20.1663C16.5505 20.1663 20.6663 16.0505 20.6663 10.9997C20.6663 5.94884 16.5505 1.83301 11.4997 1.83301ZM14.5797 13.108C14.8455 13.3738 14.8455 13.8138 14.5797 14.0797C14.4422 14.2172 14.268 14.2813 14.0938 14.2813C13.9197 14.2813 13.7455 14.2172 13.608 14.0797L11.4997 11.9713L9.39134 14.0797C9.25384 14.2172 9.07967 14.2813 8.90551 14.2813C8.73134 14.2813 8.55717 14.2172 8.41967 14.0797C8.15384 13.8138 8.15384 13.3738 8.41967 13.108L10.528 10.9997L8.41967 8.89134C8.15384 8.62551 8.15384 8.18551 8.41967 7.91967C8.68551 7.65384 9.12551 7.65384 9.39134 7.91967L11.4997 10.028L13.608 7.91967C13.8738 7.65384 14.3138 7.65384 14.5797 7.91967C14.8455 8.18551 14.8455 8.62551 14.5797 8.89134L12.4713 10.9997L14.5797 13.108Z"
            fill="#BF0637"
          />
        </svg>
        <span className="text-xs md:text-1xs text-black-muted inline-block whitespace-nowrap overflow-ellipsis overflow-hidden w-full">
          <b className="text-black-secondary">{domain}</b> is currently unavailable
        </span>
      </div>
    </div>
  );
};

export default DomainAvailability;
