import { useState, useEffect, useRef } from "react";
import { AppBtn } from "../../ui/buttons";
import { useRequest } from "../../../api/utils";
import { FormikProps } from "formik";
import { ConfigurationForm } from "../store-configurations/index";
import { CheckDomain, InitiateDomainPurchase } from "../../../api/stores";
import Portal from "@/components/portal";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { useModals } from "@/components/hooks/useModals";
import ErrorLabel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";
import ContentCopy from "./content-copy";
import Table, { TableBody, TableHead, TableHeadItem } from "@/components/ui/table";
import authContext from "../../../contexts/auth-context";
import { actionIsAllowed, SCOPES } from "../../../assets/js/utils/permissions";
import ErrorBox from "../../ui/error";
import Badge from "@/components/ui/badge";
import useTabs from "@/components/hooks/useTabs";
import MakePayments from "@/components/make-payments";
import { DomainPurchase, PAYMENT_TYPES } from "@/assets/interfaces";
import { isDomainValid } from "@/assets/js/utils/functions";
import DomainCard from "./domain-card";
import classNames from "classnames";
import DomainAvailability from "./domain-availablity";
import { InputField, InputWithAddon } from "@/components/ui/form-elements";
import { toast } from "react-hot-toast";
import useSteps from "@/components/hooks/useSteps";
import DomainPurchaseStatus from "./purchase-status";

export interface DomainData {
  domain: string;
  available: boolean;
  price: number;
  currency: string;
  alternatives?: DomainData[];
  recommended?: DomainData[];
}

interface Props {}

export const BuyDomains: React.FC<Props> = () => {
  const { subscription } = authContext.useContainer();
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchError, setSearchError] = useState("");
  const [selectedDomain, setSelectedDomain] = useState<string>(null);
  const [domainToBuy, setDomainToBuy] = useState<DomainPurchase>(null);

  const { steps, step, stepIndex, changeStep } = useSteps(["intiate_purchase", "status"], 0);
  const { modals, toggleModal } = useModals(["buy_domain"]);
  const { tabs, switchTab, switchByKey, acitveKey, active, activeValue } = useTabs(
    ["Recommended domains", "Other domains"],
    0
  );

  const initiatePurchase = useRequest(InitiateDomainPurchase);
  const checkDomainReq = useRequest(CheckDomain);

  const domainData = checkDomainReq.response?.data ?? null;
  const showAlternativesTabs = domainData?.alternatives.length > 0 && domainData?.recommended.length > 0;

  let canManageCustomDomains = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_CUSTOM_DOMAINS,
  });

  const handleBuyDomain = async (domain: DomainData) => {
    setSelectedDomain(domain.domain);
    const [response, error] = await initiatePurchase.makeRequest({ domain: domain.domain });
    if (response) {
      setDomainToBuy(response?.data);
      toggleModal("buy_domain");
    } else {
      toast.error(error?.message ?? "We couldn't initiate the purchase, please try again later");
    }

    scrollToTop();
  };

  const scrollToTop = () => {
    const element = document.querySelector(".store-config");
    if (element) {
      element.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleBlur = async () => {
    const trimmedDomain = searchQuery.trim();
    if (!trimmedDomain) return;
    else if (!validateDomain(trimmedDomain)) {
      setSearchError("Please enter a valid domain name, e.g. yourbusiness.com");
      return;
    }

    const [response, error] = await checkDomainReq.makeRequest({ domain: trimmedDomain });
  };

  if (!canManageCustomDomains) {
    return (
      <ErrorBox
        title="Upgrade required"
        message="Please upgrade to business plus plan to use custom domains on your store"
      >
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  if (step === "status") {
    return <DomainPurchaseStatus domainPurchaseId={domainToBuy?.id} />;
  }

  return (
    <>
      <div className="mb-8 md:mb-10">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-orange-500 text-white m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg width="50%" viewBox="0 0 31 30" fill="none">
            <path d="M27.7375 25.95L26.8375 25.05C27.3 24.35 27.575 23.5 27.575 22.6C27.575 20.125 25.575 18.125 23.1 18.125C20.625 18.125 18.625 20.125 18.625 22.6C18.625 25.075 20.625 27.075 23.1 27.075C24.0125 27.075 24.85 26.8 25.55 26.3375L26.45 27.2375C26.625 27.4125 26.8625 27.5 27.0875 27.5C27.325 27.5 27.55 27.4125 27.725 27.2375C28.0875 26.875 28.0875 26.3 27.7375 25.95Z" fill="currentColor" />
            <path d="M3.0377 18.2871C3.0377 18.3246 3.0127 18.3746 3.0127 18.4121C4.1627 20.7121 6.0377 22.5996 8.3377 23.7371C8.3752 23.7371 8.42519 23.7121 8.4627 23.7121C8.03769 22.2621 7.7127 20.7746 7.4752 19.2871C5.9752 19.0371 4.4877 18.7121 3.0377 18.2871Z" fill="currentColor" />
            <path d="M24.3376 8.0373C23.1626 5.5748 21.1751 3.5873 18.7251 2.4248C19.1751 3.9123 19.5501 5.4373 19.8001 6.96231C21.3251 7.21231 22.8501 7.5748 24.3376 8.0373Z" fill="currentColor" />
            <path d="M2.9126 8.03789C4.4126 7.58789 5.9376 7.21289 7.4626 6.96289C7.7126 5.47539 8.0251 4.00039 8.4501 2.55039C8.4126 2.55039 8.3626 2.52539 8.3251 2.52539C5.9751 3.68789 4.0626 5.65039 2.9126 8.03789Z" fill="currentColor" />
            <path d="M17.7751 6.7C17.4751 5.075 17.1001 3.45 16.5626 1.875C16.5376 1.7875 16.5376 1.7125 16.5251 1.6125C15.6001 1.3875 14.6251 1.25 13.6251 1.25C12.6126 1.25 11.6501 1.3875 10.7126 1.625C10.7001 1.7125 10.7126 1.7875 10.6876 1.8875C10.1626 3.4625 9.7751 5.075 9.4751 6.7C12.2376 6.4 15.0126 6.4 17.7751 6.7Z" fill="currentColor" />
            <path d="M7.2 8.97461C5.5625 9.27461 3.9625 9.66211 2.375 10.1871C2.2875 10.2121 2.2125 10.2121 2.125 10.2246C1.8875 11.1496 1.75 12.1246 1.75 13.1246C1.75 14.1371 1.8875 15.0996 2.125 16.0371C2.2125 16.0496 2.2875 16.0371 2.3875 16.0621C3.9625 16.5871 5.575 16.9746 7.2125 17.2746C6.9 14.5121 6.9 11.7371 7.2 8.97461Z" fill="currentColor" />
            <path d="M25.1251 10.2246C25.0376 10.2246 24.9626 10.2121 24.8626 10.1871C23.2876 9.66211 21.6626 9.27461 20.0376 8.97461C20.3501 11.7371 20.3501 14.5121 20.0376 17.2621C21.6626 16.9621 23.2876 16.5871 24.8626 16.0496C24.9501 16.0246 25.0251 16.0371 25.1251 16.0246C25.3501 15.0871 25.5001 14.1246 25.5001 13.1121C25.5001 12.1246 25.3626 11.1621 25.1251 10.2246Z" fill="currentColor" />
            <path d="M9.4751 19.5496C9.7751 21.1871 10.1501 22.7996 10.6876 24.3746C10.7126 24.4621 10.7001 24.5371 10.7126 24.6371C11.6501 24.8621 12.6126 24.9996 13.6251 24.9996C14.6251 24.9996 15.6001 24.8621 16.5251 24.6246C16.5376 24.5371 16.5376 24.4621 16.5626 24.3621C17.0876 22.7871 17.4751 21.1746 17.7751 19.5371C16.4001 19.6871 15.0126 19.7996 13.6251 19.7996C12.2376 19.7996 10.8501 19.6996 9.4751 19.5496Z" fill="currentColor" />
            <path d="M9.1875 8.6875C8.8125 11.6375 8.8125 14.6125 9.1875 17.575C12.1375 17.95 15.1125 17.95 18.075 17.575C18.45 14.625 18.45 11.65 18.075 8.6875C15.1125 8.3125 12.1375 8.3125 9.1875 8.6875Z" fill="currentColor" />
          </svg>
        </figure>
        <p className="text-center font-display text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Buy a
        </p>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto !leading-tight">
          New Domain
        </h2>
      </div>

      <div className="space-y-6">
        <ErrorLabel
          error={
            checkDomainReq.error
              ? checkDomainReq.error?.message ?? "We couldn't check the domain, please try again later"
              : ""
          }
        />

        <div className="bg-grey-fields-200 rounded-10 border border-grey-border border-opacity-50">
          <div className="p-4">
            <p className="text-1xs mt-1 mb-3 text-black-muted">
              Enter the domain you’d like to buy (e.g. storename.com, etc) to check it’s availability and pricing.
            </p>

            <h2 className="text-sm font-bold text-black mb-2">Domain Name</h2>
            <div
              className={`${
                domainData || checkDomainReq.isLoading ? "pb-4.5 border-b border-grey-border border-opacity-50" : ""
              }`}
            >
              <div className="flex gap-2">
                <InputWithAddon
                  placeholder="yourbusiness.com"
                  showAfter={true}
                  onBlur={handleBlur}
                  error={searchError}
                  name="domain"
                  value={searchQuery}
                  ref={inputRef}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    setSearchError(null);
                  }}
                  className="!bg-white rounded-15"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      (e.target as HTMLInputElement)?.blur();
                    }
                  }}
                >
                  {checkDomainReq.isLoading && (
                    <div className="rounded-15 h-full w-12 flex items-center justify-center border-l-0">
                      <div className="spinner spinner--sm text-primary-500"></div>
                    </div>
                  )}
                </InputWithAddon>
              </div>
              {searchQuery && searchQuery.trim() && isDomainValid(searchQuery) && domainData && (
                <DomainAvailability
                  domain={domainData?.domain}
                  price={domainData?.price}
                  currency={domainData?.currency}
                  available={domainData?.available}
                  onBuy={() => handleBuyDomain(domainData)}
                  actionDisabled={initiatePurchase.isLoading}
                  selectedDomain={selectedDomain}
                />
              )}
              {checkDomainReq.isLoading && <DomainLoadingCard main />}
            </div>

            {domainData && showAlternativesTabs && (
              <div className="flex space-x-1 my-3.75">
                {tabs.map((tab, i) => (
                  <button type="button" onClick={() => switchTab(i)} key={i}>
                    <Badge
                      size="lg"
                      uppercase={false}
                      text={tab}
                      color={active === i ? "primary" : "dark"}
                      className={classNames(
                        "cursor-pointer font-display transform transition-all duration-300 rounded-30",
                        active == i
                          ? "text-primary-500 bg-grey-input border border-primary-500"
                          : "bg-grey-fields-200 border border-transparent"
                      )}
                    />
                  </button>
                ))}
              </div>
            )}

            {(checkDomainReq.isLoading ? true : domainData && !showAlternativesTabs) && (
              <h3 className="text-1sm font-bold text-black mt-3.75 mb-1">Other Domains</h3>
            )}

            {domainData && domainData.recommended.length > 0 && (
              <AlternativeDomains
                domains={domainData.recommended}
                tabsShowing={showAlternativesTabs}
                isActive={active == 0}
                selectDomain={(domain) => handleBuyDomain(domain)}
                actionDisabled={initiatePurchase.isLoading}
                selectedDomain={selectedDomain}
              />
            )}

            {domainData && domainData.alternatives.length > 0 && (
              <AlternativeDomains
                domains={domainData.alternatives}
                tabsShowing={showAlternativesTabs}
                isActive={active == 1}
                selectDomain={(domain) => handleBuyDomain(domain)}
                actionDisabled={initiatePurchase.isLoading}
                selectedDomain={selectedDomain}
              />
            )}

            {checkDomainReq.isLoading && (
              <div className="flex flex-col gap-y-1">
                <DomainLoadingCard />
                <DomainLoadingCard />
                <DomainLoadingCard />
              </div>
            )}
          </div>
        </div>
      </div>
      {selectedDomain && (
        <Portal>
          <Modal
            show={modals.buy_domain.show}
            toggle={() => toggleModal("buy_domain")}
            title={`Buy ${selectedDomain}`}
            size="midi"
          >
            <ModalBody>
              <MakePayments
                paymentType={PAYMENT_TYPES.DOMAIN_PURCHASE}
                domain={domainToBuy}
                successMessage={`Domain purchased successfully, it'll be automatically linked to your store shortly`}
                handleSuccess={() => changeStep("status")}
                usedInModal={true}
              />
            </ModalBody>
          </Modal>
        </Portal>
      )}
    </>
  );
};

interface AlternativeDomainsProps {
  domains: DomainData[];
  tabsShowing: boolean;
  isActive: boolean;
  selectDomain: (domain: DomainData) => void;
  actionDisabled?: boolean;
  selectedDomain: string;
}

const AlternativeDomains = ({
  domains,
  tabsShowing,
  isActive,
  selectDomain,
  actionDisabled,
  selectedDomain,
}: AlternativeDomainsProps) => {
  const showList = tabsShowing ? isActive : true;

  if (!showList) return null;

  return (
    <div className="flex flex-col gap-y-1 mt-3.75">
      {domains.map((domain, _i) => (
        <DomainCard
          key={_i}
          domain={domain.domain}
          price={domain.price}
          currency={domain.currency}
          available={domain.available}
          onBuy={() => selectDomain(domain)}
          actionDisabled={actionDisabled}
          selectedDomain={selectedDomain}
        />
      ))}
    </div>
  );
};

const DomainLoadingCard = ({ main = false }: { main?: boolean }) => {
  return (
    <div className="flex mt-3.75 bg-white rounded-10 border border-grey-border border-opacity-50 p-3 justify-between items-center">
      <div className="flex flex-col gap-y-1">
        <div className="flex items-center mb-0.5">
          {main && <div className="bg-grey-loader h-5 w-5 rounded-full animate-pulse mr-2"></div>}
          <div className="bg-grey-loader h-4 w-40 rounded-full animate-pulse"></div>
        </div>
        <div className="bg-grey-loader h-3 w-20 rounded-full animate-pulse"></div>
      </div>
      <div className="bg-grey-loader h-8 w-20 rounded-lg animate-pulse"></div>
    </div>
  );
};

export default BuyDomains;

const validateDomain = (value: string) => {
  // Simple regex for domain validation (no subdomains)
  const domainRegex = /^(?!:\/\/)([a-zA-Z0-9-]{1,63}\.)+[a-zA-Z]{2,63}$/;
  return domainRegex.test(value);
};
