import { toCurrency } from "@/assets/js/utils/functions";
import { AppBtn } from "@/components/ui/buttons";
import React from "react";

const DomainCard: React.FC<{
  domain: string;
  price: number;
  currency: string;
  available: boolean;
  main?: boolean;
  className?: string;
  onBuy: () => void;
  actionDisabled?: boolean;
  selectedDomain?: string;
}> = ({ domain, price, currency, available, onBuy, main = false, className, actionDisabled, selectedDomain }) => {
  if (!available) {
    return null; // Render nothing if the domain is not available
  }

  return (
    <div
      className={`p-3 rounded-10 flex items-center justify-between my-1 bg-white border border-grey-border ${
        className ? className : ""
      }`}
    >
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-1">
          {main && (
            <>
              {/* prettier ignore */}
              <svg className="w-5" viewBox="0 0 23 23" fill="none">
                <path
                  d="M11.4997 2.33301C6.44884 2.33301 2.33301 6.44884 2.33301 11.4997C2.33301 16.5505 6.44884 20.6663 11.4997 20.6663C16.5505 20.6663 20.6663 16.5505 20.6663 11.4997C20.6663 6.44884 16.5505 2.33301 11.4997 2.33301ZM15.8813 9.39134L10.6838 14.5888C10.5555 14.7172 10.3813 14.7905 10.198 14.7905C10.0147 14.7905 9.84051 14.7172 9.71217 14.5888L7.11801 11.9947C6.85217 11.7288 6.85217 11.2888 7.11801 11.023C7.38384 10.7572 7.82384 10.7572 8.08967 11.023L10.198 13.1313L14.9097 8.41967C15.1755 8.15384 15.6155 8.15384 15.8813 8.41967C16.1472 8.68551 16.1472 9.11634 15.8813 9.39134Z"
                  fill="#39B588"
                />
              </svg>
            </>
          )}
          <span className="text-xs md:text-1xs text-black-muted inline-block whitespace-nowrap overflow-ellipsis overflow-hidden w-full">
            <b className="text-black-secondary">{domain}</b>{" "}
            {main && (available ? "is available" : "is currently unavailable")}
          </span>
        </div>
        {available && (
          <div className="text-xs text-black-muted font-medium flex items-center gap-1">
            <span>{toCurrency(price, currency)}/Year</span>
          </div>
        )}
      </div>
      {available && (
        <div className="flex gap-1 flex-end h-full items-center">
          <AppBtn
            type="button"
            size="sm"
            color={main ? "primary" : "neutral"}
            onClick={onBuy}
            className="px-4 text-sm ml-auto"
            disabled={actionDisabled}
          >
            <span className="text-xs">
              {selectedDomain === domain && actionDisabled ? "Please wait..." : "Buy Domain"}
            </span>
          </AppBtn>
        </div>
      )}
    </div>
  );
};

export default DomainCard;
