import React, { useEffect, useRef, useState } from "react";
import { UpdateStoreImagesParams } from "../../api/interfaces/stores.interface";
import { UpdateStoreDetails } from "../../api/stores";
import { useRequest } from "../../api/utils";
import { StoreRoles, Image, StoreInterface, FILE_TYPES } from "../../assets/interfaces";
import { getAvatarBg, handleImageSelectionFromFile } from "../../assets/js/utils/functions";
import { actionIsAllowed, SCOPES } from "../../assets/js/utils/permissions";
import useImageUploads from "../hooks/useImageUploads";
import { useModals } from "../hooks/useModals";
import LazyImage from "../lazy-image";
import MediaCarouselModal from "../products/image-carousel-modal";
import { toast } from "../ui/toast";

interface Props {
  store: StoreInterface;
  updateStore: (data: Partial<StoreInterface>) => void;
  userRole: StoreRoles;
}

enum ImageTypes {
  LOGO = "logo",
  HERO_IMAGE = "hero_image",
}

const StoreImages: React.FC<Props> = ({ store, updateStore, userRole }) => {
  const [heroImage, setHeroImage] = useState<Image>({
    src: null,
    name: null,
    lastModified: null,
    file: null,
    url: "",
    key: null,
  });
  const [storeLogo, setStoreLogo] = useState<Image>({
    src: null,
    name: null,
    lastModified: null,
    file: null,
    url: "",
    key: null,
  });
  const heroImageRef = useRef<HTMLInputElement>(null);
  const storeLogoRef = useRef<HTMLInputElement>(null);
  const [showingImages, setShowingImages] = useState<string[]>([]);
  const { modals, toggleModal } = useModals(["images"]);
  const [uploading, setUploading] = useState<ImageTypes>(null);
  const { isLoading, response, makeRequest, error } = useRequest<UpdateStoreImagesParams>(UpdateStoreDetails);

  const canUpdateStore = actionIsAllowed({
    userRole: userRole,
    permission: SCOPES.SETTINGS.UPDATE_STORE_DETAILS,
  });

  useImageUploads([heroImage], FILE_TYPES.STORES, (images: Image[]) => setHeroImage(images[0]));
  useImageUploads([storeLogo], FILE_TYPES.STORES, (images: Image[]) => setStoreLogo(images[0]));

  useEffect(() => {
    if (storeLogo.url) {
      toast.promise(() => saveImages({ id: store.id, logo: storeLogo.url }, ImageTypes.LOGO), getToastOptions("logo"));
    }
  }, [storeLogo.url]);

  useEffect(() => {
    if (heroImage.url) {
      toast.promise(
        () => saveImages({ id: store.id, hero_image: heroImage.url }, ImageTypes.HERO_IMAGE),
        getToastOptions("hero image")
      );
    }
  }, [heroImage.url]);

  async function saveImages(data: UpdateStoreImagesParams, type: ImageTypes) {
    const [res, err] = await makeRequest(data);

    setTimeout(() => {
      setUploading(null);

      if (err) {
        return Promise.reject(err);
      }

      updateStore({ [type]: res?.data[type] });

      return Promise.resolve(res);
    }, 1000);
  }

  function getToastOptions(image) {
    return {
      loading: {
        title: `Updating store ${image}`,
        message: "Please wait...",
      },
      success: {
        title: `Completed`,
        message: `Store ${image} successfully updated!`,
      },
      error: {
        title: "Failed",
        message: `We couldn't save your new store ${image}!`,
        actionText: "Retry",
        actionFunc: () => {},
      },
    };
  }

  function handleImageSelection(
    e: React.ChangeEvent<HTMLInputElement>,
    image: ImageTypes,
    imageData: Image,
    updateFun: (image: Image) => void,
    maxSize?: number
  ) {
    setUploading(image);
    handleImageSelectionFromFile({ e, images: imageData, saveImages: updateFun, maxSize });
  }

  const enlargeImage = (img: string) => {
    if (img) {
      setShowingImages([img]);
      toggleModal("images");
    }
  };

  return (
    <div className="w-full pb-10">
      <input
        type="file"
        name="hero-image"
        accept="image/*,.heic"
        ref={heroImageRef}
        className="hidden"
        onChange={(e) =>
          handleImageSelection(e, ImageTypes.HERO_IMAGE, heroImage, (image: Image) => setHeroImage(image), 2000)
        }
      />

      <input
        type="file"
        name="storeLogo"
        accept="image/*,.heic"
        ref={storeLogoRef}
        className="hidden"
        onChange={(e) => handleImageSelection(e, ImageTypes.LOGO, storeLogo, (image: Image) => setStoreLogo(image))}
      />
      <figure className={"w-full store-hero-image relative"}>
        {(heroImage?.src !== null || store?.hero_image) && (
          <LazyImage
            onClick={() => enlargeImage(heroImage.src ?? store?.hero_image)}
            src={heroImage.src ?? store?.hero_image}
            alt="Store hero image"
            className="absolute top-0 left-0 w-full h-full object-center object-cover cursor-pointer"
          />
        )}

        {canUpdateStore && (
          <button
            className="no-outline bg-white hover:bg-primary-80 shadow-card rounded-md flex items-center justify-center absolute right-5 bottom-5 text-black-secondary text-xs font-m px-3.75 py-2.5"
            onClick={() => heroImageRef.current.click()}
            disabled={uploading === "hero_image"}
          >
            {uploading === "hero_image" ? (
              <div className="spinner spinner--sm ml-1.5"></div>
            ) : (
              //prettier-ignore
              <svg className="w-3.75" viewBox="0 0 15 15" fill="none">
                <path d="M5.625 13.75H9.375C12.5 13.75 13.75 12.5 13.75 9.375V5.625C13.75 2.5 12.5 1.25 9.375 1.25H5.625C2.5 1.25 1.25 2.5 1.25 5.625V9.375C1.25 12.5 2.5 13.75 5.625 13.75Z" stroke="#332098" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M5.625 6.25C6.31536 6.25 6.875 5.69036 6.875 5C6.875 4.30964 6.31536 3.75 5.625 3.75C4.93464 3.75 4.375 4.30964 4.375 5C4.375 5.69036 4.93464 6.25 5.625 6.25Z" stroke="#332098" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M1.6687 11.8437L4.74995 9.77498C5.2437 9.44373 5.9562 9.48123 6.39995 9.86248L6.6062 10.0437C7.0937 10.4625 7.8812 10.4625 8.3687 10.0437L10.9687 7.81248C11.4562 7.39373 12.2437 7.39373 12.7312 7.81248L13.75 8.68748" stroke="#332098" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            )}
            <span className="inline-block ml-1.25 font-semibold text-primary-500">Change cover image</span>
          </button>
        )}

        <figure
          className={`h-16 w-16 md:h-20 md:w-20 rounded-full z-20 shadow-sm bottom-0 left-5 sm:left-6.25 lg:left-7.5 absolute translate-y-10 flex items-center justify-center text-3xl text-white font-medium font-action ${
            !store.logo ? getAvatarBg(store.name) : "bg-grey-loader"
          }`}
        >
          {storeLogo?.src !== null || store?.logo ? (
            <LazyImage
              onClick={() => enlargeImage(storeLogo?.src ?? store?.logo)}
              src={storeLogo.src ?? store?.logo}
              alt=""
              className="h-full w-full object-cover rounded-full cursor-pointer"
              loaderClasses="rounded-full"
            />
          ) : (
            store.name.charAt(0)
          )}
          {canUpdateStore && (
            <button
              className="h-7 md:h-8 rounded-full bg-primary-80 flex items-center justify-center absolute bottom-0 left-[50%] text-primary-500 px-2.5"
              onClick={() => storeLogoRef.current.click()}
              disabled={uploading === "logo"}
            >
              {uploading === "logo" ? (
                <div className="spinner spinner--sm"></div>
              ) : (
                <div className="flex items-center space-x-0.5 flex-nowrap">
                  <svg width="14" viewBox="0 0 12 12" fill="none" className="flex-shrink-0">
                    <path
                      d="M8.5 1.41421C8.63132 1.28289 8.78722 1.17872 8.9588 1.10765C9.13038 1.03658 9.31428 1 9.5 1C9.68572 1 9.86962 1.03658 10.0412 1.10765C10.2128 1.17872 10.3687 1.28289 10.5 1.41421C10.6313 1.54554 10.7355 1.70144 10.8066 1.87302C10.8776 2.0446 10.9142 2.2285 10.9142 2.41421C10.9142 2.59993 10.8776 2.78383 10.8066 2.95541C10.7355 3.12699 10.6313 3.28289 10.5 3.41421L3.75 10.1642L1 10.9142L1.75 8.16421L8.5 1.41421Z"
                      stroke="#332098"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <span className="font-medium text-1xs whitespace-nowrap">Change</span>
                </div>
              )}
            </button>
          )}
        </figure>
      </figure>

      <MediaCarouselModal
        images={showingImages}
        show={modals.images.show}
        toggle={() => toggleModal("images")}
        title="Store Image"
      />
    </div>
  );
};

export default StoreImages;
