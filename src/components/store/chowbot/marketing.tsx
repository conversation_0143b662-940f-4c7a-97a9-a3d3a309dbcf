import { UpdateAutoCheckInConfiguration, UpdateChowdeckConfiguration } from "@/api";
import { UpdateAutoCheckInConfigParams, UpdateChowdeckConfigParams } from "@/api/interfaces";
import { useRequest } from "@/api/utils";
import { StoreInterface } from "@/assets/interfaces";
import { getFieldvalues } from "@/assets/js/utils/functions";
import { AppBtn } from "@/components/ui/buttons";
import { InputField } from "@/components/ui/form-elements";
import TextArea from "@/components/ui/form-elements/textarea";
import { useFormik } from "formik";
import * as Yup from "yup";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import Toggle from "../../ui/toggle";

interface Props {
  store: StoreInterface;
  updateStore: (data: Partial<StoreInterface>) => void;
}

const MarketingConfiguration: React.FC<Props> = ({ store, updateStore }) => {
  const { response, makeRequest, isLoading, error } =
    useRequest<UpdateAutoCheckInConfigParams>(UpdateAutoCheckInConfiguration);

  const form = useFormik<StoreInterface["configuration"]["auto_customer_check_in"]>({
    initialValues: {
      enabled: store.configuration.auto_customer_check_in?.enabled ?? false,
      days: store.configuration.auto_customer_check_in?.days ?? 2,
      message:
        store.configuration.auto_customer_check_in?.message ??
        "🌟 Just a friendly reminder that we're here whenever you're ready to treat yourself again. Feel free to browse our latest offerings or simply reorder your favorites.",
    },
    onSubmit: async (values) => {
      // scroll to top so response message shows
      const [response, error] = await makeRequest({
        enabled: values.enabled,
        message: values.message,
        days: values.days,
        store_id: store.id,
      });
      const formEl = document.querySelector("#store-chowbot-settings");
      formEl.scrollTo(0, 0);
    },
    validationSchema,
  });

  return (
    <form onSubmit={form.handleSubmit} id="store-chowbot-settings">
      <div className="mb-8 md:mb-10">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 text-white m-auto flex items-center justify-center">
          {/* prettier-ignore */}
          <svg width="40%" className="text-white" viewBox="0 0 24 24" fill="none">
                <path d="M5 13.5C4.06 13.5 3.19 13.83 2.5 14.38C1.58 15.11 1 16.24 1 17.5C1 19.71 2.79 21.5 5 21.5C6.01 21.5 6.93 21.12 7.64 20.5C8.47 19.77 9 18.7 9 17.5C9 15.29 7.21 13.5 5 13.5ZM6 17.75C6 18.01 5.86 18.26 5.64 18.39L4.39 19.14C4.27 19.22 4.13 19.25 4 19.25C3.75 19.25 3.5 19.12 3.36 18.89C3.15 18.53 3.26 18.07 3.62 17.86L4.51 17.33V16.25C4.51 15.84 4.85 15.5 5.26 15.5C5.67 15.5 6 15.84 6 16.25V17.75Z" fill="currentColor"/>
                <path d="M17.25 2.42969H7.75C4.9 2.42969 3 4.32969 3 7.17969V11.6397C3 11.9897 3.36 12.2397 3.7 12.1497C4.12 12.0497 4.55 11.9997 5 11.9997C7.86 11.9997 10.22 14.3197 10.48 17.1297C10.5 17.4097 10.73 17.6297 11 17.6297H11.55L15.78 20.4497C16.4 20.8697 17.25 20.4097 17.25 19.6497V17.6297C18.67 17.6297 19.86 17.1497 20.69 16.3297C21.52 15.4897 22 14.2997 22 12.8797V7.17969C22 4.32969 20.1 2.42969 17.25 2.42969ZM15.83 10.8097H9.17C8.78 10.8097 8.46 10.4897 8.46 10.0997C8.46 9.69969 8.78 9.37969 9.17 9.37969H15.83C16.22 9.37969 16.54 9.69969 16.54 10.0997C16.54 10.4897 16.22 10.8097 15.83 10.8097Z" fill="currentColor"/>
            </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          Marketing Configurations
        </h2>
        <span className="block text-center text-1xs text-grey-subtext">Set your chowbot marketing preferences</span>
      </div>

      <ErrorLabel error={error?.message} />
      <SuccessLabel message={response ? "Marketing settings updated successfully!" : null} />
      <div className="flex flex-col  w-full">
        <div className="flex items-center justify-between w-full">
          <div className="flex gap-2.5 sm:gap-3.75 ">
            <div>
              <h2 className="font-bold text-base sm:text-[17px] lg:text-lg text-black">
                Enable auto customer check-in
              </h2>
              <p className="text-xs sm:text-1xs text-black-muted mt-1">
                Automatically check-in with customers that have ordered from your store in the last 2 days or more
              </p>
            </div>
          </div>
          <div className="pl-5 lg:pl-12">
            <Toggle
              intialState={form.values?.enabled ?? false}
              onChange={(state) => form.setFieldValue("enabled", state)}
            />
          </div>
        </div>
        {form.values.enabled && (
          <>
            <div className=" w-full border-grey-divider pt-5">
              <h2 className="font-bold text-base sm:text-[17px] lg:text-lg text-black">Check-in message</h2>
              <div className="rounded-15 mt-2 bg-grey-fields-100">
                <div className="p-3 text-dark text-xs">
                  Hi *customerName* <br />
                  <br /> We hope you enjoyed your last order with us at Tasty Kitchen! <br />
                  <br />
                  {form.values.message}
                </div>
                <TextArea
                  label="Custom check-in message"
                  rows={2}
                  maxLength={150}
                  {...getFieldvalues("message", form)}
                />
              </div>
            </div>
            <InputField
              type="number"
              label="No of days before auto check-in"
              {...getFieldvalues("days", form, "number")}
            />
          </>
        )}
      </div>

      <AppBtn isBlock className="mt-7.5" type="submit" disabled={isLoading} size="lg">
        {isLoading ? "Updating..." : "Update Configurations"}
      </AppBtn>
    </form>
  );
};

export default MarketingConfiguration;

const validationSchema = Yup.object().shape({
  enabled: Yup.boolean().required(),
  message: Yup.string().when("enabled", {
    is: true,
    then: Yup.string().required("Messages is required"),
    otherwise: null,
  }),
  days: Yup.number().when("enabled", {
    is: true,
    then: Yup.number().required("The no of days is required").min(2, "The no of days must be at least 2"),
    otherwise: null,
  }),
});
