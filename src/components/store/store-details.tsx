import Link from "next/link";
import React from "react";
import { subdomainStoreLink, toAppUrl } from "../../assets/js/utils/functions";
import { actionIsAllowed, SCOPES } from "../../assets/js/utils/permissions";
import { Plan, StoreInterface, StoreRoles } from "../../assets/interfaces";
import useCopyClipboard from "../hooks/useCopyClipboard";
import { useModals } from "../hooks/useModals";
import EditStoreLinkModal from "./edit-store-link-modal";
import ContentWithCopy from "../ui/content-with-copy";
import Portal from "../portal";
import router from "next/router";
import Badge from "../ui/badge";

interface Props {
  store: StoreInterface;
  subscription: { lastPaymentDate: string; nextPaymentDate: string; status: string; plan: Plan };
  updateStore: (stores: Partial<StoreInterface>) => void;
  userRole: StoreRoles;
}

const StoreDetails: React.FC<Props> = ({ store, subscription, updateStore, userRole }) => {
  const { modals, toggleModal } = useModals(["change_plan", "edit_store_link"]);
  const [copied, copy] = useCopyClipboard(toAppUrl(store?.slug), {
    successDuration: 1000,
  });

  const canCustomizeLink = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_CUSTOMIZE_LINK,
  });

  const canUpdateStore = actionIsAllowed({
    userRole: userRole,
    permission: SCOPES.SETTINGS.UPDATE_STORE_DETAILS,
  });

  const canManageSubscription = actionIsAllowed({
    userRole: userRole,
    permission: SCOPES.SUBSCRIPTIONS.MANAGE_SUBSCRIPTIONS,
  });

  return (
    <>
      <div className="bg-grey-fields-100 mt-3.75 rounded-15">
        <div className="flex items-start border-b border-grey-border border-opacity-50 p-3.75 sm:p-5">
          <figure
            className={`bg-white text-accent-orange-500 rounded-full w-10 h-10 sm:w-12.5 sm:h-12.5 flex flex-shrink-0 items-center justify-center mr-3.75`}
          >
            <svg className="w-6" viewBox="0 0 24 25" fill="none">
              <path
                d="M12 2.5C6.48 2.5 2 6.98 2 12.5C2 18.02 6.48 22.5 12 22.5C17.52 22.5 22 18.02 22 12.5C22 6.98 17.52 2.5 12 2.5ZM8.65 15.27C8.54 15.57 8.25 15.76 7.95 15.76C7.86 15.76 7.78 15.75 7.69 15.71C6.88 15.41 6.2 14.82 5.77 14.05C4.77 12.25 5.39 9.9 7.14 8.81L9.48 7.36C10.34 6.83 11.35 6.67 12.31 6.92C13.27 7.17 14.08 7.8 14.57 8.68C15.57 10.48 14.95 12.83 13.2 13.92L12.94 14.11C12.6 14.35 12.13 14.27 11.89 13.94C11.65 13.6 11.73 13.13 12.06 12.89L12.37 12.67C13.49 11.97 13.87 10.52 13.26 9.41C12.97 8.89 12.5 8.52 11.94 8.37C11.38 8.22 10.79 8.31 10.28 8.63L7.92 10.09C6.84 10.76 6.46 12.21 7.07 13.33C7.32 13.78 7.72 14.13 8.2 14.31C8.59 14.45 8.79 14.88 8.65 15.27ZM16.92 16.15L14.58 17.6C13.99 17.97 13.33 18.15 12.66 18.15C12.36 18.15 12.05 18.11 11.75 18.03C10.79 17.78 9.98 17.15 9.5 16.27C8.5 14.47 9.12 12.12 10.87 11.03L11.13 10.84C11.47 10.6 11.94 10.68 12.18 11.01C12.42 11.35 12.34 11.82 12.01 12.06L11.7 12.28C10.58 12.98 10.2 14.43 10.81 15.54C11.1 16.06 11.57 16.43 12.13 16.58C12.69 16.73 13.28 16.64 13.79 16.32L16.13 14.87C17.21 14.2 17.59 12.75 16.98 11.63C16.73 11.18 16.33 10.83 15.85 10.65C15.46 10.51 15.26 10.08 15.41 9.69C15.55 9.3 15.99 9.1 16.37 9.25C17.18 9.55 17.86 10.14 18.29 10.91C19.28 12.71 18.67 15.06 16.92 16.15Z"
                fill="currentColor"
              />
            </svg>
          </figure>

          <div className="w-full overflow-hidden">
            <span className="text-1xs text-black-secondary block mb-1 font-medium">Store Link</span>
            <div className="py-1.25 w-full border-b border-grey-border border-opacity-50">
              <Badge text="Old Format" color="orange" greyBg={false} className="mb-0.75" />
              <ContentWithCopy text={toAppUrl(store?.slug)} className="!flex w-full overflow-hidden">
                <h6 className="font-semibold text-sm sm:text-1sm mb-0.5 text-primary-500 flex-1 w-full overflow-hidden overflow-ellipsis whitespace-nowrap">
                  {toAppUrl(store?.slug, false)}{" "}
                </h6>
              </ContentWithCopy>
            </div>
            <div className="mt-1.5 py-1.25 w-full border-b border-grey-border border-opacity-50">
              <Badge color="orange" text="New Format" greyBg={false} className="mb-0.75" />
              <ContentWithCopy text={subdomainStoreLink(store?.slug, true)} className="!flex w-full overflow-hidden">
                <h6 className="font-semibold text-sm sm:text-1sm mb-0.5 text-primary-500 flex-1 w-full overflow-hidden overflow-ellipsis whitespace-nowrap">
                  {subdomainStoreLink(store?.slug)}{" "}
                </h6>
              </ContentWithCopy>
            </div>
            {canUpdateStore && (
              <div className="inline-flex items-center mt-1.5 font-medium">
                <span className={`text-1xs ${canCustomizeLink ? "text-black-secondary" : "text-accent-red-500"}`}>
                  {canCustomizeLink ? "Eligible for custom links:" : "Change your plan to customize link"}
                </span>

                {canCustomizeLink && (
                  <button
                    onClick={() => toggleModal("edit_store_link")}
                    className="flex items-center text-primary-500 ml-1.25 font-medium text-1xs"
                  >
                    Edit
                    {/* prettier-ignore */}
                    <svg className="w-3.5 ml-1"viewBox="0 0 13 14" fill="none" >
                      <path d="M7.18252 2.44997L2.73544 7.15706C2.56752 7.33581 2.40502 7.68789 2.37252 7.93164L2.1721 9.68664C2.10169 10.3204 2.55669 10.7537 3.18502 10.6454L4.92919 10.3475C5.17294 10.3041 5.51419 10.1254 5.6821 9.94123L10.1292 5.23414C10.8984 4.42164 11.245 3.49539 10.0479 2.36331C8.85627 1.24206 7.95169 1.63747 7.18252 2.44997Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M6.44043 3.23541C6.67335 4.73041 7.88668 5.87333 9.39251 6.025" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M1.625 12.4167H11.375" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
        <div className="flex items-start p-3.75 sm:p-5">
          <figure
            className={`bg-white text-accent-green-500 rounded-full w-10 h-10 sm:w-12.5 sm:h-12.5 flex flex-shrink-0 items-center justify-center mr-3.75`}
          >
            {/* prettier-ignore */}
            <svg className="w-6" viewBox="0 0 24 24" fill="none">
              <path d="M19 15C16.79 15 15 16.79 15 19C15 19.75 15.21 20.46 15.58 21.06C16.27 22.22 17.54 23 19 23C20.46 23 21.73 22.22 22.42 21.06C22.79 20.46 23 19.75 23 19C23 16.79 21.21 15 19 15ZM21.07 18.57L18.94 20.54C18.8 20.67 18.61 20.74 18.43 20.74C18.24 20.74 18.05 20.67 17.9 20.52L16.91 19.53C16.62 19.24 16.62 18.76 16.91 18.47C17.2 18.18 17.68 18.18 17.97 18.47L18.45 18.95L20.05 17.47C20.35 17.19 20.83 17.21 21.11 17.51C21.39 17.81 21.37 18.28 21.07 18.57Z" fill="currentColor"/>
              <path d="M22 7.55002V8.00002C22 8.55002 21.55 9.00002 21 9.00002H3C2.45 9.00002 2 8.55002 2 8.00002V7.54002C2 5.25002 3.85 3.40002 6.14 3.40002H17.85C20.14 3.40002 22 5.26002 22 7.55002Z" fill="currentColor"/>
              <path d="M2 11.5V16.46C2 18.75 3.85 20.6 6.14 20.6H12.4C12.98 20.6 13.48 20.11 13.43 19.53C13.29 18 13.78 16.34 15.14 15.02C15.7 14.47 16.39 14.05 17.14 13.81C18.39 13.41 19.6 13.46 20.67 13.82C21.32 14.04 22 13.57 22 12.88V11.49C22 10.94 21.55 10.49 21 10.49H3C2.45 10.5 2 10.95 2 11.5ZM8 17.25H6C5.59 17.25 5.25 16.91 5.25 16.5C5.25 16.09 5.59 15.75 6 15.75H8C8.41 15.75 8.75 16.09 8.75 16.5C8.75 16.91 8.41 17.25 8 17.25Z" fill="currentColor"/>
            </svg>
          </figure>
          <div className="flex-1">
            <span className="text-1xs text-black-secondary mb-1 block font-medium">Subscription</span>
            <div className="flex items-center justify-between w-full">
              <h6 className="font-display font-bold capitalize">{subscription?.plan?.name} plan</h6>
              {canManageSubscription && (
                <button
                  onClick={() => router.push("/my-store/subscription")}
                  className="flex items-center text-1xs text-primary-500 font-medium px-2 bg-white py-1.25 rounded-20"
                >
                  Manage Subscription
                  {/* prettier-ignore */}
                  <svg className="w-3 ml-0.75 mt-px" viewBox="0 0 12 12" fill="none" >
                    <path d="M3.51477 8.82849L9.17162 3.17164" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M9.17151 8.82849L9.17151 3.17164L3.51465 3.17164" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
      <Portal>
        <EditStoreLinkModal
          show={modals?.edit_store_link.show}
          toggle={() => toggleModal("edit_store_link")}
          {...{ updateStore, store }}
        />
      </Portal>
    </>
  );
};

export default StoreDetails;

/* 
 <div className="w-full rounded-10 px-4 sm:px-5 lg:px-6.25 py-3 lg:py-4 mt-8 border-grey-border border-opacity-50 border flex items-start md:items-center flex-col md:flex-row space-y-2.5 md:space-y-0 md:space-x-4 lg:space-x-5">
        <div className="flex items-center md:max-w-[80%] w-full">
          <figure
            className={`h-12.5 w-12.5 sm:h-15 sm:w-15 rounded-10 flex items-center justify-center flex-shrink-0 linear-gradient-primary`}
          >
             <svg className="w-5 sm:w-6" viewBox="0 0 14 14" fill="none">
              <path d="M5.83301 7.58348C6.08352 7.91838 6.40313 8.1955 6.77016 8.39602C7.13719 8.59655 7.54305 8.71579 7.96022 8.74567C8.37739 8.77555 8.7961 8.71536 9.18796 8.56918C9.57982 8.42301 9.93566 8.19427 10.2313 7.89848L11.9813 6.14848C12.5126 5.59839 12.8066 4.86163 12.8 4.09689C12.7933 3.33215 12.4866 2.60061 11.9458 2.05984C11.405 1.51906 10.6735 1.21232 9.90876 1.20568C9.14402 1.19903 8.40726 1.49301 7.85717 2.02431L6.85384 3.02181" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M8.16629 6.41672C7.91578 6.08181 7.59617 5.80469 7.22914 5.60417C6.86211 5.40364 6.45625 5.2844 6.03908 5.25452C5.62191 5.22465 5.2032 5.28484 4.81134 5.43101C4.41948 5.57719 4.06364 5.80593 3.76796 6.10172L2.01796 7.85172C1.48667 8.40181 1.19268 9.13856 1.19933 9.9033C1.20597 10.668 1.51272 11.3996 2.05349 11.9404C2.59426 12.4811 3.3258 12.7879 4.09054 12.7945C4.85528 12.8012 5.59204 12.5072 6.14213 11.9759L7.13963 10.9784" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </figure>
          <div className="ml-2.5 pt-1 flex-1">
            <span className="text-1xs sm:text-sm text-dark inline-block">Store Link</span>
            <div className="flex items-center -mt-1 w-full cursor-pointer" onClick={() => copy()}>
              <h4 className="text-sm font-semibold text-black max-w-[200px] lg:max-w-[300px] overflow-hidden overflow-ellipsis whitespace-nowrap">
                {copied ? "Copied!" : toAppUrl(store?.slug, false)}
              </h4>
              <button
                type="button"
                className="h-5 w-5 rounded-5 bg-primary-80 hover:bg-primary-100 flex items-center justify-center ml-1.25"
              >
                <svg width="12" viewBox="0 0 10 10" fill="none">
                  <path
                    d="M8.33333 3.75H4.58333C4.1231 3.75 3.75 4.1231 3.75 4.58333V8.33333C3.75 8.79357 4.1231 9.16667 4.58333 9.16667H8.33333C8.79357 9.16667 9.16667 8.79357 9.16667 8.33333V4.58333C9.16667 4.1231 8.79357 3.75 8.33333 3.75Z"
                    stroke="#332098"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M2.08337 6.25016H1.66671C1.44569 6.25016 1.23373 6.16237 1.07745 6.00608C0.921171 5.8498 0.833374 5.63784 0.833374 5.41683V1.66683C0.833374 1.44582 0.921171 1.23385 1.07745 1.07757C1.23373 0.921293 1.44569 0.833496 1.66671 0.833496H5.41671C5.63772 0.833496 5.84968 0.921293 6.00596 1.07757C6.16224 1.23385 6.25004 1.44582 6.25004 1.66683V2.0835"
                    stroke="#332098"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
            </div>
            {canUpdateStore && (
              <div className="flex items-center flex-shrink-0">
                <span className="text-placeholder text-1xs pt-0.5 flex-shrink-0">
                  {canCustomizeLink ? "Eligible for custom links:" : "Change your plan to get custom link"}
                </span>
                {canCustomizeLink && (
                  <button
                    className="flex items-center text-primary-500 font-semibold text-1xs ml-1 flex-shrink-0"
                    onClick={() => toggleModal("edit_store_link")}
                  >
                    <span className="pt-0.5 inline-block">Edit Link</span>
                    <svg width="10" viewBox="0 0 12 12" fill="none" className="ml-0.5">
                      <path
                        d="M8.5 1.41421C8.63132 1.28289 8.78722 1.17872 8.9588 1.10765C9.13038 1.03658 9.31428 1 9.5 1C9.68572 1 9.86962 1.03658 10.0412 1.10765C10.2128 1.17872 10.3687 1.28289 10.5 1.41421C10.6313 1.54554 10.7355 1.70144 10.8066 1.87302C10.8776 2.0446 10.9142 2.2285 10.9142 2.41421C10.9142 2.59993 10.8776 2.78383 10.8066 2.95541C10.7355 3.12699 10.6313 3.28289 10.5 3.41421L3.75 10.1642L1 10.9142L1.75 8.16421L8.5 1.41421Z"
                        stroke="#332098"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="1.5"
                      />
                    </svg>
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center w-full">
          <figure
            className={`h-12.5 w-12.5 sm:h-15 sm:w-15 rounded-10 flex items-center justify-center flex-shrink-0 linear-gradient-danger`}
          >
            <svg className="w-5 sm:w-6" viewBox="0 0 12 12" fill="none">
              <path d="M10 6V11H2V6" stroke="#fff" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M11 3.5H1V6H11V3.5Z" stroke="#fff" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M6 11V3.5" stroke="#fff" strokeLinecap="round" strokeLinejoin="round" />
              <path
                d="M6 3.5H3.75C3.41848 3.5 3.10054 3.3683 2.86612 3.13388C2.6317 2.89946 2.5 2.58152 2.5 2.25C2.5 1.91848 2.6317 1.60054 2.86612 1.36612C3.10054 1.1317 3.41848 1 3.75 1C5.5 1 6 3.5 6 3.5Z"
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M6 3.5H8.25C8.58152 3.5 8.89946 3.3683 9.13388 3.13388C9.3683 2.89946 9.5 2.58152 9.5 2.25C9.5 1.91848 9.3683 1.60054 9.13388 1.36612C8.89946 1.1317 8.58152 1 8.25 1C6.5 1 6 3.5 6 3.5Z"
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </figure>
          <div className="ml-2.5 pt-1">
            <span className="text-1xs sm:text-sm text-dark inline-block">Store Plan</span>
            <div className="flex items-center -mt-1">
              <h4 className="text-sm font-bold text-black">{subscription?.plan?.name}</h4>
            </div>
            {canManageSubscription && (
              <div className="flex items-center">
                <Link href="/my-store/change-plan">
                  <a className="flex items-center text-primary-500 font-semibold text-1xs flex-shrink-0">
                    <span className="pt-0.5 inline-block">Change Plan</span>
                    <svg width="12" viewBox="0 0 12 12" fill="none" className="ml-0.5">
                      <path d="M8.00024 2.5H4.00024C2.06725 2.5 0.500244 4.067 0.500244 6C0.500244 7.933 2.06725 9.5 4.00024 9.5H8.00024C9.93324 9.5 11.5002 7.933 11.5002 6C11.5002 4.067 9.93324 2.5 8.00024 2.5Z" stroke="#332098" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M8.00024 7.5C8.82867 7.5 9.50024 6.82843 9.50024 6C9.50024 5.17157 8.82867 4.5 8.00024 4.5C7.17182 4.5 6.50024 5.17157 6.50024 6C6.50024 6.82843 7.17182 7.5 8.00024 7.5Z" stroke="#332098" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                    </svg>
                  </a>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>


*/
