import Modal, { ModalBody, ModalProps } from "@/components/ui/modal";
import { useEffect, useState } from "react";
import { ProgressStep } from "../hooks/useProgess";

interface Props extends Partial<ModalProps> {
  steps: ProgressStep[];
  currentStep: number;
  isEmbedded?: boolean;
  stepCount?: number;
}

const ProgressModal: React.FC<Props> = ({ show, toggle, steps, title, currentStep, isEmbedded, stepCount }) => {
  const [progress, setProgress] = useState(0);
  const unitStepPercentage = 100.0 / steps.length;
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout>();
  useEffect(() => {
    if (progress === 100 && toggle) {
      toggle(false);
    }
  }, [progress]);

  useEffect(() => {
    const step = steps[currentStep];
    let count = 0;

    if (step.progress !== undefined) {
      intervalId && clearInterval(intervalId);
      setIntervalId(undefined);
      const progress = unitStepPercentage * currentStep + unitStepPercentage * (step.progress / 100.0);
      setProgress(progress);
      return;
    }

    if (step?.isLoading === true && intervalId === undefined && step.complete !== true) {
      intervalId && clearInterval(intervalId);
      const id = setInterval(async () => {
        if (count < 90 && step.isLoading) {
          const progress = unitStepPercentage * currentStep + unitStepPercentage * (count / 100.0);
          setProgress(progress);
          count += stepCount ?? 10;
        } else if (step.complete === true) {
          count = 100;
          setProgress(unitStepPercentage * currentStep + unitStepPercentage);
          setIntervalId(undefined);
          clearInterval(id);
        }
      }, 100);
      setIntervalId(id);
    }
  }, [steps, currentStep]);

  const Main = () => (
    <div className="px-8 py-8 w-full">
      <h1 className=" text-3xl text-center">{`${Math.floor(progress)}%`}</h1>
      <span className="text-dark block text-center text-sm mb-8">{steps[currentStep].label}</span>
      <div className={`rounded-2xl py-[2.5px] bg-primary-400 duration-200`} style={{ width: `${progress}%` }}></div>
      {steps[currentStep]?.useSpinner === true && progress < 100 && (
        <div className="w-[fit-content] mx-auto mt-2.5">
          <div className="spinner text-primary-500"></div>
        </div>
      )}
    </div>
  );

  if (isEmbedded) return <Main />;

  return (
    <Modal closeable={false} {...{ toggle, show, title }}>
      <ModalBody>
        <Main />
      </ModalBody>
    </Modal>
  );
};

export default ProgressModal;
