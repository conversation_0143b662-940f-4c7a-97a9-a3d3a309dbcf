import React, { useEffect, useRef } from "react";

interface Props {
  videoRef: React.RefObject<HTMLVideoElement>;
}

const CanvasBgFromVideo = ({ videoRef }: Props) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const video = videoRef.current;
    const canvas = canvasRef.current;

    if (!video || !canvas) return;

    // Once the metadata (dimensions) are loaded
    const onLoadedMetadata = () => {
      video.currentTime = 0;
    };

    // Once the first frame is loaded
    const onSeeked = () => {
      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    };

    video.addEventListener("loadedmetadata", onLoadedMetadata);
    video.addEventListener("seeked", onSeeked);

    // // Load video silently
    // video.src = videoSrc;
    // video.load();

    return () => {
      video.removeEventListener("loadedmetadata", onLoadedMetadata);
      video.removeEventListener("seeked", onSeeked);
    };
  }, []);

  return (
    <div className="w-full h-full absolute top-0 left-0 overflow-hidden z-[-1]">
      <div
        className={`absolute top-0 left-0 w-full h-full z-1 backdrop-blur-md bg-black bg-opacity-5 ${
          videoRef ? "" : "hidden"
        }`}
      ></div>
      <canvas ref={canvasRef} className={`w-full`} />
    </div>
  );
};

export default CanvasBgFromVideo;
