import React from "react";
import { RoundActionBtn } from "./buttons";

interface PaginationProps {
  totalItems: number;
  currentPage: number;
  length: number;
  per_page: number;
  label: string;
  goNext: VoidFunction;
  goPrevious: VoidFunction;
  setPage: (page: number) => void;
}

const ArrayPagination: React.FC<PaginationProps> = (props) => {
  const {
    currentPage,
    per_page,
    length,
    label,
    goNext,
    setPage,
    goPrevious,
    totalItems,
  } = props;

  const total_pages = Math.ceil(totalItems / per_page);
  const prev_page = currentPage > 1 ? currentPage - 1 : null;
  const next_page = currentPage < total_pages ? currentPage + 1 : null;

  const getPages = (maxVisbilePages: number) => {
    const isFirstSection = currentPage <= maxVisbilePages - 1;
    const isMidSection = currentPage > maxVisbilePages - 1 && currentPage <= total_pages - (maxVisbilePages - 1);
    const isLastSection = currentPage > total_pages - (maxVisbilePages - 1);
    const isMany = total_pages > maxVisbilePages + 1;
    const pages = [{ text: "1", value: 1, isActive: currentPage === 1, isPage: true }];

    const addPage = (text: string, value: number, isActive: boolean, isPage: boolean) =>
      pages.push({ text, value, isActive, isPage });
    const addElipses = () => addPage("...", null, false, false);

    if (isMany)
      for (let i = 1; i < maxVisbilePages - 1; i++) {
        const isLastLoop = i === maxVisbilePages - 2;
        if (isFirstSection) {
          const value = i + 1;
          addPage(value.toString(), value, currentPage === i + 1, true);
          if (isLastLoop) addElipses();
        } else if (isMidSection) {
          if (i === 1) addElipses();
          const value = currentPage - 2 + i;
          addPage(value.toString(), value, currentPage === value, true);
          if (isLastLoop) addElipses();
        } else if (isLastSection) {
          if (i === 1) addElipses();
          const value = total_pages - (maxVisbilePages - (i + 1));
          addPage(value.toString(), value, currentPage === value, true);
        }
        if (isLastLoop) addPage(total_pages.toString(), total_pages, currentPage === total_pages, true);
      }
    else
      for (let i = 2; i <= total_pages; i++) {
        addPage(i.toString(), i, currentPage === i, true);
      }
    return pages;
  };

  return (
    <div className="mb-12.5">
      <div className="mt-2.5 flex sm:items-center justify-between flex-wrap flex-col sm:flex-row">
        <div className="text-sm text-dark pt-1 inline-block flex-shrink-0 mt-2.5">
          Showing{" "}
          <b className="text-black-secondary font-semibold">
            {per_page * (currentPage - 1) + 1} - {per_page * (currentPage - 1) + length}
          </b>{" "}
          of <b className="text-black-secondary font-semibold">{totalItems}</b> {label}
        </div>

        {total_pages > 1 && (
          <div className="hidden sm:flex">
            <ActionBtns
              prev_page={prev_page}
              next_page={next_page}
              goNext={goNext}
              goPrevious={goPrevious}
              setPage={setPage}
              getPages={getPages}
            />
          </div>
        )}
      </div>
      <div className="mt-2.5 flex sm:hidden justify-end">
        <ActionBtns
          prev_page={prev_page}
          next_page={next_page}
          goNext={goNext}
          goPrevious={goPrevious}
          setPage={setPage}
          getPages={getPages}
        />
      </div>
    </div>
  );
};

interface ActionBtnsProps {
  prev_page: number;
  next_page: number;
  goNext: VoidFunction;
  goPrevious: VoidFunction;
  setPage: (page: number) => void;
  getPages: (maxVisbilePages: number) => { text: string; value: number; isActive: boolean; isPage: boolean }[];
}

const ActionBtns: React.FC<ActionBtnsProps> = (props) => {
  const { prev_page, next_page, goNext, goPrevious, setPage, getPages } = props;

  return (
    <div className="flex gap-2.5 items-center mt-2.5 ml-auto">
      {prev_page && (
        <RoundActionBtn size="sm" onClick={goPrevious}>
          {/* prettier-ignore */}
          <svg width="14" viewBox="0 0 15 14" fill="none">
        <path d="M13.5 7H1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
        <path d="M7.5 1L1.5 7L7.5 13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"></path>
      </svg>
        </RoundActionBtn>
      )}
      {getPages(5).map(({ value, isActive, isPage, text }, idx) =>
        isPage ? (
          <button
            key={idx}
            onClick={() => setPage(value)}
            className={`text-sm h-8 w-8 bg-grey-fields-200 rounded-full flex items-center justify-center ${
              isActive ? "w-8 bg-primary-500 text-white font-semibold block" : "text-black-muted font-medium"
            }`}
            disabled={isActive || !isPage}
          >
            {text}
          </button>
        ) : (
          <span className="h-8" key={idx}>
            {text}
          </span>
        )
      )}
      {next_page && (
        <RoundActionBtn onClick={goNext} size="sm">
          {/* prettier-ignore */}
          <svg width="14" viewBox="0 0 15 14" fill="none" >
        <path d="M1.5 7L13.5 7M13.5 7L7.5 13M13.5 7L7.5 1" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
        </RoundActionBtn>
      )}
    </div>
  );
};

export default ArrayPagination;