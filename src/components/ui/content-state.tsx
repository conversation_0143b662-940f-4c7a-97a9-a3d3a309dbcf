import classNames from "classnames";
import React from "react";
import { CustomErrorType } from "../../api/utils";
import ErrorIcon from "../../assets/icons/statuses/failed.svg";

interface ContentStateProps {
  title?: string;
  loadingText?: string;
  description?: string;
  isEmpty?: boolean;
  isLoading?: boolean;
  error?: any;
  emptyIcon?: React.ReactElement;
  errorAction?: React.ReactElement;
  errorTitle?: string;
  errorMessage?: string;
  noPadding?: boolean;
  small?: boolean;
  loadingPlaceholder?: React.ReactElement;
}

const ContentState: React.FC<ContentStateProps> = ({
  title,
  loadingText,
  isLoading,
  isEmpty,
  emptyIcon,
  description,
  errorAction,
  children,
  errorTitle,
  error,
  errorMessage,
  noPadding,
  small = false,
  loadingPlaceholder,
}) => {
  if (isLoading && loadingPlaceholder) return loadingPlaceholder;

  return (
    <div className={`flex items-center justify-center ${noPadding ? "" : "py-[80px] sm:py-[100px] lg:py-[125px]"}`}>
      {isLoading && (
        <div className="flex flex-col items-center py-5">
          <div className="spinner spinner--md text-primary-500"></div>
          <span className="text-sm text-black-placeholder mt-2 inline-block font-medium">{loadingText}</span>
        </div>
      )}

      {error && (
        <div className="w-full flex flex-col items-center">
          <figure
            className={classNames(" rounded-full flex items-center justify-center", {
              "h-15 w-15 sm:h-16 sm:w-16 lg:h-17.5 lg:w-17.5 mb-2 sm:mb-3.5": small,
              "h-18 w-18 sm:h-20 sm:w-20 lg:h-[90px] lg:w-[90px] mb-2.5 sm:mb-4 ": !small,
            })}
          >
            <ErrorIcon />
          </figure>
          <h4
            className={classNames("text-black font-bold mb-0.5", {
              "text-base sm:text-lg lg:text-xl": !small,
              "text-1sm sm:text-base lg:text-lg": small,
            })}
          >
            {errorTitle ? errorTitle : "Something went wrong!"}
          </h4>
          <p
            className={classNames("text-black-placeholder text-center max-w-[280px]", {
              "text-xs sm:text-1xs max-w-[200px]": small,
              "text-1xs sm:text-sm": !small,
            })}
          >
            {errorMessage}
          </p>
          <div className="mt-4 sm:mt-6">{errorAction}</div>
        </div>
      )}

      {isEmpty && !isLoading && !error && (
        <div className="w-full flex flex-col items-center">
          <figure
            className={classNames("rounded-full flex items-center justify-center bg-grey-fields-100", {
              "h-15 w-15 sm:h-16 sm:w-16 lg:h-17.5 lg:w-17.5 mb-2 sm:mb-3.5": small,
              "h-18 w-18 sm:h-20 sm:w-20 lg:h-[90px] lg:w-[90px] mb-2.5 sm:mb-4 ": !small,
            })}
          >
            {emptyIcon}
          </figure>
          <h4
            className={classNames("text-black font-bold mb-0.5", {
              "text-base sm:text-lg lg:text-xl": !small,
              "text-1sm sm:text-base lg:text-lg": small,
            })}
          >
            {title}
          </h4>
          <p
            className={classNames("text-black-placeholder text-center max-w-[280px]", {
              "text-xs sm:text-1xs max-w-[200px]": small,
              "text-1xs sm:text-sm": !small,
            })}
          >
            {description}
          </p>
          <div className="mt-4 sm:mt-6">{children}</div>
        </div>
      )}
    </div>
  );
};

export default ContentState;
