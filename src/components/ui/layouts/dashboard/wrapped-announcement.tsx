import React, { useEffect, useState } from "react";
import { AppBtn } from "../../buttons";
import <PERSON>dal, { <PERSON>dal<PERSON><PERSON>, ModalFooter, ModalProps } from "../../modal";
import authContext from "@/contexts/auth-context";
import dayjs from "dayjs";
import { useRouter } from "next/router";
import { COUNTRIES } from "@/assets/interfaces";
import Confetti from "react-confetti";
import { useModals } from "@/components/hooks/useModals";
import { useAppendScript } from "@/components/hooks/useAppendScript";

const WrappedAnnouncementModal: React.FC = () => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const { store } = authContext.useContainer();

  const { modals, toggleModal } = useModals(["explainer"]);

  const shouldShowModal = showModal && Boolean(store?.subscription) && dayjs().isBefore(dayjs("2025-10-07"));

  useEffect(() => {
    setTimeout(() => {
      const hasShown = !!sessionStorage.getItem("shown-wrapped-modal");

      if (!hasShown) {
        setShowModal(true);
      }
    }, 3000);
  }, []);

  const closeModal = () => {
    setShowModal(false);
    sessionStorage.setItem("shown-wrapped-modal", "true");
  };

  const goToDeliveries = () => {
    closeModal();
    router.push("/deliveries");
  };

  return (
    <>
      <Modal
        {...{ show: shouldShowModal, toggle: closeModal }}
        title=""
        size="midi"
        className="z-[1000]"
        showHeader={false}
      >
        <ModalBody noPadding className="rounded-t-10 overflow-hidden">
          <figure className="w-full sticky top-0">
            <img src="/images/wrapped-cover.png" alt="Banner Announcing Catlog Deliveries" className="w-full" />
          </figure>
          <div className="p-5 sm:p-6.25 flex-1 overflow-auto flex-col flex items-center max-w-[450px] mx-auto">
            <h2 className="font-bold font-display text-2xl sm:text-3xl text-center !leading-[1.05] text-[#191555]">
              Your 2024 Catlog <br />
              Chronicles is here!
            </h2>
            <p className="text-black-secondary text-sm text-center mt-3.5">
              See how well your business <br />
              perfromed in 2024
            </p>
            <div>
              <AppBtn onClick={() => window.open("/wrapped", "_blank")} size="lg" className="flex-1 mt-7.5">
                See your 2024 Chronicles
              </AppBtn>
            </div>
          </div>
          <Confetti width={1000} height={1000} numberOfPieces={500} recycle={false} style={{ zIndex: 200 }} />
        </ModalBody>
      </Modal>
    </>
  );
};

interface Props extends ModalProps {}

export default WrappedAnnouncementModal;
