import React, { useEffect, useRef, useState } from "react";
import useCopyClipboard from "../../../hooks/useCopyClipboard";
import router from "next/router";
import ZapIcon from "../../../../assets/icons/new/zap.svg";
import { DropdownItem as IDropdownItemNew } from "../../dropdown-new";
import cx from "classnames";
// import { initIntercomWindow } from "next-intercom";
import useClickOutside from "../../../hooks/useClickOutside";
import { COMMUNITY_LINK, WHATSAPP_LINK } from "../../../../assets/js/utils/constants";
import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import ReferralModal from "@/components/dashboard/referral-modal";
import useReferrals from "@/components/hooks/useReferrals";
import ManagePushNotifications from "./manage-push-notifications";
import { getCommunityLink } from "@/assets/js/utils/utils";
import { StoreInterface } from "@/assets/interfaces";
import { HowToWinModal } from "@/pages/referral-leaderboard";
import { subdomainStoreLink } from "@/assets/js/utils/functions";

interface Props {
  storeLink: string;
  user: {
    name: string;
    email: string;
  };
  store: StoreInterface;
}

const QuickActions: React.FC<Props> = ({ storeLink, store }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isIntercomOpen, setIsIntercomOpen] = useState(false);
  var dropdown = useRef<HTMLDivElement>(null);
  const [copied, copy] = useCopyClipboard(storeLink, { successDuration: 3000 });
  const [copiedChowbot, copyChowbotLink] = useCopyClipboard(subdomainStoreLink(store?.slug ?? "", true, "chowbot"), {
    successDuration: 3000,
  });

  const { modals, toggleModal } = useModals(["refer", "challenge"]);
  const { referrals, rewards } = useReferrals();

  const toggle = () => {
    setIsOpen(!isOpen);
  };

  useClickOutside(dropdown, () => {
    setIsOpen(false);
  });

  const dropdownItems: { [key: string]: IDropdownItemNew[] } = {
    quickActions: [
      {
        text: "Visit Store",
        link: storeLink,
        onClick: undefined,
        icon: icons.store,
      },
      {
        text: "Copy Store Link",
        link: undefined,
        onClick: () => copy(),
        icon: icons.copy,
      },
      store?.flags?.uses_chowbot
        ? {
            text: "Copy Chowbot Link",
            link: undefined,
            onClick: () => copyChowbotLink(),
            icon: icons.copy,
          }
        : null,
      {
        text: "Add Products",
        link: undefined,
        onClick: () => {
          router.push("/products/create");
        },
        icon: icons.add,
      },
      {
        text: "Join Community",
        link: getCommunityLink(store?.country),
        onClick: undefined,
        icon: icons.community,
      },
    ],
    moreActions: [
      {
        text: "Refer & Earn",
        link: undefined,
        onClick: () => toggleModal("refer"),
        icon: icons.money,
      },
      // {
      //   text: "Milestones Crossed",
      //   link: undefined,
      //   onClick: () => copy(),
      //   icon: icons.copy,
      // },
    ],
    help: [
      {
        text: "Find Answers",
        link: "https://www.notion.so/49b13cc6f3904725b8f29aa337f7d73b?pvs=4",
        onClick: undefined,
        icon: icons.findAnswers,
      },
      {
        text: "Privacy Policy",
        link: "https://catlog-help-center.notion.site/Privacy-Policy-2b9b7ac5613b48f7b53016a597a40680",
        onClick: undefined,
        icon: icons.privacy,
      },
      // {
      //   text: "Live Chat",
      //   link: undefined,
      //   onClick: openIntercom,
      //   icon: icons.chat,
      // },
      {
        text: "Talk to us on whatsapp",
        link: WHATSAPP_LINK,
        onClick: undefined,
        icon: icons.whatsapp,
      },
    ],
  };

  function openIntercom(e: React.MouseEvent) {
    e.stopPropagation();
    window.Intercom("show");
    setIsOpen(false);
  }

  function closeIntercom() {
    window.Intercom("hide");
  }

  return (
    <>
      <div ref={dropdown} className={cx("z-[990] inline-block relative")} onClick={(e) => e.stopPropagation()}>
        <button
          className="border-solid border-gray-100 border w-full flex items-center duration-300 hover:bg-gray-100 hover:bg-opacity-50 py-2 px-2.5 sm:py-2.5 sm:px-3 rounded-full text-dark dropdown-toggle"
          onClick={toggle}
        >
          <div className="flex items-center">
            <ZapIcon />
            <span className="text-black text-xs sm:text-sm inline-block whitespace-nowrap ml-0.5"> Quick Actions</span>
          </div>
          {/* prettier-ignore */}
          <svg viewBox="0 0 20 20" fill="none" className={cx(`transition-transform ml-0.75 sm:ml-1 text-dark transform flex-shrink-0 w-4 sm:w-5`, { 'rotate-180': isOpen})}>
            <path d="M15 7.5L10 12.5L5 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>

        <div
          className={cx(
            `w-full rounded-10 bg-white shadow-icon1 absolute transform transition ease-out duration-300 right-0 min-w-[220px] overflow-hidden top-[calc(100%+15px)] py-2.5`,
            { "opacity-1 translate-y-0": isOpen },
            { "pointer-events-none opacity-0 translate-y-4": !isOpen }
          )}
        >
          <ul className="flex flex-col">
            {dropdownItems.quickActions
              .filter((i) => !!i)
              .map((item, index) => (
                <DropdownItem {...{ item, index, closeDropdown: () => setIsOpen(false) }} key={index} />
              ))}
          </ul>
          <div className="my-1.25 mx-3.75 h-1 border-t border-grey-border border-opacity-50"></div>
          <ul className="flex flex-col">
            {dropdownItems.moreActions
              .filter((i) => !!i)
              .map((item, index) => (
                <DropdownItem {...{ item, index, closeDropdown: () => setIsOpen(false) }} key={index} />
              ))}
          </ul>
          <div className="my-1.25 mx-3.75 h-1 border-t border-grey-border border-opacity-50"></div>
          <ul className="flex flex-col">
            {dropdownItems.help
              .filter((i) => !!i)
              .map((item, index) => (
                <DropdownItem {...{ item, index, closeDropdown: () => setIsOpen(false) }} key={index} />
              ))}
          </ul>
          <div className="my-1.25 mx-3.75 h-1 border-t border-grey-border border-opacity-50"></div>
          <ManagePushNotifications closeDropdown={() => setIsOpen(false)} />
        </div>
      </div>
      {rewards && (
        <Portal>
          <ReferralModal
            show={modals.refer.show}
            toggle={() => toggleModal("refer")}
            rewards={rewards}
            referral_code={referrals?.referral_code}
          />
          <HowToWinModal show={modals.challenge.show} toggle={() => toggleModal("challenge")} />
        </Portal>
      )}
    </>
  );
};

interface IDropdownItem {
  index: number;
  closeDropdown: VoidFunction;
  item: IDropdownItemNew;
}

const DropdownItem: React.FC<IDropdownItem> = ({ index, closeDropdown, item }) => {
  const buttonClass =
    "flex items-center py-2.5 text-black-placeholder hover:bg-gray-100 w-full hover:bg-opacity-50 duration-150 px-3.75";

  return (
    <>
      <li key={index.toString()} onClick={closeDropdown}>
        {/* {opened && ( */}
        {item.onClick && (
          <button key={index.toString()} onClick={item.onClick} className={cx(buttonClass, item.className)}>
            {item.icon ? item.icon : null}
            <span className="text-sm ml-2.5 whitespace-nowrap inline-block text-black-secondary font-medium">
              {item.text}
            </span>
          </button>
        )}

        {item.link && (
          <a
            {...{
              target: item.link.includes("http") ? "_blank" : "",
              relative: item.link.includes("http") ? "noreferrer" : "",
              href: item.link,
            }}
            className={cx(buttonClass, item.className)}
          >
            {item.icon ? item.icon : null}
            <span className="text-sm ml-2.5 whitespace-nowrap inline-block text-black-secondary font-medium">
              {item.text}
            </span>
          </a>
        )}
      </li>
    </>
  );
};

const icons = {
  store:
    // prettier-ignore
    <svg className="w-5" viewBox="0 0 20 20" fill="none">
      <path d="M12.25 18.9583H7.75003C3.63336 18.9583 1.88336 17.2 1.88336 13.0916V9.34998C1.88336 9.00831 2.1667 8.72498 2.50836 8.72498C2.85003 8.72498 3.13336 9.00831 3.13336 9.34998V13.0916C3.13336 16.5 4.34169 17.7083 7.75003 17.7083H12.2417C15.65 17.7083 16.8584 16.5 16.8584 13.0916V9.34998C16.8584 9.00831 17.1417 8.72498 17.4834 8.72498C17.825 8.72498 18.1084 9.00831 18.1084 9.34998V13.0916C18.1167 17.2 16.3584 18.9583 12.25 18.9583Z" fill="currentColor"/>
      <path d="M9.99998 10.625C9.08331 10.625 8.24998 10.2667 7.65831 9.60835C7.06665 8.95002 6.79165 8.09169 6.88331 7.17502L7.44165 1.60835C7.47498 1.29169 7.74165 1.04169 8.06665 1.04169H11.9583C12.2833 1.04169 12.55 1.28335 12.5833 1.60835L13.1416 7.17502C13.2333 8.09169 12.9583 8.95002 12.3666 9.60835C11.75 10.2667 10.9166 10.625 9.99998 10.625ZM8.62498 2.29169L8.12498 7.30002C8.06665 7.85835 8.23331 8.38335 8.58331 8.76669C9.29165 9.55002 10.7083 9.55002 11.4166 8.76669C11.7666 8.37502 11.9333 7.85002 11.875 7.30002L11.375 2.29169H8.62498Z" fill="currentColor"/>
      <path d="M15.2583 10.625C13.5666 10.625 12.0583 9.25835 11.8833 7.57502L11.3 1.73335C11.2833 1.55835 11.3416 1.38335 11.4583 1.25002C11.575 1.11669 11.7416 1.04169 11.925 1.04169H14.4666C16.9166 1.04169 18.0583 2.06669 18.4 4.58335L18.6333 6.90002C18.7333 7.88335 18.4333 8.81669 17.7916 9.52502C17.15 10.2334 16.25 10.625 15.2583 10.625ZM12.6166 2.29169L13.1333 7.45002C13.2416 8.49169 14.2083 9.37502 15.2583 9.37502C15.8916 9.37502 16.4583 9.13335 16.8666 8.69169C17.2666 8.25002 17.45 7.65835 17.3916 7.02502L17.1583 4.73335C16.9 2.85002 16.2916 2.29169 14.4666 2.29169H12.6166V2.29169Z" fill="currentColor"/>
      <path d="M4.70002 10.625C3.70835 10.625 2.80835 10.2334 2.16668 9.52502C1.52502 8.81669 1.22502 7.88335 1.32502 6.90002L1.55002 4.60835C1.90002 2.06669 3.04168 1.04169 5.49168 1.04169H8.03335C8.20835 1.04169 8.37502 1.11669 8.50002 1.25002C8.62502 1.38335 8.67502 1.55835 8.65835 1.73335L8.07502 7.57502C7.90002 9.25835 6.39168 10.625 4.70002 10.625ZM5.49168 2.29169C3.66668 2.29169 3.05835 2.84169 2.79168 4.75002L2.56668 7.02502C2.50002 7.65835 2.69168 8.25002 3.09168 8.69169C3.49168 9.13335 4.05835 9.37502 4.70002 9.37502C5.75002 9.37502 6.72502 8.49169 6.82502 7.45002L7.34168 2.29169H5.49168V2.29169Z" fill="currentColor"/>
      <path d="M12.0834 18.9584H7.91669C7.57502 18.9584 7.29169 18.675 7.29169 18.3334V16.25C7.29169 14.5 8.25002 13.5417 10 13.5417C11.75 13.5417 12.7084 14.5 12.7084 16.25V18.3334C12.7084 18.675 12.425 18.9584 12.0834 18.9584ZM8.54169 17.7084H11.4584V16.25C11.4584 15.2 11.05 14.7917 10 14.7917C8.95002 14.7917 8.54169 15.2 8.54169 16.25V17.7084Z" fill="currentColor"/>
    </svg>,
  copy:
    // prettier-ignore
    <svg className="w-5" viewBox="0 0 20 20" fill="none">
      <path d="M9.25002 18.9584H5.75002C2.49169 18.9584 1.04169 17.5084 1.04169 14.25V10.75C1.04169 7.49169 2.49169 6.04169 5.75002 6.04169H9.25002C12.5084 6.04169 13.9584 7.49169 13.9584 10.75V14.25C13.9584 17.5084 12.5084 18.9584 9.25002 18.9584ZM5.75002 7.29169C3.16669 7.29169 2.29169 8.16669 2.29169 10.75V14.25C2.29169 16.8334 3.16669 17.7084 5.75002 17.7084H9.25002C11.8334 17.7084 12.7084 16.8334 12.7084 14.25V10.75C12.7084 8.16669 11.8334 7.29169 9.25002 7.29169H5.75002V7.29169Z" fill="currentColor"/>
      <path d="M14.25 13.9584H13.3334C12.9917 13.9584 12.7084 13.675 12.7084 13.3334V10.75C12.7084 8.16669 11.8334 7.29169 9.25002 7.29169H6.66669C6.32502 7.29169 6.04169 7.00835 6.04169 6.66669V5.75002C6.04169 2.49169 7.49169 1.04169 10.75 1.04169H14.25C17.5084 1.04169 18.9584 2.49169 18.9584 5.75002V9.25002C18.9584 12.5084 17.5084 13.9584 14.25 13.9584ZM13.9584 12.7084H14.25C16.8334 12.7084 17.7084 11.8334 17.7084 9.25002V5.75002C17.7084 3.16669 16.8334 2.29169 14.25 2.29169H10.75C8.16669 2.29169 7.29169 3.16669 7.29169 5.75002V6.04169H9.25002C12.5084 6.04169 13.9584 7.49169 13.9584 10.75V12.7084Z" fill="currentColor"/>
    </svg>,
  add:
    // prettier-ignore
    <svg className="w-5" viewBox="0 0 20 20" fill="none">
      <path d="M10.0002 18.9584C5.05854 18.9584 1.04187 14.9417 1.04187 10.0001C1.04187 5.05841 5.05854 1.04175 10.0002 1.04175C14.9419 1.04175 18.9585 5.05841 18.9585 10.0001C18.9585 14.9417 14.9419 18.9584 10.0002 18.9584ZM10.0002 2.29175C5.7502 2.29175 2.29187 5.75008 2.29187 10.0001C2.29187 14.2501 5.7502 17.7084 10.0002 17.7084C14.2502 17.7084 17.7085 14.2501 17.7085 10.0001C17.7085 5.75008 14.2502 2.29175 10.0002 2.29175Z" fill="currentColor"/>
      <path d="M13.3335 10.625H6.66687C6.3252 10.625 6.04187 10.3417 6.04187 10C6.04187 9.65833 6.3252 9.375 6.66687 9.375H13.3335C13.6752 9.375 13.9585 9.65833 13.9585 10C13.9585 10.3417 13.6752 10.625 13.3335 10.625Z" fill="currentColor"/>
      <path d="M10 13.9584C9.65833 13.9584 9.375 13.6751 9.375 13.3334V6.66675C9.375 6.32508 9.65833 6.04175 10 6.04175C10.3417 6.04175 10.625 6.32508 10.625 6.66675V13.3334C10.625 13.6751 10.3417 13.9584 10 13.9584Z" fill="currentColor"/>
    </svg>,
  community:
    // prettier-ignore
    <svg className="w-5" viewBox="0 0 20 20" fill="none">
      <path d="M7.63302 9.68335C7.60802 9.68335 7.59135 9.68335 7.56635 9.68335C7.52468 9.67502 7.46635 9.67502 7.41635 9.68335C4.99968 9.60835 3.17468 7.70835 3.17468 5.36669C3.17468 2.98335 5.11635 1.04169 7.49968 1.04169C9.88302 1.04169 11.8247 2.98335 11.8247 5.36669C11.8163 7.70835 9.98302 9.60835 7.65802 9.68335C7.64968 9.68335 7.64135 9.68335 7.63302 9.68335ZM7.49968 2.29169C5.80802 2.29169 4.42468 3.67502 4.42468 5.36669C4.42468 7.03335 5.72468 8.37502 7.38302 8.43335C7.43302 8.42502 7.54135 8.42502 7.64968 8.43335C9.28302 8.35835 10.5663 7.01669 10.5747 5.36669C10.5747 3.67502 9.19135 2.29169 7.49968 2.29169Z" fill="currentColor"/>
      <path d="M13.783 9.79165C13.758 9.79165 13.733 9.79165 13.708 9.78331C13.3663 9.81665 13.0163 9.57498 12.983 9.23331C12.9496 8.89165 13.158 8.58331 13.4996 8.54165C13.5996 8.53331 13.708 8.53331 13.7996 8.53331C15.0163 8.46665 15.9663 7.46665 15.9663 6.24165C15.9663 4.97498 14.9413 3.94998 13.6746 3.94998C13.333 3.95831 13.0496 3.67498 13.0496 3.33331C13.0496 2.99165 13.333 2.70831 13.6746 2.70831C15.6246 2.70831 17.2163 4.29998 17.2163 6.24998C17.2163 8.16665 15.7163 9.71665 13.808 9.79165C13.7996 9.79165 13.7913 9.79165 13.783 9.79165Z" fill="currentColor"/>
      <path d="M7.64134 18.7916C6.00801 18.7916 4.36634 18.375 3.12467 17.5416C1.96634 16.775 1.33301 15.725 1.33301 14.5833C1.33301 13.4416 1.96634 12.3833 3.12467 11.6083C5.62467 9.94998 9.67467 9.94998 12.158 11.6083C13.308 12.375 13.9497 13.425 13.9497 14.5666C13.9497 15.7083 13.3163 16.7666 12.158 17.5416C10.908 18.375 9.27467 18.7916 7.64134 18.7916ZM3.81634 12.6583C3.01634 13.1916 2.58301 13.875 2.58301 14.5916C2.58301 15.3 3.02467 15.9833 3.81634 16.5083C5.89134 17.9 9.39134 17.9 11.4663 16.5083C12.2663 15.975 12.6997 15.2916 12.6997 14.575C12.6997 13.8666 12.258 13.1833 11.4663 12.6583C9.39134 11.275 5.89134 11.275 3.81634 12.6583Z" fill="currentColor"/>
      <path d="M15.283 17.2917C14.9913 17.2917 14.733 17.0917 14.6746 16.7917C14.608 16.45 14.8246 16.125 15.158 16.05C15.683 15.9417 16.1663 15.7333 16.5413 15.4417C17.0163 15.0833 17.2746 14.6333 17.2746 14.1583C17.2746 13.6833 17.0163 13.2333 16.5496 12.8833C16.183 12.6 15.7246 12.4 15.183 12.275C14.8496 12.2 14.633 11.8667 14.708 11.525C14.783 11.1917 15.1163 10.975 15.458 11.05C16.1746 11.2083 16.7996 11.4917 17.308 11.8833C18.083 12.4667 18.5246 13.2917 18.5246 14.1583C18.5246 15.025 18.0746 15.85 17.2996 16.4417C16.783 16.8417 16.133 17.1333 15.4163 17.275C15.3663 17.2917 15.3246 17.2917 15.283 17.2917Z" fill="currentColor"/>
    </svg>,
  chat:
    // prettier-ignore
    <svg className="w-5" viewBox="0 0 20 20" fill="none">
      <path d="M14.0833 15.8417C13.825 15.8417 13.5667 15.7666 13.3417 15.6166L12.5417 15.0916C12.3167 14.9416 12.2084 14.6583 12.2834 14.4C12.3417 14.2083 12.3667 13.9833 12.3667 13.7333V10.3416C12.3667 8.98329 11.5167 8.13332 10.1584 8.13332H4.50002C4.40002 8.13332 4.30835 8.14167 4.21669 8.15C4.04169 8.15834 3.87502 8.09999 3.74169 7.98333C3.60836 7.86666 3.54169 7.7 3.54169 7.525V5.21665C3.54169 2.76665 5.25835 1.04999 7.70835 1.04999H14.7917C17.2417 1.04999 18.9584 2.76665 18.9584 5.21665V9.46663C18.9584 10.675 18.55 11.7416 17.8 12.475C17.2 13.0833 16.3667 13.475 15.4167 13.5916V14.5166C15.4167 15.0166 15.1417 15.4667 14.7083 15.7C14.5083 15.7917 14.2917 15.8417 14.0833 15.8417ZM13.5834 14.275L14.125 14.5833C14.175 14.5583 14.175 14.5166 14.175 14.5083V13C14.175 12.6583 14.4583 12.375 14.8 12.375C15.675 12.375 16.4167 12.1 16.925 11.5833C17.45 11.0667 17.7167 10.3333 17.7167 9.45829V5.20831C17.7167 3.43331 16.575 2.29165 14.8 2.29165H7.71668C5.94168 2.29165 4.80002 3.43331 4.80002 5.20831V6.87498H10.1667C12.2 6.87498 13.625 8.3 13.625 10.3333V13.725C13.6167 13.9166 13.6084 14.1 13.5834 14.275Z" fill="currentColor"/>
      <path d="M5.05836 18.9583C4.87503 18.9583 4.68336 18.9167 4.50836 18.825C4.11669 18.6167 3.87502 18.2167 3.87502 17.7667V17.1333C3.14168 17.0167 2.49169 16.7083 2.00836 16.225C1.37502 15.5916 1.04169 14.725 1.04169 13.725V10.3334C1.04169 8.45002 2.27501 7.06668 4.10835 6.89168C4.24168 6.88335 4.36668 6.875 4.50002 6.875H10.1584C12.1917 6.875 13.6167 8.30002 13.6167 10.3334V13.725C13.6167 14.0916 13.575 14.4333 13.4833 14.7417C13.1083 16.2417 11.8334 17.1833 10.1584 17.1833H8.08335L5.72502 18.75C5.52502 18.8917 5.29169 18.9583 5.05836 18.9583ZM4.50002 8.125C4.40002 8.125 4.30835 8.13335 4.21668 8.14168C3.01668 8.25002 2.29169 9.07502 2.29169 10.3334V13.725C2.29169 14.3916 2.50002 14.95 2.89169 15.3416C3.27502 15.725 3.83335 15.9333 4.50002 15.9333C4.84168 15.9333 5.12502 16.2167 5.12502 16.5583V17.65L7.54169 16.0417C7.64169 15.975 7.76669 15.9333 7.89169 15.9333H10.1584C11.2584 15.9333 12.0333 15.3833 12.275 14.4167C12.3333 14.2083 12.3667 13.975 12.3667 13.725V10.3334C12.3667 8.97502 11.5167 8.125 10.1584 8.125H4.50002Z" fill="currentColor"/>
    </svg>,
  findAnswers:
    // prettier-ignore
    <svg className="w-5" viewBox="0 0 20 20" fill="none">
      <path d="M13.175 16.8167C12.9583 16.8167 12.7333 16.8083 12.4917 16.7833C12.0583 16.75 11.5667 16.6667 11.0583 16.5417L9.65833 16.2083C5.81667 15.3 4.55833 13.2667 5.45833 9.43333L6.275 5.94166C6.45833 5.14999 6.675 4.50833 6.94167 3.97499C8.375 1.01666 11.1167 1.28333 13.0667 1.74166L14.4583 2.06666C16.4083 2.52499 17.6417 3.24999 18.3333 4.35833C19.0167 5.46666 19.125 6.89166 18.6667 8.84166L17.85 12.325C17.1333 15.375 15.6417 16.8167 13.175 16.8167ZM10.9333 2.70833C9.54167 2.70833 8.65833 3.28333 8.06667 4.51666C7.85 4.96666 7.65833 5.52499 7.49167 6.22499L6.675 9.71666C5.93333 12.8667 6.79167 14.2417 9.94167 14.9917L11.3417 15.325C11.7917 15.4333 12.2167 15.5 12.6 15.5333C14.8667 15.7583 15.9917 14.7667 16.625 12.0417L17.4417 8.55833C17.8167 6.94999 17.7667 5.82499 17.2667 5.01666C16.7667 4.20833 15.7833 3.65833 14.1667 3.28333L12.775 2.95833C12.0833 2.79166 11.4667 2.70833 10.9333 2.70833Z" fill="currentColor"/>
      <path d="M6.94167 18.5416C4.8 18.5416 3.43334 17.2583 2.55834 14.55L1.49167 11.2583C0.308336 7.59165 1.36667 5.52498 5.01667 4.34165L6.33334 3.91665C6.76667 3.78331 7.09167 3.69165 7.38334 3.64165C7.625 3.59165 7.85834 3.68331 8 3.87498C8.14167 4.06665 8.16667 4.31665 8.06667 4.53331C7.85 4.97498 7.65834 5.53331 7.5 6.23331L6.68334 9.72498C5.94167 12.875 6.8 14.25 9.95 15L11.35 15.3333C11.8 15.4416 12.225 15.5083 12.6083 15.5416C12.875 15.5666 13.0917 15.75 13.1667 16.0083C13.2333 16.2666 13.1333 16.5333 12.9167 16.6833C12.3667 17.0583 11.675 17.375 10.8 17.6583L9.48334 18.0916C8.525 18.3916 7.69167 18.5416 6.94167 18.5416ZM6.48334 5.18331L5.40834 5.53331C2.43334 6.49165 1.725 7.89165 2.68334 10.875L3.75 14.1666C4.71667 17.1416 6.11667 17.8583 9.09167 16.9L10.4083 16.4666C10.4583 16.45 10.5 16.4333 10.55 16.4166L9.66667 16.2083C5.825 15.3 4.56667 13.2666 5.46667 9.43331L6.28334 5.94165C6.34167 5.67498 6.40834 5.41665 6.48334 5.18331Z" fill="currentColor"/>
      <path d="M14.575 8.75833C14.525 8.75833 14.475 8.75 14.4167 8.74166L10.375 7.71666C10.0417 7.63333 9.84168 7.29166 9.92502 6.95833C10.0084 6.625 10.35 6.425 10.6834 6.50833L14.725 7.53333C15.0584 7.61666 15.2584 7.95833 15.175 8.29166C15.1084 8.56666 14.85 8.75833 14.575 8.75833Z" fill="currentColor"/>
      <path d="M12.1333 11.575C12.0833 11.575 12.0333 11.5666 11.975 11.5583L9.55001 10.9416C9.21667 10.8583 9.01667 10.5166 9.10001 10.1833C9.18334 9.84997 9.52501 9.64997 9.85834 9.73331L12.2833 10.35C12.6167 10.4333 12.8167 10.775 12.7333 11.1083C12.6667 11.3916 12.4167 11.575 12.1333 11.575Z" fill="currentColor"/>
    </svg>,
  whatsapp:
    // prettier-ignore
    <svg className="w-5" viewBox="0 0 20 20" fill="none">
      <path d="M18.3166 9.50835C18.0333 4.67502 13.6416 0.950029 8.58329 1.78336C5.09995 2.35836 2.30829 5.18335 1.76663 8.66668C1.44996 10.6833 1.86664 12.5917 2.77498 14.1667L2.0333 16.925C1.86663 17.55 2.44162 18.1167 3.05828 17.9417L5.77496 17.1917C7.00829 17.9167 8.44995 18.3333 9.99162 18.3333C14.6916 18.3333 18.5916 14.1917 18.3166 9.50835ZM14.0666 13.1C13.9916 13.25 13.8999 13.3917 13.7833 13.525C13.5749 13.75 13.3499 13.9167 13.0999 14.0167C12.8499 14.125 12.575 14.175 12.2833 14.175C11.8583 14.175 11.4 14.075 10.925 13.8667C10.4416 13.6584 9.96664 13.3833 9.49164 13.0417C9.00831 12.6917 8.5583 12.3 8.12496 11.875C7.69163 11.4417 7.30828 10.9833 6.95828 10.5083C6.61661 10.0333 6.34163 9.55835 6.14163 9.08335C5.94163 8.60835 5.84164 8.15002 5.84164 7.71669C5.84164 7.43336 5.89163 7.15836 5.99163 6.90836C6.09163 6.65002 6.24998 6.41669 6.47498 6.20836C6.74165 5.94169 7.03329 5.81669 7.34163 5.81669C7.45829 5.81669 7.57494 5.84169 7.68327 5.89169C7.79161 5.94169 7.89163 6.01669 7.96663 6.12502L8.93327 7.49168C9.00827 7.60001 9.06662 7.69168 9.09995 7.78335C9.14162 7.87501 9.15829 7.95835 9.15829 8.04168C9.15829 8.14168 9.12497 8.24169 9.06663 8.34169C9.0083 8.44169 8.93328 8.54168 8.83328 8.64168L8.51661 8.97501C8.46661 9.02501 8.44998 9.07502 8.44998 9.14168C8.44998 9.17502 8.45827 9.20835 8.46661 9.24168C8.48327 9.27501 8.49164 9.30002 8.49997 9.32502C8.57497 9.46669 8.70828 9.64167 8.89162 9.85834C9.08328 10.075 9.28332 10.3 9.49998 10.5167C9.72498 10.7417 9.94164 10.9417 10.1666 11.1333C10.3833 11.3167 10.5666 11.4417 10.7083 11.5167C10.7333 11.525 10.7583 11.5417 10.7833 11.55C10.8166 11.5667 10.85 11.5667 10.8916 11.5667C10.9666 11.5667 11.0167 11.5417 11.0667 11.4917L11.3833 11.175C11.4916 11.0667 11.5916 10.9917 11.6833 10.9417C11.7833 10.8834 11.875 10.85 11.9833 10.85C12.0666 10.85 12.15 10.8667 12.2416 10.9083C12.3333 10.95 12.4333 11 12.5333 11.075L13.9166 12.0584C14.025 12.1334 14.0999 12.225 14.1499 12.325C14.1916 12.4334 14.2166 12.5333 14.2166 12.65C14.1666 12.7917 14.1333 12.95 14.0666 13.1Z" fill="#39B588"/>
    </svg>,
  money:
    // prettier-ignore
    <svg className="w-5" viewBox="0 0 24 24" fill="none">
      <path d="M19.3 7.91998V13.07C19.3 16.15 17.54 17.47 14.9 17.47H6.10995C5.65995 17.47 5.22996 17.43 4.82996 17.34C4.57996 17.3 4.33996 17.23 4.11996 17.15C2.61996 16.59 1.70996 15.29 1.70996 13.07V7.91998C1.70996 4.83998 3.46995 3.52002 6.10995 3.52002H14.9C17.14 3.52002 18.75 4.47001 19.18 6.64001C19.25 7.04001 19.3 7.44998 19.3 7.91998Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M22.3011 10.9201V16.0701C22.3011 19.1501 20.5411 20.4701 17.9011 20.4701H9.11105C8.37105 20.4701 7.70106 20.3701 7.12106 20.1501C5.93106 19.7101 5.12105 18.8001 4.83105 17.3401C5.23105 17.4301 5.66105 17.4701 6.11105 17.4701H14.9011C17.5411 17.4701 19.3011 16.1501 19.3011 13.0701V7.9201C19.3011 7.4501 19.2611 7.03014 19.1811 6.64014C21.0811 7.04014 22.3011 8.38011 22.3011 10.9201Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.4984 13.1399C11.9564 13.1399 13.1384 11.9579 13.1384 10.4999C13.1384 9.04185 11.9564 7.85986 10.4984 7.85986C9.04038 7.85986 7.8584 9.04185 7.8584 10.4999C7.8584 11.9579 9.04038 13.1399 10.4984 13.1399Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M4.78003 8.30005V12.7001" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M16.2217 8.30029V12.7003" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  cup:
    // prettier-ignore
    <svg className="w-4.5 flex-shrink-0" viewBox="0 0 24 24" fill="none">
      <path d="M12.15 16.5V18.6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7.15002 22H17.15V21C17.15 19.9 16.25 19 15.15 19H9.15002C8.05002 19 7.15002 19.9 7.15002 21V22V22Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10"/>
      <path d="M6.15002 22H18.15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 16C8.13 16 5 12.87 5 9V6C5 3.79 6.79 2 9 2H15C17.21 2 19 3.79 19 6V9C19 12.87 15.87 16 12 16Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5.46998 11.65C4.71998 11.41 4.05998 10.97 3.53998 10.45C2.63998 9.44998 2.03998 8.24998 2.03998 6.84998C2.03998 5.44998 3.13998 4.34998 4.53998 4.34998H5.18998C4.98998 4.80998 4.88998 5.31998 4.88998 5.84998V8.84998C4.88998 9.84998 5.09998 10.79 5.46998 11.65Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M18.53 11.65C19.28 11.41 19.94 10.97 20.46 10.45C21.36 9.44998 21.96 8.24998 21.96 6.84998C21.96 5.44998 20.86 4.34998 19.46 4.34998H18.81C19.01 4.80998 19.11 5.31998 19.11 5.84998V8.84998C19.11 9.84998 18.9 10.79 18.53 11.65Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  privacy:
    // prettier-ignore
    <svg className="w-5" viewBox="0 0 24 24" fill="none">
      <path d="M10.49 2.23006L5.50003 4.11006C4.35003 4.54006 3.41003 5.90006 3.41003 7.12006V14.5501C3.41003 15.7301 4.19003 17.2801 5.14003 17.9901L9.44003 21.2001C10.85 22.2601 13.17 22.2601 14.58 21.2001L18.88 17.9901C19.83 17.2801 20.61 15.7301 20.61 14.5501V7.12006C20.61 5.89006 19.67 4.53006 18.52 4.10006L13.53 2.23006C12.68 1.92006 11.32 1.92006 10.49 2.23006Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M9.05005 11.8701L10.66 13.4801L14.96 9.18005" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
};

export default QuickActions;
