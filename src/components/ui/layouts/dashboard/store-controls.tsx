import React, { useEffect, useRef, useState } from "react";
import authContext from "../../../../contexts/auth-context";
import useClickOutside from "../../../hooks/useClickOutside";
import router from "next/router";
import StoreLogo from "../../store-logo";
import cx from "classnames";
import Toggle from "../../toggle";
import { useRequest } from "../../../../api/utils";
import { UpdateStoreMaintenanceModeParams, UpdateStorefrontVersionParams } from "../../../../api/interfaces";
import { UpdateStorefrontVersion, UpdateStoreMaintenanceMode } from "../../../../api";
import { toast } from "../../toast";
import Can from "../../can";
import { SCOPES } from "../../../../assets/js/utils/permissions";
import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import ConfirmMaintenanceMode from "./confirm-maintenance-mode";
import { StorefrontVersion } from "@/assets/interfaces/stores";

interface Props {
  isOnboarding: boolean;
}

const StoreControls: React.FC<Props> = ({ isOnboarding }) => {
  const [opened, setOpened] = useState(false);
  const [storeListOpen, setStoreListOpen] = useState(true);
  var dropdown = useRef<HTMLDivElement>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const { stores, store, switchStore, user, updateStore } = authContext.useContainer();
  const [filteredStores, setFilteredStores] = useState([]);
  const showStoreList = stores?.length > 0;
  const [maintenanceMode, setMaintenanceMode] = useState(store.maintenance_mode);
  const { modals, toggleModal } = useModals(["confirm_maintenance"]);

  const updateMaintenanceModeRequest = useRequest<UpdateStoreMaintenanceModeParams>(UpdateStoreMaintenanceMode);
  const updateStorefrontVersionRequest = useRequest<UpdateStorefrontVersionParams>(UpdateStorefrontVersion);

  useEffect(() => {
    if (searchQuery === "") {
      setFilteredStores(stores.filter((s) => s.id !== store.id));
      return;
    }

    searchQuery.trim();
    const splitQueries = searchQuery.split(" ");

    const filteredStores = stores.filter((s) => {
      return s.id !== store.id && splitQueries.some((q) => s.name.toLocaleLowerCase().includes(q));
    });

    setFilteredStores([...filteredStores]);
  }, [searchQuery, stores]);

  useClickOutside(dropdown, () => {
    setOpened(false);
  });

  const handleStoreSwitch = (store: string) => {
    switchStore(store);
  };

  const handleCreateStore = () => {
    router.push("/create-store");
  };

  const handleEnableMaintenanceMode = (state: boolean) => {
    if (state === true) {
      toggleModal("confirm_maintenance");
      return;
    }

    updateStoreMaintenanceMode(state);

    // toast.promise(() => updateStoreMaintenanceMode(state), toastOpts);
  };

  const updateStoreMaintenanceMode = async (state: boolean) => {
    const request = async () => {
      const [res, err] = await updateMaintenanceModeRequest.makeRequest({ id: store.id, state });
      if (res) {
        setMaintenanceMode(state);
        updateStore({ maintenance_mode: state });
        return Promise.resolve(res);
      }

      return Promise.reject(err);
    };

    toast.promise(request, toastOpts(request, "maintenance"));
  };

  const UpdateStorefrontVersionFun = async (state: boolean) => {
    const request = async () => {
      const [res, err] = await updateStorefrontVersionRequest.makeRequest({
        version: state ? StorefrontVersion.V2 : StorefrontVersion.V1,
      });
      if (res) {
        updateStore({ flags: { storefront_version: state ? StorefrontVersion.V2 : StorefrontVersion.V1 } });
        return Promise.resolve(res);
      }

      return Promise.reject(err);
    };

    toast.promise(request, toastOpts(request, "storefront"));
  };

  const toggle = () => {
    if (!isOnboarding) {
      setOpened(!opened);
    }
  };

  const toggleStoreList = () => setStoreListOpen(!storeListOpen);

  const toggleClassNames = cx("h-12.5 w-full px-3 py-2.5 flex items-center justify-between", {
    "bg-grey-fields-100 rounded-10": !opened,
    "bg-white rounded-t-10": opened,
  });

  return (
    <>
      <div className="min-h-[50px] w-full relative z-[999]">
        <div
          ref={dropdown}
          className={cx(
            "rounded-10 overflow-hidden w-full",
            { "absolute elevation-shadow": opened },
            { relative: !opened }
          )}
        >
          <button className={toggleClassNames} onClick={toggle}>
            <div className="flex items-center">
              <StoreLogo storeName={store.name} className="h-7.5 w-7.5 rounded-full mr-2 text-sm" logo={store.logo} />
              <h5 className="text-1sm text-black font-bold font-display">{store.name}</h5>
            </div>

            {!isOnboarding && (
              <>
                {/* prettier-ignore */}
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" 
                  className={`${
                    opened ? "rotate-180" : "rotate-0"
                  } transition-transform ml-0.75 sm:ml-1 text-dark dropdown-caret transform flex-shrink-0 w-5`}
                >
                  <path d="M15 8L10 13L5 8" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}/>
                </svg>
              </>
            )}
          </button>

          {/* <div className="absolute top-full w-full left-0 bg-red-500 py-10 z-[999]"></div> */}
          <div
            className={cx(
              `overflow-hidden bg-white transform transition ease-out duration-300 left-0 relative w-full`,
              { "opacity-1 translate-y-0 px-3 pt-1.25": opened },
              { "pointer-events-none opacity-0 translate-y-4 max-h-0 p-0": !opened }
            )}
          >
            <ul className="flex flex-col w-full">
              {/* <li className="w-full border-t border-grey-border border-opacity-50">
                <button className="w-full py-3.75 flex items-center no-outline justify-between">
                  <div className="flex items-center">
                    <div className="text-black-placeholder mr-2">{icons.gift}</div>
                    <span className="text-black-secondary text-sm font-medium">Manage Features</span>
                  </div>
                  <div className="text-dark flex-shrink-0">{icons.arrowRight}</div>
                </button>
              </li> */}
              {showStoreList && (
                <li className="w-full border-t border-grey-border border-opacity-50 pb-3.75">
                  <button
                    className="w-full pt-3.75 flex items-center no-outline justify-between"
                    onClick={toggleStoreList}
                  >
                    <div className="flex items-center">
                      <div className="text-black-placeholder mr-2">{icons.store}</div>
                      <span className="text-black-secondary text-sm font-medium">Switch Stores</span>
                    </div>
                    {/* prettier-ignore */}
                    <svg viewBox="0 0 20 20" fill="none" 
                      className={`${
                        storeListOpen ? "rotate-180" : "rotate-0"
                      } transition-transform ml-0.75 sm:ml-1 text-dark dropdown-caret transform flex-shrink-0 w-5`}
                    >
                      <path d="M15 8L10 13L5 8" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}/>
                    </svg>
                  </button>
                  <div
                    className={cx(
                      "transform transition-all ease-out duration-300 overflow-hidden relative flex flex-col",
                      {
                        "max-h-0 translate-y-3 pointer-events-none": !storeListOpen,
                        "max-h-[160px] translate-y-0": storeListOpen,
                      }
                    )}
                  >
                    <div className="sticky top-0 pt-3.75 bg-white">
                      <input
                        type="text"
                        className="w-full h-9 bg-grey-fields-100 rounded-5 font-medium text-1xs py-2.5 px-3 placeholder:text-black-placeholder border border-transparent focus:border-primary-500 focus:placeholder-opacity-80 ease-out duration-300 transition-all no-outline focus:bg-opacity-50 text-black-secondary"
                        placeholder="Search Stores"
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                    <ul className="pt-2.5 flex flex-col w-full flex-1 overflow-y-auto">
                      {filteredStores.map((s, index) => (
                        <li className="w-full" key={index}>
                          <button
                            className="w-full flex items-center justify-between py-1.25"
                            onClick={() => handleStoreSwitch(s.id)}
                          >
                            <div className="flex items-center">
                              <StoreLogo
                                storeName={s.name}
                                className="h-6.25 w-6.25 rounded-full mr-2 text-xs"
                                logo={s.logo}
                              />
                              <h5 className="text-1xs text-black-secondary font-medium whitespace-nowrap max-w-[150px] md:max-w-[137px] xl:max-w-[148px] overflow-hidden overflow-ellipsis">
                                {s.name}
                              </h5>
                            </div>
                            <div className="text-dark flex-shrink-0">{icons.arrowRight}</div>
                          </button>
                        </li>
                      ))}
                    </ul>
                    {filteredStores.length == 0 && (
                      <p className="py-2.5 text-center text-xs text-grey-muted"> No stores found</p>
                    )}
                  </div>
                </li>
              )}
            </ul>
            <button
              className="py-3.75 border-t border-grey-border border-opacity-50 w-full flex items-center text-primary-500"
              onClick={handleCreateStore}
            >
              <div className="mr-2">{icons.plus}</div>
              <span className="font-medium text-sm">Add New Store</span>
            </button>
            <div className="w-full border-t border-grey-border justify-between flex border-opacity-50 py-3.75">
              <span className="font-medium text-sm text-dark ">New Storefront</span>
              <Toggle
                intialState={store.flags?.storefront_version === StorefrontVersion.V2}
                onChange={(state) => {
                  UpdateStorefrontVersionFun(state);
                }}
              />
            </div>
            <Can data={{ permission: SCOPES.SETTINGS.UPDATE_MAINTENANCE_MODE }}>
              <div className="w-full border-t border-grey-border justify-between flex border-opacity-50 py-3.75">
                <span className="font-medium text-sm text-dark ">Maintenance mode</span>
                <Toggle
                  intialState={maintenanceMode}
                  onChange={(state) => {
                    handleEnableMaintenanceMode(state);
                  }}
                />
              </div>
            </Can>
          </div>
        </div>
      </div>
      <Portal>
        <ConfirmMaintenanceMode
          show={modals.confirm_maintenance.show}
          toggle={() => toggleModal("confirm_maintenance")}
          complete={() => updateStoreMaintenanceMode(true)}
          title=""
        />
      </Portal>
    </>
  );
};

const toastOpts = (retry: Function, type: "maintenance" | "storefront") => ({
  loading: {
    title: type === "maintenance" ? "Updating maintenance mode" : "Updating storefront version",
    message: "Please wait...",
  },
  success: {
    title: type === "maintenance" ? "Maintenance mode updated!" : "Storefront version updated!",
    message: "Maintenance mode updated!",
  },
  error: {
    title: type === "maintenance" ? "Maintenance mode update failed!" : "Storefront version update failed!",
    message: "Something went wrong! Please retry",
    retry,
  },
});

const icons = {
  gift:
    // prettier-ignore
    <svg className="w-4.5" viewBox="0 0 18 18" fill="none">
      <path d="M14.9775 7.5H2.97748V13.5C2.97748 15.75 3.72748 16.5 5.97748 16.5H11.9775C14.2275 16.5 14.9775 15.75 14.9775 13.5V7.5Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
      <path d="M16.125 5.25V6C16.125 6.825 15.7275 7.5 14.625 7.5H3.375C2.2275 7.5 1.875 6.825 1.875 6V5.25C1.875 4.425 2.2275 3.75 3.375 3.75H14.625C15.7275 3.75 16.125 4.425 16.125 5.25Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
      <path d="M8.73002 3.75H4.59002C4.33502 3.4725 4.34252 3.045 4.61252 2.775L5.67752 1.71C5.95502 1.4325 6.41252 1.4325 6.69002 1.71L8.73002 3.75Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
      <path d="M13.4025 3.75H9.26251L11.3025 1.71C11.58 1.4325 12.0375 1.4325 12.315 1.71L13.38 2.775C13.65 3.045 13.6575 3.4725 13.4025 3.75Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
      <path d="M6.70502 7.5V11.355C6.70502 11.955 7.36502 12.3075 7.86752 11.985L8.57252 11.52C8.82752 11.355 9.15002 11.355 9.39752 11.52L10.065 11.97C10.56 12.3 11.2275 11.9475 11.2275 11.3475V7.5H6.70502Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
    </svg>,
  arrowRight:
    // prettier-ignore
    <svg className="w-4" viewBox="0 0 16 16" fill="none">
      <path d="M3.33331 8H7.99998H12.6666" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
      <path d="M8 3.33334L12.6667 8L8 12.6667" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
    </svg>,
  store:
    // prettier-ignore
    <svg className="w-4.5" viewBox="0 0 18 18" fill="none">
      <path d="M2.25751 8.415V11.7825C2.25751 15.15 3.60751 16.5 6.97501 16.5H11.0175C14.385 16.5 15.735 15.15 15.735 11.7825V8.415" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
      <path d="M8.99999 9C10.3725 9 11.385 7.8825 11.25 6.51L10.755 1.5H7.25249L6.74999 6.51C6.61499 7.8825 7.62749 9 8.99999 9Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
      <path d="M13.7325 9C15.2475 9 16.3575 7.77 16.2075 6.2625L15.9975 4.2C15.7275 2.25 14.9775 1.5 13.0125 1.5H10.725L11.25 6.7575C11.3775 7.995 12.495 9 13.7325 9Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
      <path d="M4.22997 9C5.46747 9 6.58497 7.995 6.70497 6.7575L6.86997 5.1L7.22997 1.5H4.94247C2.97747 1.5 2.22747 2.25 1.95747 4.2L1.75497 6.2625C1.60497 7.77 2.71497 9 4.22997 9Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
      <path d="M9 12.75C7.7475 12.75 7.125 13.3725 7.125 14.625V16.5H10.875V14.625C10.875 13.3725 10.2525 12.75 9 12.75Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.2}/>
    </svg>,
  plus:
    // prettier-ignore
    <svg className="w-4.5" viewBox="0 0 18 18" fill="none">
      <path d="M4.5 9H13.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M9 13.5V4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
};

export default StoreControls;
