import classNames from "classnames";
import Radio from "./form-elements/radio";

interface Props {
  title: React.ReactElement;
  icon: React.ReactElement;
  description: string | React.ReactElement;
  value: string;
  chosen: string;
  className?: string;
  showDetails?: boolean;
  onChange: () => void;
  name: string;
  disabled?: boolean;
}
const ItemSelectorCard: React.FC<Props> = ({
  children,
  onChange,
  value,
  chosen,
  title,
  icon,
  description,
  className,
  showDetails = true,
  name,
  disabled,
}) => {
  const isSelected = value === chosen;
  return (
    <div
      onClick={() => {
        if (disabled) {
          return;
        }
        onChange();
      }}
      className={classNames(
        "flex items-start cursor-pointer p-4.5 sm:p-5 bg-grey-fields-100 w-full",
        {
          "opacity-60": disabled,
        },
        className
      )}
    >
      <div className="flex-shrink-0 mr-3 sm:mr-3.75">
        <figure className="rounded-full w-8 h-8 sm:h-10 sm:w-10 bg-white shadow-card flex items-center justify-center">
          {icon}
        </figure>
      </div>
      <div className="w-full">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-display text-sm sm:text-base font-bold text-black -mb-1">{title} </h3>
            <p className="text-xs mt-1.25 sm:text-1xs text-black-muted">{description} </p>
          </div>
          <Radio name={name} value={value} chosen={chosen} onChange={onChange} disabled={disabled}></Radio>
        </div>
        {children && (
          <div
            onClick={(e) => e.stopPropagation()}
            className={`border-t overflow-hidden duration-200 ease-out border-grey-border border-opacity-50 ${
              isSelected && showDetails ? "pt-3.75 mt-3.75" : "h-0 border-none"
            }`}
          >
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

export default ItemSelectorCard;
