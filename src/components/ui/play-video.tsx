import React, { useEffect, useRef, useState } from "react";
import { SmoothHorizontalScrolling } from "../../assets/js/utils/functions";
import LazyImage from "../lazy-image";
import Modal, { ModalBody } from "../ui/modal";
import { title } from "process";
import CanvasBgFromVideo from "./canvas-bg-from-video";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  video: string;
  title: string;
}

const PlayVideoModal: React.FC<Props> = ({ show, toggle, video, title }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  return (
    <Modal title={title || "Play Video"} {...{ show, toggle }}>
      <ModalBody noPadding className="relative">
        <div className="w-full h-[70vh] flex items-center justify-center relative">
          <video
            src={video}
            ref={videoRef}
            controls
            className="max-w-full max-h-full object-contain z-[50]"
            playsInline
            crossOrigin="anonymous"
          ></video>
          <CanvasBgFromVideo videoRef={videoRef} />
        </div>
      </ModalBody>
    </Modal>
  );
};

export default PlayVideoModal;
