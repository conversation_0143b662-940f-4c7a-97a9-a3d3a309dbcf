import cx from "classnames";
import React, { ReactNode, useEffect, useRef, useState } from "react";
import useClickOutside from "../hooks/useClickOutside";
import Portal from "../portal";

type FunctionChild = (cond: boolean) => ReactNode;

interface Props {
  items: DropdownItem[];
  vPosition?: "TOP" | "BOTTOM" | "AUTO";
  hPosition?: "LEFT" | "RIGHT" | "AUTO";
  gap?: boolean;
  className?: string;
  children: ReactNode | FunctionChild;
  size?: "sm" | "md" | "lg";
  maxHeight?: string;
  maxWidth?: string;
  scrollable?: boolean;
  fullWidth?: boolean;
  // tiny?: boolean;
}

// Define the position type to include bottom
interface Position {
  top: number;
  bottom: number;
  left: number;
  width: number;
  right: number;
}

const Dropdown: React.FC<Props> = (props) => {
  const {
    vPosition = "BOTTOM",
    hPosition = "RIGHT",
    className,
    gap = true,
    size = "lg",
    maxHeight,
    maxWidth,
    scrollable = false,
    fullWidth = false,
  } = props;
  const [opened, setOpened] = useState(false);
  const [position, setPosition] = useState<Position>({ top: 0, bottom: 0, left: 0, width: 0, right: 0 });
  const [isAnimating, setIsAnimating] = useState(false);
  const [isPositioned, setIsPositioned] = useState(false);
  const [isFirstRender, setIsFirstRender] = useState(true);
  const [effectivePosition, setEffectivePosition] = useState<"TOP" | "BOTTOM">("BOTTOM");
  const [effectiveHorizontalPosition, setEffectiveHorizontalPosition] = useState<"LEFT" | "RIGHT">("RIGHT");
  const buttonClass =
    "flex items-center p-2 pl-2.5 text-black-secondary pr-2 hover:bg-gray-100 w-full hover:bg-opacity-50 duration-150";
  const toggleCaret = useRef<SVGElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const positioningTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
      if (positioningTimeoutRef.current) {
        clearTimeout(positioningTimeoutRef.current);
      }
    };
  }, []);

  // Determine the best position for the dropdown
  const determinePosition = () => {
    if (!triggerRef.current || !dropdownRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const dropdownHeight = dropdownRef.current.offsetHeight;
    const dropdownWidth = dropdownRef.current.offsetWidth;
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;

    // Calculate available space above and below
    const spaceAbove = triggerRect.top;
    const spaceBelow = windowHeight - triggerRect.bottom;

    // Add some buffer (20px) to ensure the dropdown doesn't touch the edge
    const buffer = 20;

    // Check if there's enough space in the preferred vertical direction
    const preferredPosition = vPosition === "TOP" ? "TOP" : "BOTTOM";
    const hasEnoughSpaceInPreferredDirection =
      preferredPosition === "TOP" ? spaceAbove >= dropdownHeight + buffer : spaceBelow >= dropdownHeight + buffer;

    // If there's enough space in the preferred direction, use it
    if (hasEnoughSpaceInPreferredDirection) {
      setEffectivePosition(preferredPosition);
    } else {
      // Otherwise, choose the direction with more space
      if (spaceAbove > spaceBelow) {
        setEffectivePosition("TOP");
      } else {
        setEffectivePosition("BOTTOM");
      }
    }

    // For horizontal positioning, only use LEFT alignment if explicitly set
    // Otherwise, default to RIGHT alignment
    if (hPosition === "LEFT") {
      setEffectiveHorizontalPosition("LEFT");
    } else {
      setEffectiveHorizontalPosition("RIGHT");
    }
  };

  // Update position when dropdown is opened
  useEffect(() => {
    if (opened && triggerRef.current) {
      // Reset positioning state
      setIsPositioned(false);
      setIsAnimating(false);

      const rect = triggerRef.current.getBoundingClientRect();
      const scrollY = window.scrollY;
      const scrollX = window.scrollX;

      // Store both top and bottom positions of the trigger
      setPosition({
        top: rect.top - 10,
        bottom: rect.bottom + 10,
        left: rect.left + scrollX,
        right: window.innerWidth - (rect.right + scrollX),
        width: rect.width,
      });

      // Wait for positioning to complete before showing the dropdown
      if (positioningTimeoutRef.current) {
        clearTimeout(positioningTimeoutRef.current);
      }

      // Use a longer delay for the first render to ensure proper positioning
      const positioningDelay = isFirstRender ? 100 : 50;

      positioningTimeoutRef.current = setTimeout(() => {
        // Determine the best position after the dropdown is in the DOM
        determinePosition();
        setIsPositioned(true);

        // Start animation after positioning is complete
        if (animationTimeoutRef.current) {
          clearTimeout(animationTimeoutRef.current);
        }

        animationTimeoutRef.current = setTimeout(() => {
          setIsAnimating(true);
          setIsFirstRender(false);
        }, 10);
      }, positioningDelay);
    } else {
      // When closing, stop the animation and reset positioning state
      setIsAnimating(false);
      setIsPositioned(false);
    }
  }, [opened, isFirstRender]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsAnimating(false);

        // Add a small delay before closing to allow the animation to complete
        if (animationTimeoutRef.current) {
          clearTimeout(animationTimeoutRef.current);
        }

        animationTimeoutRef.current = setTimeout(() => {
          setOpened(false);
        }, 200);
      }
    };

    // Handle scroll events to close dropdown when external scrolling occurs
    const handleScroll = (event: Event) => {
      // Check if the scroll event is from the dropdown itself
      const isDropdownScroll = dropdownRef.current?.contains(event.target as Node);

      // Only close if scrolling is happening outside the dropdown
      if (!isDropdownScroll && opened) {
        setIsAnimating(false);

        if (animationTimeoutRef.current) {
          clearTimeout(animationTimeoutRef.current);
        }

        animationTimeoutRef.current = setTimeout(() => {
          setOpened(false);
        }, 200);
      }
    };

    if (opened) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("scroll", handleScroll, true);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("scroll", handleScroll, true);
    };
  }, [opened]);

  // Handle toggle button click
  useEffect(() => {
    if (triggerRef.current) {
      const toggleCaretEl = triggerRef.current.querySelector(".dropdown-caret") as SVGElement;
      const toggleEl = triggerRef.current.querySelector(".dropdown-toggle");

      if (toggleCaretEl) {
        toggleCaret.current = toggleCaretEl;
      }

      if (toggleEl) {
        const toggleFn = (e: Event) => {
          e.stopPropagation();
          if (!opened) {
            setOpened(true);
          } else {
            setIsAnimating(false);

            if (animationTimeoutRef.current) {
              clearTimeout(animationTimeoutRef.current);
            }

            animationTimeoutRef.current = setTimeout(() => {
              setOpened(false);
            }, 200);
          }
        };
        toggleEl.addEventListener("click", toggleFn);

        return () => {
          toggleEl.removeEventListener("click", toggleFn);
        };
      }
    }
  }, [opened]);

  // Handle caret rotation
  useEffect(() => {
    if (toggleCaret.current) {
      toggleCaret.current.classList.toggle("rotate-180");
    }
  }, [opened]);

  // Calculate the animation position
  const getAnimationPosition = () => {
    const dropdownHeight = dropdownRef.current?.offsetHeight || 0;
    const dropdownWidth = dropdownRef.current?.offsetWidth || 0;

    let topPosition = 0;
    let leftPosition = 0;
    let transformY = "";
    let transformX = "";

    if (effectivePosition === "TOP") {
      // For TOP position, position the dropdown above the trigger
      topPosition = position.top - dropdownHeight - (gap ? 5 : 0);
      transformY = isAnimating ? "translateY(0)" : "translateY(5px)";
    } else {
      // For BOTTOM position, position the dropdown below the trigger
      topPosition = position.bottom + (gap ? 5 : 0);
      transformY = isAnimating ? "translateY(0)" : "translateY(-5px)";
    }

    if (effectiveHorizontalPosition === "LEFT") {
      // Align the left edge of the dropdown with the left edge of the trigger
      leftPosition = position.left;
      // No horizontal transform for LEFT alignment
      transformX = "";
    } else {
      // Align the right edge of the dropdown with the right edge of the trigger
      leftPosition = position.left + position.width - dropdownWidth;
      // No horizontal transform for RIGHT alignment
      transformX = "";
    }

    return {
      top: topPosition,
      left: leftPosition,
      transform: transformY,
    };
  };

  return (
    <div ref={triggerRef} className={cx("inline-block relative", className)} onClick={(e) => e.stopPropagation()}>
      {typeof props.children === "function" ? (props.children as FunctionChild)(opened) : props.children}

      <Portal>
        <div
          ref={dropdownRef}
          className={cx(
            `fixed z-[1000] rounded-10 bg-white shadow-md transition-all ease-out duration-200`,
            { "min-w-[100px]": size === "sm" },
            { "min-w-[160px]": size === "md" },
            { "min-w-[190px]": size === "lg" },
            { "opacity-0 pointer-events-none": !isAnimating || !opened || !isPositioned },
            { "opacity-100 pointer-events-auto": isAnimating && opened && isPositioned },
            { "overflow-hidden": !scrollable && !maxHeight },
            { "overflow-y-auto": scrollable || maxHeight }
          )}
          style={{
            ...getAnimationPosition(),
            ...(maxHeight ? { maxHeight } : {}),
            ...(maxWidth ? { maxWidth } : {}),
            ...(fullWidth ? { width: triggerRef.current?.clientWidth, right: 0 } : {}),
          }}
        >
          <ul>
            {props.items
              .filter((i) => !i.skip)
              .map((item, index) => (
                <li
                  key={index.toString()}
                  className="border-b border-grey-border border-opacity-50"
                  onClick={() => {
                    setIsAnimating(false);

                    if (animationTimeoutRef.current) {
                      clearTimeout(animationTimeoutRef.current);
                    }

                    animationTimeoutRef.current = setTimeout(() => {
                      setOpened(false);
                    }, 200);
                  }}
                >
                  {item.onClick && (
                    <button
                      key={index.toString()}
                      onClick={item?.disabled ? null : item.onClick}
                      className={cx(buttonClass, item.className, {
                        "!text-placeholder !cursor-not-allowed": item?.disabled,
                      })}
                    >
                      {item.icon ? <div className="flex-shrink-0">{item.icon}</div> : null}
                      <span className="text-[13.5px] ml-2 whitespace-nowrap inline-block">{item.text}</span>
                    </button>
                  )}

                  {item.link && (
                    <a
                      {...{
                        target: item.link.includes("http") ? "_blank" : "",
                        relative: item.link.includes("http") ? "noreferrer" : "",
                        href: item?.disabled ? "" : item.link,
                      }}
                      className={cx(buttonClass, item.className, {
                        "!text-placeholder !cursor-not-allowed": item?.disabled,
                      })}
                    >
                      {item.icon ? <div className="flex-shrink-0">{item.icon}</div> : null}
                      <span className="text-[13.5px] ml-2 whitespace-nowrap inline-block">{item.text}</span>
                    </a>
                  )}
                </li>
              ))}
          </ul>
        </div>
      </Portal>
    </div>
  );
};

export type DropdownItem = {
  text: React.ReactElement | string;
  link?: string;
  onClick?: VoidFunction | ((e?: React.MouseEvent) => void);
  icon?: JSX.Element;
  className?: string;
  skip?: boolean;
  disabled?: boolean;
};

export default Dropdown;
