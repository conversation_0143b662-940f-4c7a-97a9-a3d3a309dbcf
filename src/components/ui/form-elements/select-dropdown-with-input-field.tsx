import React, { useState, useRef, useEffect } from "react";

interface SelectOption {
  text: string;
  value: any;
}

interface SelectInputFieldProps {
  label: string;
  name?: string;
  selectOptions: SelectOption[];
  selectValue?: any;
  inputValue?: string;
  onSelectChange?: (value: any) => void;
  onInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  selectPlaceholder?: string;
  inputPlaceholder?: string;
  selectWidth?: string; // Tailwind width class e.g., 'w-1/3'
  inputWidth?: string; // Tailwind width class e.g., 'w-2/3'
  error?: string;
  disabled?: boolean;
}

const SelectInputField: React.FC<SelectInputFieldProps> = ({
  label,
  name,
  selectOptions,
  selectValue,
  inputValue,
  onSelectChange,
  onInputChange,
  selectPlaceholder = "Select",
  inputPlaceholder = "Enter text",
  selectWidth = "w-1/3",
  inputWidth = "w-2/3",
  error,
  disabled,
}) => {
  const [isSelectOpen, setIsSelectOpen] = useState(false);
  const [selectSearchText, setSelectSearchText] = useState("");
  const [filteredOptions, setFilteredOptions] = useState(selectOptions);

  const selectRef = useRef<HTMLDivElement>(null);

  const handleSelectToggle = () => {
    if (!disabled) {
      setIsSelectOpen((prev) => !prev);
    }
  };

  const handleSelectOptionClick = (optionValue: any) => {
    if (onSelectChange) {
      onSelectChange(optionValue);
    }
    setIsSelectOpen(false);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
      setIsSelectOpen(false);
    }
  };

  useEffect(() => {
    if (isSelectOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isSelectOpen]);

  useEffect(() => {
    if (selectSearchText) {
      const filtered = selectOptions.filter((option) =>
        option.text.toLowerCase().includes(selectSearchText.toLowerCase())
      );
      setFilteredOptions(filtered);
    } else {
      setFilteredOptions(selectOptions);
    }
  }, [selectSearchText, selectOptions]);

  const selectedOption = selectOptions.find((option) => option.value === selectValue);

  return (
    <div className={`w-full mt-4 first:mt-0 ${disabled ? "opacity-70" : ""}`}>
      {/* Container */}
      <div
        className={`relative flex items-center border rounded-lg h-12.5 overflow-hidden ${
          error ? "border-accent-red-500" : "border-grey-divider"
        } ${disabled ? "bg-gray-100" : "bg-white"}`}
      >
        {/* Label */}
        <label
          className="absolute left-1/2 transform -translate-x-1/2 -top-2 bg-white px-1 text-xs text-placeholder"
          style={{ zIndex: 1 }}
        >
          {label}
        </label>
        {/* Select Dropdown */}
        <div className={`relative ${selectWidth} h-full`} ref={selectRef}>
          <button
            type="button"
            className="h-full w-full flex items-center px-4 text-left focus:outline-none"
            onClick={handleSelectToggle}
            disabled={disabled}
          >
            <span className={`flex-1 truncate ${selectedOption ? "text-black-secondary" : "text-placeholder"}`}>
              {selectedOption ? selectedOption.text : selectPlaceholder}
            </span>
            {/* Arrow Icon */}
            <svg
              width="18"
              viewBox="0 0 20 20"
              fill="none"
              className={`transition-transform ml-2 ${isSelectOpen ? "transform rotate-180" : ""}`}
            >
              <path
                d="M15 7.5L10 12.5L5 7.5"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
          {/* Dropdown Options */}
          {isSelectOpen && (
            <div className="absolute top-full left-0 right-0 bg-white border border-grey-divider shadow-card z-10 mt-1 max-h-60 overflow-auto">
              {/* Optional Search Input */}
              <div className="p-2 border-b border-grey-divider">
                <input
                  type="text"
                  placeholder="Search..."
                  value={selectSearchText}
                  onChange={(e) => setSelectSearchText(e.target.value)}
                  className="w-full px-2 py-1 border border-grey-divider rounded focus:outline-none"
                />
              </div>
              <ul>
                {filteredOptions.length > 0 ? (
                  filteredOptions.map((option, index) => (
                    <li
                      key={index}
                      className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleSelectOptionClick(option.value)}
                    >
                      {option.text}
                    </li>
                  ))
                ) : (
                  <li className="px-4 py-2 text-gray-500">No options found</li>
                )}
              </ul>
            </div>
          )}
        </div>
        {/* Thin Line Separator */}
        <div className="h-full w-px bg-grey-divider"></div>
        {/* Input Field */}
        <div className={`relative ${inputWidth} h-full`}>
          <input
            type="text"
            name={name}
            value={inputValue}
            onChange={onInputChange}
            placeholder={inputPlaceholder}
            disabled={disabled}
            className="h-full w-full px-4 text-black-secondary placeholder-placeholder focus:outline-none"
          />
        </div>
      </div>
      {/* Error Message */}
      {error && (
        <div className="text-accent-red-500 text-xs font-medium mt-1 flex items-center">
          {/* Icon */}
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="mr-1">
            <path
              d="M7.018 3.55288L2.15392 11.6731C2.05363 11.8467 2.00057 12.0437 2 12.2442C1.99944 12.4447 2.0514 12.6419 2.15071 12.8162C2.25003 12.9904 2.39323 13.1356 2.56607 13.2373C2.73892 13.339 2.93538 13.3937 3.13592 13.3959H12.8641C13.0646 13.3937 13.2611 13.339 13.4339 13.2373C13.6068 13.1356 13.75 12.9904 13.8493 12.8162C13.9486 12.6419 14.0006 12.4447 14 12.2442C13.9994 12.0437 13.9464 11.8467 13.8461 11.6731L8.982 3.55288C8.87963 3.3841 8.73548 3.24456 8.56347 3.14772C8.39146 3.05088 8.1974 3 8 3C7.8026 3 7.60854 3.05088 7.43653 3.14772C7.26452 3.24456 7.12037 3.3841 7.018 3.55288V3.55288Z"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path d="M8 6.50452V8.8016" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8 11.0992H8.00718" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {/* Error Message */}
          <span className="inline-block">{error}</span>
        </div>
      )}
    </div>
  );
};

export default SelectInputField;
