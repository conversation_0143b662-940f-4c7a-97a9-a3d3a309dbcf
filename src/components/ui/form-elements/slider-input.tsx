import useDebounce from "@/components/hooks/useDebounce";
import { ReactElement, useEffect, useRef, useState } from "react";
import InputField from "./input-field";
import classNames from "classnames";
import useScreenSize from "@/components/hooks/useScreenSize";

interface Props {
  min: number;
  max: number;
  onChange: (value: number) => void;
  value: number;
  start?: number;
  className?: string;
  theme?: "primary" | "success";
  maxLabel?: string;
  minLabel?: string;
  showLabel?: boolean;
  debounceTimeout?: number;
  customTrack?: ReactElement;
  customHandle?: ReactElement;
}

const SliderInput: React.FC<Props> = ({
  min,
  onChange,
  value,
  className,
  max,
  theme = "primary",
  maxLabel,
  minLabel,
  showLabel = true,
  start = 0,
  debounceTimeout = 100,
  customHandle,
  customTrack,
}) => {
  const button = useRef<HTMLButtonElement>(null);

  const [relativeLeft, setRelativeLeft] = useState(0);
  const [val, setValue] = useState(0);

  useDebounce(val, debounceTimeout, () => onChange(val));

  const { width } = useScreenSize();

  useEffect(() => {
    if (button.current) {
      let mouseDown = false;

      const sliderElement = document.getElementById("slider-container");
      const handleElement = document.getElementById("slider-handle");

      const minXPos = (min / (max - start + 1)) * sliderElement?.clientWidth;

      if (value > 0 && value >= min && value <= max && value >= start) {
        const relativeLeft = (value / (max - start + 1)) * sliderElement?.clientWidth;
        setRelativeLeft(relativeLeft);
      } else {
        setRelativeLeft(minXPos);
      }

      const handleDown = () => {
        mouseDown = true;
      };

      const handleUp = () => {
        mouseDown = false;
      };

      const handleMove = (event: any) => {
        if (mouseDown) {
          event.stopPropagation();
          const dragX = event.x ?? event.touches[0].clientX;
          handleDelta(dragX);
        }
      };

      const handleClick = (event: any) => {
        event.stopPropagation();
        const element = event.target;
        if (element.id !== "slider-container") return;
        const clickX = event.x ?? event.touches[0].clientX;
        handleDelta(clickX);
      };

      const handleDelta = (xPos: number) => {
        const handleWidth = handleElement?.clientWidth;
        const sliderWidth = sliderElement?.clientWidth;
        const sliderRect = sliderElement.getBoundingClientRect();

        let relativeLeft = xPos - (sliderRect.x + handleWidth / 2);

        const atMin = relativeLeft <= minXPos;
        const atMax = relativeLeft >= sliderWidth - handleWidth;

        if (atMin) {
          relativeLeft = minXPos;
        } else if (atMax) {
          relativeLeft = sliderWidth - handleWidth;
        }

        const value = Math.ceil((relativeLeft / sliderWidth) * (max - start + 1));
        setValue(value);
        setRelativeLeft(relativeLeft);
      };

      button.current.addEventListener("mousedown", handleDown);
      button.current.addEventListener("touchstart", handleDown);

      sliderElement?.addEventListener("click", handleClick);

      document.addEventListener("mouseup", handleUp);
      document.addEventListener("touchend", handleUp);

      document.addEventListener("mousemove", handleMove);
      document.addEventListener("touchmove", handleMove);

      return () => {
        button.current?.removeEventListener("mousedown", handleDown);
        button.current?.removeEventListener("touchstart", handleDown);
        sliderElement?.removeEventListener("click", handleClick);

        document.removeEventListener("mouseup", handleUp);
        document.removeEventListener("touchend", handleUp);
        document.removeEventListener("mousemove", handleMove);
        document.removeEventListener("touchmove", handleMove);
      };
    }
  }, [button.current, max, min, width]);

  return (
    <div className={`${className} w-full flex items-center gap-2.5`}>
      <div className="w-full">
        <div
          id="slider-container"
          className={classNames("flex-1  relative", {
            "h-2.5 rounded-full": !customTrack,
            "h-[fit-content]": customTrack,
            [themes[theme].track]: !customHandle,
          })}
        >
          {customTrack && customTrack}
          {!customTrack && (
            <div
              style={{ width: `${relativeLeft + 10}px` }}
              className={classNames(
                "h-full absolute left-0 bottom-0 pointer-events-none rounded-full",
                themes[theme].progress
              )}
            ></div>
          )}
          <button
            style={{ left: relativeLeft }}
            ref={button}
            className={classNames("left-0 absolute top-[50%] translate-y-[-50%]", {
              [themes[theme].handle]: !customHandle,
              "block w-5 h-5 rounded-full border-[3px] border-white shadow-slider-handle": !customHandle,
              "block w-[fit-content] h-full l ": customHandle,
            })}
            id="slider-handle"
            type="button"
          >
            {customHandle && customHandle}
          </button>
        </div>
        {showLabel && (
          <div className="flex items-center justify-between w-full mt-1.5">
            <span className="text-dark text-xs">{minLabel ?? start}</span>
            <span className="text-dark text-xs">{maxLabel ?? max}</span>
          </div>
        )}
      </div>
    </div>
  );
};

const themes = {
  primary: {
    track: "bg-primary-100",
    handle: "bg-primary-500",
    progress: "bg-primary-400",
  },
  success: {
    track: "bg-grey-fields-100",
    handle: "bg-accent-green-500",
    progress: "bg-accent-green-500",
  },
};

export default SliderInput;
