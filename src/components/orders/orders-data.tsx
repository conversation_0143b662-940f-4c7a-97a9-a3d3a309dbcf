import React, { useState } from "react";
import {
  formatDate,
  genarateStringFromVariantValues,
  removeCountryCode,
  toAppUrl,
  toCurrency,
} from "../../assets/js/utils/functions";
import { OrderInterface, ProductItemInterface, CartItem } from "../../assets/interfaces";
import { getItemThumbnail } from "../../assets/js/utils/utils";
import ContentWithCopy from "../ui/content-with-copy";
import StoreLogo from "../ui/store-logo";
import LazyImage from "../lazy-image";

interface CustomerDetailsProps {
  order: OrderInterface;
  showCustomer?: () => void;
}

const CustomerDetails: React.FC<CustomerDetailsProps> = ({ order, showCustomer }) => {
  const { customer } = order;
  const [showDeliveryDetails, setShowDeliveryDetails] = useState(false);

  return (
    <div className="bg-grey-header border border-dashed rounded-[12px] px-4 mb-2">
      <div
        className={`flex justify-between items-center py-2.5 cursor-pointer ${showDeliveryDetails ? "border-b" : ""}`}
        onClick={() => setShowDeliveryDetails(!showDeliveryDetails)}
      >
        <div className="flex items-center">
          {/* prettier-ignore */}
          <svg width="30" height="30"  fill="none" >
            <path d="M30 15C30 6.71573 23.2843 0 15 0C6.71573 0 0 6.71573 0 15C0 23.2843 6.71573 30 15 30C23.2843 30 30 23.2843 30 15Z" fill="white"/>
            <path d="M14.6 13.7938C14.5375 13.7875 14.4625 13.7875 14.3938 13.7938C12.9063 13.7438 11.725 12.525 11.725 11.025C11.725 9.49375 12.9625 8.25 14.5 8.25C16.0313 8.25 17.275 9.49375 17.275 11.025C17.2688 12.525 16.0875 13.7438 14.6 13.7938Z" stroke="#332098" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M11.475 16.1C9.9625 17.1125 9.9625 18.7625 11.475 19.7688C13.1937 20.9188 16.0125 20.9188 17.7312 19.7688C19.2437 18.7563 19.2437 17.1063 17.7312 16.1C16.0187 14.9562 13.2 14.9562 11.475 16.1Z" stroke="#332098" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <h4 className="text-sm text-dark font-semibold ml-1 mt-1.5">Customer Details</h4>
        </div>
        <button>
          {/* prettier-ignore */}
          <svg width="14" height="8" viewBox="0 0 14 8" fill="none" className={`ml-1 transition-all duration-100 ease-out transform ${showDeliveryDetails ? "" : "rotate-180"}`}>
            <path d="M13.2754 7.35796C13.0535 7.57985 12.7063 7.60002 12.4617 7.41847L12.3916 7.35796L7.00016 1.96685L1.60877 7.35796C1.38688 7.57985 1.03966 7.60002 0.794985 7.41847L0.724887 7.35796C0.502997 7.13607 0.482825 6.78885 0.664371 6.54417L0.724887 6.47407L6.55822 0.640741C6.78011 0.418852 7.12733 0.39868 7.372 0.580225L7.4421 0.640741L13.2754 6.47407C13.5195 6.71815 13.5195 7.11388 13.2754 7.35796Z" fill="#656565" />
          </svg>
        </button>
      </div>
      {showDeliveryDetails && (
        <dl className="text-sm text-dark py-5">
          <div className="flex mb-3">
            <dt className="text-grey-subtext min-w-[200px]">Name</dt>
            <div
              className={`flex items-center ${showCustomer ? "text-primary-500 cursor-pointer" : "text-dark"}`}
              onClick={showCustomer}
            >
              {showCustomer && <StoreLogo logo={null} storeName={customer?.name} className="h-7 w-7 mr-1.5 -mt-1" />}
              <dd className="font-semibold">{customer.name}</dd>
              {showCustomer && (
                <>
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 8 8" fill="none" className="w-2 -mt-0.5 ml-1">
                    <path d="M1 7L7 1" stroke="currentColor" strokeLinecap="round" strokeWidth={1.5} strokeLinejoin="round" />
                    <path d="M1 1H7V7" stroke="currentColor" strokeLinecap="round" strokeWidth={1.5} strokeLinejoin="round" />
                  </svg>
                </>
              )}
            </div>
          </div>
          {customer?.email && (
            <div className="flex mb-3">
              <dt className="text-grey-subtext min-w-[200px]">Email</dt>
              <dd className="font-semibold">{customer.email}</dd>
            </div>
          )}
          <div className="flex">
            <dt className="text-grey-subtext min-w-[200px]">Phone Number</dt>
            <dd className="font-semibold">{removeCountryCode(customer.phone)}</dd>
          </div>
        </dl>
      )}
    </div>
  );
};
interface DeliveryDetailsProps {
  order: OrderInterface;
}

const DeliveryDetails: React.FC<DeliveryDetailsProps> = ({ order }) => {
  const {
    delivery_info: { delivery_address, name, phone, delivery_area },
  } = order;
  const [showDeliveryDetails, setShowDeliveryDetails] = useState(false);

  return (
    <div className="bg-grey-header border border-dashed rounded-[12px] px-4">
      <div
        className={`flex justify-between items-center py-2.5 cursor-pointer ${showDeliveryDetails ? "border-b" : ""}`}
        onClick={() => setShowDeliveryDetails(!showDeliveryDetails)}
      >
        <div className="flex items-center">
          {/* prettier-ignore */}
          <svg width="30" height="30" viewBox="0 0 30 30" fill="none">
            <rect width="30" height="30" rx="15" fill="white" />
            <path d="M14.9998 15.8941C16.0768 15.8941 16.9498 15.0211 16.9498 13.9441C16.9498 12.8672 16.0768 11.9941 14.9998 11.9941C13.9228 11.9941 13.0498 12.8672 13.0498 13.9441C13.0498 15.0211 13.9228 15.8941 14.9998 15.8941Z" stroke="#332098" strokeWidth="1.40625" />
            <path d="M9.76256 12.8063C10.9938 7.39375 19.0126 7.4 20.2376 12.8125C20.9563 15.9875 18.9813 18.675 17.2501 20.3375C15.9938 21.55 14.0063 21.55 12.7438 20.3375C11.0188 18.675 9.04381 15.9813 9.76256 12.8063Z" stroke="#332098" strokeWidth="1.40625" />
          </svg>
          <h4 className="text-sm text-dark font-semibold ml-1 mt-1.5">Delivery Details</h4>
        </div>
        <button>
          {/* prettier-ignore */}
          <svg width="14" height="8" viewBox="0 0 14 8" fill="none" className={`ml-1 transition-all duration-100 ease-out transform ${showDeliveryDetails ? "" : "rotate-180"}`}>
            <path d="M13.2754 7.35796C13.0535 7.57985 12.7063 7.60002 12.4617 7.41847L12.3916 7.35796L7.00016 1.96685L1.60877 7.35796C1.38688 7.57985 1.03966 7.60002 0.794985 7.41847L0.724887 7.35796C0.502997 7.13607 0.482825 6.78885 0.664371 6.54417L0.724887 6.47407L6.55822 0.640741C6.78011 0.418852 7.12733 0.39868 7.372 0.580225L7.4421 0.640741L13.2754 6.47407C13.5195 6.71815 13.5195 7.11388 13.2754 7.35796Z" fill="#656565" />
          </svg>
        </button>
      </div>
      {showDeliveryDetails && (
        <dl className="text-sm text-dark py-5">
          {name && (
            <div className="flex mb-3">
              <dt className="text-grey-subtext min-w-[200px]">Contact Name</dt>
              <dd className="font-semibold">{name}</dd>
            </div>
          )}
          {phone && (
            <div className="flex mb-3">
              <dt className="text-grey-subtext min-w-[200px]">Contact Phone Number</dt>
              <dd className="font-semibold">{removeCountryCode(phone)}</dd>
            </div>
          )}
          {delivery_area && (
            <div className="flex mb-3">
              <dt className="text-grey-subtext min-w-[200px]">Delivery Area</dt>
              <dd className="font-semibold">{delivery_area.name}</dd>
            </div>
          )}
          <div className="flex">
            <dt className="text-grey-subtext min-w-[200px]">Delivery Address</dt>
            <dd className="font-semibold">{delivery_address}</dd>
          </div>
        </dl>
      )}
    </div>
  );
};

interface OrderDetailsProps {
  order: OrderInterface;
  showProductDetails?: (item: ProductItemInterface) => void;
  showHeader?: boolean;
}

const OrderDetails: React.FC<OrderDetailsProps> = ({ order, showProductDetails, showHeader }) => {
  const { items, fees, total_amount, id, discount_amount, status, created_at } = order;
  return (
    <div className="border border-dashed rounded-15 mb-5 overflow-hidden">
      {showHeader && (
        <div className="flex flex-col bg-grey-header items-center py-5 sm:py-7.5">
          <figure className="w-11 h-11 sm:h-12.5 sm:w-12.5 rounded-full bg-accent-500 flex items-center justify-center">
            {/* prettier-ignore */}
            <svg viewBox="0 0 22 23" fill="none" className="w-4 sm:w-5">
              <mask id="mask0_2642_25196" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="5" width="22" height="18">
                <path fillRule="evenodd" clipRule="evenodd" d="M0.166504 5.08398H21.3845V22.6141H0.166504V5.08398Z" fill="#fff"/>
              </mask>
              <g mask="url(#mask0_2642_25196)">
                <path fillRule="evenodd" clipRule="evenodd" d="M5.275 6.70898C4.79725 6.70898 3.20041 6.90182 2.74216 9.37615L1.90583 15.8762C1.63391 17.6994 1.84408 19.0189 2.532 19.8173C3.21125 20.606 4.42675 20.9895 6.24674 20.9895H15.2904C16.4257 20.9895 17.976 20.7631 18.912 19.6819C19.6552 18.825 19.9108 17.5488 19.6725 15.8881L18.8286 9.33173C18.4689 7.71648 17.5199 6.70898 16.3022 6.70898H5.275ZM15.2904 22.6145H6.24675C3.92516 22.6145 2.30775 22.0457 1.30133 20.8768C0.290579 19.7047 -0.047421 17.9464 0.297079 15.6519L1.13775 9.12373C1.69025 6.13048 3.71066 5.08398 5.275 5.08398H16.3022C17.8731 5.08398 19.783 6.12723 20.4276 9.05332L21.2823 15.6692C21.5889 17.8045 21.2054 19.5172 20.1405 20.7457C19.081 21.9677 17.404 22.6145 15.2904 22.6145V22.6145Z" fill="#fff"/>
              </g>
              <path fillRule="evenodd" clipRule="evenodd" d="M15.4392 6.47102C14.9907 6.47102 14.6267 6.10702 14.6267 5.65852C14.6267 3.52543 12.8912 1.79102 10.7592 1.79102H10.7429C9.72351 1.79102 8.73009 2.2016 8.00968 2.91768C7.28601 3.6381 6.87109 4.63802 6.87109 5.65852C6.87109 6.10702 6.50709 6.47102 6.05859 6.47102C5.61009 6.47102 5.24609 6.10702 5.24609 5.65852C5.24609 4.20793 5.83543 2.78985 6.86243 1.7661C7.88618 0.748849 9.29776 0.166016 10.7397 0.166016H10.7624C13.7882 0.166016 16.2517 2.62952 16.2517 5.65852C16.2517 6.10702 15.8877 6.47102 15.4392 6.47102" fill="#fff"/>
              <path fillRule="evenodd" clipRule="evenodd" d="M13.9717 11.3496H13.9219C13.4734 11.3496 13.1094 10.9856 13.1094 10.5371C13.1094 10.0886 13.4734 9.72461 13.9219 9.72461C14.3704 9.72461 14.7593 10.0886 14.7593 10.5371C14.7593 10.9856 14.4202 11.3496 13.9717 11.3496" fill="#fff"/>
              <path fillRule="evenodd" clipRule="evenodd" d="M7.65471 11.3496H7.60596C7.15746 11.3496 6.79346 10.9856 6.79346 10.5371C6.79346 10.0886 7.15746 9.72461 7.60596 9.72461C8.05446 9.72461 8.44337 10.0886 8.44337 10.5371C8.44337 10.9856 8.10321 11.3496 7.65471 11.3496" fill="#fff"/>
            </svg>
          </figure>
          <span className="text-sm mt-5">
            {formatDate(created_at, { month: "short", day: "numeric", year: "numeric" })}
          </span>
          <h5 className="text-2xl mt-1.5 mb-2 font-bold">
            <span className="text-dark mr-1.5">Order</span>
            <span>{id}</span>
          </h5>
          <div className="flex bg-white rounded-full w-max py-1 pl-1.5 pr-2">
            {/* prettier-ignore */}
            <svg viewBox="0 0 28 28" fill="none" className="w-6 sm:w-7">
              <rect width="28" height="28" rx="14" fill="#F1F1F9" />
              <path d="M13.9998 14.9756C13.9023 14.9756 13.8048 14.9531 13.7148 14.9006L7.09232 11.0681C6.82982 10.9106 6.73232 10.5656 6.88982 10.2956C7.04732 10.0256 7.39232 9.93563 7.66232 10.0931L13.9998 13.7606L20.2998 10.1156C20.5698 9.95813 20.9148 10.0556 21.0723 10.3181C21.2298 10.5881 21.1323 10.9331 20.8698 11.0906L14.2923 14.9006C14.1948 14.9456 14.0973 14.9756 13.9998 14.9756Z" fill="#332098" />
              <path d="M14 21.7693C13.6925 21.7693 13.4375 21.5143 13.4375 21.2068V14.4043C13.4375 14.0968 13.6925 13.8418 14 13.8418C14.3075 13.8418 14.5625 14.0968 14.5625 14.4043V21.2068C14.5625 21.5143 14.3075 21.7693 14 21.7693Z" fill="#332098" />
              <path d="M14 22.0625C13.34 22.0625 12.6875 21.92 12.17 21.635L8.16498 19.4075C7.07748 18.8075 6.22998 17.36 6.22998 16.115V11.8775C6.22998 10.6325 7.07748 9.1925 8.16498 8.585L12.17 6.365C13.1975 5.795 14.8025 5.795 15.83 6.365L19.835 8.5925C20.9225 9.1925 21.77 10.64 21.77 11.885V16.1225C21.77 17.3675 20.9225 18.8075 19.835 19.415L15.83 21.635C15.3125 21.92 14.66 22.0625 14 22.0625ZM14 7.0625C13.5275 7.0625 13.0625 7.16 12.7175 7.3475L8.71248 9.575C7.99248 9.9725 7.35498 11.0525 7.35498 11.8775V16.115C7.35498 16.94 7.99248 18.02 8.71248 18.425L12.7175 20.6525C13.4 21.035 14.6 21.035 15.2825 20.6525L19.2875 18.425C20.0075 18.02 20.645 16.9475 20.645 16.115V11.8775C20.645 11.0525 20.0075 9.9725 19.2875 9.5675L15.2825 7.34C14.9375 7.16 14.4725 7.0625 14 7.0625Z" fill="#332098" />
            </svg>
            <span className="text-xs sm:text-sm text-primary-700 font-semibold ml-1.5 pt-1.5 inline-block">
              {status}
            </span>
          </div>
        </div>
      )}
      <div className="h-full overflow-y-auto">
        {items.map((item, index) => (
          <Item item={item} key={index} showProductDetails={showProductDetails} />
        ))}
      </div>
      <div className="border-t text-1xs sm:text-sm text-grey-subtext py-4 sm:py-5 px-4 space-y-2.5 sm:space-y-3">
        {fees.map((fee, index) => (
          <div className="flex justify-between" key={index}>
            <div className="flex items-center flex-1 mr-1">
              <figure
                className={`w-6 h-6 sm:h-7.5 sm:w-7.5 rounded-full bg-opacity-8 flex items-center justify-center flex-shrink-0 ${
                  FEE_COLOR_CODES[fee.type]
                }`}
              >
                {ORDER_ICONS[fee.type]}
              </figure>
              <span className="ml-1.5 mt-1 sm:mt-2">{fee.label}</span>
            </div>
            <span className="text-dark font-semibold mt-1 sm:mt-2 whitespace-nowrap">{toCurrency(fee.amount)}</span>
          </div>
        ))}
        <div className="flex justify-between">
          <div className="flex">
            <figure className="w-6 h-6 sm:h-7.5 sm:w-7.5 rounded-full bg-primary-500 bg-opacity-5 text-primary-500 flex items-center justify-center flex-shrink-0">
              {ORDER_ICONS.FEE}
            </figure>
            <span className="ml-1.5 mt-1 sm:mt-2">Total Amount</span>
          </div>
          <span className="text-dark font-semibold mt-1 sm:mt-2">{toCurrency(total_amount)}</span>
        </div>
        <div className="flex justify-between">
          <div className="flex">
            s
            <figure className="w-6 h-6 sm:h-7.5 sm:w-7.5 rounded-full bg-primary-500 bg-opacity-5 text-primary-500 flex items-center justify-center flex-shrink-0">
              {ORDER_ICONS.LINK}
            </figure>
            <span className="ml-1.5 mt-1 sm:mt-2">Order Link</span>
          </div>
          <ContentWithCopy text={toAppUrl(`orders/${id}`)} className="max-w-[50%] overflow-hidden">
            <span className="text-1xs font-semibold text-primary-500 inline-block mt-0.5 max-w-full overflow-ellipsis whitespace-nowrap overflow-hidden">
              {toAppUrl(`orders/${id}`)}
            </span>
          </ContentWithCopy>
        </div>
      </div>
    </div>
  );
};

const Item: React.FC<{ item: CartItem; showProductDetails?: (item: ProductItemInterface) => void }> = ({
  item,
  showProductDetails,
}) => {
  const isImageVariant = item.item?.variants?.type === "images";
  const isCustomVariant = item.item?.variants?.type === "custom";
  const deletedOrUnavailable = item.is_deleted || item.is_unavailable;
  const variantHasExtraOption = isImageVariant && item.item?.variants?.options[0]?.values !== undefined;

  return (
    <div
      className="w-full flex items-center px-5 sm:px-6.25 py-3 sm:py-3.75 order-item hover:bg-grey-bg hover:bg-opacity-70 cursor-pointer"
      onClick={showProductDetails ? () => showProductDetails({ ...item.item, price: getItemPrice(item) }) : null}
    >
      <figure className="h-[45px] w-[45px] sm:h-12.5 sm:w-12.5 rounded-md overflow-hidden flex-shrink-0 relative">
        <LazyImage
          src={isImageVariant ? item.variant.image : getItemThumbnail(item.item)}
          alt={item.item.name}
          className={`h-full w-full object-cover`}
        />
      </figure>
      <div className="flex-1 ml-2.5 flex items-center justify-between overflow-hidden">
        <div className="flex-1 overflow-hidden">
          <h5 className="text-dark overflow-hidden text-1sm sm:text-base max-w-full whitespace-nowrap overflow-ellipsis -mb-1.5 mt-1.5 capitalize">
            {item?.item?.name}{" "}
            {isCustomVariant && !item.is_deleted && (
              <span className="text-grey-subtext">({genarateStringFromVariantValues(item?.variant?.values)})</span>
            )}
          </h5>
          <span className="text-black font-semibold text-xs">
            {toCurrency(item?.variant ? item?.variant.price : item?.item.price)}
          </span>
        </div>
        <div className="bg-grey-loader px-2 pt-1.5 pb-0.5 rounded-5 text-1xs flex-shrink-0">
          Quantity: <b className="text-black inline-block ml-1">{item.quantity}</b>
        </div>
      </div>
    </div>
  );
};

const getItemPrice = (cartItem: CartItem) => {
  return cartItem.is_deleted || cartItem.is_unavailable
    ? 0
    : cartItem.variant
    ? cartItem.variant.price
    : cartItem.item.price;
};

export const FEE_COLOR_CODES = {
  DISCOUNT: "text-danger-400 bg-danger-400",
  DELIVERY: "text-success bg-success",
};

export const ORDER_ICONS = {
  DELIVERY:
    // prettier-ignore
    <svg viewBox="0 0 24 24" fill="none" className="w-3.5 sm:w-4">
      <path d="M3.17017 7.43994L12.0002 12.5499L20.7701 7.46991" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12.0002 21.61V12.54" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M9.93011 2.48L4.59012 5.45003C3.38012 6.12003 2.39014 7.80001 2.39014 9.18001V14.83C2.39014 16.21 3.38012 17.89 4.59012 18.56L9.93011 21.53C11.0701 22.16 12.9401 22.16 14.0801 21.53L19.4201 18.56C20.6301 17.89 21.6201 16.21 21.6201 14.83V9.18001C21.6201 7.80001 20.6301 6.12003 19.4201 5.45003L14.0801 2.48C12.9301 1.84 11.0701 1.84 9.93011 2.48Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M17 13.2401V9.58014L7.51001 4.1001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  FEE:
    // prettier-ignore
    <svg viewBox="0 0 15 15" fill="none" className="w-3.5 sm:w-4">
      <path d="M10.625 13.2812H4.375C2.09375 13.2812 0.78125 11.9688 0.78125 9.6875V5.3125C0.78125 3.03125 2.09375 1.71875 4.375 1.71875H10.625C12.9062 1.71875 14.2188 3.03125 14.2188 5.3125V9.6875C14.2188 11.9688 12.9062 13.2812 10.625 13.2812ZM4.375 2.65625C2.5875 2.65625 1.71875 3.525 1.71875 5.3125V9.6875C1.71875 11.475 2.5875 12.3438 4.375 12.3438H10.625C12.4125 12.3438 13.2812 11.475 13.2812 9.6875V5.3125C13.2812 3.525 12.4125 2.65625 10.625 2.65625H4.375Z" fill="currentColor"/>
      <path d="M7.5 9.84375C6.20625 9.84375 5.15625 8.79375 5.15625 7.5C5.15625 6.20625 6.20625 5.15625 7.5 5.15625C8.79375 5.15625 9.84375 6.20625 9.84375 7.5C9.84375 8.79375 8.79375 9.84375 7.5 9.84375ZM7.5 6.09375C6.725 6.09375 6.09375 6.725 6.09375 7.5C6.09375 8.275 6.725 8.90625 7.5 8.90625C8.275 8.90625 8.90625 8.275 8.90625 7.5C8.90625 6.725 8.275 6.09375 7.5 6.09375Z" fill="currentColor"/>
      <path d="M3.4375 9.53125C3.18125 9.53125 2.96875 9.31875 2.96875 9.0625V5.9375C2.96875 5.68125 3.18125 5.46875 3.4375 5.46875C3.69375 5.46875 3.90625 5.68125 3.90625 5.9375V9.0625C3.90625 9.31875 3.69375 9.53125 3.4375 9.53125Z" fill="currentColor"/>
      <path d="M11.5625 9.53125C11.3063 9.53125 11.0938 9.31875 11.0938 9.0625V5.9375C11.0938 5.68125 11.3063 5.46875 11.5625 5.46875C11.8187 5.46875 12.0312 5.68125 12.0312 5.9375V9.0625C12.0312 9.31875 11.8187 9.53125 11.5625 9.53125Z" fill="currentColor"/>
    </svg>,
  TOTAL:
    //prettier-ignore
    <svg viewBox="0 0 24 24" fill="none" className="w-3.5 sm:w-4">
      <path d="M19.3 7.91998V13.07C19.3 16.15 17.54 17.47 14.9 17.47H6.10995C5.65995 17.47 5.22996 17.43 4.82996 17.34C4.57996 17.3 4.33996 17.23 4.11996 17.15C2.61996 16.59 1.70996 15.29 1.70996 13.07V7.91998C1.70996 4.83998 3.46995 3.52002 6.10995 3.52002H14.9C17.14 3.52002 18.75 4.47001 19.18 6.64001C19.25 7.04001 19.3 7.44998 19.3 7.91998Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M22.3011 10.9201V16.0701C22.3011 19.1501 20.5411 20.4701 17.9011 20.4701H9.11105C8.37105 20.4701 7.70106 20.3701 7.12106 20.1501C5.93106 19.7101 5.12105 18.8001 4.83105 17.3401C5.23105 17.4301 5.66105 17.4701 6.11105 17.4701H14.9011C17.5411 17.4701 19.3011 16.1501 19.3011 13.0701V7.9201C19.3011 7.4501 19.2611 7.03014 19.1811 6.64014C21.0811 7.04014 22.3011 8.38011 22.3011 10.9201Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.4984 13.1399C11.9564 13.1399 13.1384 11.9579 13.1384 10.4999C13.1384 9.04185 11.9564 7.85986 10.4984 7.85986C9.04038 7.85986 7.8584 9.04185 7.8584 10.4999C7.8584 11.9579 9.04038 13.1399 10.4984 13.1399Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M4.78003 8.30005V12.7001" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M16.2217 8.30029V12.7003" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  DISCOUNT:
    //prettier-ignore
    <svg viewBox="0 0 16 16" fill="none" className="w-3.5 sm:w-4 text-danger-400">
      <path d="M5.71318 10.6814C5.58651 10.6814 5.45984 10.6347 5.35984 10.5347C5.16651 10.3414 5.16651 10.0214 5.35984 9.82806L9.7265 5.46141C9.91984 5.26807 10.2398 5.26807 10.4332 5.46141C10.6265 5.65474 10.6265 5.97474 10.4332 6.16807L6.06651 10.5347C5.96651 10.6347 5.83984 10.6814 5.71318 10.6814Z" fill="currentColor"/>
      <path d="M5.98676 7.40562C5.26009 7.40562 4.66675 6.8123 4.66675 6.08563C4.66675 5.35897 5.26009 4.76562 5.98676 4.76562C6.71342 4.76562 7.30674 5.35897 7.30674 6.08563C7.30674 6.8123 6.71342 7.40562 5.98676 7.40562ZM5.98676 5.7723C5.81342 5.7723 5.66675 5.91231 5.66675 6.09231C5.66675 6.27231 5.80676 6.41229 5.98676 6.41229C6.16676 6.41229 6.30674 6.27231 6.30674 6.09231C6.30674 5.91231 6.16009 5.7723 5.98676 5.7723Z" fill="currentColor"/>
      <path d="M10.3466 11.2259C9.61993 11.2259 9.02661 10.6326 9.02661 9.90592C9.02661 9.17926 9.61993 8.58594 10.3466 8.58594C11.0733 8.58594 11.6666 9.17926 11.6666 9.90592C11.6666 10.6326 11.0733 11.2259 10.3466 11.2259ZM10.3466 9.59261C10.1733 9.59261 10.0266 9.7326 10.0266 9.9126C10.0266 10.0926 10.1666 10.2326 10.3466 10.2326C10.5266 10.2326 10.6666 10.0926 10.6666 9.9126C10.6666 9.7326 10.5266 9.59261 10.3466 9.59261Z" fill="currentColor"/>
      <path d="M7.99992 15.1654C4.04659 15.1654 0.833252 11.952 0.833252 7.9987C0.833252 4.04536 4.04659 0.832031 7.99992 0.832031C11.9533 0.832031 15.1666 4.04536 15.1666 7.9987C15.1666 11.952 11.9533 15.1654 7.99992 15.1654ZM7.99992 1.83203C4.59992 1.83203 1.83325 4.5987 1.83325 7.9987C1.83325 11.3987 4.59992 14.1654 7.99992 14.1654C11.3999 14.1654 14.1666 11.3987 14.1666 7.9987C14.1666 4.5987 11.3999 1.83203 7.99992 1.83203Z" fill="currentColor"/>
    </svg>,
  LINK:
    //prettier-ignore
    <svg viewBox="0 0 15 15" fill="none" className="w-3.5 sm:w-4">
      <path d="M4.84375 11.5625C2.60625 11.5625 0.78125 9.7375 0.78125 7.5C0.78125 5.2625 2.60625 3.4375 4.84375 3.4375C5.1 3.4375 5.3125 3.65 5.3125 3.90625C5.3125 4.1625 5.1 4.375 4.84375 4.375C3.11875 4.375 1.71875 5.775 1.71875 7.5C1.71875 9.225 3.11875 10.625 4.84375 10.625C6.56875 10.625 7.96875 9.225 7.96875 7.5C7.96875 7.24375 8.18125 7.03125 8.4375 7.03125C8.69375 7.03125 8.90625 7.24375 8.90625 7.5C8.90625 9.7375 7.08125 11.5625 4.84375 11.5625Z" fill="currentColor"/>
      <path d="M10 11.7188C9.74375 11.7188 9.53125 11.5062 9.53125 11.25C9.53125 10.9938 9.74375 10.7812 10 10.7812C11.8063 10.7812 13.2812 9.30625 13.2812 7.5C13.2812 5.69375 11.8063 4.21875 10 4.21875C8.19375 4.21875 6.71875 5.69375 6.71875 7.5C6.71875 7.75625 6.50625 7.96875 6.25 7.96875C5.99375 7.96875 5.78125 7.75625 5.78125 7.5C5.78125 5.175 7.675 3.28125 10 3.28125C12.325 3.28125 14.2188 5.175 14.2188 7.5C14.2188 9.825 12.325 11.7188 10 11.7188Z" fill="currentColor"/>
    </svg>,
};

export { OrderDetails, CustomerDetails, DeliveryDetails };
