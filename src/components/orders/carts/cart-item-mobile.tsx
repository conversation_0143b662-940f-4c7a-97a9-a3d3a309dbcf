import { CartInterface } from "../../../assets/interfaces";
import Badge from "../../ui/badge";
import { dateIsPastOneHour } from "./cart-item";

interface CartItemMobileProps {
  index: number;
  cart: CartInterface;
  showDetails: (data: CartInterface) => void;
}
export const CartItemMobile: React.FC<CartItemMobileProps> = ({ cart, showDetails, index }) => {
  const colors = ["accent-orange-500", "accent-green-500", "accent-red-500", "accent-yellow-500", "primary-500"];
  const colorClass = colors[index % colors.length];

  return (
    <li
      className="rounded-10 border border-grey-divider p-3 mb-2.5 cursor-pointer hover:bg-grey-fields-100 hover:bg-opacity-30 transition-all ease-out"
      onClick={() => showDetails(cart)}
    >
      <div className="relative flex items-center">
        <figure
          className={`text-${colorClass} bg-${colorClass} bg-opacity-10 w-14 h-14 rounded-8 flex items-center justify-center overflow-hidden`}
        >
          <div className="w-6 text-current">
            {/* prettier-ignore */}
            <svg width="25" height="25" viewBox="0 0 25 25" fill="none">
              <path d="M12.5007 2.08331C9.77148 2.08331 7.55273 4.30206 7.55273 7.03123C7.55273 9.70831 9.64648 11.875 12.3757 11.9687C12.459 11.9583 12.5423 11.9583 12.6048 11.9687C12.6257 11.9687 12.6361 11.9687 12.6569 11.9687C12.6673 11.9687 12.6673 11.9687 12.6777 11.9687C15.3444 11.875 17.4382 9.70831 17.4486 7.03123C17.4486 4.30206 15.2298 2.08331 12.5007 2.08331Z" fill="currentColor"/>
              <path d="M17.791 14.7396C14.8848 12.8021 10.1452 12.8021 7.2181 14.7396C5.89518 15.625 5.16602 16.8229 5.16602 18.1041C5.16602 19.3854 5.89518 20.5729 7.20768 21.4479C8.66602 22.4271 10.5827 22.9166 12.4993 22.9166C14.416 22.9166 16.3327 22.4271 17.791 21.4479C19.1035 20.5625 19.8327 19.375 19.8327 18.0833C19.8223 16.8021 19.1035 15.6146 17.791 14.7396Z" fill="currentColor"/>
            </svg>
          </div>
        </figure>
        <div className="flex flex-col items-start ml-2.5">
          <div className="absolute right-0 top-0">
            {dateIsPastOneHour(cart?.updated_at) ? (
              !cart.order ? (
                <Badge color="red" text={`DROPPED OFF`} />
              ) : (
                <Badge color="green" text={`PLACED ORDER`} />
              )
            ) : (
              <Badge color="orange" text={`IN PROGRESS`} />
            )}
          </div>
          <span className="text-sm font-medium text-black mb-2.5 inline-block overflow-hidden overflow-ellipsis whitespace-nowrap">
            {cart?.customer?.name ?? "No Name"}
          </span>
          <div className="flex items-center text-dark text-1xs font-medium">
            {cart?.location?.city ? (
              <span className="">
                {cart?.location?.city}, {cart?.location?.country_name}
              </span>
            ) : (
              "No Location"
            )}
          </div>
        </div>
      </div>
    </li>
  );
};
