import React, { useState } from "react";
import { useModals } from "../../hooks/useModals";
import { AppBtn } from "../../ui/buttons";
import Table, { TableHead, TableHeadItem, TableBody } from "../../ui/table";
import CustomerDetailsModal from "../modals/customer";
import { CartInterface } from "../../../assets/interfaces";
import { useFetcher } from "../../../api/utils";
import usePagination from "../../hooks/usePagination";
import useScreenSize from "../../hooks/useScreenSize";
import ContentState from "../../ui/content-state";
import ClearSearch from "../../clear-search";
import Pagination from "../../ui/pagination";
import { GetCarts } from "@/api";
import { GetCartsParams } from "@/api/interfaces";
import CartItem from "./cart-item";
import Portal from "@/components/portal";
import { CartItemMobile } from "./cart-item-mobile";
import CartDetailsModal from "./cart-details";

const CartsList: React.FC = () => {
  const PER_PAGE = 10;
  const { currentPage, goNext, goPrevious, setPage } = usePagination();
  const [selected, setSelected] = useState<CartInterface>(null);
  const { modals, toggleModal } = useModals(["cart_info", "customer"]);
  const { isSmall } = useScreenSize();

  const { response, error, isLoading, makeRequest } = useFetcher<GetCartsParams>(GetCarts, {
    page: currentPage,
    per_page: PER_PAGE,
    sort: "DESC",
  });
  const carts = response?.data?.data;
  const pageNotReady = isLoading || error || !response?.data || carts.length < 1;

  const showDetails = (data: CartInterface) => {
    setSelected(data);
    toggleModal(`cart_info`);
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-3.75">
        <div className="flex items-center justify-between w-full">
          <h4 className={`text-base sm:text-lg text-black font-semibold`}>Latest Carts</h4>
        </div>
      </div>
      {pageNotReady && (
        <ContentState
          loadingText="Loading Carts..."
          isLoading={isLoading}
          isEmpty={carts?.length < 1}
          emptyIcon={
            // prettier-ignore
            <svg width="50%" viewBox="0 0 35 35" fill="none">
              <path opacity="0.4" d="M28.0584 8.13757H27.4751L22.5459 3.2084C22.1522 2.81465 21.5105 2.81465 21.1022 3.2084C20.7084 3.60215 20.7084 4.24382 21.1022 4.65215L24.5876 8.13757H10.4126L13.898 4.65215C14.2917 4.2584 14.2917 3.61673 13.898 3.2084C13.5042 2.81465 12.8626 2.81465 12.4542 3.2084L7.53966 8.13757H6.95633C5.64383 8.13757 2.91675 8.13757 2.91675 11.8709C2.91675 13.2855 3.20841 14.2188 3.82091 14.8313C4.17091 15.1959 4.59383 15.3855 5.04591 15.4876C5.46883 15.5896 5.92091 15.6042 6.35841 15.6042H28.6417C29.0938 15.6042 29.5167 15.5751 29.9251 15.4876C31.1501 15.1959 32.0834 14.3209 32.0834 11.8709C32.0834 8.13757 29.3563 8.13757 28.0584 8.13757Z" fill="#AAAAAA"/>
              <path d="M28.6563 15.6042H6.3584C5.9209 15.6042 5.46882 15.5896 5.0459 15.4875L6.8834 26.6875C7.29173 29.1959 8.38548 32.0834 13.2417 32.0834H21.423C26.3376 32.0834 27.2126 29.6188 27.7376 26.8625L29.9396 15.4875C29.5313 15.575 29.0938 15.6042 28.6563 15.6042ZM15.473 25.025C15.473 25.5938 15.0209 26.0459 14.4521 26.0459C13.8834 26.0459 13.4313 25.5938 13.4313 25.025V20.2125C13.4313 19.6438 13.8834 19.1917 14.4521 19.1917C15.0209 19.1917 15.473 19.6438 15.473 20.2125V25.025ZM21.7146 25.025C21.7146 25.5938 21.2626 26.0459 20.6938 26.0459C20.1251 26.0459 19.673 25.5938 19.673 25.025V20.2125C19.673 19.6438 20.1251 19.1917 20.6938 19.1917C21.2626 19.1917 21.7146 19.6438 21.7146 20.2125V25.025Z" fill="#AAAAAA"/>
            </svg>
          }
          title="No Carts to show"
          description="Share your link with customers to start taking orders"
          errorMessage="We couldn't load your carts, click on the button to retry"
          errorTitle="Fetching carts failed"
          error={error}
          errorAction={
            <AppBtn size="sm" onClick={() => makeRequest()}>
              Reload carts
            </AppBtn>
          }
        />
      )}
      {!pageNotReady && (
        <>
          <div className="mt-5">
            {!isSmall && (
              <Table>
                <TableHead>
                  <TableHeadItem>Customer</TableHeadItem>
                  <TableHeadItem>Items</TableHeadItem>
                  <TableHeadItem>Order</TableHeadItem>
                  <TableHeadItem>Location</TableHeadItem>
                  <TableHeadItem>Status</TableHeadItem>
                  <TableHeadItem>Last Update</TableHeadItem>
                </TableHead>
                <TableBody>
                  {carts.map((cart, index) => (
                    <CartItem {...{ cart, showDetails }} key={index} />
                  ))}
                </TableBody>
              </Table>
            )}

            {isSmall && (
              <ul>
                {carts.map((cart, index) => (
                  <CartItemMobile key={index} index={index} showDetails={showDetails} cart={cart} />
                ))}
              </ul>
            )}
            <Pagination
              {...{
                setPage,
                currentPage,
                length: carts?.length,
                data: response?.data,
                goNext,
                goPrevious,
                label: "carts",
                per_page: PER_PAGE,
              }}
            />
          </div>

          <Portal>
            {selected && (
              <CartDetailsModal
                hasCustomer={!!selected.customer}
                show={modals.cart_info.show}
                toggle={() => toggleModal("cart_info")}
                cart={selected}
                toggleCustomerModal={() => toggleModal("customer")}
              />
            )}
            {selected?.customer && (
              <CustomerDetailsModal
                show={modals.customer.show}
                toggle={() => toggleModal("customer")}
                customer={selected.customer}
              />
            )}
          </Portal>
        </>
      )}
    </div>
  );
};

export default CartsList;
