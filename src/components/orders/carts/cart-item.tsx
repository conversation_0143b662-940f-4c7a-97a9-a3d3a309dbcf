import React from "react";
import { formatDateTime } from "../../../assets/js/utils/functions";
import { CartInterface } from "../../../assets/interfaces";
import { TableCell, TableRow } from "../../ui/table";
import StoreLogo from "../../ui/store-logo";
import Badge from "../../ui/badge";
import dayjs from "dayjs";

interface Props {
  cart: CartInterface;
  showDetails: (data: CartInterface) => void;
}

const CartItem: React.FC<Props> = ({ cart, showDetails }) => {
  return (
    <TableRow onClick={() => showDetails(cart)}>
      <TableCell>
        <div className="flex items-center font-black-secondary font-medium">
          <StoreLogo storeName={cart.customer?.name} logo={null} className="h-6.25 w-6.25 text-xs font-bold mr-1.5" />
          {cart.customer?.name ?? "No Name"}
        </div>
      </TableCell>
      <TableCell>
        <Badge color="dark" text={`${cart?.items.length ?? 0} Items`} />
      </TableCell>

      <TableCell>{cart?.order ?? "-"}</TableCell>
      <TableCell>
        {cart?.location?.city ? (
          <div className="flex items-center">
            {/* prettier-ignore */}
            <svg width="20" viewBox="0 0 24 24" fill="none"  className="text-grey-muted mr-1">
              <path d="M20.6211 8.45C19.5711 3.83 15.5411 1.75 12.0011 1.75C12.0011 1.75 12.0011 1.75 11.9911 1.75C8.46107 1.75 4.42107 3.82 3.37107 8.44C2.20107 13.6 5.36107 17.97 8.22107 20.72C9.28107 21.74 10.6411 22.25 12.0011 22.25C13.3611 22.25 14.7211 21.74 15.7711 20.72C18.6311 17.97 21.7911 13.61 20.6211 8.45ZM12.0011 13.46C10.2611 13.46 8.85107 12.05 8.85107 10.31C8.85107 8.57 10.2611 7.16 12.0011 7.16C13.7411 7.16 15.1511 8.57 15.1511 10.31C15.1511 12.05 13.7411 13.46 12.0011 13.46Z" fill="currentColor"/>
            </svg>
            {cart?.location?.city}, {cart?.location?.country_name}
          </div>
        ) : (
          "-"
        )}
      </TableCell>
      <TableCell>
        {dateIsPastOneHour(cart?.updated_at) ? (
          !cart.order ? (
            <Badge color="red" text={`DROPPED OFF`} />
          ) : (
            <Badge color="green" text={`PLACED ORDER`} />
          )
        ) : (
          <Badge color="orange" text={`IN PROGRESS`} />
        )}
      </TableCell>
      <TableCell className="hidden sm:table-cell">{formatDateTime(cart?.updated_at)}</TableCell>
    </TableRow>
  );
};

export const dateIsPastOneHour = (date: string) => {
  const now = dayjs();
  const targetDate = dayjs(date);

  const isMoreThanOneHour = now.diff(targetDate, "hour") > 1;

  return isMoreThanOneHour;
};

export default CartItem;
