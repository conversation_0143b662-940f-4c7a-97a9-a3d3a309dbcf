import React, { useEffect, useState } from "react";
import Modal, { ModalBody } from "../../ui/modal";
import { CartInterface } from "@/assets/interfaces";
import StoreLogo from "../../ui/store-logo";
import { formatDateTime, removeCountryCode } from "@/assets/js/utils/functions";
import { ProductDetailIcons } from "../../products/products-page/product-details";
import Badge from "../../ui/badge";
import { dateIsPastOneHour } from "./cart-item";
import { ORDER_ICONS } from "../orders-data";
import { useRouter } from "next/router";
import OrderItem from "../order-page/order-item";
import authContext from "@/contexts/auth-context";

interface Props {
  cart: CartInterface;
  show: boolean;
  hasCustomer?: boolean;
  toggle: VoidFunction;
  toggleCustomerModal: VoidFunction;
}

const CartDetailsModal: React.FC<Props> = ({ show, toggle, hasCustomer, toggleCustomerModal, cart }) => {
  const { store } = authContext.useContainer();
  const router = useRouter();

  return (
    <>
      <Modal size="md" show={show} toggle={toggle} title="Cart Details">
        <ModalBody>
          <div className="space-y-2.5">
            <div className="border-b border-grey-divider pb-3.75">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-1sm sm:text-base text-black-secondary mb-1.5 font-display">
                    Customer Info
                  </h4>
                  {cart?.customer && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center flex-1 overflow-hidden">
                        <StoreLogo
                          storeName={cart?.customer.name}
                          logo={null}
                          className="h-9 w-9 text-1sm sm:text-base font-bold mr-3"
                        />
                        <div className="overflow-hidden mr-2.5">
                          <h4 className="text-1sm sm:text-base -mb-1 font-bold whitespace-nowrap overflow-ellipsis overflow-hidden leading-snug mt-0.5">
                            {cart?.customer.name}{" "}
                          </h4>
                          <span className="text-dark text-xs sm:text-1xs inline-block leading-none">
                            {cart?.customer.phone ? removeCountryCode(cart?.customer.phone) : "No Customer Data"}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                {hasCustomer ? (
                  <button
                    onClick={toggleCustomerModal}
                    className="rounded-full flex items-center py-1.75 px-2.5 sm:px-3 sm:py-[9px] bg-grey-fields-200 font-medium text-primary-500 text-1xs lg:text-sm flex-shrink-0"
                  >
                    <span className="mr-0.5">View Profile</span>
                    {/* prettier-ignore */}
                    <svg width={14} viewBox="0 0 15 16" fill="none">
                    <path d="M4.39355 11.5359L11.4646 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M11.4639 11.5359L11.4639 4.46482L4.3928 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  </button>
                ) : (
                  <span>-</span>
                )}
              </div>
            </div>

            <div className="border-b border-grey-divider pb-3.75">
              <div className="flex items-center justify-between  py-1.75">
                <div className="flex items-center">
                  <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-dark">
                    {ProductDetailIcons.availability}
                  </div>
                  <span className="text-1xs sm:text-sm text-dark">Status</span>
                </div>
                {dateIsPastOneHour(cart?.updated_at) ? (
                  !cart.order ? (
                    <Badge color="red" text={`DROPPED OFF`} />
                  ) : (
                    <Badge color="green" text={`PLACED ORDER`} />
                  )
                ) : (
                  <Badge color="orange" text={`IN PROGRESS`} />
                )}
              </div>
              <div className="flex items-center justify-between  py-1.75">
                <div className="flex items-center">
                  <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-grey-subtext">
                    {/* prettier-ignore */}
                    <svg width="15" viewBox="0 0 24 24" fill="none">
                      <path d="M12 13.4299C13.7231 13.4299 15.12 12.0331 15.12 10.3099C15.12 8.58681 13.7231 7.18994 12 7.18994C10.2769 7.18994 8.88 8.58681 8.88 10.3099C8.88 12.0331 10.2769 13.4299 12 13.4299Z" stroke="currentColor" strokeWidth="1.5"/>
                      <path d="M3.62001 8.49C5.59001 -0.169998 18.42 -0.159997 20.38 8.5C21.53 13.58 18.37 17.88 15.6 20.54C13.59 22.48 10.41 22.48 8.39001 20.54C5.63001 17.88 2.47001 13.57 3.62001 8.49Z" stroke="currentColor" strokeWidth="1.5"/>
                    </svg>
                  </div>
                  <span className=" text-sm text-dark">Location</span>
                </div>
                <span className="font-medium pt-1 ml-2 text-black-secondary text-sm">
                  {cart?.location ? `${cart?.location.city}, ${cart?.location.country_name}` : "No Location"}
                </span>
              </div>
              <div className="flex items-center justify-between  py-1.75">
                <div className="flex items-center">
                  <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-grey-subtext">
                    {ProductDetailIcons.payment}
                  </div>
                  <span className="text-1xs sm:text-sm text-dark">Currency</span>
                </div>
                <span className="ml-2 text-black-secondary font-medium text-1xs">
                  {cart?.currency ?? store?.currencies?.default}
                </span>
              </div>
              <div className="flex items-center justify-between  py-1.75">
                <div className="flex items-center">
                  <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-grey-subtext">
                    {ProductDetailIcons.date}
                  </div>
                  <span className="text-1xs sm:text-sm text-dark">Last Update</span>
                </div>
                <span className="ml-2 text-black-secondary font-medium text-1xs">
                  {formatDateTime(cart?.updated_at)}
                </span>
              </div>

              {cart?.order && (
                <div className="flex items-center justify-between py-1.75">
                  <div className="flex items-center">
                    <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-dark">
                      {ORDER_ICONS.DELIVERY}
                    </div>
                    <span className=" text-sm text-dark">Order</span>
                  </div>
                  <button
                    onClick={() => router.push(`/orders/${cart?.order}`)}
                    className="rounded-full flex items-center py-1.5 px-2 sm:px-2.5 sm:py-1.75 bg-grey-fields-200 font-medium text-primary-500 text-1xs flex-shrink-0"
                  >
                    <span className="mr-0.5">View Order</span>
                    {/* prettier-ignore */}
                    <svg width={14} viewBox="0 0 15 16" fill="none">
                      <path d="M4.39355 11.5359L11.4646 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M11.4639 11.5359L11.4639 4.46482L4.3928 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                </div>
              )}
            </div>
            <h4 className="font-semibold text-1sm sm:text-base text-black-secondary mb-1.5 font-display">Cart Items</h4>
            {cart.items.map((item, index) => (
              <OrderItem
                item={{ ...item, item: item.object }}
                key={index}
                currency={cart?.currency ?? store?.currencies?.default}
                showBorderOnLast={false}
              />
            ))}
          </div>
        </ModalBody>
      </Modal>
    </>
  );
};

export default CartDetailsModal;
