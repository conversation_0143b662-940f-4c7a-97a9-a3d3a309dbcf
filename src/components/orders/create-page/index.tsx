import Error<PERSON>abel from "@/components/ui/error-label";
import { FormikErrors, FormikProps, useFormik } from "formik";
import * as Yup from "yup";
import useSteps from "@/components/hooks/useSteps";
import { useEffect, useState } from "react";
import { CURRENCIES, DELIVERY_METHODS, OrderForm, ORDER_STATUSES } from "@/assets/interfaces";
import ItemsForm from "./items-form";
import CustomerForm from "./customer-info-form";
import DeliveryForm from "./delivery-info-form";
import ExtraInfoForm from "./extra-info-form";
import CreateOrderFooter from "./footer";
import { removeUndefinedValues, transformYupErrorsIntoObject } from "@/assets/js/utils/utils";
import { phoneValidation } from "@/assets/js/utils/common-validations";
import { useRequest } from "@/api/utils";
import { CreateOrder } from "@/api";
import { CreateOrderParams, ORDER_FEE_TYPES } from "@/api/interfaces";
import { getProductsCurrency, phoneObjectToString } from "@/assets/js/utils/functions";
import authContext from "@/contexts/auth-context";
import Success from "./success";
import cartContext from "@/contexts/cart-context";
import { useAppendScript } from "@/components/hooks/useAppendScript";
import { checkErrors, validationSchema } from "./create-order.utils";

export interface CreateOrderFormProps {
  isActive: boolean;
  form: FormikProps<OrderForm>;
  toggleStep: () => void;
  stepComplete: boolean;
  isEditing?: boolean;
  isFirstStepComplete?: boolean;
  isPickup?: boolean;
  currencies?: CURRENCIES[];
}

const CreateOrderForm = () => {
  const { storeId, store } = authContext.useContainer();
  const { changeCurrency, currencies } = cartContext.useContainer();

  const formSteps = ["ITEMS", "CUSTOMER_INFO", "DELIVERY_INFO", "EXTRA_INFO"];
  const { steps, step, stepIndex, canNext, canPrevious, next, previous, changeStep, isActive } = useSteps(
    ["EMPTY", ...formSteps, "SUCCESS"],
    1
  );

  const toggleStep = (s: string) => (step === s ? changeStep("EMPTY") : changeStep(s));
  const [hasError, setHasError] = useState(false);
  const [firstStepIsComplete, setFirstStepIsComplete] = useState(false);
  const createRequest = useRequest<CreateOrderParams>(CreateOrder);

  const [stepsStatus, setStepsStatus] = useState({
    ITEMS: false,
    CUSTOMER_INFO: false,
    DELIVERY_INFO: false,
    EXTRA_INFO: false,
  });

  const googleApiKey = process.env.NEXT_PUBLIC_GOOGLE_API_KEY;
  useAppendScript(
    `https://maps.googleapis.com/maps/api/js?key=${googleApiKey}&libraries=places&callback=initMap&solution_channel=GMP_QB_addressselection_v1_cA`,
    "GOOGLE_PLACE_SCRIPT"
  );

  const form = useFormik<OrderForm>({
    initialValues: {
      items: [],
      delivery_info: {
        delivery_address: "",
        phone: { code: "+234", digits: "" },
        use_customer_info: true,
      },
      customer_info: {
        id: "",
        phone: { code: "+234", digits: "" },
      },
      extra_info: {
        fees: {},
        channel: null,
        is_paid: false,
        status: ORDER_STATUSES.PENDING,
      },
      delivery_method: DELIVERY_METHODS.DELIVERY,
    },
    onSubmit: async (values) => {
      switch (stepIndex) {
        case 1:
          setFirstStepIsComplete(true);
          next();
          break;
        case 2:
          next();
          break;
        case 3:
          next();
          break;
        case 4:
          changeStep("EMPTY");
          handleCreateOrder();
          break;
        default:
        //do nothing
      }
    },
    validationSchema: validationSchema(stepIndex),
  });

  useEffect(() => {
    setHasError(false);
    computeStepStatuses(form);
  }, [form.values]);

  useEffect(() => {
    if (store) {
      setTimeout(() => {
        form.setFieldValue("currency", getProductsCurrency());
      }, 1000);
    }
  }, [store]);

  const computeStepStatuses = async (form: FormikProps<OrderForm>) => {
    const statusCopy = { ...stepsStatus };
    const errorStatuses = await checkErrors(form);
    formSteps.forEach((value) => (statusCopy[value] = !errorStatuses[value]));
    setStepsStatus(statusCopy);
  };

  const handleCreateOrder = async () => {
    const errors = await form.validateForm();
    if (Object.keys(errors).length === 0) {
      const valuesCopy = { ...form.values };
      const {
        delivery_info: { delivery_address, area, name, phone, use_customer_info },
        customer_info: { phone: customerPhone, ...customer_rest },
        extra_info,
        items,
        delivery_method,
      } = valuesCopy;

      const feesArray = Object.keys(extra_info.fees)
        .filter((f) => f !== ORDER_FEE_TYPES.COUPON)
        .map((f) => ({ type: f, amount: extra_info?.fees?.[f]?.amount }));
      const requestData = {
        customer: {
          ...customer_rest,
          phone: phoneObjectToString(customerPhone),
        },
        delivery_info:
          delivery_method === DELIVERY_METHODS.DELIVERY
            ? {
                delivery_address,
                area,
                name: use_customer_info ? customer_rest.name : name,
                phone: phoneObjectToString(use_customer_info ? customerPhone : phone),
              }
            : undefined,
        items: items.map(({ item_id, variant, variant_id, quantity }) => ({
          item_id,
          variant,
          variant_id,
          quantity,
        })),
        store: storeId,
        coupon: extra_info?.fees?.["COUPON"]?.meta?.coupon_code,
        channel: extra_info.channel,
        delivery_method,
        currency: currencies?.active,
        fees: feesArray,
        is_paid: extra_info.is_paid,
        status: extra_info.status,
      };

      const [res, error] = await createRequest.makeRequest(removeUndefinedValues(requestData) as any);

      if (res) {
        changeStep("SUCCESS");
      }
    } else {
      setHasError(true);
    }
  };

  return (
    <div className="w-full h-full overflow-y-auto mx-auto pb-20 py-12.5 sm:pt-16 lg:pt-20 text-center">
      {step !== "SUCCESS" && (
        <>
          <div className="flex flex-col items-center">
            <figure className="h-[70px] w-[70px] sm:h-20 sm:w-20 bg-accent-orange-500 flex items-center justify-center rounded-full">
              {/* prettier-ignore */}
              <svg width="40" height="40" viewBox="0 0 20 20" fill="none" >
                <path d="M16.0333 4.65H15.7L12.8833 1.83333C12.6583 1.60833 12.2916 1.60833 12.0583 1.83333C11.8333 2.05833 11.8333 2.425 12.0583 2.65833L14.05 4.65H5.94996L7.94163 2.65833C8.16663 2.43333 8.16663 2.06666 7.94163 1.83333C7.71663 1.60833 7.34996 1.60833 7.11663 1.83333L4.30829 4.65H3.97496C3.22496 4.65 1.66663 4.65 1.66663 6.78333C1.66663 7.59166 1.83329 8.125 2.18329 8.475C2.38329 8.68333 2.62496 8.79166 2.88329 8.85C3.12496 8.90833 3.38329 8.91666 3.63329 8.91666H16.3666C16.625 8.91666 16.8666 8.9 17.1 8.85C17.8 8.68333 18.3333 8.18333 18.3333 6.78333C18.3333 4.65 16.775 4.65 16.0333 4.65Z" fill="white"/>
                <path d="M16.375 8.91667H3.6333C3.3833 8.91667 3.12497 8.90834 2.8833 8.85001L3.9333 15.25C4.16663 16.6833 4.79163 18.3333 7.56663 18.3333H12.2416C15.05 18.3333 15.55 16.925 15.85 15.35L17.1083 8.85001C16.875 8.90001 16.625 8.91667 16.375 8.91667ZM8.84163 14.3C8.84163 14.625 8.5833 14.8833 8.2583 14.8833C7.9333 14.8833 7.67497 14.625 7.67497 14.3V11.55C7.67497 11.225 7.9333 10.9667 8.2583 10.9667C8.5833 10.9667 8.84163 11.225 8.84163 11.55V14.3ZM12.4083 14.3C12.4083 14.625 12.15 14.8833 11.825 14.8833C11.5 14.8833 11.2416 14.625 11.2416 14.3V11.55C11.2416 11.225 11.5 10.9667 11.825 10.9667C12.15 10.9667 12.4083 11.225 12.4083 11.55V14.3Z" fill="white"/>
              </svg>
            </figure>
            <h2 className="text-2lg sm:text-2xl font-bold md:text-3xl text-black-500 mt-2.5">Record an Order</h2>
          </div>
          <form onSubmit={form.handleSubmit} className="max-w-[450px] px-5 mx-auto mt-7.5 sm:mt-9">
            <ErrorLabel
              error={createRequest?.error ? createRequest?.error?.message : hasError ? "You have some errors" : ""}
              perm={true}
            />
            <ItemsForm
              isActive={isActive("ITEMS")}
              isFirstStepComplete={firstStepIsComplete}
              form={form}
              toggleStep={() => toggleStep("ITEMS")}
              stepComplete={stepsStatus.ITEMS}
              currencies={store?.currencies?.storefront}
            />
            {firstStepIsComplete && (
              <>
                <CustomerForm
                  isActive={isActive("CUSTOMER_INFO")}
                  isFirstStepComplete={firstStepIsComplete}
                  form={form}
                  toggleStep={() => toggleStep("CUSTOMER_INFO")}
                  stepComplete={stepsStatus.CUSTOMER_INFO}
                  currencies={store?.currencies?.storefront}
                />
                <DeliveryForm
                  isActive={isActive("DELIVERY_INFO")}
                  isFirstStepComplete={firstStepIsComplete}
                  form={form}
                  toggleStep={() => toggleStep("DELIVERY_INFO")}
                  stepComplete={stepsStatus.DELIVERY_INFO}
                  currencies={store?.currencies?.storefront}
                />
                <ExtraInfoForm
                  isActive={isActive("EXTRA_INFO")}
                  isFirstStepComplete={firstStepIsComplete}
                  form={form}
                  toggleStep={() => toggleStep("EXTRA_INFO")}
                  stepComplete={stepsStatus.EXTRA_INFO}
                  currencies={store?.currencies?.storefront}
                />
              </>
            )}
          </form>

          <CreateOrderFooter
            {...{
              createOrder: handleCreateOrder,
              showFooter: firstStepIsComplete,
              isCreatingOrder: createRequest.isLoading,
            }}
          />
        </>
      )}

      {step === "SUCCESS" && <Success data={createRequest?.response?.data} />}
    </div>
  );
};

export default CreateOrderForm;
