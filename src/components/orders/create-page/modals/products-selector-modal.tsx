import { CURRENCIES, ProductItemInterface } from "@/assets/interfaces";
import { getActualPrice, getItemThumbnail } from "@/assets/js/utils/utils";
import { useModals } from "@/components/hooks/useModals";
import LazyImage from "@/components/lazy-image";
import Portal from "@/components/portal";
import { AppBtn } from "@/components/ui/buttons";
import Checkbox from "@/components/ui/form-elements/checkbox";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import SearchBar from "@/components/ui/search-bar-new";
import { FormatCurrencyFun } from "@/contexts/cart-context";

import React, { useEffect, useState } from "react";
interface Props {
  show: boolean;
  toggle: (show: boolean) => void;
  items: ProductItemInterface[];
  selected: string[];
  selectItem: (item: string) => void;
  formatAsCurrency: FormatCurrencyFun;
}

const ProductsSelectorModal: React.FC<Props> = (props) => {
  const { show, toggle, items, selected, selectItem, formatAsCurrency } = props;
  const [searchQuery, setSearchQuery] = useState("");

  const filteredItems = (() => {
    if (searchQuery === "") {
      return items;
    }
    searchQuery.trim();
    const splitQueries = searchQuery.split(" ");

    return items.filter((item) => {
      let match = false;
      splitQueries.map((query) => {
        if (item.name.toLocaleLowerCase().includes(query)) match = true;
      });

      return match;
    });
  })();

  useEffect(() => {
    if (!show) setSearchQuery("");
  }, [show]);

  const isItemSelected = (id: string) => selected.includes(id);

  return (
    <Portal>
      <Modal show={show} toggle={toggle} title="Select Products" size="md">
        <ModalBody className="relative !pt-0 p-5 sm:p-7.5" noPadding>
          <div className="sticky top-0 z-50 bg-white pt-4 pb-1">
            <SearchBar {...{ searchQuery, setSearchQuery }} />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-5">
            {filteredItems.map((item, index) => (
              <div
                key={index}
                className="w-full flex items-start py-4 pr-4 border-b-[1px] border-gray-100 last:border-0 cursor-pointer"
                onClick={() => selectItem(item.id)}
                role="button"
              >
                <div className="flex items-center w-full overflow-hidden">
                  <figure className="flex-shrink-0 h-[34px] w-[34px] rounded-md overflow-hidden mr-3 relative">
                    <LazyImage
                      src={getItemThumbnail(item)}
                      className="h-full w-full object-cover rounded-md relative z-10"
                      alt={item.name}
                    />
                  </figure>
                  <div className="w-[75%]">
                    <span className="text-sm sm:text-1sm mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis text-black-secondary block -mb-0.5">
                      {item.name}
                    </span>
                    <span className="text-xs font-semibold text-black">{formatAsCurrency(getActualPrice(item))}</span>
                  </div>
                </div>
                <Checkbox
                  onChange={() => selectItem(item.id)}
                  checked={isItemSelected(item.id)}
                  id={item.id}
                  name={item.name}
                  neutral
                  className="ml-auto mt-1"
                  round
                  small
                ></Checkbox>
              </div>
            ))}
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="w-full">
            <AppBtn isBlock size="lg" onClick={() => toggle(false)}>
              Select Items
            </AppBtn>
          </div>
        </ModalFooter>
      </Modal>
    </Portal>
  );
};

export default ProductsSelectorModal;
