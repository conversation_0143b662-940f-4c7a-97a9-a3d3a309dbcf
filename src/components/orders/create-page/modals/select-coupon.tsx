import { CouponItemInterface, CURRENCIES } from "@/assets/interfaces";
import { toCurrency } from "@/assets/js/utils/functions";
import Portal from "@/components/portal";
import AppBtn from "@/components/ui/buttons/app-btn";
import Modal, { <PERSON>dal<PERSON><PERSON>, ModalFooter } from "@/components/ui/modal";
import { useState } from "react";

interface Props {
  toggle: (state: boolean) => void;
  show: boolean;
  addCoupon: (coupon: CouponItemInterface) => void;
  coupons: CouponItemInterface[];
  isLoading?: boolean;
  activeCurrency: CURRENCIES;
}
const SelectCouponModal: React.FC<Props> = ({ show, toggle, coupons, addCoupon, isLoading, activeCurrency }) => {
  const [selected, setSelected] = useState<CouponItemInterface>(null);
  coupons = coupons.filter((c) => c.active && c.quantity > 0 && new Date(c.end_date) > new Date());

  const handleSave = () => {
    addCoupon({ ...selected });
    toggle(false);
    setSelected(null);
  };
  return (
    <Portal>
      <Modal show={show} toggle={toggle} title="Select Coupon" size="midi">
        <ModalBody>
          {coupons.length > 0 && !isLoading && (
            <ul className="flex flex-col divide-y divide-grey-divider">
              {coupons.map((coupon, index) => (
                <li
                  className="py-4 px-2.5 sm:px-3.75 flex items-center justify-between text-black text-sm cursor-pointer hover:bg-grey-light w-full"
                  onClick={() => setSelected(coupon)}
                  key={index}
                >
                  <div>
                    <span className="font-semibold block text-dark">{coupon.coupon_code}</span>
                    <span className="text-sm text-black-muted">
                      {coupon.percentage
                        ? `${coupon.percentage}% OFF`
                        : toCurrency(coupon.discount_amount, activeCurrency, true)}{" "}
                    </span>
                  </div>
                  {selected?.id === coupon?.id && (
                    <div>
                      {/* prettier-ignore */}
                      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" className="ml-auto">
                          <rect width="18" height="18" rx="9" fill="#39B588"/>
                          <path d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z" fill="white"/>
                        </svg>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          )}
          {coupons.length < 1 && !isLoading && (
            <div className="py-5 px-2.5 text-placeholder text-center text-xs cursor-pointer">
              No valid coupons to show
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <AppBtn size="lg" isBlock onClick={handleSave}>
            Continue
          </AppBtn>
        </ModalFooter>
      </Modal>
    </Portal>
  );
};
export default SelectCouponModal;
