import router from "next/router";
import { CURRENCIES, CartItem, ProductItemInterface } from "../../../assets/interfaces";
import { genarateStringFromVariantValues, toCurrency } from "../../../assets/js/utils/functions";
import { getItemThumbnail } from "../../../assets/js/utils/utils";
import LazyImage from "../../lazy-image";
import Badge from "../../ui/badge";

interface IProps {
  item: CartItem;
  showProductDetails?: (item: ProductItemInterface) => void;
  currency: CURRENCIES;
  showBorderOnLast?: boolean;
}

const OrderItem: React.FC<IProps> = ({ item, showProductDetails, currency, showBorderOnLast = true }) => {
  const isImageVariant = item.item?.variants?.type === "images" && item.item?.variants?.options?.length > 0;
  const isCustomVariant = item.item?.variants?.type === "custom" && item.item?.variants?.options?.length > 0;

  const variantHasExtraOption = isImageVariant && item.item?.variants?.options[0]?.values !== undefined;

  return (
    <div
      className={`w-full flex items-center py-3 sm:py-3.75 border-b border-grey-divider hover:bg-grey-fields-100 hover:bg-opacity-30 cursor-pointer ${
        !showBorderOnLast && "last:border-0"
      }`}
      onClick={() => router.push(`/products/${item.item.id}`)}
    >
      <figure className="h-10 w-10 rounded-10 overflow-hidden flex-shrink-0 relative">
        <LazyImage
          src={isImageVariant ? item?.item?.variants?.options[0]?.image : getItemThumbnail(item.item)}
          alt={item.item.name}
          className={`h-full w-full object-cover`}
        />
      </figure>
      <div className="flex-1 ml-2.5 flex items-center justify-between overflow-hidden">
        <div className="flex-1 overflow-hidden">
          <h5 className="text-dark overflow-hidden text-sm max-w-full whitespace-nowrap overflow-ellipsis capitalize flex items-center mt-1">
            {item?.item?.name}
            {isCustomVariant && !item.is_deleted && (
              <span className="text-grey-subtext bg-grey-fields-100 py-0.75 px-1.75 inline-block text-xs ml-1.5 font-medium rounded-5">
                {genarateStringFromVariantValues(item?.variant?.values)}
              </span>
            )}

            {variantHasExtraOption && !item.is_deleted && (
              <span className="text-black-placeholder inline-flex items-center px-2 py-1 bg-grey-fields-100 text-xs sm:text-1xs rounded-5 font-body font-medium capitalize ml-1.5">
                {Object.keys(item.item?.variants?.options[0]?.values)[0]} {Object.values(item?.variant?.values)[0]}
              </span>
            )}
          </h5>
          <span className="text-black-secondary font-semibold text-1xs">
            {toCurrency(item?.variant ? item?.variant.price : item?.item.price, currency)}
          </span>
        </div>
        <Badge text={`${item.quantity} UNIT${item.quantity > 1 ? "S" : ""}`} className="font-body" />
      </div>
    </div>
  );
};

export const getItemPrice = (cartItem: CartItem) => {
  return cartItem.is_deleted || cartItem.is_unavailable
    ? 0
    : cartItem.variant
    ? cartItem.variant.price
    : cartItem.item.price;
};

export default OrderItem;
