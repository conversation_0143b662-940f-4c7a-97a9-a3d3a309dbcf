import React from "react";
import { GetCustomerParams } from "../../../api/interfaces/orders-customers.interface";
import { GetCustomer } from "../../../api/orders-customers";
import { useFetcher } from "../../../api/utils";
import {
  getUserCountry,
  millify,
  removeCountryCode,
  resolvePhone,
  toCurrency,
} from "../../../assets/js/utils/functions";
import { CustomerInterface } from "../../../assets/interfaces";
import { AppBtn } from "../../ui/buttons";
import Modal, { ModalBody, ModalFooter } from "../../ui/modal";
import StoreLogo from "../../ui/store-logo";
import useFluxState from "../../hooks/useFluxState";
import { useModals } from "../../hooks/useModals";
import Portal from "../../portal";
import EditCustomerModal from "./edit-customer";
import { discountDetailsIcons } from "@/components/products/discounts/modals/discount-details";
import OrderList from "./order-list";
import { getWhatsappLink } from "@/assets/js/utils/utils";
interface CustomerDetailsModalProps {
  show: boolean;
  toggle: (status: boolean) => void;
  customer: CustomerInterface;
}

const CustomerDetailsModal: React.FC<CustomerDetailsModalProps> = ({ show, toggle, customer }) => {
  const { modals, toggleModal } = useModals(["edit", "order_list"]);
  const { response, error, isLoading, makeRequest } = useFetcher<GetCustomerParams>(GetCustomer, {
    id: customer.id,
  });
  const [customerData, setCustomerData] = useFluxState<CustomerInterface>(response?.data ?? null, [response]);
  const stats = getCustomerStats(customerData ?? { total_orders: 1, total_order_amount: 0 });

  return (
    <>
      <Modal title="Customer Information" {...{ show, toggle }}>
        <ModalBody>
          {isLoading && (
            <div className="flex flex-col items-center text-primary-500 py-10">
              <div className="spinner"></div>
              <span className="text-sm font-semibold block mt-5 text-grey-placeholder">Loading Customer</span>
            </div>
          )}

          {error && <div>Something went wrong</div>}

          {customerData && !isLoading && (
            <>
              <div className="flex items-center text-dark">
                <StoreLogo logo={null} storeName={customerData.name ?? "-"} className="h-10 w-10 text-xl font-bold" />
                <div className="ml-4">
                  <h5 className="text-lg font-bold text-black -mb-1 font-display">{customerData.name ?? "-"}</h5>
                  <span className="text-grey-subtext text-1xs">
                    <b className="font-medium">Customer ID: </b>
                    {customerData.id}
                  </span>
                </div>
              </div>
              <div className="w-full rounded-15 border card-shadow border-grey-divider grid lg:gap-3 grid-cols-2 my-5">
                {stats.map(({ icon, label, color, value }, index) => (
                  <div
                    className={`flex items-center w-full py-4 px-3 ${
                      index === 1 ? "border-grey-divider border-l pl-3" : ""
                    }`}
                    key={index}
                  >
                    <figure
                      className={`h-11 w-11 sm:h-11.5 sm:w-11.5 rounded-full flex items-center justify-center flex-shrink-0 bg-grey-fields-100 text-grey-subtext`}
                    >
                      {icon}
                    </figure>
                    <div className="ml-2.5">
                      <h4
                        className={`text-base sm:text-lg mb-0.5 font-display !leading-none font-bold mt-0.5 ${
                          value === 0 ? "text-placeholder" : "text-black"
                        }`}
                      >
                        {value}
                      </h4>
                      <span className="text-xs md:text-1xs text-dark inline-block !leading-none">{label}</span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="text-sm text-dark">
                <div className="flex justify-between mb-4">
                  <div className="flex items-center">
                    <figure className="h-7.5 w-7.5 rounded-full bg-grey-fields-100 flex items-center justify-center text-dark">
                      {/* prettier-ignore */}
                      <svg width="50%" viewBox="0 0 15 15" fill="none">
                        <path d="M13.7312 11.4562C13.7312 11.6812 13.6812 11.9125 13.575 12.1375C13.4687 12.3625 13.3313 12.575 13.15 12.775C12.8438 13.1125 12.5062 13.3563 12.125 13.5125C11.75 13.6688 11.3438 13.75 10.9063 13.75C10.2688 13.75 9.5875 13.6 8.86875 13.2938C8.15 12.9875 7.43125 12.575 6.71875 12.0563C6 11.5313 5.31875 10.95 4.66875 10.3062C4.025 9.65625 3.44375 8.975 2.925 8.2625C2.4125 7.55 2 6.8375 1.7 6.13125C1.4 5.41875 1.25 4.7375 1.25 4.0875C1.25 3.6625 1.325 3.25625 1.475 2.88125C1.625 2.5 1.8625 2.15 2.19375 1.8375C2.59375 1.44375 3.03125 1.25 3.49375 1.25C3.66875 1.25 3.84375 1.2875 4 1.3625C4.1625 1.4375 4.30625 1.55 4.41875 1.7125L5.86875 3.75625C5.98125 3.9125 6.0625 4.05625 6.11875 4.19375C6.175 4.325 6.20625 4.45625 6.20625 4.575C6.20625 4.725 6.1625 4.875 6.075 5.01875C5.99375 5.1625 5.875 5.3125 5.725 5.4625L5.25 5.95625C5.18125 6.025 5.15 6.10625 5.15 6.20625C5.15 6.25625 5.15625 6.3 5.16875 6.35C5.1875 6.4 5.20625 6.4375 5.21875 6.475C5.33125 6.68125 5.525 6.95 5.8 7.275C6.08125 7.6 6.38125 7.93125 6.70625 8.2625C7.04375 8.59375 7.36875 8.9 7.7 9.18125C8.025 9.45625 8.29375 9.64375 8.50625 9.75625C8.5375 9.76875 8.575 9.7875 8.61875 9.80625C8.66875 9.825 8.71875 9.83125 8.775 9.83125C8.88125 9.83125 8.9625 9.79375 9.03125 9.725L9.50625 9.25625C9.6625 9.1 9.8125 8.98125 9.95625 8.90625C10.1 8.81875 10.2437 8.775 10.4 8.775C10.5187 8.775 10.6438 8.8 10.7812 8.85625C10.9187 8.9125 11.0625 8.99375 11.2188 9.1L13.2875 10.5687C13.45 10.6812 13.5625 10.8125 13.6312 10.9687C13.6937 11.125 13.7312 11.2812 13.7312 11.4562Z" stroke="currentColor" strokeMiterlimit="10"/>
                      </svg>
                    </figure>
                    <span className="ml-2">Phone Number</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-black font-medium">{removeCountryCode(customerData.phone)}</span>
                    <div className="flex items-center border-grey-border space-x-1.5 ml-2.5">
                      <a
                        className="h-4 w-4 text-placeholder"
                        href={getWhatsappLink(customerData.phone.replace("+", ""))}
                        target="_blank"
                        rel="noreferrer"
                      >
                        {/* prettier-ignore */}
                        <svg width="100%" viewBox="0 0 24 24" fill="none">
                          <path d="M6.9 20.6C8.4 21.5 10.2 22 12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 13.8 2.5 15.5 3.3 17L2.44044 20.306C2.24572 21.0549 2.93892 21.7317 3.68299 21.5191L6.9 20.6Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M16.5 14.8485C16.5 15.0105 16.4639 15.177 16.3873 15.339C16.3107 15.501 16.2116 15.654 16.0809 15.798C15.86 16.041 15.6167 16.2165 15.3418 16.329C15.0714 16.4415 14.7784 16.5 14.4629 16.5C14.0033 16.5 13.512 16.392 12.9937 16.1715C12.4755 15.951 11.9572 15.654 11.4434 15.2805C10.9251 14.9025 10.4339 14.484 9.9652 14.0205C9.501 13.5525 9.08187 13.062 8.70781 12.549C8.33826 12.036 8.04081 11.523 7.82449 11.0145C7.60816 10.5015 7.5 10.011 7.5 9.543C7.5 9.237 7.55408 8.9445 7.66224 8.6745C7.77041 8.4 7.94166 8.148 8.18052 7.923C8.46895 7.6395 8.78443 7.5 9.11793 7.5C9.24412 7.5 9.37031 7.527 9.48297 7.581C9.60015 7.635 9.70381 7.716 9.78493 7.833L10.8305 9.3045C10.9116 9.417 10.9702 9.5205 11.0108 9.6195C11.0513 9.714 11.0739 9.8085 11.0739 9.894C11.0739 10.002 11.0423 10.11 10.9792 10.2135C10.9206 10.317 10.835 10.425 10.7268 10.533L10.3843 10.8885C10.3348 10.938 10.3122 10.9965 10.3122 11.0685C10.3122 11.1045 10.3167 11.136 10.3257 11.172C10.3393 11.208 10.3528 11.235 10.3618 11.262C10.4429 11.4105 10.5826 11.604 10.7809 11.838C10.9837 12.072 11.2 12.3105 11.4344 12.549C11.6778 12.7875 11.9121 13.008 12.151 13.2105C12.3853 13.4085 12.5791 13.5435 12.7323 13.6245C12.7549 13.6335 12.7819 13.647 12.8135 13.6605C12.8495 13.674 12.8856 13.6785 12.9261 13.6785C13.0028 13.6785 13.0613 13.6515 13.1109 13.602L13.4534 13.2645C13.5661 13.152 13.6743 13.0665 13.7779 13.0125C13.8816 12.9495 13.9852 12.918 14.0979 12.918C14.1835 12.918 14.2737 12.936 14.3728 12.9765C14.472 13.017 14.5756 13.0755 14.6883 13.152L16.18 14.2095C16.2972 14.2905 16.3783 14.385 16.4279 14.4975C16.473 14.61 16.5 14.7225 16.5 14.8485Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10"/>
                        </svg>
                      </a>
                      <a className="h-4 w-4 text-placeholder ml-1.25" href={`tel:${resolvePhone(customerData.phone)}`}>
                        {/* prettier-ignore */}
                        <svg width="100%" viewBox="0 0 24 24" fill="none">
                          <path d="M21.97 18.33C21.97 18.69 21.89 19.06 21.72 19.42C21.55 19.78 21.33 20.12 21.04 20.44C20.55 20.98 20.01 21.37 19.4 21.62C18.8 21.87 18.15 22 17.45 22C16.43 22 15.34 21.76 14.19 21.27C13.04 20.78 11.89 20.12 10.75 19.29C9.6 18.45 8.51 17.52 7.47 16.49C6.44 15.45 5.51 14.36 4.68 13.22C3.86 12.08 3.2 10.94 2.72 9.81C2.24 8.67 2 7.58 2 6.54C2 5.86 2.12 5.21 2.36 4.61C2.6 4 2.98 3.44 3.51 2.94C4.15 2.31 4.85 2 5.59 2C5.87 2 6.15 2.06 6.4 2.18C6.66 2.3 6.89 2.48 7.07 2.74L9.39 6.01C9.57 6.26 9.7 6.49 9.79 6.71C9.88 6.92 9.93 7.13 9.93 7.32C9.93 7.56 9.86 7.8 9.72 8.03C9.59 8.26 9.4 8.5 9.16 8.74L8.4 9.53C8.29 9.64 8.24 9.77 8.24 9.93C8.24 10.01 8.25 10.08 8.27 10.16C8.3 10.24 8.33 10.3 8.35 10.36C8.53 10.69 8.84 11.12 9.28 11.64C9.73 12.16 10.21 12.69 10.73 13.22C11.27 13.75 11.79 14.24 12.32 14.69C12.84 15.13 13.27 15.43 13.61 15.61C13.66 15.63 13.72 15.66 13.79 15.69C13.87 15.72 13.95 15.73 14.04 15.73C14.21 15.73 14.34 15.67 14.45 15.56L15.21 14.81C15.46 14.56 15.7 14.37 15.93 14.25C16.16 14.11 16.39 14.04 16.64 14.04C16.83 14.04 17.03 14.08 17.25 14.17C17.47 14.26 17.7 14.39 17.95 14.56L21.26 16.91C21.52 17.09 21.7 17.3 21.81 17.55C21.91 17.8 21.97 18.05 21.97 18.33Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10"/>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between mb-4">
                  <div className="flex items-center">
                    <figure className="h-7.5 w-7.5 rounded-full bg-grey-fields-100 flex items-center justify-center text-dark">
                      {/* prettier-ignore */}
                      <svg width="50%" viewBox="0 0 15 15" fill="none">
                        <path d="M10.625 12.8125H4.375C2.5 12.8125 1.25 11.875 1.25 9.6875V5.3125C1.25 3.125 2.5 2.1875 4.375 2.1875H10.625C12.5 2.1875 13.75 3.125 13.75 5.3125V9.6875C13.75 11.875 12.5 12.8125 10.625 12.8125Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M10.625 5.625L8.66875 7.1875C8.025 7.7 6.96875 7.7 6.325 7.1875L4.375 5.625" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </figure>
                    <span className="ml-2">Email Address</span>
                  </div>
                  <span className="text-black mt-2 font-medium">{customerData.email ?? "-"}</span>
                </div>
              </div>
              {customerData?.orders?.length > 0 && (
                <div className="flex items-center justify-between pb-2">
                  <div className="flex items-center">
                    <figure className="h-7.5 w-7.5 rounded-full bg-grey-fields-100 flex items-center justify-center text-dark">
                      {discountDetailsIcons.products}
                    </figure>

                    <span className="ml-2 text-gray-600 text-1xs sm:text-sm">Orders</span>
                  </div>
                  <button
                    className="flex items-center cursor-pointer select-none"
                    onClick={() => toggleModal("order_list")}
                  >
                    <span className={`mr-2 font-semibold text-primary-500 text-1xs sm:text-sm`}>
                      {customerData?.orders.length} Order{customerData?.orders?.length > 1 ? "s" : ""}
                    </span>
                    {/* prettier-ignore */}
                    <svg viewBox="0 0 14 15" fill="none" className="w-3">
                    <path d="M1 7.5L13 7.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M7 13.5L13 7.5L7 1.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  </button>
                </div>
              )}
            </>
          )}
        </ModalBody>
        {!isLoading && !error && (
          <ModalFooter>
            <AppBtn isBlock onClick={() => toggleModal("edit")} size="lg">
              Edit Customer
            </AppBtn>
          </ModalFooter>
        )}
      </Modal>
      {customerData && (
        <Portal>
          <EditCustomerModal
            customer={customerData}
            show={modals.edit.show}
            toggle={() => toggleModal("edit")}
            updateCustomer={setCustomerData}
          />

          {customerData.orders?.length > 0 && (
            <OrderList
              show={modals.order_list.show}
              toggle={() => toggleModal("order_list")}
              orders={customerData.orders}
            />
          )}
        </Portal>
      )}
    </>
  );
};

const getCustomerStats = (data) => {
  return [
    {
      icon:
        // prettier-ignore
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M19.9597 8.96002C19.2897 8.22002 18.2797 7.79002 16.8797 7.64002V6.88002C16.8797 5.51002 16.2997 4.19002 15.2797 3.27002C14.2497 2.33002 12.9097 1.89002 11.5197 2.02002C9.12975 2.25002 7.11975 4.56002 7.11975 7.06002V7.64002C5.71975 7.79002 4.70975 8.22002 4.03975 8.96002C3.06975 10.04 3.09975 11.48 3.20975 12.48L3.90975 18.05C4.11975 20 4.90975 22 9.20975 22H14.7897C19.0897 22 19.8797 20 20.0897 18.06L20.7897 12.47C20.8997 11.48 20.9197 10.04 19.9597 8.96002ZM11.6597 3.41002C12.6597 3.32002 13.6097 3.63002 14.3497 4.30002C15.0797 4.96002 15.4897 5.90002 15.4897 6.88002V7.58002H8.50975V7.06002C8.50975 5.28002 9.97975 3.57002 11.6597 3.41002ZM8.41975 13.15H8.40975C7.85975 13.15 7.40975 12.7 7.40975 12.15C7.40975 11.6 7.85975 11.15 8.40975 11.15C8.96975 11.15 9.41975 11.6 9.41975 12.15C9.41975 12.7 8.96975 13.15 8.41975 13.15ZM15.4197 13.15H15.4097C14.8597 13.15 14.4097 12.7 14.4097 12.15C14.4097 11.6 14.8597 11.15 15.4097 11.15C15.9697 11.15 16.4197 11.6 16.4197 12.15C16.4197 12.7 15.9697 13.15 15.4197 13.15Z" fill="#F35508"/>
      </svg>,
      label: "Total Purchases",
      value: data.total_orders,
      color: "",
    },
    {
      icon:
        // prettier-ignore
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M11.2499 8.08002V10.94L10.2399 10.59C9.72992 10.41 9.41992 10.24 9.41992 9.37002C9.41992 8.66002 9.94992 8.08002 10.5999 8.08002H11.2499Z" fill="#39B588"/>
          <path d="M14.58 14.63C14.58 15.34 14.05 15.92 13.4 15.92H12.75V13.06L13.76 13.41C14.27 13.59 14.58 13.76 14.58 14.63Z" fill="#39B588"/>
          <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.19C2 19.83 4.17 22 7.81 22H16.19C19.83 22 22 19.83 22 16.19V7.81C22 4.17 19.83 2 16.19 2ZM14.26 12C15.04 12.27 16.08 12.84 16.08 14.63C16.08 16.17 14.88 17.42 13.4 17.42H12.75V18C12.75 18.41 12.41 18.75 12 18.75C11.59 18.75 11.25 18.41 11.25 18V17.42H10.89C9.25 17.42 7.92 16.03 7.92 14.33C7.92 13.92 8.25 13.58 8.67 13.58C9.08 13.58 9.42 13.92 9.42 14.33C9.42 15.21 10.08 15.92 10.89 15.92H11.25V12.53L9.74 12C8.96 11.73 7.92 11.16 7.92 9.37C7.92 7.83 9.12 6.58 10.6 6.58H11.25V6C11.25 5.59 11.59 5.25 12 5.25C12.41 5.25 12.75 5.59 12.75 6V6.58H13.11C14.75 6.58 16.08 7.97 16.08 9.67C16.08 10.08 15.75 10.42 15.33 10.42C14.92 10.42 14.58 10.08 14.58 9.67C14.58 8.79 13.92 8.08 13.11 8.08H12.75V11.47L14.26 12Z" fill="#39B588"/>
        </svg>,
      label: "Purchase Volume",
      value: `${getUserCountry().currency} ${millify(data.total_order_amount, 2)}`,
      color: "",
    },
  ];
};

export default CustomerDetailsModal;
