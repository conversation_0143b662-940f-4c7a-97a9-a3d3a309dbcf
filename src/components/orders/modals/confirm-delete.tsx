import React from "react";
import { AppBtn } from "../../ui/buttons";
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalProps } from "../../ui/modal";
import { OrderInterface } from "../../../assets/interfaces";
import { toCurrency } from "../../../assets/js/utils/functions";
import Badge from "../../ui/badge";
import { statusBadgeColors } from "../order-page/order-toolbar";

interface Props extends ModalProps {
  order: OrderInterface;
  complete: () => void;
}

const ConfirmDeleteModal: React.FC<Props> = ({ show, toggle, order, complete }) => {
  return (
    <Modal {...{ show, toggle }} title="Delete Order" size="sm" className="z-[1000]">
      <div className="flex flex-col flex-auto overflow-hidden">
        <ModalBody>
          <div className="text-center">
            <figure className="h-12.5 w-12.5 sm:h-15 sm:w-15 bg-red-700 m-auto rounded-full flex items-center justify-center">
              {/* prettier-ignore */}
              <svg width="45%" viewBox="0 0 50 50" fill="none">
              <path d="M43.8957 10.8959C40.5415 10.5625 37.1874 10.3125 33.8124 10.125V10.1042L33.354 7.39585C33.0415 5.47919 32.5832 2.60419 27.7082 2.60419H22.2499C17.3957 2.60419 16.9374 5.35419 16.604 7.37502L16.1665 10.0417C14.229 10.1667 12.2915 10.2917 10.354 10.4792L6.10405 10.8959C5.22905 10.9792 4.60405 11.75 4.68738 12.6042C4.77071 13.4584 5.52071 14.0834 6.39571 14L10.6457 13.5834C21.5624 12.5 32.5624 12.9167 43.6041 14.0209C43.6666 14.0209 43.7082 14.0209 43.7707 14.0209C44.5624 14.0209 45.2499 13.4167 45.3332 12.6042C45.3957 11.75 44.7707 10.9792 43.8957 10.8959Z" fill="white" />
              <path d="M40.0624 16.9583C39.5624 16.4375 38.8749 16.1458 38.1665 16.1458H11.8332C11.1249 16.1458 10.4166 16.4375 9.93739 16.9583C9.45822 17.4791 9.18738 18.1875 9.22905 18.9166L10.5207 40.2916C10.7499 43.4583 11.0416 47.4166 18.3124 47.4166H31.6874C38.9582 47.4166 39.2499 43.4791 39.4791 40.2916L40.7707 18.9375C40.8124 18.1875 40.5415 17.4791 40.0624 16.9583ZM28.4582 36.9791H21.5207C20.6666 36.9791 19.9582 36.2708 19.9582 35.4166C19.9582 34.5625 20.6666 33.8541 21.5207 33.8541H28.4582C29.3124 33.8541 30.0207 34.5625 30.0207 35.4166C30.0207 36.2708 29.3124 36.9791 28.4582 36.9791ZM30.2082 28.6458H19.7916C18.9374 28.6458 18.2291 27.9375 18.2291 27.0833C18.2291 26.2291 18.9374 25.5208 19.7916 25.5208H30.2082C31.0624 25.5208 31.7707 26.2291 31.7707 27.0833C31.7707 27.9375 31.0624 28.6458 30.2082 28.6458Z" fill="white" />
            </svg>
            </figure>
            <p className="text-sm text-dark mt-3.5 max-w-[360px] mx-auto">
              This action will completely remove this order from your store, you can cancel the order instead. Are you
              sure you want to proceed?
            </p>
            <div className="mt-4 p-3 bg-grey-fields-100 rounded-md">
              <div className="flex justify-between items-center">
                <span className="text-xs text-black-muted">Order ID:</span>
                <span className="text-xs text-black-secondary">{order.id}</span>
              </div>
              <div className="flex justify-between items-center mt-2.5">
                <span className="text-xs text-black-muted">Customer:</span>
                <span className="text-xs text-black-secondary">{order.customer?.name || "N/A"}</span>
              </div>
              <div className="flex justify-between items-center mt-2.5">
                <span className="text-xs text-black-muted">Status:</span>
                <Badge color={statusBadgeColors[order.status] as any} text={order.status} size="sm" greyBg={false} />
              </div>
              <div className="flex justify-between items-center mt-2.5">
                <span className="text-xs text-black-muted">Amount:</span>
                <span className="text-xs text-black-secondary font-semibold">
                  {toCurrency(order.total_amount, order.currency)}
                </span>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <AppBtn isBlock onClick={complete} color="danger" className="flex-1">
            Delete Order
          </AppBtn>
        </ModalFooter>
      </div>
    </Modal>
  );
};

export default ConfirmDeleteModal;
