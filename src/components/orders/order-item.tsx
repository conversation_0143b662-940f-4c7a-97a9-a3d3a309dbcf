import { CartItem, ProductItemInterface } from "../../assets/interfaces";
import { genarateStringFromVariantValues, toCurrency } from "../../assets/js/utils/functions";
import { getActualPrice, getItemThumbnail } from "../../assets/js/utils/utils";
import LazyImage from "../lazy-image";
import RoundActionButton from "../ui/buttons/round-action-btn";

interface OrderItemProps {
  cItem: CartItem;
  updateItemQuantity: (id: string, dir: "+" | "-", variant?: string) => void;
  addOrRemoveItem: (item: ProductItemInterface, variants?: string[]) => void;
}

const OrderItem: React.FC<OrderItemProps> = ({ cItem, updateItemQuantity, addOrRemoveItem }) => {
  const itemHasVariants = cItem.item?.variants?.options?.length > 0;
  const isImageVariant = cItem.item?.variants?.type === "images";
  const isCustomVariant = cItem.item?.variants?.type === "custom";
  const deletedOrUnavailable = cItem.is_deleted || cItem.is_unavailable;
  const variantHasExtraOption = isImageVariant && cItem.item?.variants?.options[0]?.values !== undefined;

  return (
    <li className="border-b border-grey-border border-opacity-50 last:border-b-0 py-3.75 first:pt-0 flex flex-col">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2.5 flex-1 mr-3">
          <figure className="h-10 w-10 sm:h-11.25 sm:w-11.25 rounded-[6px] sm:rounded-lg overflow-hidden relative">
            <LazyImage
              src={
                isImageVariant && !deletedOrUnavailable
                  ? cItem.variant.image
                  : getItemThumbnail(cItem.item)
              }
              alt=""
              className="w-full h-full object-cover"
            />
          </figure>
          <div className="self-center">
            <h4 className="text-1sm sm:text-base text-dark capitalize font-medium -mb-1">
              {cItem?.item?.name} {isCustomVariant && ` - ${genarateStringFromVariantValues(cItem?.variant?.values)}`}
              {variantHasExtraOption && ` - ${Object.values(cItem?.variant?.values)[0]}`}
            </h4>
            <span className="text-xs sm:text-1xs text-black-secondary font-semibold">
              {toCurrency(getActualPrice(cItem?.variant ? cItem?.variant : cItem?.item))}
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-2.5">
          <RoundActionButton
            className="border border-grey-border border-opacity-50"
            onClick={() => updateItemQuantity(cItem.item_id, "-", cItem?.variant?.id)}
          >
            <span className="text-base text-black-placeholder font-semibold">-</span>
          </RoundActionButton>
          <span className="text-black text-1sm sm:text-base font-bold font-display inline-flex w-4 items-center justify-center">
            {cItem.quantity}
          </span>
          <RoundActionButton
            className="border border-grey-border border-opacity-50"
            onClick={() => updateItemQuantity(cItem.item_id, "+", cItem?.variant?.id)}
          >
            <span className="text-base text-black-placeholder font-semibold">+</span>
          </RoundActionButton>
        </div>
      </div>
      {itemHasVariants && (
        <button
          className="text-primary-500 font-medium text-xs mt-2 py-0.75 px-2.5 rounded-5 bg-grey-fields-100 self-end"
          type="button"
          onClick={() => addOrRemoveItem(cItem?.item)}
        >
          Change or add new option
        </button>
      )}
    </li>
  );
};

export default OrderItem;
