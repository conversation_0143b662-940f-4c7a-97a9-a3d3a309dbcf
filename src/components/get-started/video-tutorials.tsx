import LazyImage from "@/components/lazy-image";
import classNames from "classnames";
import React, { useState } from "react";
import { useModals } from "../hooks/useModals";
import useSlider from "../hooks/useSlider";
import Portal from "../portal";
import ContentAccordion from "./content-accordion";
import FrontPagesVideoPlayer from "./video-player";

const VideoTutorials = () => {
  const { modals, toggleModal } = useModals(["video"]);
  const [selectedVideo, setSelectedVideo] = useState("");

  const { next, slider, slide, previous, slides, currentSlide, switchSlides } = useSlider({
    slides: videos.length,
    sameWidthAsSlider: true,
    gap: 0,
  });

  const isActive = (index) => currentSlide === index;

  const playVideo = (v) => {
    setSelectedVideo(v.src);
    toggleModal("video");
  };

  return (
    <ContentAccordion title={<Title />}>
      <div className="border-t border-grey-divider py-5 sm:py-6.25">
        <div>
          <ul
            className="w-full grid overflow-hidden overflow-x-hidden"
            style={{ gridTemplateColumns: `repeat(${videos.length},100%)` }}
            ref={slider}
          >
            {videos.map((v, i) => (
              <>
                <div className="w-full px-5 sm:px-6.25 lg:px-7.5" ref={slide} key={i}>
                  <figure className="w-full rounded-[12px] sm:rounded-[18px] h-[45vw] md:h-[32vw] lg:h-[22vw] mb-5 overflow-hidden relative">
                    <LazyImage src={v.cover} alt={v.title} className="w-full h-full" />

                    <div className="w-full absolute top-0 left-0 z-50 bg-primary-900 bg-opacity-[0.3] h-full flex items-center justify-center">
                      <button
                        className="h-12.5 w-12.5 bg-white rounded-full flex items-center justify-center"
                        onClick={() => playVideo(v)}
                      >
                        {/* prettier-ignore */}
                        <svg width="27" viewBox="0 0 24 24" fill="none" className="text-primary-500">
                          <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM14.66 13.73L13.38 14.47L12.1 15.21C10.45 16.16 9.1 15.38 9.1 13.48V12V10.52C9.1 8.61 10.45 7.84 12.1 8.79L13.38 9.53L14.66 10.27C16.31 11.22 16.31 12.78 14.66 13.73Z" fill="currentColor"/>
                        </svg>
                      </button>
                    </div>
                  </figure>
                </div>
              </>
            ))}
          </ul>
        </div>
        <div className="mb-5 flex items-center justify-center space-x-1.25 ">
          {videos.map((video, index) => (
            <div
              key={index}
              className={classNames("h-2 rounded-full transition-all ease-out duration-300", {
                "w-4 bg-accent-orange-500": isActive(index),
                "w-2 bg-[#E5E5E5]": !isActive(index),
              })}
            ></div>
          ))}
        </div>
        <div className="pt-3.75 border-t border-grey-divider w-full flex items-center justify-between px-5 sm:px-6.25 lg:px-7.5">
          <h4 className="text-black font-bold text-1sm sm:text-base lg:text-[17px]">{videos[currentSlide].title}</h4>
          <div className="ml-auto flex items-center space-x-3.5 mt-0">
            <button
              className="h-10 w-10 flex items-center justify-center rounded-full bg-grey-fields-100 text-primary-500"
              onClick={previous}
            >
              {/* prettier-ignore */}
              <svg width="30%" viewBox="0 0 14 14" fill="none">
                <path d="M13 7H1" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 1L1 7L7 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <button
              className="h-10 w-10 flex items-center justify-center rounded-full bg-primary-500 text-white"
              onClick={next}
            >
              {/* prettier-ignore */}
              <svg width="30%" viewBox="0 0 14 14" fill="none">
                <path d="M1 7H13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 1L13 7L7 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
      <Portal>
        <FrontPagesVideoPlayer show={modals.video.show} toggle={() => toggleModal("video")} ytVideoId={selectedVideo} />
      </Portal>
    </ContentAccordion>
  );
};

const Title = () => {
  return (
    <div className="flex items-center">
      <figure className="h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9 rounded-full bg-grey-fields-200 flex items-center justify-center">
        {/* prettier-ignore */}
        <svg width="70%" viewBox="0 0 24 24" fill="none">
          <path opacity="0.4" d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="#F35508"/>
          <path d="M9.09961 12V10.52C9.09961 8.60999 10.4496 7.83999 12.0996 8.78999L13.3796 9.52999L14.6596 10.27C16.3096 11.22 16.3096 12.78 14.6596 13.73L13.3796 14.47L12.0996 15.21C10.4496 16.16 9.09961 15.38 9.09961 13.48V12Z" fill="#F35508"/>
        </svg>
      </figure>
      <h4 className="text-black font-bold text-base sm:text-[17px] lg:text-lg ml-2.5">Learn how it works on Catlog</h4>
    </div>
  );
};

const videos = [
  {
    src: "XxPwkbFvTk8",
    title: "How customers make orders from your store",
    cover: "https://res.cloudinary.com/catlog/image/upload/v1679670352/video-covers/store-links-cover-2.png",
  },
  {
    src: "moJr7jYJnf4",
    title: "How to collect payments with Catlog",
    cover: "https://res.cloudinary.com/catlog/image/upload/v1679614357/video-covers/payments-cover.png",
  },
  {
    src: "Vjmt8IZMgb8",
    title: "How to upload your products to catlog",
    cover: "https://res.cloudinary.com/catlog/image/upload/v1679670352/video-covers/store-links-cover-1.png",
  },
  {
    src: "MAWJMK1UIek",
    title: "How to manage your orders & customers",
    cover: "https://res.cloudinary.com/catlog/image/upload/v1679614350/video-covers/orders-and-customers-cover.png",
  },
  {
    src: "pKBfTCrFtJk",
    title: "How to get paid with invoice links",
    cover: "https://res.cloudinary.com/catlog/image/upload/v1679614345/video-covers/invoice-links-cover.png",
  },
];

export default VideoTutorials;
