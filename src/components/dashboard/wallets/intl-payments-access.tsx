import Portal from "@/components/portal";
import React from "react";
import InternationalPaymentRequestModal from "./modals/international-payments-modal";
import { AppBtn } from "@/components/ui/buttons";
import { ModalState } from "@/components/hooks/useModals";
import { useFetcher } from "@/api/utils";
import { GetLatestWalletRequest } from "@/api";

interface IProps {
  modals: ModalState;
  toggleModal: (key: string) => void;
}

const IntlPaymentsAccess: React.FC<IProps> = ({ modals, toggleModal }) => {
  const { response, makeRequest, isLoading, error } = useFetcher(GetLatestWalletRequest);
  const hasPendingRequest = response?.data?.status === "PENDING";

  return (
    <>
      <div className="">
        <div className="mb-2">
          {/* prettier-ignore */}
          <svg className="bg-gray-100 p-4 w-16 h-16 rounded-full" viewBox="0 0 30 31" fill="none">
            <path opacity="0.4" d="M9.5627 26.6373C9.5252 26.6373 9.4752 26.6623 9.4377 26.6623C7.0127 25.4623 5.0377 23.4748 3.8252 21.0498C3.8252 21.0123 3.8502 20.9623 3.8502 20.9248C5.3752 21.3748 6.9502 21.7123 8.5127 21.9748C8.7877 23.5498 9.1127 25.1123 9.5627 26.6373Z" fill="#39B588"/>
            <path opacity="0.4" d="M26.1748 21.0623C24.9373 23.5498 22.8748 25.5623 20.3623 26.7748C20.8373 25.1873 21.2373 23.5873 21.4998 21.9748C23.0748 21.7123 24.6248 21.3748 26.1498 20.9248C26.1373 20.9748 26.1748 21.0248 26.1748 21.0623Z" fill="#39B588"/>
            <path opacity="0.4" d="M26.2748 10.1371C24.6998 9.66211 23.1123 9.27461 21.4998 8.99961C21.2373 7.38711 20.8498 5.78711 20.3623 4.22461C22.9498 5.46211 25.0373 7.54961 26.2748 10.1371Z" fill="#39B588"/>
            <path opacity="0.4" d="M9.56211 4.3627C9.11211 5.8877 8.78711 7.4377 8.52461 9.0127C6.91211 9.2627 5.31211 9.6627 3.72461 10.1377C4.93711 7.6252 6.94961 5.5627 9.43711 4.3252C9.47461 4.3252 9.52461 4.3627 9.56211 4.3627Z" fill="#39B588"/>
            <path d="M19.3627 8.7375C16.4627 8.4125 13.5377 8.4125 10.6377 8.7375C10.9502 7.025 11.3502 5.3125 11.9127 3.6625C11.9377 3.5625 11.9252 3.4875 11.9377 3.3875C12.9252 3.15 13.9377 3 15.0002 3C16.0502 3 17.0752 3.15 18.0502 3.3875C18.0627 3.4875 18.0627 3.5625 18.0877 3.6625C18.6502 5.325 19.0502 7.025 19.3627 8.7375Z" fill="#39B588"/>
            <path d="M8.2375 19.8627C6.5125 19.5502 4.8125 19.1502 3.1625 18.5877C3.0625 18.5627 2.9875 18.5752 2.8875 18.5627C2.65 17.5752 2.5 16.5627 2.5 15.5002C2.5 14.4502 2.65 13.4252 2.8875 12.4502C2.9875 12.4377 3.0625 12.4377 3.1625 12.4127C4.825 11.8627 6.5125 11.4502 8.2375 11.1377C7.925 14.0377 7.925 16.9627 8.2375 19.8627Z" fill="#39B588"/>
            <path d="M27.5002 15.5002C27.5002 16.5627 27.3502 17.5752 27.1127 18.5627C27.0127 18.5752 26.9377 18.5627 26.8377 18.5877C25.1752 19.1377 23.4752 19.5502 21.7627 19.8627C22.0877 16.9627 22.0877 14.0377 21.7627 11.1377C23.4752 11.4502 25.1877 11.8502 26.8377 12.4127C26.9377 12.4377 27.0127 12.4502 27.1127 12.4502C27.3502 13.4377 27.5002 14.4502 27.5002 15.5002Z" fill="#39B588"/>
            <path d="M19.3627 22.2627C19.0502 23.9877 18.6502 25.6877 18.0877 27.3377C18.0627 27.4377 18.0627 27.5127 18.0502 27.6127C17.0752 27.8502 16.0502 28.0002 15.0002 28.0002C13.9377 28.0002 12.9252 27.8502 11.9377 27.6127C11.9252 27.5127 11.9377 27.4377 11.9127 27.3377C11.3627 25.6752 10.9502 23.9877 10.6377 22.2627C12.0877 22.4252 13.5377 22.5377 15.0002 22.5377C16.4627 22.5377 17.9252 22.4252 19.3627 22.2627Z" fill="#39B588"/>
            <path d="M19.7042 20.2042C16.5778 20.5986 13.4222 20.5986 10.2958 20.2042C9.90139 17.0778 9.90139 13.9222 10.2958 10.7958C13.4222 10.4014 16.5778 10.4014 19.7042 10.7958C20.0986 13.9222 20.0986 17.0778 19.7042 20.2042Z" fill="#39B588"/>
          </svg>
        </div>
        <h2 className="text-lg font-semibold mb-1">Introducing international payments</h2>
        <p className="text-sm text-gray-600 mb-4">You can now collect payments in multiple currencies.</p>
        <AppBtn
          className="text-white px-4 py-2 mb-2 rounded-15 font-semibold"
          onClick={() => toggleModal("international_payments")}
          disabled={hasPendingRequest}
        >
          {hasPendingRequest ? "Request Pending" : "Request Access"}
          {hasPendingRequest ? (
            //prettier-ignore
            <svg className="ml-1 w-4" viewBox="0 0 24 24" fill="none">
              <path d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M15.71 15.18L12.61 13.33C12.07 13.01 11.63 12.24 11.63 11.61V7.51001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          ) : (
            //prettier-ignore
            <svg className="ml-1 w-4" viewBox="0 0 16 17" fill="none">
              <path d="M3.33301 8.5H12.6663" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8 3.83301L12.6667 8.49967L8 13.1663" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          )}
        </AppBtn>
        {/* Adding the image in the bottom left */}
        <img
          src="/images/currencies.png"
          alt="Currency icons"
          className="absolute bottom-0 right-0 w-26 h-22 ml-2 rounded-br-15"
        />
      </div>
      <Portal>
        <InternationalPaymentRequestModal
          show={modals.international_payments.show}
          toggle={() => toggleModal("international_payments")}
        />
      </Portal>
    </>
  );
};

export default IntlPaymentsAccess;
