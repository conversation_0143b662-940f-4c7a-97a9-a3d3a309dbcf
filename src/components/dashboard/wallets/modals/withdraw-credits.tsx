import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "@/components/ui/modal";
import { ReferredUsers } from "@/assets/interfaces";
import authContext from "@/contexts/auth-context";
import WalletContext from "@/contexts/wallet-context";
import { useFormik } from "formik";
import { InputWithAddon } from "@/components/ui/form-elements";
import { getFieldvalues, toCurrency } from "@/assets/js/utils/functions";
import { toKobo, toNaira } from "@/assets/js/utils/utils";
import ErrorLabel from "@/components/ui/error-label";
import { useState } from "react";
import { AppBtn } from "@/components/ui/buttons";
import { useRequest } from "@/api/utils";
import { ConvertCreditsToCash } from "@/api/credits-and-referrals";
import useSteps from "@/components/hooks/useSteps";
import SuccessAnimation from "@/components/ui/success-animation";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
}

const WithdrawCreditsModal: React.FC<Props> = ({ show, toggle }) => {
  const {
    getCreditsReq,
    catlogCredits: wallet,
    balances,
    canManageWallet,
    storeId,
    getRewards,
    updateBalance,
  } = WalletContext.useContainer();
  const minCashoutAmount = getRewards()?.min_cash_out ?? 5000_00;
  const cashoutDisabled = wallet.balance < minCashoutAmount;

  const { step, next, changeStep } = useSteps(["amount", "success"], 0);
  const [error, setError] = useState("");

  const creditsToCashReq = useRequest(ConvertCreditsToCash);

  const form = useFormik({
    initialValues: {
      amount: 0,
    },
    onSubmit: async (values) => {
      if (toKobo(values.amount) < minCashoutAmount) {
        setError(`You'll need at least ${toCurrency(toNaira(minCashoutAmount), wallet.currency)} to convert to cash`);
        return;
      }

      const [res, err] = await creditsToCashReq.makeRequest({ amount: values.amount });

      if (err) {
        setError(err?.message);
        return;
      }

      next();
      updateBalance({
        walletId: "catlogCredit",
        type: "debit",
        amount: toKobo(values.amount),
      });
    },
  });

  const close = () => {
    form.resetForm();
    changeStep("amount");

    toggle(false);
  };

  return (
    <Modal {...{ show, toggle: close }} title="Convert Credits to Cash" size="midi">
      <ModalBody>
        {step === "amount" && (
          <div>
            {(cashoutDisabled || error) && (
              <div className="-mb-2.5">
                <ErrorLabel
                  error={
                    cashoutDisabled
                      ? `You'll need at least ${toCurrency(
                          toNaira(minCashoutAmount),
                          wallet.currency
                        )} to convert to cash`
                      : error
                  }
                  perm={!error}
                  setError={setError}
                />
              </div>
            )}
            <h4 className="font-display text-black-secondary font-bold text-1sm -mb-2.5">Amount to convert</h4>
            <InputWithAddon
              placeholder="Enter Amount"
              {...getFieldvalues("amount", form)}
              type="number"
              value={Number(form.values.amount).toString()}
              inputMode="numeric"
              onChange={(e) => form.setFieldValue("amount", Number(e.target.value))}
            >
              <div className="bg-white text-black-placeholder h-full flex items-center text-1xs rounded-l-10 px-3 font-medium">
                {wallet.currency}
              </div>
              <div className="flex items-center ml-auto py-1.25 px-2.5 bg-grey-fields-100 rounded-15 absolute font-normal text-xxs top-1/2 transform -translate-y-1/2 right-3 text-black-placeholder">
                Balance:
                <span className="text-black-secondary font-medium inline-block ml-0.5">
                  {toCurrency(toNaira(wallet.balance), wallet.currency)}
                </span>
                {/* //should millify */}
              </div>
            </InputWithAddon>
            <div className="h-10 border-l-2 border-dashed border-grey-divider w-[80%] ml-[10%]"></div>
            <div className="bg-grey-fields-100 rounded-10 flex items-center justify-between px-3 py-2.5">
              <span className="text-black-muted font-medium text-1xs">You Receive in Wallet</span>
              <h4 className="font-display text-black font-bold text-1xs">
                {toCurrency(form.values.amount / 2, wallet.currency)}
              </h4>
            </div>
          </div>
        )}

        {step === "success" && (
          <div className="flex flex-col items-center w-full py-8">
            <figure className="mb-3.5">
              <SuccessAnimation />
            </figure>
            <h2 className="text-center text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto !leading-tight font-light">
              <b className="font-bold">
                {wallet.currency} {Number(form.values.amount)}
              </b>{" "}
              has been <br /> converted to cash
            </h2>
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        {step === "amount" ? (
          <AppBtn
            isBlock
            type="submit"
            className="flex-1"
            disabled={creditsToCashReq?.isLoading || cashoutDisabled}
            size="lg"
            onClick={() => form.handleSubmit()}
          >
            {creditsToCashReq?.isLoading ? "Processing..." : "Convert"}
          </AppBtn>
        ) : (
          <AppBtn isBlock className="flex-1" size="lg" onClick={() => close()}>
            Continue
          </AppBtn>
        )}
      </ModalFooter>
    </Modal>
  );
};

export default WithdrawCreditsModal;
