import Modal, { ModalBody } from "@/components/ui/modal";
import WalletContext from "@/contexts/wallet-context";
import ErrorBox from "@/components/ui/error";
import { AppBtn } from "@/components/ui/buttons";
import { useRouter } from "next/router";
import { WHATSAPP_LINK } from "@/assets/js/utils/constants";
import useCopyClipboard from "@/components/hooks/useCopyClipboard";
import LazyImage from "@/components/lazy-image";
import { Wallet } from "@/assets/interfaces";

interface Props {
  show: boolean;
  wallet: Wallet;
  toggle: (state: boolean) => void;
}

const BankAccounts: React.FC<Props> = ({ show, toggle, wallet }) => {
  const router = useRouter();

  const [_, copy] = useCopyClipboard("", {
    successDuration: 600,
  });

  return (
    <Modal {...{ show, toggle }} title="Account Details" size="midi">
      <ModalBody>
        {!wallet.has_completed_kyc && (
          <ErrorBox
            title="No Accounts"
            message={"Please complete your KYC to get a bank account in your business name"}
            className="py-5 mt-0"
          >
            <AppBtn size="sm" className="mt-5" onClick={() => router.push("/payments/kyc")}>
              Complete KYC
            </AppBtn>
          </ErrorBox>
        )}

        {wallet?.has_completed_kyc && wallet?.accounts.length < 1 && (
          <ErrorBox
            title="Account creation in progress"
            message={"We're currently creating your account"}
            className="py-5 mt-0"
          >
            <AppBtn
              size="sm"
              className="mt-5"
              onClick={() => window.open(`${WHATSAPP_LINK}&text=Hi, My bank account wasn't created`, "_blank")}
            >
              Contact Support
            </AppBtn>
          </ErrorBox>
        )}
        {wallet?.accounts &&
          wallet?.accounts.map((account, index) => (
            <div
              className="flex items-start justify-between py-3.75 first:pt-0 border-t first:border-0 border-grey-divider"
              key={index}
            >
              <div>
                <div className="text-1xs sm:text-sm text-black-secondary font-medium capitalize">
                  {account?.account_name}
                </div>
                <div>
                  <div className="flex items-center mt-1.75">
                    <h1 className="mr-1.25 text-lg sm:text-xl font-bold">{account?.account_number ?? ""}</h1>
                    <button
                      className="w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-md flex items-center justify-center text-accent-purple-500 no-outline"
                      onClick={() => copy(account?.account_number)}
                    >
                      {/* prettier-ignore */}
                      <svg width="57%" viewBox="0 0 14 15" fill="none">
                      <path d="M6.47508 13.6556H4.02508C1.74425 13.6556 0.729248 12.6406 0.729248 10.3598V7.90977C0.729248 5.62894 1.74425 4.61394 4.02508 4.61394H6.47508C8.75591 4.61394 9.77091 5.62894 9.77091 7.90977V10.3598C9.77091 12.6406 8.75591 13.6556 6.47508 13.6556ZM4.02508 5.48894C2.21675 5.48894 1.60425 6.10144 1.60425 7.90977V10.3598C1.60425 12.1681 2.21675 12.7806 4.02508 12.7806H6.47508C8.28341 12.7806 8.89591 12.1681 8.89591 10.3598V7.90977C8.89591 6.10144 8.28341 5.48894 6.47508 5.48894H4.02508Z" fill="currentColor" />
                      <path d="M9.97508 10.1556H9.33341C9.09425 10.1556 8.89591 9.95727 8.89591 9.7181V7.90977C8.89591 6.10144 8.28341 5.48894 6.47508 5.48894H4.66675C4.42758 5.48894 4.22925 5.2906 4.22925 5.05144V4.40977C4.22925 2.12894 5.24425 1.11394 7.52508 1.11394H9.97508C12.2559 1.11394 13.2709 2.12894 13.2709 4.40977V6.85977C13.2709 9.1406 12.2559 10.1556 9.97508 10.1556ZM9.77091 9.2806H9.97508C11.7834 9.2806 12.3959 8.6681 12.3959 6.85977V4.40977C12.3959 2.60144 11.7834 1.98894 9.97508 1.98894H7.52508C5.71675 1.98894 5.10425 2.60144 5.10425 4.40977V4.61394H6.47508C8.75591 4.61394 9.77091 5.62894 9.77091 7.90977V9.2806Z" fill="currentColor" />
                    </svg>
                    </button>
                  </div>
                  <div className="flex items-center text-black-muted mt-0.5 text-xs font-medium">
                    {account?.bank_name}
                  </div>
                  {account?.provider === "SQUADCO" && (
                    <div className="text-accent-red-500">Please pause use of this account temporarily</div>
                  )}
                </div>
              </div>
              <figure className="w-10 h-10 flex items-center justify-center rounded-full overflow-hidden relative">
                <LazyImage
                  src={account?.image}
                  alt={`${account?.bank_name} logo`}
                  className="w-full h-full object-cover"
                  loaderClasses="rounded-full"
                />
              </figure>
            </div>
          ))}
      </ModalBody>
    </Modal>
  );
};

export default BankAccounts;
