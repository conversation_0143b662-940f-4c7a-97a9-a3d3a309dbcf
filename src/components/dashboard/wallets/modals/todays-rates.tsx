import Modal, { ModalBody } from "@/components/ui/modal";
import { CURRENCIES, CurrencyRates, Wallet } from "@/assets/interfaces";
import { useState } from "react";
import { useFetcher } from "@/api/utils";
import { GetAllConversionRates } from "@/api";
import { humanFriendlyDate, toCurrency } from "@/assets/js/utils/functions";
import { SelectDropdown } from "@/components/ui/form-elements";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  initialCurrency: CURRENCIES;
}

const TodaysRateModal: React.FC<Props> = ({ show, toggle, initialCurrency }) => {
  const [baseCurrency, setBaseCurrency] = useState<CURRENCIES>(initialCurrency);
  const getAllConversionRates = useFetcher(GetAllConversionRates);
  const rates: CurrencyRates = getAllConversionRates.response?.data?.rates ?? {};

  const currencyRates = rates[baseCurrency] ?? {};

  return (
    <Modal {...{ show, toggle }} title="Today's Rates" size="midi">
      <ModalBody>
        <SelectDropdown
          label="Base Currency"
          value={baseCurrency}
          onChange={(e) => setBaseCurrency(e.target.value as CURRENCIES)}
          options={Object.keys(rates).map((currency) => ({ value: currency, text: currency }))}
          className="mb-3.75"
        />
        <div className="border rounded-15 border-grey-border border-opacity-50 overflow-hidden">
          <div className="flex items-center justify-between p-3.5 border-b border-grey-border border-opacity-50 bg-grey-fields-100">
            <div className="text-black-placeholder flex-1">Currency</div>
            <div className="text-black-placeholder flex-1 flex justify-center">
              {/* prettier-ignore */}
              <svg className="w-5 text-black-placeholder" viewBox="0 0 24 24" fill="none">
                <path d="M20.5 14.99L15.49 20.01" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.5 14.99H20.5" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.5 9.00999L8.51 3.98999" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M20.5 9.01001H3.5" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div className="text-black-placeholder flex-1 text-right">Rate</div>
          </div>
          {Object.keys(currencyRates).map((currency, index) => (
            <div
              className="flex items-center justify-between p-3.75 border-b border-grey-border border-opacity-50 text-dark text-sm last:border-0"
              key={index}
            >
              <div className="flex-1">{toCurrency(1, currency)}</div>
              <div className="flex-1"></div>
              <div className="flex-1 text-right">{toCurrency(1 / currencyRates[currency], baseCurrency, false, 4)}</div>
            </div>
          ))}
        </div>
        <div className="flex items-center text-1xs text-dark mt-2.5">
          Last updated on {humanFriendlyDate(getAllConversionRates.response?.data?.created_at, true)}
          {/* prettier-ignore */}
          <svg className="w-3.5 ml-1" viewBox="0 0 24 24" fill="none">
            <path d="M14.55 21.67C18.84 20.54 22 16.64 22 12C22 6.48 17.56 2 12 2C5.33 2 2 7.56 2 7.56M2 7.56V3M2 7.56H4.01H6.44" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M2 12C2 17.52 6.48 22 12 22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="3 3"/>
          </svg>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default TodaysRateModal;
