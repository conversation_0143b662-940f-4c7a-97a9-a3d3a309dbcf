import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "@/components/ui/modal";
import { useFormik } from "formik";
import { InputField, MultiSelect, SelectDropdown, TextArea } from "@/components/ui/form-elements";
import * as yup from "yup";
import { useState } from "react";
import { AppBtn } from "@/components/ui/buttons";
import { useRequest } from "@/api/utils";
import useSteps from "@/components/hooks/useSteps";
import SuccessAnimation from "@/components/ui/success-animation";
import { RequestInternationalPayment } from "@/api";
import ErrorLabel from "@/components/ui/error-label";
import authContext from "@/contexts/auth-context";
import { CURRENCY_OPTIONS, paymentsEnabledCurrencies } from "@/assets/js/utils/constants";
import { getFieldvalues } from "@/assets/js/utils/functions";
import { CURRENCIES } from "@/assets/interfaces";
import { InternationalPaymentRequestParams } from "@/api/interfaces";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
}

const InternationalPaymentRequestModal: React.FC<Props> = ({ show, toggle }) => {
  const { step, next, changeStep } = useSteps(["form", "success"], 0);
  const { subscription, store } = authContext.useContainer();
  const existingWallets = store?.wallets.map((wallet) => wallet.currency) ?? [];

  const { response, makeRequest, isLoading, error } =
    useRequest<InternationalPaymentRequestParams>(RequestInternationalPayment);

  let canRequestInternationalPayments = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_REQUEST_INTERNATIONAL_PAYMENTS,
  });

  const form = useFormik({
    initialValues: {
      requested_currencies: [] as CURRENCIES[],
      reason: "",
      collect_payments_from_abroad: "",
      current_payment_method: "",
      plans_to_get_customers_abroad: "",
    },
    validationSchema: generateValidationSchema(),
    onSubmit: async (values) => {
      const [res, err] = await makeRequest({
        ...values,
        collect_payments_from_abroad: values.collect_payments_from_abroad === "yes" ? true : false,
      });

      if (res) {
        next();
      }
    },
  });

  const currencyOptions = paymentsEnabledCurrencies
    .map((p) => CURRENCY_OPTIONS.find((o) => o.value === p))
    .filter((currency) => !existingWallets.includes(currency.value));

  const close = () => {
    form.resetForm();
    changeStep("form");
    toggle(false);
  };

  return (
    <Modal {...{ show, toggle: close }} title="Request Access" size="midi">
      <ModalBody>
        {!canRequestInternationalPayments && (
          <div className="flex flex-col items-center w-full py-8 max-w-[375px] mx-auto">
            <figure className="mb-3.5">
              {/* prettier-ignore */}
              <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="100" height="100" rx="50" fill="#39B588"/>
                <path d="M49.9378 29.167C38.4378 29.167 29.1045 38.5003 29.1045 50.0003C29.1045 61.5003 38.4378 70.8337 49.9378 70.8337C61.4378 70.8337 70.7712 61.5003 70.7712 50.0003C70.7712 38.5003 61.4587 29.167 49.9378 29.167ZM57.7503 50.7295L50.0003 59.542L49.0837 60.5837C47.8128 62.0212 46.7712 61.6462 46.7712 59.7087V51.4587H43.2295C41.6253 51.4587 41.1878 50.4795 42.2503 49.2712L50.0003 40.4587L50.917 39.417C52.1878 37.9795 53.2295 38.3545 53.2295 40.292V48.542H56.7712C58.3753 48.542 58.8128 49.5212 57.7503 50.7295Z" fill="white"/>
              </svg>
            </figure>
            <h2 className="text-center text-black text-base sm:text-lg lg:text-xl mx-auto !leading-tight font-bold">
              Unlock International Payments!
            </h2>

            <p className="text-dark text-sm leading-tight text-center mx-auto mt-2">
              You'll need to be on the Business+ plan to collect payments internationally. Upgrade to collect payments
              in multiple currencies and expand your reach!
            </p>
          </div>
        )}
        {canRequestInternationalPayments && step === "form" && (
          <div>
            {error && <ErrorLabel error={error.message} />}
            <div className="mt-4 mb-4">
              <MultiSelect
                label="Select Currencies"
                options={currencyOptions}
                {...getFieldvalues("requested_currencies", form)}
              />
            </div>
            <div className="mb-4">
              <TextArea label="Why do you need these currencies?" rows={3} {...getFieldvalues("reason", form)} />
            </div>
            <div className="mb-4">
              <SelectDropdown
                label="Do you currently get paid internationally?"
                options={paymentOptions}
                {...getFieldvalues("collect_payments_from_abroad", form)}
              />
            </div>
            {form.values.collect_payments_from_abroad === "yes" && (
              <div className="mb-4">
                <InputField
                  label="How do you currently collect those payments?"
                  {...getFieldvalues("current_payment_method", form)}
                />
              </div>
            )}
            {form.values.collect_payments_from_abroad === "no" && (
              <div className="mb-4">
                <InputField
                  label="How do you intend to get customers abroad?"
                  {...getFieldvalues("current_payment_method", form)}
                />
              </div>
            )}
          </div>
        )}

        {canRequestInternationalPayments && step === "success" && (
          <div className="flex flex-col items-center w-full py-8">
            <figure className="mb-3.5">
              <SuccessAnimation />
            </figure>
            <h2 className="text-center text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto !leading-tight font-light">
              Your request has been <br /> <b>Successfully Sent</b>
            </h2>

            <p className="text-dark text-sm leading-tight text-center max-w-xs mx-auto mt-4">
              Your request to enable international payments on your account has been received and is under review.
            </p>
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        {canRequestInternationalPayments && step === "form" ? (
          <AppBtn
            isBlock
            type="submit"
            className="flex-1"
            disabled={isLoading}
            size="lg"
            onClick={() => form.handleSubmit()}
          >
            {isLoading ? "Submitting..." : "Continue"}
          </AppBtn>
        ) : canRequestInternationalPayments && step === "success" ? (
          <AppBtn isBlock className="flex-1" size="lg" onClick={() => close()}>
            Alright, Got it!
          </AppBtn>
        ) : (
          <AppBtn isBlock className="flex-1" size="lg" href="/my-store/change-plan">
            Upgrade to Business+ plan
          </AppBtn>
        )}
      </ModalFooter>
    </Modal>
  );
};

const generateValidationSchema = () => {
  return yup.object().shape({
    requested_currencies: yup
      .array()
      .of(
        yup
          .string()
          .oneOf([
            CURRENCIES.USD,
            CURRENCIES.GBP,
            CURRENCIES.NGN,
            CURRENCIES.GHC,
            CURRENCIES.ZAR,
            CURRENCIES.KES,
            CURRENCIES.CAD,
          ])
      )
      .min(1, "Please select at least one currency"),
    reason: yup.string().required("Reason is required"),
    collect_payments_from_abroad: yup.string().required("Please specify if you are currently receiving payments"),
  });
};

const paymentOptions = [
  { value: "yes", text: "Yes" },
  { value: "no", text: "No" },
];

export default InternationalPaymentRequestModal;
