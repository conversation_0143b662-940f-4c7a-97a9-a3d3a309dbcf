import React, { useContext, useState, useEffect } from "react";
import classNames from "classnames";
import { useRouter } from "next/router";
import WalletContext from "@/contexts/wallet-context";
import { toNaira } from "@/assets/js/utils/utils";
import { ModalState } from "../../hooks/useModals";
import { AppBtn } from "../../ui/buttons";
import NigeriaFlag from "@/assets/icons/flags/nigeria.svg";
import GhanaFlag from "@/assets/icons/flags/ghana.svg";
import { amountFormat } from "@/assets/js/utils/functions";

interface IProps {
  modals: ModalState;
  toggleModal: (key: string) => void;
}

const WalletCarousel: React.FC<IProps> = ({ modals, toggleModal }) => {
  const { getWalletsReq, getWalletBalance, canManageWallet, canOwnWallet, store } = WalletContext.useContainer();

  const wallets = getWalletsReq.response?.data?.wallets || [];
  const [currentIndex, setCurrentIndex] = useState(0);
  const currentWallet = wallets[currentIndex]; // Get the wallet at the current carousel position
  const router = useRouter();

  const nextWallet = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % wallets.length);
  };

  const prevWallet = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + wallets.length) % wallets.length);
  };

  // Handle loading or empty wallet array
  if (getWalletsReq.isLoading) {
    return <div>Loading wallets...</div>;
  }

  if (wallets.length === 0) {
    return <div>No wallets available</div>;
  }

  return (
    <div className="relative w-full max-w-md mx-auto">
      {/* Wallet Display */}
      <div className="bg-white mt-4.5 p-3.75 lg:p-4 rounded-15 shadow-lg">
        {/* Wallet Header */}
        <div className="bg-grey-fields-100 rounded-30 inline-flex items-center uppercase text-dark text-xxs py-1 px-1.5 font-semibold">
          <figure className="h-5 w-5 rounded-full overflow-hidden mr-1.25">
            {currencyMap[currentWallet.currency].icon()}
          </figure>
          {currencyMap[currentWallet.currency].fullName} ({currentWallet.currency})
        </div>

        {/* Balance Display */}
        <div className="flex items-center justify-between mt-2">
          <h1 className={classNames("text-2lg font-bold", currentWallet.balance > 0 ? "text-black" : "text-dark")}>
            {amountFormat(toNaira(currentWallet.balance))}
          </h1>
          <button
            onClick={getWalletBalance}
            className="h-6 w-6 rounded-5 bg-primary-80 text-primary-500 flex items-center justify-center ml-1.5"
          >
            {/* Reload Balance Icon */}
            <svg className="w-1/2" viewBox="0 0 24 24" fill="none">
              <path
                d="M14.55 21.67C18.84 20.54 22 16.64 22 12C22 6.48 17.56 2 12 2C5.33 2 2 7.56 2 7.56M2 7.56V3M2 7.56H4.01H6.44"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M2 12C2 17.52 6.48 22 12 22"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="3 3"
              />
            </svg>
          </button>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center mt-4 space-x-4">
          <WalletCTA
            label="Request"
            onClick={() => router.push("/payments/request-payment")}
            disabled={!currentWallet.has_completed_kyc}
          />
          <WalletCTA label="Convert" onClick={() => toggleModal("convert")} />
          <WalletCTA
            label="Withdraw"
            onClick={() => toggleModal("make_withdrawal")}
            disabled={currentWallet.balance <= 0 || !canManageWallet}
          />
          {currentWallet.currency === "NGN" && (
            <WalletCTA label="Accounts" onClick={() => toggleModal("bank_accounts")} />
          )}
        </div>

        <AppBtn size="md" isBlock color="neutral" className="mt-3.75" onClick={() => toggleModal("history")}>
          Transaction History
        </AppBtn>
      </div>

      {/* Carousel Navigation */}
      <button
        onClick={prevWallet}
        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-gray-200 rounded-full p-2 shadow-lg focus:outline-none"
      >
        {/* Left Arrow SVG */}
        <svg className="w-5 h-5 text-gray-700" viewBox="0 0 24 24" fill="none">
          <path
            d="M15 18l-6-6 6-6"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
      <button
        onClick={nextWallet}
        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gray-200 rounded-full p-2 shadow-lg focus:outline-none"
      >
        {/* Right Arrow SVG */}
        <svg className="w-5 h-5 text-gray-700" viewBox="0 0 24 24" fill="none">
          <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      </button>
    </div>
  );
};

interface WalletCTAProps {
  label: string;
  onClick: () => void;
  disabled?: boolean;
}

const WalletCTA: React.FC<WalletCTAProps> = ({ label, onClick, disabled }) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className="flex flex-col items-center disabled:opacity-50 disabled:cursor-not-allowed"
  >
    <span className="flex items-center justify-center h-12 w-12 p-2.5 rounded-full border border-grey-divider mb-1">
      {/* Placeholder Icon */}
    </span>
    <h2 className="text-1xs text-black font-bold">{label}</h2>
  </button>
);

const currencyMap = {
  NGN: {
    fullName: "Nigerian Naira",
    icon: NigeriaFlag,
  },
  GHS: {
    fullName: "Ghanaian Cedi",
    icon: GhanaFlag,
  },
  USD: {
    fullName: "US Dollar",
    icon: GhanaFlag, // Replace with the correct icon for USD
  },
  KES: {
    fullName: "Kenyan Shilling",
    icon: NigeriaFlag, // Replace with the correct icon for KES
  },
};

export default WalletCarousel;
