import React, { useEffect, useRef, useState } from "react";
import { SmoothHorizontalScrolling } from "../../assets/js/utils/functions";
import LazyImage from "../lazy-image";
import Modal, { ModalBody } from "../ui/modal";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  images: string[];
  videos?: string[];
  current?: number;
  title?: string;
}

const MediaCarouselModal: React.FC<Props> = ({ show, toggle, images, videos, current = 0, title }) => {
  const [currentIndex, setCurrentIndex] = useState(current);
  const slider = useRef<HTMLDivElement>(null);
  const totalLength = images.length + videos?.length;

  useEffect(() => {
    if (slider.current) {
      handleSlideChange();

      window.addEventListener("resize", () => handleSlideChange(false));

      return () => window.removeEventListener("resize", () => handleSlideChange(false));
    }
  }, [currentIndex, slider.current]);

  useEffect(() => {
    setCurrentIndex(current);
  }, [current]);

  const handleSlideChange = (animate = true) => {
    const sliderEl = slider.current;

    if (sliderEl) {
      const slideWidth = sliderEl?.clientWidth;

      if (animate) {
        const slidePosition = currentIndex * slideWidth;
        const slideAmount = slidePosition - sliderEl?.scrollLeft + currentIndex;

        SmoothHorizontalScrolling(sliderEl, 100, slideAmount, sliderEl?.scrollLeft);
      } else {
        sliderEl.scrollLeft = currentIndex * slideWidth + currentIndex;
      }
    }
  };

  const slide = (dir: "next" | "prev") => {
    if (dir === "next" && currentIndex < totalLength - 1) {
      setCurrentIndex(currentIndex + 1);
    } else if (dir === "prev" && currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  return (
    <Modal title={title || "Product Medias"} {...{ show, toggle }}>
      <ModalBody noPadding className="relative">
        <div
          className="grid overflow-x-hidden"
          style={{ gridTemplateColumns: `repeat(${totalLength}, 100%)`, scrollBehavior: "smooth" }}
          ref={slider}
        >
          {videos &&
            videos.map((video, index) => (
              <div className="w-full p-5 sm:p-6.25" key={index}>
                <div className="w-full relative" style={{ paddingTop: "100%" }}>
                  <video
                    src={video}
                    controls
                    className="max-w-full max-h-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform"
                    playsInline
                  ></video>
                </div>
              </div>
            ))}
          {images.map((image, index) => (
            <div className="w-full p-5 sm:p-6.25" key={index}>
              <figure className="w-full relative" style={{ paddingTop: "100%" }}>
                <LazyImage
                  src={image}
                  alt=""
                  className="max-w-full max-h-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform"
                />
              </figure>
            </div>
          ))}
        </div>
        <div className="flex w-full justify-between px-5 sm:px-6.25 absolute top-1/2 -translate-y-1/2 transform">
          {currentIndex > 0 && (
            <button
              className="h-10 w-10 rounded-full bg-primary-500 hover:bg-primary-700 flex items-center justify-center shadow-md"
              onClick={() => slide("prev")}
            >
              {/* prettier-ignore */}
              <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
                <path d="M9.375 3.75L5.625 7.5L9.375 11.25" stroke="#FCFBFF" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
          )}
          {currentIndex < totalLength - 1 && (
            <button
              className="h-10 w-10 rounded-full bg-primary-500 hover:bg-primary-700 flex items-center justify-center shadow-md ml-auto"
              onClick={() => slide("next")}
            >
              <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
                <path
                  d="M5.625 11.25L9.375 7.5L5.625 3.75"
                  stroke="#FCFBFF"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          )}
        </div>
      </ModalBody>
    </Modal>
  );
};

export default MediaCarouselModal;
