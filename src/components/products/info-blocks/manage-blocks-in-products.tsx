import { InfoBlockInterface } from "@/assets/interfaces";
import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import { AppBtn, RoundActionBtn } from "@/components/ui/buttons";
import ContentState from "@/components/ui/content-state";
import DataAccordion from "@/components/ui/data-accordion";
import { productPageIcons } from "@/components/ui/layouts/product";
import { Product } from "@/pages/products/create";
import { FormikProps } from "formik";
import React, { useMemo, useState } from "react";
import InfoBlocksExplainer from "../create-products/info-blocks-explainer";
import EditInfoBlock from "./modals/edit-info-block";
import Badge from "@/components/ui/badge";
import LazyImage from "@/components/lazy-image";
import DeleteInfoBlockConfirmation from "./modals/delete-info-block";
import SelectInfoBlock from "./modals/select-info-block";
import { ensureUniqueItems, enumToHumanFriendly } from "@/assets/js/utils/functions";

interface Props {
  infoBlocks: InfoBlockInterface[];
  blocksLoading: boolean;
  setInfoBlocks: (blocks: InfoBlockInterface[]) => void;
  blocksError: any;
  form: FormikProps<Product>;
}

const ManageBlocksInProducts = ({ form, infoBlocks, blocksLoading, setInfoBlocks, blocksError }: Props) => {
  const { modals, toggleModal } = useModals([
    "add_info_block",
    "delete_info_block",
    "edit_info_block",
    "info_block_explainer",
  ]);

  const [selectedBlock, setSelectedBlock] = useState<{
    edit: InfoBlockInterface | null;
    delete: InfoBlockInterface | null;
  }>({
    edit: null,
    delete: null,
  });

  const editBlock = (block: InfoBlockInterface) => {
    setSelectedBlock((prev) => ({
      ...prev,
      edit: block,
    }));

    toggleModal("edit_info_block");
  };

  const deleteBlock = (block: InfoBlockInterface) => {
    setSelectedBlock((prev) => ({
      ...prev,
      delete: block,
    }));

    toggleModal("delete_info_block");
  };

  const removeBlockFromProduct = (block: InfoBlockInterface) => {
    // setInfoBlocks(infoBlocks.filter((b) => b.id !== block.id));

    const updatedInfoBlocks = [...form.values.info_blocks].filter((b) => b.id !== block.id);
    form.setFieldValue("info_blocks", ensureUniqueItems(updatedInfoBlocks));
  };

  const updateBlocks = (block: InfoBlockInterface, id: string) => {
    
    const blocks = [...infoBlocks];
    
    const blockIndex = blocks.findIndex((block) => block.id === id);
    if (blockIndex !== -1) {
      blocks[blockIndex] = block;
      setInfoBlocks(blocks)
      // form.setFieldValue("info_blocks", ensureUniqueItems(blocks.map(b => b.id)));
    }
  };

  const removeBlock = (infoBlock: InfoBlockInterface) => {
    setInfoBlocks(infoBlocks.filter((block) => block.id !== infoBlock.id));

    const updatedInfoBlocks = form.values.info_blocks.filter((block) => block.id !== infoBlock.id);
    form.setFieldValue("info_blocks", ensureUniqueItems(updatedInfoBlocks));

    setSelectedBlock((prev) => ({
      ...prev,
      delete: null,
    }));
    toggleModal("delete_info_block");
  };

  return (
    <>
      <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
        <DataAccordion
          isClosed={true}
          title={
            <span className="text-base font-bold font-body tracking-normal">
              <span className="font-display">Extra Product Info</span>
            </span>
          }
          className="!pb-0"
        >
          <div className="flex gap-2 my-3.75">
            <AppBtn color="neutral" className="flex-1" onClick={(e) => toggleModal("add_info_block")}>
              + Add/Remove Extra Info
            </AppBtn>
            <button
              className="flex items-center justify-center font-medium whitespace-nowrap bg-grey-fields-200 hover:bg-opacity-50 disabled:!text-primary-300 border border-grey-fields-100 hover:border-opacity-80 h-11 sm:h-12  sm:px-3.75 text-1xs sm:text-sm rounded-10 text-black-secondary px-2 font-action !outline-none transition-all cursor-pointer box-border group flex-shrink-0"
              type="button"
              onClick={(e) => toggleModal("info_block_explainer")}
            >
              {/* prettier-ignore */}
              <svg width={20} height={20} viewBox="0 0 20 20" fill="none">
              <path d="M9.99984 18.9577C5.05817 18.9577 1.0415 14.941 1.0415 9.99935C1.0415 5.05768 5.05817 1.04102 9.99984 1.04102C14.9415 1.04102 18.9582 5.05768 18.9582 9.99935C18.9582 14.941 14.9415 18.9577 9.99984 18.9577ZM9.99984 2.29102C5.74984 2.29102 2.2915 5.74935 2.2915 9.99935C2.2915 14.2493 5.74984 17.7077 9.99984 17.7077C14.2498 17.7077 17.7082 14.2493 17.7082 9.99935C17.7082 5.74935 14.2498 2.29102 9.99984 2.29102Z" fill="currentColor"/>
              <path d="M10 11.4577C9.65833 11.4577 9.375 11.1743 9.375 10.8327V6.66602C9.375 6.32435 9.65833 6.04102 10 6.04102C10.3417 6.04102 10.625 6.32435 10.625 6.66602V10.8327C10.625 11.1743 10.3417 11.4577 10 11.4577Z" fill="currentColor"/>
              <path d="M9.99984 14.1664C9.8915 14.1664 9.78317 14.1414 9.68317 14.0997C9.58317 14.0581 9.4915 13.9997 9.40817 13.9247C9.33317 13.8414 9.27484 13.7581 9.23317 13.6497C9.1915 13.5497 9.1665 13.4414 9.1665 13.3331C9.1665 13.2247 9.1915 13.1164 9.23317 13.0164C9.27484 12.9164 9.33317 12.8247 9.40817 12.7414C9.4915 12.6664 9.58317 12.6081 9.68317 12.5664C9.88317 12.4831 10.1165 12.4831 10.3165 12.5664C10.4165 12.6081 10.5082 12.6664 10.5915 12.7414C10.6665 12.8247 10.7248 12.9164 10.7665 13.0164C10.8082 13.1164 10.8332 13.2247 10.8332 13.3331C10.8332 13.4414 10.8082 13.5497 10.7665 13.6497C10.7248 13.7581 10.6665 13.8414 10.5915 13.9247C10.5082 13.9997 10.4165 14.0581 10.3165 14.0997C10.2165 14.1414 10.1082 14.1664 9.99984 14.1664Z" fill="currentColor" />
            </svg>
            </button>
          </div>

          <div className="flex flex-col gap-3">
            {form.values.info_blocks?.map((block, _i) => (
              <InfoBlockItem
                key={_i}
                data={block}
                deleteAction={() => removeBlockFromProduct(block)}
                editAction={() => editBlock(block)}
              />
            ))}
          </div>
        </DataAccordion>
      </div>

      <Portal>
        <InfoBlocksExplainer
          title=""
          show={modals.info_block_explainer.show}
          toggle={() => toggleModal("info_block_explainer")}
        />
        <SelectInfoBlock
          show={modals.add_info_block.show}
          allBlocks={infoBlocks}
          productBlocks={form.values.info_blocks}
          toggle={() => toggleModal("add_info_block")}
          setProductBlocks={(blocks: InfoBlockInterface[]) =>
            form.setFieldValue("info_blocks", ensureUniqueItems(blocks))
          }
          setAllBlocks={(blocks: InfoBlockInterface[]) => setInfoBlocks(blocks)}
        />
        {selectedBlock.edit !== null && (
          <EditInfoBlock
            onSuccess={() => toggleModal("edit_info_block")}
            show={modals?.edit_info_block.show}
            productName={form.values.name}
            addBlock={(block: InfoBlockInterface) => {
              // Update the infoBlocks state
              setInfoBlocks([...infoBlocks, block]);

              // const updatedBlocks = ensureUniqueItems([...form.values.info_blocks, block.id]);
              form.setFieldValue("info_blocks", [...form.values.info_blocks, block.id]);
            }}
            block={selectedBlock.edit}
            toggle={() => toggleModal("edit_info_block")}
            updateBlocks={(block: InfoBlockInterface) => updateBlocks(block, selectedBlock?.edit?.id)}
          />
        )}
        {selectedBlock.delete !== null && (
          <DeleteInfoBlockConfirmation
            show={modals.delete_info_block.show}
            toggle={() => toggleModal("delete_info_block")}
            deleteBlock={removeBlock}
            selected={selectedBlock.delete}
          />
        )}
      </Portal>
    </>
  );
};

interface BlockItemProps {
  data?: InfoBlockInterface;
  editAction: VoidFunction;
  deleteAction: VoidFunction;
}

const InfoBlockItem: React.FC<BlockItemProps> = ({ data, editAction, deleteAction }) => {
  return (
    <div className="relative h-full">
      <div className="flex flex-col gap-2 bg-grey-fields-100 items-center rounded-10 relative h-full">
        <div className="flex justify-between items-center w-full px-3 pt-2.5">
          <div className="flex gap-2 justify-between items-center flex-wrap">
            <h2 className="text-black font-semibold text-base">{data.title}</h2>
            {data.tag && data.tag && (
              <Badge
                greyBg={false}
                color="dark"
                size="sm"
                uppercase={false}
                className="text-1xs font-medium"
                text={data.tag}
              />
            )}
          </div>
          <div className="flex gap-2 items-center">
            <RoundActionBtn
              icon="edit"
              white
              grey={false}
              className="bg-white text-black-secondary"
              onClick={editAction}
            />
            <RoundActionBtn
              icon="delete"
              white
              grey={false}
              className="bg-white text-accent-red-500"
              onClick={deleteAction}
            />
          </div>
        </div>

        {data.content_type === "TEXT" && (
          <div className="flex-1  w-full h-full border rounded-10 border-gray-border border-opacity-50 bg-white p-3 my-0">
            <p className="text-black-muted text-1xs overflow-ellipsis">{data.text_content}</p>
          </div>
        )}

        {data.content_type === "IMAGES" && (
          <div className="flex-1 h-full border rounded-10 border-gray-border border-opacity-50 bg-white p-3 my-0 grid items-start w-full grid-cols-[repeat(auto-fit,120px)] sm:grid-cols-[repeat(auto-fit,120px)] gap-2.5">
            {data.image_content.map((url, index) => (
              <div key={index}>
                <figure
                  className="w-28 h-28 border border-grey-fields-100 rounded-10 sm:h-[100px] relative group"
                  key={index}
                >
                  <LazyImage
                    src={url}
                    alt={""}
                    className="w-full h-full object-cover rounded-10"
                    loaderClasses="rounded-10"
                  />
                </figure>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ManageBlocksInProducts;
