// import EmojiPicker from "emoji-picker-react";
import dynamic from "next/dynamic";
import React, { useEffect, useRef } from "react";
import { useState } from "react";
import { DeleteStoreInfoBlockParams, UpdateStoreInfoBlocksParams } from "../../../api/interfaces/stores.interface";
import { DeleteStoreInfoBlock, UpdateStoreInfoBlocks } from "../../../api/stores";
import { useRequest } from "../../../api/utils";
import { FILE_TYPES, InfoBlockInterface } from "../../../assets/interfaces";
import { AppBtn, RoundActionBtn } from "../../ui/buttons";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import { toast } from "../../ui/toast";
import { InputField, SelectDropdown, TextArea } from "../../ui/form-elements";
import { useFormik } from "formik";
import * as Yup from "yup";
import { getFieldvalues, handleImageSelectionFromFile } from "@/assets/js/utils/functions";
import LazyImage from "../../lazy-image";
import { Image as ImageType } from "../../../assets/interfaces";
import useImageUploads from "../../hooks/useImageUploads";

interface Props {
  submitBtnRef: React.MutableRefObject<HTMLButtonElement>;
  storeId?: string;
  setIsLoading?: (val: boolean) => void;
  onSuccess?: () => void;
  block: InfoBlockInterface;
  updateBlocks: (block: InfoBlockInterface, selected: number) => void;
  selected: number;
  setFormDirty: (val: boolean) => void;
}

interface FormProps {
  id: number;
  title: string;
  type: string;
  images: ImageType[];
  content: string;
}

const EditInfoBlocks: React.FC<Props> = ({
  submitBtnRef,
  storeId,
  setIsLoading,
  onSuccess,
  block,
  updateBlocks,
  selected,
  setFormDirty,
}) => {
  const {
    isLoading,
    makeRequest,
    error,
    response: successResponse,
  } = useRequest<UpdateStoreInfoBlocksParams>(UpdateStoreInfoBlocks);
  const [errorText, setErrorText] = useState<string>(null);
  const imagePicker = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setIsLoading && setIsLoading(isLoading);
  }, [isLoading]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    form.submitForm();
  };

  const form = useFormik<InfoBlockInterface>({
    initialValues: {
      title: block.title || "",
      text_content: block.text_content || "",
      content_type: block.content_type || "TEXT",
      image_content: block.image_content || [],
      is_visible: true,
    },
    validationSchema: Yup.object().shape({
      title: Yup.string().required("Title is required"),
      content_type: Yup.string().oneOf(["TEXT", "IMAGES"], "Invalid content type").required("Content type is required"),
      text_content: Yup.string().when("content_type", {
        is: "TEXT",
        then: Yup.string().required("Text content is required"),
        otherwise: Yup.string().notRequired(),
      }),
    }),
    onSubmit: async (values) => {
      if (values.content_type === "IMAGES" && values.image_content.length < 1) {
        setErrorText("At least one image URL is required");
        return;
      }
      const [response, error] = await makeRequest({
        id: block.id,
        block: values,
      });
      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors(error.fields);
        }
      } else {
        if (values.content_type === "IMAGES") {
          form.setFieldValue("text_content", "");
        }
        if (values.content_type === "TEXT") {
          form.setFieldValue("image_content", []);
        }
        updateBlocks(values, selected);
        form.resetForm({ values: response?.data });
        onSuccess();
      }
    },
  });
  const [images, setImages] = useState<ImageType[]>(
    form.values.image_content.map((url) => ({
      url,
      file: null,
      isUploading: false,
      uploadProgress: 100,
      name: "",
      src: "",
      lastModified: 0,
    }))
  );

  useEffect(() => setFormDirty(form.dirty), [form.dirty]);

  useEffect(() => {
    form.setFieldValue(
      "image_content",
      images.map((img) => img.url)
    );
  }, [images]);
  useImageUploads(images, FILE_TYPES.ITEMS, setImages);

  const removeImage = (e: React.MouseEvent<HTMLButtonElement>, index: number) => {
    e.preventDefault();
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  return (
    <form onSubmit={handleSubmit} className="w-full pb-20">
      <ErrorLabel error={errorText || error?.message} />
      <SuccessLabel message={successResponse ? "Store categories updated successfully!" : ""} />

      <InputField label={`Block Title`} {...getFieldvalues("title", form)} />

      <SelectDropdown
        label="Content Type"
        options={[
          { value: "TEXT", text: "Text" },
          { value: "IMAGES", text: "Images" },
        ]}
        {...getFieldvalues("content_type", form)}
        onChange={(e) => {
          form.setFieldValue("content_type", e.target.value);
        }}
      />
      {form.values.content_type === "TEXT" && (
        <TextArea label={`Content`} rows={4} {...getFieldvalues("text_content", form)} />
      )}
      {form.values.content_type === "IMAGES" && (
        <>
          <div className="grid mt-3.75 items-start w-full grid-cols-[repeat(auto-fit,80px)] sm:grid-cols-[repeat(auto-fit,120px)] gap-2.5">
            <input
              type="file"
              ref={imagePicker}
              name="product-images"
              multiple
              accept="image/*,.heic"
              id="product-images"
              className="hidden"
              onChange={(e) =>
                handleImageSelectionFromFile({
                  e,
                  images: images,
                  saveImages: (images: ImageType[]) => setImages(images),
                })
              }
            />

            {images.map(({ name, src, url, isUploading, uploadProgress, error }, index) => (
              <div key={index}>
                <figure
                  className="w-20 h-20 md:h-[120px] md:w-[120px] border border-grey-fields-100 rounded-10 sm:h-[100px] relative group"
                  key={index}
                >
                  {src || url ? (
                    <>
                      <LazyImage
                        src={src || url}
                        alt={name}
                        className="w-full h-full object-cover rounded-10"
                        loaderClasses="rounded-10"
                      />
                    </>
                  ) : (
                    <div className="h-full w-full rounded-10 bg-grey-loader animate-pulse flex items-center justify-center">
                      <div className="spinner spinner--sm"></div>
                    </div>
                  )}
                  <button
                    className="h-6 w-6 bg-black bg-opacity-80 flex items-center justify-center absolute transform -top-2 -right-2 rounded-full transition-all opacity-0 group-hover:opacity-100 z-30"
                    onClick={(e) => removeImage(e, index)}
                    type="button"
                  >
                    {/* prettier-ignore */}
                    <svg width="12" height="12" viewBox="0 0 15 15" fill="none">
                      <path d="M11.25 3.75L3.75 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M3.75 3.75L11.25 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                </figure>
                {(isUploading || uploadProgress > 0) && (
                  <div
                    className={`mt-1.5 h-1 w-full rounded-10 overflow-hidden ${
                      error ? "bg-accent-red-500 bg-opacity-10" : "bg-grey-divider"
                    }`}
                  >
                    <div
                      className={`h-full transition-all duration-200 ease-out ${
                        error ? "bg-accent-red-500" : "bg-accent-green-500"
                      }`}
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                )}
              </div>
            ))}

            <button
              className="no-outline w-20 h-20 md:h-[120px] md:w-[120px] rounded-10 border border-dashed border-grey-subtext text-grey-subtext hover:border-primary-500 hover:text-primary-500 transition-all flex items-center justify-center"
              onClick={() => imagePicker.current.click()}
              type="button"
            >
              {/* prettier-ignore */}
              <svg width="20" viewBox="0 0 30 30" fill="none">
                <path d="M15 6.25V23.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6.24976 15H23.7498" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
          </div>
        </>
      )}

      <button type="submit" className="hidden" ref={submitBtnRef}></button>
    </form>
  );
};

export default EditInfoBlocks;
