import { InfoBlockInterface, ProductItemInterface } from "@/assets/interfaces";
import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import { AppBtn, RoundActionBtn } from "@/components/ui/buttons";
import ContentState from "@/components/ui/content-state";
import DataAccordion from "@/components/ui/data-accordion";
import { productPageIcons } from "@/components/ui/layouts/product";
import { Product } from "@/pages/products/create";
import { FormikProps } from "formik";
import React, { useMemo, useState } from "react";
import InfoBlocksExplainer from "../create-products/info-blocks-explainer";
import EditInfoBlock from "./modals/edit-info-block";
import Badge from "@/components/ui/badge";
import LazyImage from "@/components/lazy-image";
import DeleteInfoBlockConfirmation from "./modals/delete-info-block";
import SelectInfoBlock from "./modals/select-info-block";
import { ensureUniqueItems, enumToHumanFriendly } from "@/assets/js/utils/functions";
import { useRequest } from "@/api/utils";
import { EditItems } from "@/api";
import { EditItemParams } from "@/api/interfaces";

interface Props {
  infoBlocks: InfoBlockInterface[];
  blocksLoading: boolean;
  setInfoBlocks: (blocks: InfoBlockInterface[]) => void;
  blocksError: any;
  product: ProductItemInterface;
  setProduct: (product: ProductItemInterface) => void; 

}

const ManageBlocksInProductPage = ({ infoBlocks, blocksLoading, setInfoBlocks, blocksError, product, setProduct }: Props) => {
  const { modals, toggleModal } = useModals([
    "add_info_block",
    "delete_info_block",
    "edit_info_block",
    "info_block_explainer",
  ]);
  const { isLoading, makeRequest, error, response } = useRequest<EditItemParams>(EditItems);


  const [selectedBlock, setSelectedBlock] = useState<{
    edit: InfoBlockInterface | null;
    delete: InfoBlockInterface | null;
  }>({
    edit: null,
    delete: null,
  });

  const editBlock = (block: InfoBlockInterface) => {
    setSelectedBlock((prev) => ({
      ...prev,
      edit: block,
    }));

    toggleModal("edit_info_block");
  };


  const deleteBlock = (id: string) => {
    const block = infoBlocks.find(infoBlock => infoBlock.id === id)
    setSelectedBlock((prev) => ({
      ...prev,
      delete: block,
    }));

    toggleModal("delete_info_block");
  };

  const removeBlockFromProduct = async (infoBlock: InfoBlockInterface) => {
    const updatedInfoBlocks = product.info_blocks.filter((block) => block.id !== infoBlock.id);

  // Create a new product object with the updated info_blocks
    const updatedProd = {
    id: product.id,
    name: product.name,
    description: product.description,
    price: product.price,
    price_unit: product.price_unit,
    images: product.images,
    thumbnail: product.thumbnail,
    thumbnail_type: product.thumbnail_type,
    category: typeof product.category === "object" ? product.category.id : product.category,
    is_always_available: product.is_always_available,
    variants: product.variants,
    videos: product.videos,
    info_blocks: updatedInfoBlocks.map(block => block.id),
    // add any other fields you want to send
  };

      const [response, error] = await makeRequest({ id: product?.id, item: updatedProd as any });
      if (error) {
        return;
      }

      console.log(updatedInfoBlocks)

      const updatedProduct = { ...response.data, info_blocks: updatedInfoBlocks };
      setProduct(updatedProduct);
      toggleModal("delete_info_block");
  };

  const updateBlocks = (block: InfoBlockInterface, id: string) => {
    const blocks = [...infoBlocks];
    const blockIndex = blocks.findIndex((block) => block.id === id);
    if (blockIndex !== -1) {
      blocks[blockIndex] = block;
      setInfoBlocks(blocks)
      setProduct({
        ...product,
        info_blocks: ensureUniqueItems(blocks),
      });
    }
  };

  const removeBlock = (id: string) => {
    setInfoBlocks(infoBlocks.filter((block) => block.id !== id));

    const updatedInfoBlocks = product.info_blocks.filter((block) => block.id !== id);
    setProduct({
        ...product,
        info_blocks: ensureUniqueItems(updatedInfoBlocks),
      });
    setSelectedBlock((prev) => ({
      ...prev,
      delete: null,
    }));
    toggleModal("delete_info_block");
  };

  return (
    <>
      <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
        {product && product.info_blocks && product.info_blocks.length > 0 && <DataAccordion
          isClosed={false}
          deactivated
          title={
            <span className="text-base font-bold font-body tracking-normal">
              <span className="font-display">Product Info Blocks</span>
            </span>
          }
          className="!pb-0"
        >
          <div className="flex flex-col gap-3 my-3.75">
            {product.info_blocks?.map((block, _i) => (
              <InfoBlockItem
                key={_i}
                data={block}
                deleteAction={() => deleteBlock(block.id)}
                editAction={() => editBlock(block)}
              />
            ))}
          </div>
        </DataAccordion>}
      </div>

      <Portal>
        <InfoBlocksExplainer
          title=""
          show={modals.info_block_explainer.show}
          toggle={() => toggleModal("info_block_explainer")}
        />
        <SelectInfoBlock
          show={modals.add_info_block.show}
          allBlocks={infoBlocks}
          productBlocks={product.info_blocks}
          toggle={() => toggleModal("add_info_block")}
          setProductBlocks={(blocks: InfoBlockInterface[]) =>
            setProduct({
            ...product,
            info_blocks: ensureUniqueItems(blocks),
          })
          }
          setAllBlocks={(blocks: InfoBlockInterface[]) => setInfoBlocks(blocks)}
        />
        {selectedBlock.edit !== null && (
          <EditInfoBlock
            onSuccess={() => toggleModal("edit_info_block")}
            show={modals?.edit_info_block.show}
            productName={product.name}
            allowCreate={false}
            addBlock={(block: InfoBlockInterface) => {
              // Update the infoBlocks state
              setInfoBlocks([...infoBlocks, block]);

              const updatedBlocks = ensureUniqueItems([...product.info_blocks, block]);
              setProduct({
              ...product,
              info_blocks: ensureUniqueItems(updatedBlocks.map(b => b.id)),
            });
            }}
            block={selectedBlock.edit}
            toggle={() => toggleModal("edit_info_block")}
            updateBlocks={(block: InfoBlockInterface) => updateBlocks(block, selectedBlock?.edit?.id)}
          />
        )}
        {selectedBlock.delete !== null && (
          <DeleteInfoBlockConfirmation
            show={modals.delete_info_block.show}
            toggle={() => toggleModal("delete_info_block")}
            deleteBlock={removeBlockFromProduct}
            loadingState={isLoading}
            selected={selectedBlock.delete}
          />
        )}
      </Portal>
    </>
  );
};

interface BlockItemProps {
  data?: InfoBlockInterface;
  editAction: VoidFunction;
  deleteAction: VoidFunction;
}

const InfoBlockItem: React.FC<BlockItemProps> = ({ data, editAction, deleteAction }) => {
  return (
    <div className="relative h-full">
      <div className="flex flex-col gap-2 bg-grey-fields-100 items-center rounded-10 relative h-full">
        <div className="flex justify-between items-center w-full px-3 pt-2.5">
          <div className="flex gap-2 justify-between items-center flex-wrap">
            <h2 className="text-black font-semibold text-base">{data.title}</h2>
            {data.tag && data.tag && (
              <Badge
                greyBg={false}
                color="dark"
                size="sm"
                uppercase={false}
                className="text-1xs font-medium"
                text={data.tag}
              />
            )}
          </div>
          <div className="flex gap-2 items-center">
            <RoundActionBtn
              icon="edit"
              white
              grey={false}
              className="bg-white text-black-secondary"
              onClick={editAction}
            />
            <RoundActionBtn
              icon="delete"
              white
              grey={false}
              className="bg-white text-accent-red-500"
              onClick={deleteAction}
            />
          </div>
        </div>

        {data.content_type === "TEXT" && (
          <div className="flex-1  w-full h-full border rounded-10 border-gray-border border-opacity-50 bg-white p-3 my-0">
            <p className="text-black-muted text-1xs overflow-ellipsis">{data.text_content}</p>
          </div>
        )}

        {data.content_type === "IMAGES" && (
          <div className="flex-1 h-full border rounded-10 border-gray-border border-opacity-50 bg-white p-3 my-0 grid items-start w-full grid-cols-[repeat(auto-fit,120px)] sm:grid-cols-[repeat(auto-fit,120px)] gap-2.5">
            {data.image_content.map((url, index) => (
              <div key={index}>
                <figure
                  className="w-28 h-28 border border-grey-fields-100 rounded-10 sm:h-[100px] relative group"
                  key={index}
                >
                  <LazyImage
                    src={url}
                    alt={""}
                    className="w-full h-full object-cover rounded-10"
                    loaderClasses="rounded-10"
                  />
                </figure>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ManageBlocksInProductPage;
