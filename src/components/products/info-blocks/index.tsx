import { useEffect, useMemo, useRef, useState } from "react";
import { Category, InfoBlockInterface, StrippedItem } from "../../../assets/interfaces";
import { emit, useListenerState } from "../../hooks/useListener";
import { StoreProductCategories } from "../../store";
import { AppBtn, RoundActionBtn } from "../../ui/buttons";
import { productPageIcons } from "../../ui/layouts/product";
import Modal, { ModalBody, ModalFooter } from "../../ui/modal";
import Table, { TableBody, TableCell, TableHead, TableHeadItem, TableRow } from "../../ui/table";
import authContext from "../../../contexts/auth-context";
import ContentState from "../../ui/content-state";
import { useFetcher, useRequest } from "../../../api/utils";
import { AssignItemsToInfoBlock, DeleteStoreInfoBlock, GetStoreCategories, GetStoreInfoBlocks, UpdateStoreInfoBlocks } from "../../../api";
import RoundActionButton from "../../ui/buttons/round-action-btn";
import useCopyClipboard from "../../hooks/useCopyClipboard";
import { enumToHumanFriendly, subdomainStoreLink, toAppUrl, toCurrency } from "../../../assets/js/utils/functions";
import useSearchParams from "../../hooks/useSearchParams";
import Portal from "../../portal";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import ErrorBox from "../../ui/error";
import CreateInfoBlocks from "./create-info-blocks";
import { useFormik } from "formik";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import { ModalState } from "../../hooks/useModals";
import EditInfoBlocks from "./edit-info-blocks";
import LazyImage from "../../lazy-image";
import { AssignItemsParams, DeleteStoreInfoBlockParams, UpdateStoreInfoBlocksParams } from "@/api/interfaces";
import Badge from "../../ui/badge";
import Dropdown, { DropdownItem } from "@/components/ui/dropdown-new";
import ProductsSelectorModal from "@/components/products/modals/select-products-modal";
import AssignedItemsModal from "./modals/assigned-items";
import { toast } from "@/components/ui/toast";

interface Props {
  modals: ModalState;
  toggleModal: (modal: string) => void;
}

const InfoBlocksMain: React.FC<Props> = ({ modals, toggleModal }) => {
  const { store, subscription } = authContext.useContainer();
  const { search } = useSearchParams(["search"]);
  const { response, isLoading, error, makeRequest } = useFetcher(GetStoreInfoBlocks, { id: store?.id });
  const {
    isLoading: assignLoading,
    makeRequest: makeAssignRequest,
    error: updateError,
    response: successResponse,
  } = useRequest<AssignItemsParams>(AssignItemsToInfoBlock);

  const [infoBlocks, setInfoBlocks] = useState<InfoBlockInterface[]>([]);

  // Update state when the response changes
  useEffect(() => {
    if (response?.data?.info_blocks) {
      setInfoBlocks(response.data.info_blocks);
    }
  }, [response]);

  const clearSearch = () => {
    const params = new URLSearchParams(window.location.search);
    params.delete("search"); // Remove the "search" parameter
    window.history.replaceState({}, "", `${window.location.pathname}`);
  };

  const filteredBlocks = useMemo(() => {
    if (search && search !== null && search !== "") {
      const query = search.toLowerCase();
      return infoBlocks.filter(
        (block) =>
          block.title.toLowerCase().includes(query) || // Match title
          block.text_content?.toLowerCase().includes(query) // Match text content
      );
    }
    return infoBlocks; // Return all blocks if no search query
  }, [search, infoBlocks]);

  let canManageBlocks = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS,
  });

  const [selected, setSelected] = useState<{
    edit: number;
    delete: number;
    assignToItems: number,
    assignedItems: number,
  }>({
    edit: null,
    delete: null,
    assignToItems: null,
    assignedItems: null,
  });

  const [assignItemsSelection, setAssignItemsSelection] = useState<string[]>([]);

  const editBlock = (index: number) => {
    setSelected((prev) => ({
      ...prev,
      edit: index,
    }));

    toggleModal("edit");
  };

  const deleteBlock = (index: number) => {
    setSelected((prev) => ({
      ...prev,
      delete: index,
    }));

    toggleModal("delete");
  };

  const assignItems = (index: number) => {
    setSelected((prev) => ({
      ...prev,
      assignToItems: index,
    }));
    // Initialize selection from the block's items
    setAssignItemsSelection(
      Array.isArray(infoBlocks[index]?.items)
        ? infoBlocks[index].items.map((item) => (typeof item === "object" ? item : item))
        : []
    );
    toggleModal("assign_items");
  };
  const removeBlock = (index: number) => {
    setInfoBlocks((prevBlocks) => [
      ...prevBlocks.slice(0, index), // Keep blocks before the index
      ...prevBlocks.slice(index + 1), // Keep blocks after the index
    ]);
  };


    const viewAssigned = (index: number) => {

      setSelected((prev) => ({
      ...prev,
      assignedItems: index,
    }));

    toggleModal("assigned_items");
  };
  const createBlock = (block: InfoBlockInterface) => {
    setInfoBlocks((prevBlocks) => [...prevBlocks, block]);
  };

  const updateBlocks = (block: InfoBlockInterface, index: number) => {
    var blocks = [...infoBlocks];
    blocks[index] = block;
    setInfoBlocks(blocks);
  };
  const handleAssign =  async () => {
    // const updatedBlocks = [...infoBlocks];
    // updatedBlocks[selected.assignToItems] = {
    //   ...updatedBlocks[selected.assignToItems],
    //   items: assignItemsSelection,
    // };
    // setInfoBlocks(updatedBlocks);

    // Optionally, make your API call here as well
     const [res, err] = await makeAssignRequest({
      id: infoBlocks[selected.assignToItems]?.id,
      items: assignItemsSelection,
    });
    if (res) {
      makeRequest()
      setSelected((prev) => ({
        ...prev,
        assignToItems: null,
      }));
      toggleModal("assign_items");
      toast.success({
        title: "Products assigned",
        message: "Products have been successfully assigned to this info block."
      });
      return;
      }

      toast.error({
        title: "Failed to assign products",
        message: "An error occurred while assigning products to this info block.",
      });
  };

    const handleRemoveAssigned =  async (id: string, selected: number) => {
    const updatedBlocks = [...infoBlocks];
    const block = updatedBlocks[selected];
    const newItems = block.items.filter(i => id !== i)
    // Optionally, make your API call here as well
     const [res, err] = await makeAssignRequest({
      id: infoBlocks[selected]?.id,
      items: newItems,
    });
    if (res) {
      toast.success({
        title: "Product Removed",
        message: "Product has been successfully removed from this info block."
      });
      updatedBlocks[selected] = {
      ...updatedBlocks[selected],
      items: newItems,
    };
    setInfoBlocks(updatedBlocks);
      return;
      }

      toast.error({
        title: "Failed to remove products",
        message: "An error occurred while removing product from this info block.",
      });
  };

  if (!canManageBlocks) {
    return (
      <ErrorBox title="Upgrade required" message="Upgrade to the basic or business plus plan to manage info blocks">
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  return (
    <>
      <div className="pb-20">
        {isLoading || !infoBlocks || infoBlocks.length < 1 || !filteredBlocks || filteredBlocks.length < 1 ? (
          <>
            <ContentState
              isLoading={isLoading}
              loadingText="Loading info blocks..."
              errorTitle="Failed to load info blocks"
              error={error}
              errorMessage="We couldn't load your info blocks please reload page"
              errorAction={
                <AppBtn size="md" onClick={() => makeRequest()}>
                  Reload Info Blocks
                </AppBtn>
              }
              isEmpty={filteredBlocks.length < 1}
              emptyIcon={<div className="w-8.75 h-8.75 text-grey-muted">{productPageIcons.category}</div>}
              title="No info blocks"
              description={
                search && search !== null && search !== ""
                  ? "Your Search Results didn't yield any matches"
                  : "Create an info block"
              }
              loadingPlaceholder={LoadingPlaceholder()}
            >
              {!search || search == null || search === "" ? (
                <AppBtn onClick={() => toggleModal("create")} size="md" className="max-w-[240px] m-auto">
                  Create Info Block
                </AppBtn>
              ) : (
                <AppBtn onClick={() => clearSearch()} size="md" className="max-w-[240px] m-auto">
                  Clear Search Results
                </AppBtn>
              )}
            </ContentState>
          </>
        ) : (
          <div className="grid grid-cols-[repeat(auto-fit,minmax(350px,1fr))] gap-5">
            {filteredBlocks.map((block, i) => (
              <InfoBlock key={i} data={block} deleteAction={() => deleteBlock(i)} editAction={() => editBlock(i)} itemAction={() => assignItems(i)} viewAction={() => viewAssigned(i)} />
            ))}
          </div>
        )}
      </div>
      <Portal>
        <InfoBlocksModal
          show={modals?.create.show}
          toggle={() => toggleModal("create")}
          blocks={infoBlocks}
          createBlock={(block: InfoBlockInterface) => createBlock(block)}
        />
        {selected.edit !== null && (
          <EditInfoBlockModal
            show={modals?.edit.show}
            block={infoBlocks[selected.edit]}
            toggle={() => toggleModal("edit")}
            updateBlocks={(block: InfoBlockInterface, selected: number) => updateBlocks(block, selected)}
            selected={selected.edit}
          />
        )}
        {selected.delete !== null && (
          <DeleteInfoBlockModal
            show={modals?.delete.show}
            toggle={() => toggleModal("delete")}
            block={infoBlocks[selected.delete]}
            removeBlock={(block: InfoBlockInterface) => removeBlock(selected.delete)}
            blocks={infoBlocks}
          />
        )}
        {selected.assignToItems !== null && (
           <ProductsSelectorModal
            show={modals.assign_items.show}
            storeId={store?.id}
            isItemSelected={(id) => assignItemsSelection.includes(id)}
            selected={assignItemsSelection}
            selectItem={(products) => {
              setAssignItemsSelection(products.map((p) => p.id));
            }}
            toggle={() => toggleModal("assign_items")}
            selectItems={handleAssign}
            loadingState={assignLoading}
            formatAsCurrency={toCurrency}
          />
        )}

        {selected.assignedItems !== null && (
          <AssignedItemsModal 
          show={modals.assigned_items.show}
          toggle={() => toggleModal("assigned_items")}
          items={infoBlocks[selected.assignedItems].items}
          formatAsCurrency={toCurrency}
          removeItem={(id: string) => handleRemoveAssigned(id, selected.assignedItems)}
          loadingState={assignLoading}
          addNew={() => {
            var index = selected.assignedItems;
            setSelected((prev) => ({
              ...prev,
              assignToItems: index,
            }));
            setSelected((prev) => ({
              ...prev,
              assignToItems: index,
              assignedItems: null
            }));
            toggleModal("assigned_items");
            toggleModal("assign_items");
          }} />
        )}
      </Portal>
    </>
  );
};

interface BlockItemProps {
  data: InfoBlockInterface;
  editAction: VoidFunction;
  deleteAction: VoidFunction;
  itemAction: VoidFunction;
  viewAction: VoidFunction
}

const InfoBlock: React.FC<BlockItemProps> = ({ data, editAction, deleteAction, itemAction, viewAction }) => {
  const dropdownItems: DropdownItem[] = [
    {
      text: "View Assigned Products",
      onClick: viewAction,
      icon: <RoundActionBtn size="sm" grey icon="products" />,
      skip: !data.items || data?.items.length < 1
    },
    {
      text: "Assign to Products",
      onClick: itemAction,
      icon: <RoundActionBtn size="sm" grey icon="add_circle" />,
    },
    {
      text: "Edit Block",
      onClick: editAction,
      icon: <RoundActionBtn size="sm" grey icon="edit" />,
    },
    {
      text: "Delete Block",
      onClick: deleteAction,
      icon: <RoundActionBtn size="sm" grey icon="delete" className="text-accent-red-500" />,
      className: "text-accent-red-500",
    },
  ];

  return (
    <div className="relative h-full">
      <div className="flex flex-col gap-2 bg-grey-fields-100 items-center rounded-10 relative h-full">
        <div className="flex justify-between items-center w-full px-3 pt-2.5">
          <div className="flex space-x-2 justify-start items-center flex-wrap flex-1 mr-3.75">
            <h2
              className={`text-black font-semibold text-base overflow-hidden whitespace-nowrap overflow-ellipsis ${
                data?.tag ? "max-w-[50%]" : "max-w-[80%]"
              }`}
            >
              {data.title}
            </h2>
            {data.tag && data.tag && (
              <Badge
                greyBg={false}
                color="dark"
                size="sm"
                uppercase={false}
                className="text-1xs font-medium"
                text={enumToHumanFriendly(data.tag, "-")}
              />
            )}
          </div>
          <div className="flex gap-2 items-center">
            <div>
              <Dropdown items={dropdownItems}>
                <RoundActionBtn
                  icon="options"
                  white
                  grey={false}
                  className="bg-white text-black-secondary dropdown-toggle"
                />
              </Dropdown>
            </div>
            {/* <RoundActionBtn
              icon="edit"
              white
              grey={false}
              className="bg-white text-black-secondary"
              onClick={editAction}
            />
            <RoundActionBtn
              icon="delete"
              white
              grey={false}
              className="bg-white text-accent-red-500"
              onClick={deleteAction}
            /> */}
          </div>
        </div>

        {data.content_type === "TEXT" && (
          <div className="flex-1  w-full h-full border rounded-10 border-gray-border border-opacity-50 bg-white p-3 my-0 ">
            <p className="text-black-muted text-1xs overflow-ellipsis whitespace-pre-line">{data.text_content}</p>
          </div>
        )}

        {data.content_type === "IMAGES" && (
          <div className="flex-1 h-full border rounded-10 border-gray-border border-opacity-50 bg-white p-3 my-0 items-start w-full gap-3 flex flex-wrap">
            {data.image_content.map((image, index) => (
              <div key={index} className="">
                <figure
                  className="w-24 h-24 sm:h-30 sm:w-30 border border-grey-fields-100 rounded-10 relative group"
                  key={index}
                >
                  <LazyImage
                    src={image}
                    alt={""}
                    className="w-full h-full object-cover rounded-10"
                    loaderClasses="rounded-10"
                  />
                </figure>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

interface ModalProps {
  toggle: (state: boolean) => void;
  show: boolean;
  blocks: InfoBlockInterface[];
  createBlock: (block: InfoBlockInterface) => void;
}

const InfoBlocksModal: React.FC<ModalProps> = ({ toggle, show, blocks, createBlock }) => {
  const { store, updateStore } = authContext.useContainer();
  const submitBtnRef = useRef<HTMLButtonElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  const onSuccess = () => {
    toggle(false);
  };
  const createAction = () => {
    submitBtnRef.current?.click();
  };

  return (
    <Modal title="Create New Product Info Block" toggle={toggle} show={show} size="midi">
      <ModalBody>
        <CreateInfoBlocks {...{ submitBtnRef, setIsLoading, onSuccess, blocks, createBlock }} />
      </ModalBody>
      <ModalFooter>
        <AppBtn id="create-info-block" isBlock disabled={isLoading} onClick={() => createAction()} size="lg">
          {isLoading ? "Creating..." : "Create Product Info Block"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

interface Edit {
  show: boolean;
  toggle: (status: boolean) => void;
  block: InfoBlockInterface;
  updateBlocks: (block: InfoBlockInterface, selected: number) => void;
  selected: number;
}

export const EditInfoBlockModal: React.FC<Edit> = ({ toggle, show, block, updateBlocks, selected }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formDirty, setFormDirty] = useState(false);
  const submitBtnRef = useRef<HTMLButtonElement>(null);

  const onSuccess = () => {
    toggle(false);
  };
  const updateAction = () => {
    submitBtnRef.current?.click();
  };

  return (
    <Modal title={`Edit ${block?.title}`} toggle={toggle} show={show} size="midi">
      <ModalBody>
        <EditInfoBlocks {...{ submitBtnRef, block, setIsLoading, updateBlocks, selected, onSuccess, setFormDirty }} />
      </ModalBody>
      <ModalFooter>
        <AppBtn
          id="update-info-block"
          isBlock
          disabled={!formDirty || isLoading}
          onClick={() => updateAction()}
          size="lg"
        >
          {isLoading ? "Updating..." : "Edit Product Info Block"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

interface DelProps {
  show: boolean;
  toggle: (status: boolean) => void;
  block: InfoBlockInterface;
  blocks: InfoBlockInterface[];
  removeBlock: (block: InfoBlockInterface) => void;
}

export const DeleteInfoBlockModal: React.FC<DelProps> = ({ show, toggle, block, removeBlock, blocks }) => {
  const { makeRequest, isLoading, error, response } = useRequest<DeleteStoreInfoBlockParams>(DeleteStoreInfoBlock);

  const handleItemDelete = async () => {
    const [res, err] = await makeRequest({ id: block.id });
    if (!err) {
      toggle(false);
      removeBlock(block);
    }
  };

  return (
    <Modal {...{ show, toggle }} title="Delete Block" size="sm">
      <ModalBody>
        <ErrorLabel error={error?.message} />
        <SuccessLabel message={response?.message || ""} />
        <div className="text-center py-3.5">
          <figure className="h-12.5 w-12.5 sm:h-15 sm:w-15 bg-red-700 m-auto rounded-full flex items-center justify-center">
            {/* prettier-ignore */}
            <svg width="45%" viewBox="0 0 50 50" fill="none">
              <path d="M43.8957 10.8959C40.5415 10.5625 37.1874 10.3125 33.8124 10.125V10.1042L33.354 7.39585C33.0415 5.47919 32.5832 2.60419 27.7082 2.60419H22.2499C17.3957 2.60419 16.9374 5.35419 16.604 7.37502L16.1665 10.0417C14.229 10.1667 12.2915 10.2917 10.354 10.4792L6.10405 10.8959C5.22905 10.9792 4.60405 11.75 4.68738 12.6042C4.77071 13.4584 5.52071 14.0834 6.39571 14L10.6457 13.5834C21.5624 12.5 32.5624 12.9167 43.6041 14.0209C43.6666 14.0209 43.7082 14.0209 43.7707 14.0209C44.5624 14.0209 45.2499 13.4167 45.3332 12.6042C45.3957 11.75 44.7707 10.9792 43.8957 10.8959Z" fill="white" />
              <path d="M40.0624 16.9583C39.5624 16.4375 38.8749 16.1458 38.1665 16.1458H11.8332C11.1249 16.1458 10.4166 16.4375 9.93739 16.9583C9.45822 17.4791 9.18738 18.1875 9.22905 18.9166L10.5207 40.2916C10.7499 43.4583 11.0416 47.4166 18.3124 47.4166H31.6874C38.9582 47.4166 39.2499 43.4791 39.4791 40.2916L40.7707 18.9375C40.8124 18.1875 40.5415 17.4791 40.0624 16.9583ZM28.4582 36.9791H21.5207C20.6666 36.9791 19.9582 36.2708 19.9582 35.4166C19.9582 34.5625 20.6666 33.8541 21.5207 33.8541H28.4582C29.3124 33.8541 30.0207 34.5625 30.0207 35.4166C30.0207 36.2708 29.3124 36.9791 28.4582 36.9791ZM30.2082 28.6458H19.7916C18.9374 28.6458 18.2291 27.9375 18.2291 27.0833C18.2291 26.2291 18.9374 25.5208 19.7916 25.5208H30.2082C31.0624 25.5208 31.7707 26.2291 31.7707 27.0833C31.7707 27.9375 31.0624 28.6458 30.2082 28.6458Z" fill="white" />
            </svg>
          </figure>
          <h4 className="text-black text-base font-semibold ">Do you want to delete this block?</h4>
          <p className="text-sm text-grey-subtext mt-1  mx-auto  max-w-[195px] sm:max-w-[270px]">
            This block would be deleted and removed from all related items.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="danger" onClick={handleItemDelete} disabled={isLoading} size="lg">
          Delete Block
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};
export default InfoBlocksMain;


const LoadingPlaceholder = () => {
  return (
    <div className="grid grid-cols-[repeat(auto-fill,minmax(275px,1fr))] gap-5">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="w-full rounded-15 border bg-grey-loader border-grey-border border-opacity-50">
           <div className="border-t px-1.5 border-grey-divider  pt-1.5 flex items-center justify-between">
            <div className="h-4 bg-white animate-pulse rounded-15 w-25"></div>
            <div className="h-9 bg-white animate-pulse rounded-full w-9"></div>
          </div>
          <div className="bg-white rounded-t-15 rounded-b-15 mt-3 h-36 p-1.5">
            <div className="flex flex-col mt-3.75 gap-2.5">
              <div className="h-5 bg-grey-loader animate-pulse rounded-15 w-full"></div>
              <div className="h-5 bg-grey-loader animate-pulse rounded-15 w-full"></div>
              <div className="h-5 bg-grey-loader animate-pulse rounded-15 w-full"></div>
          </div>
          </div>
        </div>
      ))}
    </div>
  );
};
