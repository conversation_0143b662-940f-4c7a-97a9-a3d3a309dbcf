import { useEffect, useState } from "react";
import { ProductItemInterface } from "../../../assets/interfaces";
import { getItemThumbnail } from "../../../assets/js/utils/utils";
import authContext from "../../../contexts/auth-context";
import LazyImage from "../../lazy-image";
import { AppBtn } from "../../ui/buttons";
import Checkbox from "../../ui/form-elements/checkbox";
import Modal, { ModalBody, ModalFooter } from "../../ui/modal";
import MultiSelectDropdown from "../../ui/multi-select-dropdown";
import SearchBar from "../../ui/search-bar";
import { getProductItemThumbnail } from "../../../assets/js/utils/utils";
interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  onSave: (selected: string[]) => void;
  onSearch: (query: string) => void;
  items: ProductItemInterface[];
  value: string[];
  loadingItems?: boolean;
}

const SelectSpecificProductsModal: React.FC<Props> = ({ show, toggle, items, onSave, value, loadingItems }) => {
  const [filteredItems, setFilteredItems] = useState([...items]);
  const [selected, setSelected] = useState(value);
  const [searchQuery, setSearchQuery] = useState("");
  const { categories } = authContext.useContainer();
  const selectedAll = selected.length == items.length;

  useEffect(() => {
    if (JSON.stringify(selected) !== JSON.stringify(value)) {
      setSelected(value);
    }
  }, [value]);

  useEffect(() => {
    setFilteredItems(items);
  }, [items]);

  useEffect(() => {
    if (searchQuery === "") {
      setFilteredItems(items);
      return;
    }

    searchQuery.trim();
    const splitQueries = searchQuery.split(" ");

    const filteredItems = items.filter((item) => {
      let match = false;
      splitQueries.map((query) => {
        if (item.name.toLocaleLowerCase().includes(query)) match = true;
      });

      return match;
    });

    setFilteredItems(filteredItems);
  }, [searchQuery]);

  const selectAll = () => {
    let newSelected = [];

    if (!selectedAll) {
      newSelected = items.map((i) => i.id);
    }

    setSelected(newSelected);
  };

  const getDropdownCategoryItems = () => {
    return categories.map((category) => {
      return {
        label: `${category.emoji}  ${category.name}`,
        value: category.id,
      };
    });
  };

  const selectByCategories = (categories: string[]) => {
    const selectedCopy = [...selected];
    const itemsInCategory = items.filter((i) => i?.category && categories.includes(i.category.id));

    itemsInCategory.forEach((i) => {
      if (!selected.includes(i.id)) {
        selectedCopy.push(i.id);
      }
    });

    setSelected(selectedCopy);
  };

  const toggleItem = (item: ProductItemInterface) => {
    const selectedIndex = selected.indexOf(item.id);
    const selectedCopy = [...selected];

    if (selectedIndex > -1) {
      selectedCopy.splice(selectedIndex, 1);
    } else {
      selectedCopy.push(item.id);
    }

    setSelected(selectedCopy);
  };

  const isItemSelected = (id: string) => selected.includes(id);

  return (
    <Modal {...{ show, toggle }} title="Select Products" bgClose={false} size="md">
      <ModalBody className="relative !pt-0 p-5 sm:p-7.5" noPadding>
        {!loadingItems && (
          <>
            <div className="sticky top-0 z-50 bg-white pt-4 pb-1">
              <SearchBar {...{ searchQuery, setSearchQuery }} />
              <div className="flex items-center justify-between mt-2.5">
                <div className="flex items-center w-[fit-content cursor-pointer]" onClick={selectAll}>
                  <Checkbox
                    onChange={selectAll}
                    checked={selectedAll}
                    name="select_all"
                    className="mr-1"
                    id="select_all"
                    neutral
                    round
                  ></Checkbox>
                  <span className="text-sm font-medium text-dark">Select All</span>
                </div>
                <MultiSelectDropdown
                  label="Select by Category"
                  items={getDropdownCategoryItems()}
                  onSave={selectByCategories}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-5 mt-2">
              {filteredItems.map((item, index) => (
                <div
                  key={index}
                  className="w-fit flex items-center py-4 pr-4 border-b-[1px] border-gray-100 last:border-0 cursor-pointer"
                  onClick={() => toggleItem(item)}
                  role="button"
                >
                  <figure className=" flex-shrink-0 h-[34px] w-[34px] rounded-md overflow-hidden mr-3 relative">
                    <LazyImage
                      src={getItemThumbnail(item)}
                      className="h-full w-full object-cover rounded-md relative z-10"
                      alt={item.name}
                    />
                  </figure>
                  <span className="w-[60%] text-sm sm:text-1sm mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis text-black-secondary">
                    {item.name}
                  </span>
                  <Checkbox
                    onChange={() => toggleItem(item)}
                    checked={isItemSelected(item.id)}
                    id={item.id}
                    name={item.name}
                    neutral
                    className="ml-auto"
                    round
                    small
                  ></Checkbox>
                </div>
              ))}
            </div>
          </>
        )}
        {loadingItems && (
          <div className="pt-20 pb-12 flex flex-col items-center justify-center">
            <div className="spinner spinner--md text-primary-500"></div>
            <span className="text-sm text-placeholder inline-block mt-0.5">Loading Items...</span>
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <div className="w-full">
          <AppBtn onClick={() => onSave(selected)} isBlock size="lg" disabled={loadingItems || selected.length < 1}>
            Save Selections
          </AppBtn>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default SelectSpecificProductsModal;
