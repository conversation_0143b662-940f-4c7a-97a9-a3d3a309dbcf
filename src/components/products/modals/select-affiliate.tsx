import React, { useEffect, useState } from "react";
import { useModals } from "../../hooks/useModals";
import Mo<PERSON>, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "../../ui/modal";
import { AppBtn } from "../../ui/buttons";
import { useFetcher } from "../../../api/utils";
import { GetAffiliates } from "../../../api/affiliates";
import { GetAffiliatesParams } from "../../../api/interfaces/affiliates.interface";
import { AffiliateInterface } from "../../../assets/interfaces/affiliates.interface";
import ContentState from "../../ui/content-state";
import { toAppUrl } from "../../../assets/js/utils/functions";
import useCopyClipboard from "../../hooks/useCopyClipboard";
import { toast } from "../../ui/toast";
import StoreLogo from "@/components/ui/store-logo";

interface Props {
  show: boolean;
  toggle: (status: boolean) => void;
  productSlug: string;
}

const SelectAffiliate: React.FC<Props> = ({ show, toggle, productSlug }) => {
  const [selectedAffiliate, setSelectedAffiliate] = useState<AffiliateInterface | null>(null);
  const [affiliateLink, setAffiliateLink] = useState<string>("");
  const [copied, copy] = useCopyClipboard(affiliateLink, { successDuration: 2000 });

  const count = Number.MAX_SAFE_INTEGER;

  const { response, error, isLoading, makeRequest } = useFetcher<GetAffiliatesParams>(
    GetAffiliates,
    {
      page: 1,
      per_page: count,
    },
    ["page", "per_page"]
  );

  const affiliates: AffiliateInterface[] = response?.data?.data || [];

  useEffect(() => {
    if (selectedAffiliate) {
      const link = toAppUrl(`p/${productSlug}?ref=${selectedAffiliate.slug}`);
      setAffiliateLink(link);
    }
  }, [selectedAffiliate, productSlug]);

  const handleAffiliateSelect = (affiliate: AffiliateInterface) => {
    setSelectedAffiliate(affiliate);
  };

  const handleCopyLink = () => {
    if (affiliateLink) {
      copy();

      toggle(false);
    }
  };

  const isSelected = (affiliate: AffiliateInterface) => selectedAffiliate?.id === affiliate.id;

  return (
    <Modal size="sm" {...{ show, toggle }} title="Select an affiliate">
      <ModalBody>
        {isLoading || error || !affiliates.length ? (
          <ContentState
            loadingText="Loading affiliates..."
            isLoading={isLoading}
            isEmpty={!affiliates.length}
            emptyIcon={
              // prettier-ignore
              <svg width="45%" viewBox="0 0 24 24" fill="none">
                <path opacity="0.4" d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.96 11.48 12.04 11.48 12.1 11.49C12.12 11.49 12.13 11.49 12.15 11.49C12.16 11.49 12.16 11.49 12.17 11.49C14.73 11.4 16.74 9.32 16.75 6.75C16.75 4.13 14.62 2 12 2Z" fill="#AAAAAA"/>
                <path d="M17.08 14.1499C14.29 12.2899 9.74002 12.2899 6.93002 14.1499C5.66002 14.9999 4.96002 16.1499 4.96002 17.3799C4.96002 18.6099 5.66002 19.7499 6.92002 20.5899C8.32002 21.5299 10.16 21.9999 12 21.9999C13.84 21.9999 15.68 21.5299 17.08 20.5899C18.34 19.7399 19.04 18.5999 19.04 17.3599C19.03 16.1299 18.34 14.9899 17.08 14.1499Z" fill="#AAAAAA"/>
              </svg>
            }
            title="No affiliates to show"
            description="You need to create affiliates first"
            errorMessage="We couldn't load the affiliates, click on the button to retry"
            errorTitle="Fetching affiliates failed"
            error={error}
            errorAction={
              <AppBtn size="sm" onClick={() => makeRequest()}>
                Reload Affiliates
              </AppBtn>
            }
          />
        ) : (
          <ul className="flex flex-col divide-y divide-grey-divider">
            {affiliates.map((affiliate, index) => (
              <li
                className="py-3 last:pb-0 first:pt-0 flex items-start justify-between text-dark text-sm cursor-pointer hover:bg-grey-light hover:bg-opacity-60 text-left"
                onClick={() => handleAffiliateSelect(affiliate)}
                key={index}
              >
                <div className="flex items-center">
                  <StoreLogo storeName={affiliate.name} className="h-10 w-10 text-xl " logo={null} />
                  <div className="ml-2.5">
                    <span className="block max-w-full overflow-ellipsis whitespace-nowrap overflow-hidden">
                      {affiliate.name}
                    </span>
                    <span className="text-xs text-black-secondary font-medium">{affiliate.email}</span>
                  </div>
                </div>
                {isSelected(affiliate) && (
                  <>
                    {/* prettier-ignore */}
                    <svg width="18px" viewBox="0 0 18 18" fill="none" 
                      className="transition-all ease-out duration-200 transform text-accent-green-500 scale-1 opacity-1">
                      <rect width="18" height="18" rx="9" fill="currentColor"/>
                      <path d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z" fill="white"/>
                    </svg>
                  </>
                )}
              </li>
            ))}
          </ul>
        )}
      </ModalBody>
      <ModalFooter>
        <AppBtn disabled={!selectedAffiliate} onClick={handleCopyLink} isBlock size="lg">
          {copied ? "Copied!" : "Copy Affiliate Link"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default SelectAffiliate;
