import { ProgressReturnProps } from "@/components/hooks/useProgess";
import ProgressModal from "@/components/ui/progress-modal";
import Modal, { ModalBody } from "../../ui/modal";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  loadProgress: ProgressReturnProps;
  onComplete?: () => void;
}

export interface VideoDimensions {
  width: number;
  height: number;
}

const VideoResourcesLoader: React.FC<Props> = ({ show, toggle, loadProgress, onComplete }) => {
  return (
    <>
      <Modal {...{ show, toggle }} title="Loading Resources..." size="md">
        <ModalBody className="flex flex-col items-center">
          <>
            <div className="px-10">
              <ProgressModal
                steps={loadProgress.progressSteps}
                currentStep={loadProgress.currentProgressStep}
                isEmbedded
              />
            </div>
          </>
        </ModalBody>
      </Modal>
    </>
  );
};

export default VideoResourcesLoader;
