import { VideoProgress, VideoProgresses } from "@/components/hooks/useVideoTranscode";
import { VideoSquare } from "iconsax-react";
import Modal, { ModalBody } from "../../ui/modal";
import VideoUploadCard from "../create-products/video-upload-card";
import { Video } from "@/assets/interfaces";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  progresses: VideoProgresses;
  videoGroups: { taskId: string; videos: Video[] }[];
  retryTranscode: (task: string, videoKey: string) => void;
}

const VideoUploadProgress: React.FC<Props> = ({ show, toggle, retryTranscode, progresses, videoGroups }) => {
  const taskGroups = Object.keys(progresses ?? {});


  return (
    <Modal {...{ show, toggle }} title="Processing videos..." size="midi">
      <ModalBody>
        <div className="flex items-center flex-col w-full">
          <div className="flex items-center flex-col">
            <figure className="h-[70px] w-[70px] bg-accent-green-500 flex flex-col items-center justify-center rounded-full">
              <VideoSquare variant="Bold" className="text-white" size={30} />
            </figure>
            <h3 className="text-center text-xl sm:text-2lg leading-none font-light mt-3.75">
              Some videos <br /> <span className="font-bold">are still processing</span>
            </h3>
            <p className="sm:text-sm text-1xs text-center text-grey-subtext mt-0.5">
              Please wait for videos to process completely
            </p>
          </div>

          <div className="space-y-2.5 mt-5 w-full">
            {taskGroups.map((key) => {
              const taskGroup = progresses[key];
              return Object.values(taskGroup).map((p) => {
                const videoGroup = videoGroups.find((group) => group.taskId === key);
                const video = videoGroup?.videos[p.meta?.videoIndex ?? 0];
                return (
                  <VideoUploadCard
                    minimal
                    retryTask={() => retryTranscode(key, video.meta.id)}
                    videoProgress={p}
                    video={video}
                    key={key}
                  />
                );
              });
            })}
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default VideoUploadProgress;
