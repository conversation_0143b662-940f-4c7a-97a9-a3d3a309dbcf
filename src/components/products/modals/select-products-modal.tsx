import { GetStoreItems } from "@/api";
import { GetStoreItemsParams } from "@/api/interfaces";
import { useFetcher } from "@/api/utils";
import { CartInterface, CartItem, CURRENCIES, ProductItemInterface } from "@/assets/interfaces";
import { checkItemAvailability, getActualPrice, getItemThumbnail } from "@/assets/js/utils/utils";
import { useModals } from "@/components/hooks/useModals";
import LazyImage from "@/components/lazy-image";
import Portal from "@/components/portal";
import { AppBtn } from "@/components/ui/buttons";
import Checkbox from "@/components/ui/form-elements/checkbox";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import SearchBar from "@/components/ui/search-bar-new";
import { FormatCurrencyFun } from "@/contexts/cart-context";

import React, { useEffect, useState } from "react";
interface Props {
  show: boolean;
  toggle: (show: boolean) => void;
  selectItem: (products: ProductItemInterface[]) => void;
  selected: string[];
  storeId: string;
  formatAsCurrency: FormatCurrencyFun;
  isItemSelected: (id: string) => boolean;
  loadingState?: boolean;
  selectItems?: VoidFunction;
}

const ProductsSelectorModal: React.FC<Props> = (props) => {
  const { show, toggle, selectItem, formatAsCurrency, storeId, selected, isItemSelected, loadingState = false, selectItems} = props;
  const [searchQuery, setSearchQuery] = useState("");

  const { response, error, isLoading, makeRequest } = useFetcher<GetStoreItemsParams>(GetStoreItems, {
    per_page: Number.MAX_SAFE_INTEGER,
    page: 1,
    filter: { store: storeId },
  });

  const storeProducts: ProductItemInterface[] = response?.data?.items
    ? [...response?.data?.items?.featured_items, ...response?.data?.items?.other_items]
    : [];

  const items = storeProducts
    .map((i) => ({
      ...i,
      available_to_purchase: checkItemAvailability(i),
      variants:
        i?.variants?.options?.length > 0
          ? {
              ...i?.variants,
              options: i?.variants?.options.map((o) => {
                return {
                  ...o,
                  available_to_purchase: checkItemAvailability(i, o),
                };
              }),
            }
          : undefined,
    }))
    .filter((i) => {
      if (i.variants?.options?.length > 0) {
        return i.available_to_purchase && i.variants?.options.some((v) => v.available_to_purchase);
      }

      return i.available_to_purchase;
    });

  const filteredItems = (() => {
    if (searchQuery === "") {
      return items;
    }
    searchQuery.trim();
    const splitQueries = searchQuery.split(" ");

    return items.filter((item) => {
      let match = false;
      splitQueries.map((query) => {
        if (item.name.toLocaleLowerCase().includes(query)) match = true;
      });

      return match;
    });
  })();

  const setSelected = (id: string) => {
    const isAlreadySelected = selected.includes(id);

    const updatedSelected = isAlreadySelected
      ? selected.filter((productId) => productId !== id) // Remove the product if already selected
      : [...selected, id]; // Add the product if not already selected

    const selectedProducts = filteredItems.filter((product) => updatedSelected.includes(product.id));
    selectItem(selectedProducts); // Update the selected products
  };
  useEffect(() => {
    if (!show) {
      setSearchQuery("");
    //   setSelected([]); // Reset selected items when modal is closed
    }
  }, [show]);


  const handleSelect = () => {
     if (selectItems) {
      selectItems();
    } else {
      toggle(false);
    }
  }

//   const toggleSelection = (id: string) => {
//     setSelected((prevSelected) =>
//       prevSelected.includes(id) ? prevSelected.filter((itemId) => itemId !== id) : [...prevSelected, id]
//     );
//   };

  return (
    <Portal>
      <Modal show={show} toggle={toggle} title="Select Products" size="md">
        <ModalBody className="relative !pt-0 p-5 sm:p-7.5" noPadding>
          <div className="sticky top-0 z-50 bg-white pt-4 pb-1">
            <SearchBar {...{ searchQuery, setSearchQuery }} />
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 gap-x-5">
            {filteredItems && !isLoading && filteredItems.map((item, index) => (
              <div
              key={index}
              className="w-full flex items-start py-4 last:border-0 cursor-pointer"
              onClick={() => setSelected(item.id)}
              role="button"
            >
              <div className="flex flex-col items-center w-full overflow-hidden">
                <figure className="flex-shrink-0 h-[150px] md:h-[130px] w-full rounded-10 overflow-hidden relative border border-grey-fields-200">
                  <LazyImage
                    src={getItemThumbnail(item)}
                    className="h-full w-full object-cover rounded-10 relative z-10"
                    alt={item.name}
                  />
                  <Checkbox
                    onChange={() => setSelected(item.id)}
                    checked={isItemSelected(item.id)}
                    id={item.id}
                    name={item.name}
                    neutral
                    className="ml-auto mt-1 absolute top-1.5 right-2 border-grey-border border-[2px] bg-white rounded-full z-[10]"
                    round
                    small
                    ></Checkbox>
                </figure>
                <div className="w-full">
                  <span className="text-sm sm:text-1sm mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis text-dark font-bold font-display block -mb-0.5">
                    {item.name}
                  </span>
                  <span className="text-xxs md:text-xs font-bold text-black">{formatAsCurrency(getActualPrice(item))}</span>
                </div>
              </div>
            </div>
            ))}
             {isLoading && (
                <div className="flex mx-auto col-span-3 flex-col items-center py-5">
                <div className="spinner spinner--md text-primary-500"></div>
                <span className="text-sm text-black-placeholder mt-2 inline-block font-medium">Loading Products</span>
                </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="w-full">
            <AppBtn isBlock size="lg" disabled={loadingState} onClick={handleSelect}>
              {loadingState? "loading..." : "Select Items"}
            </AppBtn>
          </div>
        </ModalFooter>
      </Modal>
    </Portal>
  );
};

export default ProductsSelectorModal;
