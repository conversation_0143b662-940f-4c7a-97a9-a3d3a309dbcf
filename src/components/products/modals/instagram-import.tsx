import { CheckInstagramToken, GenerateInstagramAccessToken, GetInstagramMedia, GetInstagramUser } from "@/api";
import { GetInstagramAccessTokenParams, GetInstagramMediaParams } from "@/api/interfaces";
import { useFetcher, useRequest } from "@/api/utils";
import { paramsFromObject } from "@/assets/js/utils/functions";
import useInfiniteScroll from "@/components/hooks/useInfiniteScroll";
import usePagination from "@/components/hooks/usePagination";
import useSteps from "@/components/hooks/useSteps";
import LazyImage from "@/components/lazy-image";
import Portal from "@/components/portal";
import { AppBtn } from "@/components/ui/buttons";
import ErrorLabel from "@/components/ui/error-label";
import Modal, { ModalBody, ModalFooter, ModalProps } from "@/components/ui/modal";
import { toast } from "@/components/ui/toast";
import Toggle from "@/components/ui/toggle";
import authContext from "@/contexts/auth-context";
import { FormikProps, useFormik } from "formik";
import { MutableRefObject, useCallback, useEffect, useState } from "react";

export interface InstagramMedia {
  id: string;
  media_type: "IMAGE" | "VIDEO" | "CAROUSEL_ALBUM";
  media_url: string;
  permalink?: string;
  caption?: string;
  thumbnail_url?: string;
  post_id?: string;
}

export interface InstagramUser {
  account_type: string;
  id: string;
  media_count: number;
  username: string;
}

export interface ImportForm {
  selected_posts: { [key: string]: InstagramMedia };
  autogenerate_details?: boolean;
}
interface Props extends ModalProps {
  access_code: string;
  handleSelectComplete: (posts: InstagramMedia[]) => void;
  maxUploadable: number;
}

const titleMap = {
  CONNECT: "Connect to Instagram",
  SELECT: "Select items from instagram account",
};

const IG_AUTH_URL = "https://www.instagram.com/oauth/authorize";

const InstagramImportModal: React.FC<Props> = (props) => {
  const { toggle, show, access_code, handleSelectComplete, maxUploadable } = props;
  const { user, updateUser } = authContext.useContainer();
  const { isActive, step, next, previous, changeStep } = useSteps(["CONNECT", "SELECT"], 0);
  const [ref, setRef] = useState<MutableRefObject<HTMLDivElement>>();
  const [pagination, setPagination] = useState<any>({ limit: 5 });
  const [posts, setPosts] = useState<InstagramMedia[]>([]);
  const [instagramUser, setInstagramUser] = useState<InstagramUser>();

  const getPostsReq = useRequest<GetInstagramMediaParams>(GetInstagramMedia);
  const getUserReq = useRequest(GetInstagramUser);
  const checkTokenReq = useFetcher(CheckInstagramToken);

  const fetchPosts = async () => {
    const [res, error] = await getPostsReq.makeRequest({ pagination });
    if (res) {
      setPosts([...posts, ...(res?.data?.data ?? {})]);
    }
  };
  const fetchUser = async () => {
    const [res, error] = await getUserReq.makeRequest({});
    if (res) {
      setInstagramUser(res?.data);
    }
  };

  useEffect(() => {
    if (step === "SELECT" && show) {
      fetchPosts();
      // fetchUser();
    }
  }, [pagination, step, show]);

  useInfiniteScroll(ref, () => {
    const next = getPostsReq.response?.data?.paging?.cursors?.after;
    if (next) {
      setPagination({ ...pagination, next });
    }
  });

  useEffect(() => {
    if (checkTokenReq.response && !checkTokenReq.error) {
      changeStep("SELECT");
    }
  }, [user, checkTokenReq]);

  const form = useFormik<ImportForm>({
    initialValues: {
      selected_posts: {},
      autogenerate_details: false,
    },
    onSubmit: (values) => {
      handleSelectComplete(Object.values(values.selected_posts));
    },
  });

  const selectedArray = Object.keys(form.values.selected_posts);

  return (
    <Portal>
      <Modal {...{ toggle, show, title: titleMap[step] }}>
        <ModalBody setRef={(r) => setRef(r)} noPadding>
          {isActive("CONNECT") && (
            <ConnectToInstagram {...{ access_code, updateUser, user, onSuccess: () => checkTokenReq.makeRequest() }} />
          )}
          {isActive("SELECT") && (
            <SelectProducts {...{ posts, getPostsReq, instagramUser: null, form, maxUploadable }} />
          )}
        </ModalBody>
        {step !== "CONNECT" && (
          <ModalFooter>
            <AppBtn disabled={selectedArray?.length === 0} onClick={() => form.handleSubmit()} isBlock size="lg">
              Proceed
            </AppBtn>
          </ModalFooter>
        )}
      </Modal>
    </Portal>
  );
};

export default InstagramImportModal;

interface ConnectToInstagramProps {
  access_code: string;
  updateUser: (data: any) => void;
  user: any;
  onSuccess: VoidFunction;
}

const ConnectToInstagram: React.FC<ConnectToInstagramProps> = ({ access_code, user, updateUser, onSuccess }) => {
  const { makeRequest, isLoading, error, response } =
    useRequest<GetInstagramAccessTokenParams>(GenerateInstagramAccessToken);

  const getInstagramAuthCode = useCallback(() => {
    const appKey = process.env.NEXT_PUBLIC_INSTAGRAM_APP_KEY;
    const redirectUri = window.location.href.includes("localhost")
      ? `https://socialsizzle.herokuapp.com/auth/`
      : window.location.origin + window.location.pathname;

    const params = paramsFromObject({
      client_id: appKey,
      redirect_uri: redirectUri,
      scope: "instagram_business_basic",
      response_type: "code",
      enable_fb_login: "0",
      force_authentication: "0",
    });

    const url = `${IG_AUTH_URL}?${params}`;

    window.location.href = url;
  }, []);

  useEffect(() => {
    if (access_code) getToken();
  }, [access_code]);

  const getToken = async () => {
    const redirectUri = window.location.href.includes("localhost")
      ? `https://socialsizzle.herokuapp.com/auth/`
      : window.location.origin + window.location.pathname;

    const [res, error] = await makeRequest({
      access_code,
      redirect_uri: redirectUri,
    });

    if (res) {
      onSuccess();
    }
  };

  return (
    <div className="p-5 sm:p-6.25">
      <div className="flex flex-col items-center max-w-xs m-auto py-7.5">
        <ErrorLabel error={error?.message} />
        <figure className="h-16 w-16 sm:h-18 sm:w-18 rounded-full flex items-center justify-center bg-accent-red-500 text-white">
          {/* prettier-ignore */}
          <svg className="w-6 sm:w-8" viewBox="0 0 24 24" fill="none">
            <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM12 15.88C9.86 15.88 8.12 14.14 8.12 12C8.12 9.86 9.86 8.12 12 8.12C14.14 8.12 15.88 9.86 15.88 12C15.88 14.14 14.14 15.88 12 15.88ZM17.92 6.88C17.87 7 17.8 7.11 17.71 7.21C17.61 7.3 17.5 7.37 17.38 7.42C17.26 7.47 17.13 7.5 17 7.5C16.73 7.5 16.48 7.4 16.29 7.21C16.2 7.11 16.13 7 16.08 6.88C16.03 6.76 16 6.63 16 6.5C16 6.37 16.03 6.24 16.08 6.12C16.13 5.99 16.2 5.89 16.29 5.79C16.52 5.56 16.87 5.45 17.19 5.52C17.26 5.53 17.32 5.55 17.38 5.58C17.44 5.6 17.5 5.63 17.56 5.67C17.61 5.7 17.66 5.75 17.71 5.79C17.8 5.89 17.87 5.99 17.92 6.12C17.97 6.24 18 6.37 18 6.5C18 6.63 17.97 6.76 17.92 6.88Z" fill="white"/>
          </svg>
        </figure>
        <h2 className="text-2lg sm:text-2xl my-2.5 font-display text-black text-center font-light">
          Connect your <br />
          <b className="font-bold">Instagram Account</b>
        </h2>
        <span className="text-center text-sm text-black-placeholder">
          Connecting your instagram account allows you import your posts as products on Catlog
        </span>
        <AppBtn onClick={getInstagramAuthCode} className="mt-7.5" isBlock size="lg" disabled={isLoading}>
          {isLoading ? "Authenticating..." : "Connect"}
        </AppBtn>
      </div>
    </div>
  );
};

interface SelectProductsProps {
  form: FormikProps<ImportForm>;
  getPostsReq: any;
  posts: InstagramMedia[];
  maxUploadable: number;
  instagramUser?: InstagramUser;
}

const SelectProducts: React.FC<SelectProductsProps> = ({ form, getPostsReq, posts, maxUploadable, instagramUser }) => {
  const { isLoading } = getPostsReq;
  const selectedArray = Object.keys(form.values.selected_posts);

  const handleSelectToggle = (data: InstagramMedia) => {
    const selectedPostsCopy = { ...form.values.selected_posts };

    if (isSelected(data.id)) {
      delete selectedPostsCopy[data.id];
    } else if (selectedArray.length >= maxUploadable) {
      toast.error({
        title: `You can only select ${maxUploadable} posts`,
        message: "To select this post, please unselect another post",
      });
    } else {
      selectedPostsCopy[data.id] = data;
    }

    form.setFieldValue("selected_posts", selectedPostsCopy);
  };

  const isSelected = (id: string) => {
    return form.values.selected_posts[id] !== undefined;
  };

  return (
    <>
      <div className="p-5 sm:p-6.25 border-b bg-white border-grey-divider w-full sticky top-0 z-30 flex items-center justify-between">
        <span className="text-sm text-black-placeholder block h-full">
          <b>{selectedArray.length}</b> posts selected
        </span>
        <span className="text-sm text-black-placeholder block h-full">
          Max To Select: <b>{maxUploadable}</b>{" "}
        </span>
        {/* <div className="flex items-center gap-3.75">
          <span className="font-medium text-sm text-black-placeholder"> Select All</span>
          <Toggle
            onChange={(s) => form.setFieldValue("autogenerate_details", s)}
            intialState={form.values.autogenerate_details}
          />
        </div> */}
      </div>

      {posts?.length > 0 && (
        <div className="grid grid-cols-2 gap-2.5 p-5 sm:p-6.25">
          {posts.map((d, idx) => (
            <figure
              key={idx}
              onClick={() => handleSelectToggle(d)}
              className="cursor-pointer relative rounded-2xl overflow-hidden w-full"
              style={{ aspectRatio: "1/1" }}
            >
              <LazyImage
                src={d.media_type === "VIDEO" ? d.thumbnail_url : d.media_url}
                className="object-cover h-full w-full"
              />
              <div className="z-10 absolute w-7.5 p-1.5 rounded-full top-2.5 left-2.5 bg-opacity-20  bg-black text-white">
                {MediaIconMap[d.media_type]}
              </div>
              {isSelected(d.id) && (
                <div className="top-0 left-0 w-full h-full absolute bg-black bg-opacity-40 flex flex-col items-center justify-center">
                  {/* prettier-ignore */}
                  <svg className="w-[20%] text-white" viewBox="0 0 24 24" fill="none">
                    <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              )}
            </figure>
          ))}
        </div>
      )}

      {isLoading && (
        <div className="py-10">
          <div className="spinner text-primary-500 m-auto"></div>
        </div>
      )}
    </>
  );
};

const MediaIconMap = {
  CAROUSEL_ALBUM:
    //prettier-ignore
    <svg className="w-full" viewBox="0 0 24 24" fill="none">
      <path d="M16 12.9V17.1C16 20.6 14.6 22 11.1 22H6.9C3.4 22 2 20.6 2 17.1V12.9C2 9.4 3.4 8 6.9 8H11.1C14.6 8 16 9.4 16 12.9Z" fill="currentColor"/>
      <path d="M17.0998 2H12.8998C9.81668 2 8.37074 3.09409 8.06951 5.73901C8.00649 6.29235 8.46476 6.75 9.02167 6.75H11.0998C15.2998 6.75 17.2498 8.7 17.2498 12.9V14.9781C17.2498 15.535 17.7074 15.9933 18.2608 15.9303C20.9057 15.629 21.9998 14.1831 21.9998 11.1V6.9C21.9998 3.4 20.5998 2 17.0998 2Z" fill="currentColor"/>
    </svg>,
  VIDEO:
    //prettier-ignore
    <svg className="w-full"viewBox="0 0 24 24" fill="none">
      <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM14.66 13.73L13.38 14.47L12.1 15.21C10.45 16.16 9.1 15.38 9.1 13.48V12V10.52C9.1 8.61 10.45 7.84 12.1 8.79L13.38 9.53L14.66 10.27C16.31 11.22 16.31 12.78 14.66 13.73Z" fill="currentColor"/>
    </svg>,
  IMAGE:
    //prettier-ignore
    <svg className="w-full" viewBox="0 0 24 24" fill="none">
      <path d="M2.58078 19.0112L2.56078 19.0312C2.29078 18.4413 2.12078 17.7713 2.05078 17.0312C2.12078 17.7613 2.31078 18.4212 2.58078 19.0112Z" fill="currentColor"/>
      <path d="M9.00109 10.3811C10.3155 10.3811 11.3811 9.31553 11.3811 8.00109C11.3811 6.68666 10.3155 5.62109 9.00109 5.62109C7.68666 5.62109 6.62109 6.68666 6.62109 8.00109C6.62109 9.31553 7.68666 10.3811 9.00109 10.3811Z" fill="currentColor"/>
      <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.19C2 17.28 2.19 18.23 2.56 19.03C3.42 20.93 5.26 22 7.81 22H16.19C19.83 22 22 19.83 22 16.19V13.9V7.81C22 4.17 19.83 2 16.19 2ZM20.37 12.5C19.59 11.83 18.33 11.83 17.55 12.5L13.39 16.07C12.61 16.74 11.35 16.74 10.57 16.07L10.23 15.79C9.52 15.17 8.39 15.11 7.59 15.65L3.85 18.16C3.63 17.6 3.5 16.95 3.5 16.19V7.81C3.5 4.99 4.99 3.5 7.81 3.5H16.19C19.01 3.5 20.5 4.99 20.5 7.81V12.61L20.37 12.5Z" fill="currentColor"/>
    </svg>,
};
