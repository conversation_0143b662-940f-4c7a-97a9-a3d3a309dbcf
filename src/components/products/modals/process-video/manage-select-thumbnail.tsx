import { uploadClient } from "@/api/client";
import { FILE_TYPES, Image, Video } from "@/assets/interfaces";
import { generateKey } from "@/assets/js/utils/image-selection";
import { getProductThumbnail } from "@/assets/js/utils/utils";
import useImageUploads, { uploadImageBlob } from "@/components/hooks/useImageUploads";
import { toast } from "@/components/ui/toast";
import { Product, ProductForm } from "@/pages/products/create";
import { FormikProps } from "formik";
import { useEffect, useState } from "react";
import { Toaster } from "react-hot-toast";
import Modal, { ModalBody } from "../../../ui/modal";
import SelectThumbnailMain from "../../select-thumbnail";

interface Props {
  currentVideo: Video;
  finalizeVideo: (img: Blob) => void;
  setDimensions: (dimensions: VideoDimensions) => void;
}

export interface VideoDimensions {
  width: number;
  height: number;
}

const SelectVideoThumbnail: React.FC<Props> = ({ currentVideo, finalizeVideo, setDimensions }) => {
  return <SelectThumbnailMain onSelectThumbnail={finalizeVideo} {...{ videoFile: currentVideo.file, setDimensions }} />;
};

export default SelectVideoThumbnail;
