import { Video } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import { VideoCircle } from "iconsax-react";
import React, { PointerEvent, useEffect, useMemo, useRef, useState } from "react";
import Mo<PERSON>, { <PERSON>dal<PERSON><PERSON>, ModalFooter } from "../../../ui/modal";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  currentVideo: Video;
  onTrim: (start: number, end: number) => void;
  value?: { start: number; end: number };
}

export interface VideoDimensions {
  width: number;
  height: number;
}

const TrimProductVideo: React.FC<Props> = ({ show, toggle, onTrim, value, currentVideo }) => {
  const [duration, setDuration] = useState("");
  return (
    <>
      <Modal {...{ show, toggle }} title="Trim Product Video" size="md">
        <ModalBody className="flex flex-col items-center">
          <div className="bg-grey-fields-200 rounded-2xl  ">
            <p className="text-1xs py-2.5 px-3.75 text-black-placeholder">
              {(currentVideo.file as File)?.name}
              {duration}
            </p>
            <div className="w-full border p-3.75 bg-white border-grey-divider rounded-2xl">
              {currentVideo.file && (
                <VideoTrimmer
                  initialEnd={value?.end}
                  initialStart={value.start}
                  setDuration={setDuration}
                  onChange={onTrim}
                  maxDuration={30}
                  src={currentVideo.src}
                />
              )}
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <AppBtn className="" onClick={(_) => toggle(false)} isBlock color="primary">
            Trim & Continue
          </AppBtn>
        </ModalFooter>
      </Modal>
    </>
  );
};

interface VideoTrimmerProps {
  src: string;
  initialStart?: number;
  initialEnd?: number;
  maxDuration?: number;
  onChange?: (start: number, end: number) => void;
  setDuration: (t: string) => void;
  setDimensions?: (d: VideoDimensions) => void;
}

export const VideoTrimmer: React.FC<VideoTrimmerProps> = ({
  src,
  setDuration: _setDuration,
  initialStart = 0,
  maxDuration,
  initialEnd,
  onChange,
  setDimensions,
}) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const trackRef = useRef<HTMLDivElement | null>(null);
  const [loaded, setLoaded] = useState(false);

  const [duration, setDuration] = useState(0);
  let [trimStart, setTrimStart] = useState(initialStart);
  let [trimEnd, setTrimEnd] = useState(initialEnd ?? 0);
  const [dragging, setDragging] = useState<"left" | "right" | null>(null);

  const secondsToPercent = (sec: number) => (duration === 0 ? 0 : (sec / duration) * 100);
  const clamp = (val: number, min: number, max: number) => Math.min(Math.max(val, min), max);

  const enforceMax = (start: number, end: number): [number, number] => {
    if (!maxDuration) return [start, end];
    const len = end - start;
    if (len <= maxDuration) return [start, end];

    if (dragging === "left") {
      start = end - maxDuration;
    } else if (dragging === "right") {
      end = start + maxDuration;
    } else {
      end = start + maxDuration;
    }
    return [start, end];
  };

  useEffect(() => {
    _setDuration(formatTime(videoRef?.current?.duration ?? 0));
  }, [loaded]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoaded = () => {
      setDimensions?.({
        width: video.videoWidth,
        height: video.videoHeight,
      });
      const d = video.duration;
      setDuration(d);

      let s = clamp(initialStart, 0, d);
      let e = clamp(initialEnd ?? d, 0, d);
      [s, e] = enforceMax(s, e);
      setTrimStart(s);
      setTrimEnd(e);
      onChange?.(s, e);
    };

    const handleTimeUpdate = () => {
      if (!video) return;
      if (video.currentTime < trimStart) {
        video.currentTime = trimStart + 0.1;
      }
      if (video.currentTime > trimEnd) {
        video.pause();
        video.currentTime = trimStart + 0.1;
      }
    };

    video.addEventListener("loadedmetadata", handleLoaded);
    video.addEventListener("timeupdate", handleTimeUpdate);

    return () => {
      video.removeEventListener("loadedmetadata", handleLoaded);
      video.removeEventListener("timeupdate", handleTimeUpdate);
    };
  }, [maxDuration, trimEnd, trimStart]);

  const onPointerDown = (e: PointerEvent<HTMLDivElement>, handle: "left" | "right") => {
    e.preventDefault();
    setDragging(handle);
  };

  useEffect(() => {
    const handleMove = (e: PointerEvent | MouseEvent) => {
      if (!dragging || !trackRef.current) return;
      const bounds = trackRef.current.getBoundingClientRect();
      const px = clamp(e.clientX - bounds.left, 0, bounds.width);
      const percent = px / bounds.width;
      const sec = percent * duration;

      if (dragging === "left") {
        let newStart = clamp(sec, 0, trimEnd - 0.1);
        if (maxDuration) newStart = Math.max(newStart, trimEnd - maxDuration);
        [newStart, trimEnd] = enforceMax(newStart, trimEnd);
        setTrimStart(newStart);
        // videoRef.current!.currentTime = newStart;
        onChange?.(newStart, trimEnd);
      } else if (dragging === "right") {
        let newEnd = clamp(sec, trimStart + 0.1, duration);
        if (maxDuration) newEnd = Math.min(newEnd, trimStart + maxDuration);
        [trimStart, newEnd] = enforceMax(trimStart, newEnd);
        setTrimEnd(newEnd);
        onChange?.(trimStart, newEnd);
      }
    };

    const handleUp = () => setDragging(null);

    window.addEventListener("pointermove", handleMove);
    window.addEventListener("pointerup", handleUp);
    return () => {
      window.removeEventListener("pointermove", handleMove);
      window.removeEventListener("pointerup", handleUp);
    };
  }, [dragging, duration, trimStart, trimEnd, maxDuration, onChange]);

  const leftPercent = secondsToPercent(trimStart);
  const rightPercent = secondsToPercent(trimEnd);

  return (
    <div className="flex flex-col gap-4 w-full max-w-3xl mx-auto">
      <video
        onCanPlayThrough={() => setLoaded(true)}
        loop={false}
        ref={videoRef}
        src={src}
        controls
        className="w-full rounded-xl overflow-hidden"
        playsInline
      />
      <div className="flex items-end p-2.5 bg-grey-fields-100 rounded-8 gap-2.5">
        <button
          type="button"
          onClick={() => videoRef?.current?.play()}
          className="rounded-full flex-shrink-0 h-8.75 w-8.75 flex items-center justify-center bg-white"
        >
          <VideoCircle size={18} className="text-primary-500" variant="Bold" />
        </button>

        <div className="w-full">
          <div>
            {loaded && (
              <ul className="flex items-center text-[10px] mb-1.25 text-black-placeholder justify-between">
                {new Array(7).fill(null).map((_, i) => (
                  <li className="" key={i}>
                    {(((videoRef?.current?.duration ?? 0) / 7.0) * i).toFixed(1)}s
                  </li>
                ))}
                <li className="">{videoRef?.current?.duration?.toFixed(1) ?? 0}s</li>
              </ul>
            )}
          </div>
          <div ref={trackRef} className="relative h-8.75 w-full bg-white overflow-hidden rounded-md  select-none">
            <video loop={false} src={src} className="absolute -z-10 top-1/2 -translate-y-1/2 opacity-20" playsInline />
            <div
              className="absolute top-0 h-full border-[2px] border-primary-500  rounded-md"
              style={{ left: `${leftPercent}%`, width: `${rightPercent - leftPercent}%` }}
            />

            <div
              role="slider"
              aria-valuemin={0}
              aria-valuemax={duration}
              aria-valuenow={trimStart}
              className="absolute top-1/2 -translate-y-1/2 -translate-x-1/2 h-[70%] w-1 bg-primary-500 rounded-sm cursor-[ew-resize]"
              style={{ left: `${leftPercent}%` }}
              onPointerDown={(e) => onPointerDown(e, "left")}
            />

            <div
              role="slider"
              aria-valuemin={0}
              aria-valuemax={duration}
              aria-valuenow={trimEnd}
              className="absolute top-1/2 -translate-y-1/2 -translate-x-1/2 h-[70%] w-1 bg-primary-500 rounded-sm cursor-[ew-resize]"
              style={{ left: `${rightPercent}%` }}
              onPointerDown={(e) => onPointerDown(e, "right")}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export function formatTime(seconds: number): string {
  if (isNaN(seconds)) {
    return "";
  }
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes} min ${Math.ceil(secs) < 10 ? "0" : ""}${secs.toFixed(0)} secs`;
}

export default TrimProductVideo;
