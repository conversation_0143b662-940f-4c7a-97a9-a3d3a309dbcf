import { uploadClient } from "@/api/client";
import { FILE_TYPES, Image, Video } from "@/assets/interfaces";
import { generateKey } from "@/assets/js/utils/image-selection";
import { getProductThumbnail } from "@/assets/js/utils/utils";
import useImageUploads, { uploadImageBlob } from "@/components/hooks/useImageUploads";
import { toast } from "@/components/ui/toast";
import { Product, ProductForm } from "@/pages/products/create";
import { FormikProps } from "formik";
import { useEffect, useRef, useState } from "react";
import { Toaster } from "react-hot-toast";
import Modal, { ModalBody } from "../../../ui/modal";
import SelectThumbnailMain, { SelectThumbnailRef } from "../../select-thumbnail";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  form: FormikProps<Product>;
  currentVideoIndex: number;
  transcodeVideo: (dimensions: VideoDimensions) => void;
}

export interface VideoDimensions {
  width: number;
  height: number;
}

const SelectVideoThumbnail: React.FC<Props> = ({ show, toggle, currentVideoIndex, form, transcodeVideo }) => {
  // hooks
  const thumbnailRef = useRef<SelectThumbnailRef>(null);
  const currentVideo = form.values?.videos?.[currentVideoIndex];
  const [dimensions, setDimensions] = useState<VideoDimensions>({ width: 0, height: 0 });
  const videoFile = currentVideo?.file;

  const handleSelectThumbnail = (img: Blob) => {
    toggle(false);
    transcodeVideo(dimensions);
    // return
    uploadImageBlob(img, (i) => {
      const videos = [...(form?.values.videos ?? [])];
      videos[currentVideoIndex].meta = { ...(videos?.[currentVideoIndex]?.meta ?? {}), thumbnail: i };
      form.setFieldValue("videos", videos);

      if (!getProductThumbnail(form.values)) {
        form.setFieldValue("thumbnail_type", "video");
      }
    });
  };

  return (
    <>
      <Modal {...{ show, toggle }} title="Select Video Thumbnail" size="md">
        <ModalBody className="flex flex-col items-center">
          <>
            <div className="w-full">
              <SelectThumbnailMain
                ref={thumbnailRef}
                onSelectThumbnail={handleSelectThumbnail}
                {...{ videoFile, setDimensions }}
              />
            </div>
          </>
        </ModalBody>
      </Modal>
    </>
  );
};

export default SelectVideoThumbnail;
