import { Video } from "@/assets/interfaces";
import { PauseCircle, PlayCircle, TickCircle } from "iconsax-react";
import React, { PointerEvent, useEffect, useRef, useState } from "react";
import CanvasBgFromVideo from "@/components/ui/canvas-bg-from-video";

interface Props {
  currentVideo: Video;
  onTrim: (start: number, end: number) => void;
  value?: { start: number; end: number };
}

export interface VideoDimensions {
  width: number;
  height: number;
}

const TrimVideoManager: React.FC<Props> = ({ onTrim, value, currentVideo }) => {
  const [duration, setDuration] = useState("");
  return (
    <div className="bg-grey-fields-100 rounded-15">
      <div className="w-full border p-3.75 bg-white border-grey-divider rounded-15">
        {currentVideo.file && (
          <VideoTrimmer
            initialEnd={value?.end}
            initialStart={value.start}
            setDuration={setDuration}
            onChange={onTrim}
            maxDuration={45}
            src={currentVideo.src}
          />
        )}
      </div>
    </div>
  );
};

interface VideoTrimmerProps {
  src: string;
  initialStart?: number;
  initialEnd?: number;
  maxDuration?: number;
  onChange?: (start: number, end: number) => void;
  setDuration: (t: string) => void;
  setDimensions?: (d: VideoDimensions) => void;
}

export const VideoTrimmer: React.FC<VideoTrimmerProps> = ({
  src,
  setDuration: _setDuration,
  initialStart = 0,
  maxDuration,
  initialEnd,
  onChange,
  setDimensions,
}) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const trackRef = useRef<HTMLDivElement | null>(null);
  const [loaded, setLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const [duration, setDuration] = useState(0);
  let [trimStart, setTrimStart] = useState(initialStart);
  let [trimEnd, setTrimEnd] = useState(initialEnd ?? 0);
  const [dragging, setDragging] = useState<"left" | "right" | null>(null);

  const secondsToPercent = (sec: number) => (duration === 0 ? 0 : (sec / duration) * 100);
  const clamp = (val: number, min: number, max: number) => Math.min(Math.max(val, min), max);

  const enforceMax = (start: number, end: number): [number, number] => {
    if (!maxDuration) return [start, end];
    const len = end - start;
    if (len <= maxDuration) return [start, end];

    if (dragging === "left") {
      start = end - maxDuration;
    } else if (dragging === "right") {
      end = start + maxDuration;
    } else {
      end = start + maxDuration;
    }
    return [start, end];
  };

  useEffect(() => {
    _setDuration(formatTime(videoRef?.current?.duration ?? 0));
  }, [loaded]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoaded = () => {
      setDimensions?.({
        width: video.videoWidth,
        height: video.videoHeight,
      });
      const d = video.duration;
      setDuration(d);

      let s = clamp(initialStart, 0, d);
      let e = clamp(initialEnd ?? d, 0, d);
      [s, e] = enforceMax(s, e);
      setTrimStart(s);
      setTrimEnd(e);
      onChange?.(s, e);
    };

    const handleTimeUpdate = () => {
      if (!video || dragging) return;
      //   console.log(video.currentTime,trimStart,trimEnd)
      if (video.currentTime < trimStart) {
        video.currentTime = trimStart + 0.1;
      }
      if (video.currentTime > trimEnd) {
        video.pause();
        video.currentTime = trimStart + 0.1;
      }
    };

    video.addEventListener("loadedmetadata", handleLoaded);
    video.addEventListener("timeupdate", handleTimeUpdate);

    return () => {
      video.removeEventListener("loadedmetadata", handleLoaded);
      video.removeEventListener("timeupdate", handleTimeUpdate);
    };
  }, [maxDuration, trimEnd, trimStart]);

  const onPointerDown = (e: PointerEvent<HTMLDivElement>, handle: "left" | "right") => {
    e.preventDefault();
    setDragging(handle);
  };

  useEffect(() => {
    const handleMove = (e: PointerEvent | MouseEvent) => {
      if (!dragging || !trackRef.current) return;
      const bounds = trackRef.current.getBoundingClientRect();
      const px = clamp(e.clientX - bounds.left, 0, bounds.width);
      const percent = px / bounds.width;
      const sec = percent * duration;

      if (dragging === "left") {
        let newStart = clamp(sec, 0, trimEnd - 0.1);
        if (maxDuration) newStart = Math.max(newStart, trimEnd - maxDuration);
        [newStart, trimEnd] = enforceMax(newStart, trimEnd);
        setTrimStart(newStart);
        videoRef.current!.currentTime = newStart;
        onChange?.(newStart, trimEnd);
      } else if (dragging === "right") {
        let newEnd = clamp(sec, trimStart + 0.1, duration);
        if (maxDuration) newEnd = Math.min(newEnd, trimStart + maxDuration);
        [trimStart, newEnd] = enforceMax(trimStart, newEnd);
        setTrimEnd(newEnd);
        onChange?.(trimStart, newEnd);
      }
    };

    const handleUp = () => setDragging(null);

    window.addEventListener("pointermove", handleMove);
    window.addEventListener("pointerup", handleUp);

    return () => {
      window.removeEventListener("pointermove", handleMove);
      window.removeEventListener("pointerup", handleUp);
    };
  }, [dragging, duration, trimStart, trimEnd, maxDuration, onChange]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => setIsPlaying(false); // optional

    video.addEventListener("play", handlePlay);
    video.addEventListener("pause", handlePause);
    video.addEventListener("ended", handleEnded);

    return () => {
      video.removeEventListener("play", handlePlay);
      video.removeEventListener("pause", handlePause);
      video.removeEventListener("ended", handleEnded);
    };
  }, []);

  const leftPercent = secondsToPercent(trimStart);
  const rightPercent = secondsToPercent(trimEnd);

  return (
    <div className="flex flex-col gap-4 w-full max-w-3xl mx-auto">
      <div className="w-full h-[35vh] bg-black rounded-15 flex items-center justify-center relative group">
        <video
          onCanPlayThrough={() => setLoaded(true)}
          loop={false}
          ref={videoRef}
          src={src}
          controls={false}
          className="max-w-full max-h-full overflow-hidden relative z-50"
          playsInline
        />
        <div
          className="absolute top-0 left-0 w-full h-full z-100 flex items-center justify-center cursor-pointer"
          onClick={() => (isPlaying ? videoRef?.current?.pause() : videoRef?.current?.play())}
        >
          <button
            type="button"
            className="w-10 h-10 bg-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          >
            {isPlaying ? (
              <PauseCircle size={24} className="text-primary-500" variant="Bold" />
            ) : (
              <PlayCircle size={24} className="text-primary-500" variant="Bold" />
            )}
          </button>
        </div>
        <CanvasBgFromVideo videoRef={videoRef} />
      </div>
      <div className="flex items-end p-2.5 bg-grey-fields-100 rounded-8 gap-2.5">
        <div className="w-full">
          <div>
            {loaded && (
              <ul className="flex items-center text-[10px] mb-1.25 text-black-placeholder justify-between">
                {new Array(7).fill(null).map((_, i) => (
                  <li className="" key={i}>
                    {(((videoRef?.current?.duration ?? 0) / 7.0) * i).toFixed(1)}s
                  </li>
                ))}
                <li className="">{videoRef?.current?.duration?.toFixed(1) ?? 0}s</li>
              </ul>
            )}
          </div>
          <div ref={trackRef} className="relative h-8.75 w-full bg-white overflow-hidden rounded-md  select-none">
            <video loop={false} src={src} className="absolute -z-10 top-1/2 -translate-y-1/2 opacity-40" playsInline />
            <div
              className="absolute top-0 h-full border-[2px] border-primary-500  rounded-md"
              style={{ left: `${leftPercent}%`, width: `${rightPercent - leftPercent}%` }}
            />

            <div
              role="slider"
              aria-valuemin={0}
              aria-valuemax={duration}
              aria-valuenow={trimStart}
              className="absolute top-1/2 -translate-y-1/2 -translate-x-1/2 h-[70%] w-1 bg-primary-500 rounded-sm cursor-[ew-resize]"
              style={{ left: `${leftPercent}%` }}
              onPointerDown={(e) => onPointerDown(e, "left")}
            />

            <div
              role="slider"
              aria-valuemin={0}
              aria-valuemax={duration}
              aria-valuenow={trimEnd}
              className="absolute top-1/2 -translate-y-1/2 -translate-x-1/2 h-[70%] w-1 bg-primary-500 rounded-sm cursor-[ew-resize]"
              style={{ left: `${rightPercent}%` }}
              onPointerDown={(e) => onPointerDown(e, "right")}
            />
          </div>
          <div className="text-1xs text-black-secondary flex items-center mt-2.5">
            <TickCircle size={16} className="text-accent-green-500 mr-0.5" variant="Bold" />
            {Math.round(trimEnd - trimStart)}s Selected
          </div>
        </div>
      </div>
    </div>
  );
};

export function formatTime(seconds: number): string {
  if (isNaN(seconds)) {
    return "";
  }
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return ` • ${minutes} min ${secs < 10 ? "0" : ""}${secs.toFixed(0)} secs`;
}

export default TrimVideoManager;
