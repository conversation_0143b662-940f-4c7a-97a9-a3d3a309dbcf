import { Media, MediaType } from "@/assets/interfaces";
import useMediaProcessor from "@/components/hooks/useMediaProcessor";
import { ProductUploadStep } from "@/components/hooks/useProductController";
import { AppBtn } from "@/components/ui/buttons";
import ItemSelectorCard from "@/components/ui/item-selector-card";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { ProductForm } from "@/pages/products/create";
import { DocumentUpload, Instagram, VideoSlash, Warning2 } from "iconsax-react";
import { useRef, useState } from "react";

interface Props {
  show: boolean;
  toggleModal: VoidFunction;
  pastProgress?: ProductForm;
  maxUploadable: number;
  loadLastProgress: VoidFunction;
  setMedias: (m: Media[]) => void;
  medias: Media[];
  setStep: (step: ProductUploadStep) => void;
  canProcessVideos?: boolean;
  openCountModal: VoidFunction;
}

const SelectManualMethod: React.FC<Props> = ({
  show,
  setMedias,
  medias,
  setStep,
  maxUploadable,
  toggleModal,
  pastProgress,
  canProcessVideos = true,
  loadLastProgress,
  openCountModal,
}) => {
  const mediaPicker = useRef<HTMLInputElement>(null);
  const [selectedMethod, setSelectedMethod] = useState("");
  const excludedMediaTypes = canProcessVideos ? [] : [MediaType.VIDEO];

  const { processFiles } = useMediaProcessor(
    (m) => {
      setStep("media");
      setMedias(m);
      toggleModal();
    },
    medias,
    maxUploadable
  );

  const handleMethodSelect = (method: string) => {
    switch (method) {
      case "device": {
        mediaPicker.current.click();
        break;
      }
      case "none": {
        toggleModal();
        openCountModal();
        break;
      }
    }
  };

  return (
    <Modal title="Add manually" show={show} toggle={toggleModal} size="sm">
      <ModalBody>
        <div className="flex items-center flex-col">
          <figure className="h-[70px] w-[70px] bg-accent-green-500 flex flex-col items-center justify-center rounded-full">
            {/* prettier-ignore */}
            <svg width="30" height="30" viewBox="0 0 30 30" fill="none" >
            <path d="M25.2625 9.77578L15.6375 15.3508C15.25 15.5758 14.7625 15.5758 14.3625 15.3508L4.7375 9.77578C4.05 9.37578 3.875 8.43828 4.4 7.85078C4.7625 7.43828 5.175 7.10078 5.6125 6.86328L12.3875 3.11328C13.8375 2.30078 16.1875 2.30078 17.6375 3.11328L24.4125 6.86328C24.85 7.10078 25.2625 7.45078 25.625 7.85078C26.125 8.43828 25.95 9.37578 25.2625 9.77578Z" fill="white"/>
            <path d="M14.2875 17.6746V26.1996C14.2875 27.1496 13.325 27.7746 12.475 27.3621C9.9 26.0996 5.5625 23.7371 5.5625 23.7371C4.0375 22.8746 2.7875 20.6996 2.7875 18.9121V12.4621C2.7875 11.4746 3.825 10.8496 4.675 11.3371L13.6625 16.5496C14.0375 16.7871 14.2875 17.2121 14.2875 17.6746Z" fill="white"/>
            <path d="M15.7125 17.6746V26.1996C15.7125 27.1496 16.675 27.7746 17.525 27.3621C20.1 26.0996 24.4375 23.7371 24.4375 23.7371C25.9625 22.8746 27.2125 20.6996 27.2125 18.9121V12.4621C27.2125 11.4746 26.175 10.8496 25.325 11.3371L16.3375 16.5496C15.9625 16.7871 15.7125 17.2121 15.7125 17.6746Z" fill="white"/>
            </svg>
          </figure>
          <h3 className="text-center text-2lg sm:text-3lg leading-tight font-light mt-3.75">
            Add media <br /> <span className="font-bold">for multiple products</span>
          </h3>
          <p className="sm:text-sm text-1xs text-center text-grey-subtext mt-2.5">
            Pick one media file for every product you want to add, <br /> you’ll be able to add more media later
          </p>
          {canProcessVideos === false && (
            <div className="p-2.5 bg-opacity-20 mt-3.75 rounded-md gap-2.5 bg-accent-orange-100 text-accent-orange-500 text-xs flex items-center justify-center">
              <Warning2 size={15} className="text-accent-orange-500" /> You cannot process videos on this device!
            </div>
          )}
          <div className="rounded-15 overflow-hidden w-full mt-5">
            {options.map((o) => (
              <ItemSelectorCard
                title={<>{o.title}</>}
                value={o.value}
                icon={o.icon}
                chosen={selectedMethod}
                description={o.description}
                name={o.name}
                onChange={() => setSelectedMethod(o.value)}
                className="border-b last:border-none border-grey-border "
                key={o.value}
              />
            ))}
          </div>
          <input
            type="file"
            ref={mediaPicker}
            name="product-medias"
            multiple
            accept={canProcessVideos ? "video/mp4,webm,ogg,video/quicktime,image/*,.heic" : "image/*,.heic"}
            id="product-medias"
            className="hidden"
            onChange={(e) => processFiles(e.target.files, excludedMediaTypes)}
          />
        </div>
      </ModalBody>
      <ModalFooter className="flex-col">
        {pastProgress !== null && (
          <AppBtn className="mb-2.5" isBlock color="neutral" onClick={loadLastProgress}>
            Load Media from last Progress
          </AppBtn>
        )}
        <AppBtn onClick={() => handleMethodSelect(selectedMethod)} disabled={selectedMethod === ""} isBlock>
          Continue
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};
export default SelectManualMethod;

const options = [
  {
    title: "Select from Device",
    description: "Upload images or videos straight from your device",
    icon: <DocumentUpload variant="Bold" size={20} className="text-accent-green-500" />,
    value: "device",
    name: "device",
  },
  // {
  //   title: "Select from Instagram",
  //   description: "Select media from your linked Instagram account",
  //   icon: <Instagram variant="Bold" size={20} className="text-accent-orange-500" />,
  //   value: "instagram",
  //   name: "instagram",
  // },
  {
    title: "Proceed without media",
    description: "Create products without media files",
    icon: <VideoSlash variant="Bold" size={20} className="text-accent-red-500" />,
    value: "none",
    name: "none",
  },
];
