import React, { useEffect, useState } from "react";
import { ExportItemsAsCsv, GetItems } from "@/api/items";
import { ProductItemInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "@/components/ui/modal";
import SelectSpecificProductsModal from "./select-specific-products";
import usePDFDownloads from "@/components/hooks/usePDFDownloads";
import { GetItemsParams } from "@/api/interfaces/items.interface";
import { useFetcher, useRequest } from "@/api/utils";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
}

const ExportProductsModal: React.FC<Props> = ({ show, toggle }) => {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [showSelectModal, setShowSelectModal] = useState(false);

  // Fetch products for export
  const productsRequest = useRequest<GetItemsParams>(GetItems);

  const items = productsRequest?.response?.data?.items || [];
  const loadingItems = productsRequest.isLoading;

  useEffect(() => {
    if (show && !items.length && !loadingItems) {
      productsRequest.makeRequest({
        filter: { search: "" },
        page: 1,
        per_page: Number.MAX_SAFE_INTEGER, // Get a reasonable number of products for selection
      });
    }
  }, [items, show, loadingItems]);

  const { isLoadingPdf, download } = usePDFDownloads({
    request: ExportItemsAsCsv,
    data: { items: selectedProducts },
    filename: `products-export-${new Date().toISOString().split("T")[0]}`,
    fileType: "csv",
  });

  const handleExport = () => {
    if (selectedProducts.length === 0 || selectedProducts.length === items.length) {
      // If no products are selected, export all products
      download({ items: [] });
    } else {
      // Export selected products
      download({ items: selectedProducts });
    }
    toggle(false);
  };

  const handleSaveSelections = (selected: string[]) => {
    setSelectedProducts(selected);
    setShowSelectModal(false);
  };

  return (
    <>
      <Modal {...{ show, toggle }} title="Export Products" size="md">
        <ModalBody>
          <div className="mb-6">
            <p className="text-sm text-black-secondary">
              Select the products you want to export. If you don't select any products, all products will be exported.
            </p>
          </div>
          <div className="flex flex-col space-y-4">
            <AppBtn onClick={() => setShowSelectModal(true)} color="neutral" isBlock size="lg">
              Select Products
            </AppBtn>
            {selectedProducts.length > 0 && (
              <div className="bg-grey-fields-200 p-4 rounded-10">
                <p className="text-sm font-medium">
                  {selectedProducts.length} product{selectedProducts.length !== 1 ? "s" : ""} selected
                </p>
              </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="w-full">
            <AppBtn onClick={handleExport} isBlock size="lg" disabled={isLoadingPdf}>
              {isLoadingPdf ? "Exporting..." : "Export Products"}
            </AppBtn>
          </div>
        </ModalFooter>
      </Modal>

      <SelectSpecificProductsModal
        show={showSelectModal}
        toggle={setShowSelectModal}
        items={items}
        onSave={handleSaveSelections}
        value={selectedProducts}
        onSearch={() => {}}
        loadingItems={loadingItems}
      />
    </>
  );
};

export default ExportProductsModal;
