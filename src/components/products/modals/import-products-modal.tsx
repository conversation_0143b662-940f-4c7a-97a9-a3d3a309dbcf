import React, { useEffect, useRef, useState } from "react";
import { ImportItems } from "@/api/items";
import { toast } from "@/components/ui/toast";
import Modal, { <PERSON>dalBody, ModalFooter } from "@/components/ui/modal";
import { AppBtn } from "@/components/ui/buttons";
import Error<PERSON>abel from "@/components/ui/error-label";
import SuccessLabel from "@/components/ui/success-label";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
}

const ACCEPTED_FILE_TYPES = [
  "text/csv",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "application/vnd.ms-excel",
  "application/x-iwork-numbers-sffnumbers",
];

const ImportProductsModal: React.FC<Props> = ({ show, toggle }) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<{ success: boolean; message: string } | null>(null);
  const pickerRef = useRef<HTMLInputElement>(null);
  const dragAreaRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (!show) {
      setFile(null);
      setUploadResult(null);
    }
  }, [show]);

  const handleDrop = (e: React.DragEvent) => {
    const processFile = (file: File) => {
      if (!ACCEPTED_FILE_TYPES.includes(file.type)) {
        toast.error({
          title: "Invalid File",
          message: "File uploaded is not a CSV file",
        });
        return;
      } else {
        setFile(file);
      }
    };

    e.preventDefault(); // Prevent default behavior
    dragAreaRef.current.classList.remove("border-primary-500");

    if (e.dataTransfer.items) {
      // Use DataTransferItemList interface to access the file(s)
      if (e.dataTransfer.items[0].kind === "file") {
        const file = e.dataTransfer.items[0].getAsFile();
        processFile(file);
      }
    } else {
      // Use DataTransfer interface to access the file(s)
      const file = e.dataTransfer.files[0];
      processFile(file);
    }
  };

  const openPicker = () => {
    pickerRef.current.click();
  };

  const removeFile = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setFile(null);
    setUploadResult(null);
    pickerRef.current.value = "";
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    const dragArea = dragAreaRef.current;
    dragArea.classList.add("border-primary-500");
  };

  const handleDragExit = (e: React.DragEvent) => {
    e.preventDefault();
    const dragArea = dragAreaRef.current;
    dragArea.classList.remove("border-primary-500");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files[0];
    if (selectedFile && ACCEPTED_FILE_TYPES.includes(selectedFile.type)) {
      setFile(selectedFile);
      setUploadResult(null);
    } else {
      toast.error({
        title: "Invalid File",
        message: "Please select a spreadsheet file",
      });
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    setIsUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/item-files/import`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${localStorage.token}`,
        },
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setUploadResult({
          success: true,
          message: "Products imported successfully. The import is being processed in the background.",
        });
        setTimeout(() => {
          toggle(false);
        }, 3000);
      } else {
        // Handle validation errors or other failures
        const errorMessage =
          result.errors && result.errors.length > 0
            ? result.errors.join(", ")
            : result.message || "Failed to import products. Please try again.";

        setUploadResult({
          success: false,
          message: errorMessage,
        });
      }
    } catch (error) {
      setUploadResult({
        success: false,
        message: "An error occurred while importing products. Please try again.",
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Modal {...{ show, toggle }} title="Import Products" size="md">
      <ModalBody>
        <div className="w-full">
          {uploadResult && (
            <div className="mt-4">
              {uploadResult.success ? (
                <SuccessLabel message={uploadResult.message} />
              ) : (
                <ErrorLabel error={uploadResult.message} />
              )}
            </div>
          )}
          <div className="bg-grey-fields-100 rounded-10 px-3 py-2 mb-2.5 flex items-start border border-grey-divider">
            <figure className="h-6 w-6 rounded-full bg-white shadow-pill flex items-center justify-center flex-shrink-0">
              {/* prettier-ignore */}
              <svg width="80%" className="text-accent-yellow-500" viewBox="0 0 24 24" fill="none">
                <path d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM11.25 8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V13C12.75 13.41 12.41 13.75 12 13.75C11.59 13.75 11.25 13.41 11.25 13V8ZM12.92 16.38C12.87 16.51 12.8 16.61 12.71 16.71C12.61 16.8 12.5 16.87 12.38 16.92C12.26 16.97 12.13 17 12 17C11.87 17 11.74 16.97 11.62 16.92C11.5 16.87 11.39 16.8 11.29 16.71C11.2 16.61 11.13 16.51 11.08 16.38C11.03 16.26 11 16.13 11 16C11 15.87 11.03 15.74 11.08 15.62C11.13 15.5 11.2 15.39 11.29 15.29C11.39 15.2 11.5 15.13 11.62 15.08C11.86 14.98 12.14 14.98 12.38 15.08C12.5 15.13 12.61 15.2 12.71 15.29C12.8 15.39 12.87 15.5 12.92 15.62C12.97 15.74 13 15.87 13 16C13 16.13 12.97 16.26 12.92 16.38Z" fill="currentColor"/>
              </svg>
            </figure>
            <span className="block font-medium text-dark text-xs ml-2">
              Please save your file as .xlsx or .csv before uploading. .numbers files are not supported.
            </span>
          </div>
          <button
            className="w-full max-w-[3800px] mx-auto border border-grey-border border-dashed rounded-15 cursor-pointer relative block transition-all ease-out"
            style={{ paddingTop: "55%" }}
            onClick={openPicker}
            onDragOver={handleDragOver}
            onDragLeave={handleDragExit}
            onDrop={handleDrop}
            ref={dragAreaRef}
            type="button"
          >
            <div className="absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center">
              {/* prettier-ignore */}
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                <path opacity="0.4" d="M36.6997 28.0333L31.4831 15.8333C30.5331 13.6 29.1164 12.3333 27.4997 12.25C25.8997 12.1666 24.3497 13.2833 23.1664 15.4166L19.9997 21.1C19.3331 22.3 18.3831 23.0166 17.3497 23.1C16.2997 23.2 15.2497 22.65 14.3997 21.5666L14.0331 21.1C12.8497 19.6166 11.3831 18.9 9.88307 19.05C8.38307 19.2 7.09974 20.2333 6.24974 21.9166L3.36641 27.6666C2.33307 29.75 2.43307 32.1666 3.64974 34.1333C4.86641 36.1 6.98307 37.2833 9.29974 37.2833H30.5664C32.7997 37.2833 34.8831 36.1666 36.1164 34.3C37.3831 32.4333 37.5831 30.0833 36.6997 28.0333Z" fill="#656565" />
                <path d="M11.6167 13.9666C14.7279 13.9666 17.2501 11.4445 17.2501 8.33328C17.2501 5.22208 14.7279 2.69995 11.6167 2.69995C8.50553 2.69995 5.9834 5.22208 5.9834 8.33328C5.9834 11.4445 8.50553 13.9666 11.6167 13.9666Z" fill="#656565" />
              </svg>
              <span className="inline-block text-xs text-dark mt-4 max-w-[200px] text-center">
                Drag a CSV or Excel file into this box or click to upload
              </span>
            </div>
            {file && (
              <figure className="absolute top-2.5 left-2.5 right-2.5 bottom-2.5 rounded-md overflow-hidden bg-white flex items-center justify-center">
                <div className="flex flex-col items-center justify-center">
                  <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                    <path
                      d="M33.3333 6.66667H6.66667C5.19333 6.66667 4 7.86 4 9.33333V30.6667C4 32.14 5.19333 33.3333 6.66667 33.3333H33.3333C34.8067 33.3333 36 32.14 36 30.6667V9.33333C36 7.86 34.8067 6.66667 33.3333 6.66667ZM26.6667 26.6667H13.3333V23.3333H26.6667V26.6667ZM26.6667 20H13.3333V16.6667H26.6667V20ZM26.6667 13.3333H13.3333V10H26.6667V13.3333Z"
                      fill="#656565"
                    />
                  </svg>
                  <span className="text-sm font-medium mt-2">{file.name}</span>
                </div>
                <button
                  className="bg-accent-red-500 flex items-center justify-center h-6 w-6 rounded-full absolute top-2.5 left-2.5"
                  onClick={removeFile}
                >
                  {/* prettier-ignore */}
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <path d="M9 3L3 9" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M3 3L9 9" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>

                <span className="flex absolute bottom-4 rounded-10 bg-black bg-opacity-50 blur-bg text-white text-xxs font-medium py-2 px-4">
                  Click to select new file
                </span>
              </figure>
            )}
          </button>
          <input
            type="file"
            name="csv-file"
            accept=".csv,.xlsx,.xls"
            className="hidden"
            onChange={handleFileChange}
            ref={pickerRef}
          />
        </div>
      </ModalBody>
      <ModalFooter>
        <div className="w-full">
          <AppBtn onClick={handleUpload} isBlock size="lg" disabled={!file || isUploading}>
            {isUploading ? "Importing..." : "Import Products"}
          </AppBtn>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default ImportProductsModal;
