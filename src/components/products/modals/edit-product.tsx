import { useEffect, useMemo, useRef, useState } from "react";
import { getFieldvalues, getProductsCurrency, getUserCountry } from "../../../assets/js/utils/functions";
import { AppBtn, RoundActionBtn } from "../../ui/buttons";
import { DatePickerInput, InputField, SelectDropdown, TextArea } from "../../ui/form-elements";
import Modal, { <PERSON>dalBody, ModalFooter } from "../../ui/modal";
import PickProductMedia from "../pick-product-media";
import ProductCategoriesModal from "./product-categories";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useFetcher, useRequest } from "../../../api/utils";
import { CreateItems, EditItems } from "../../../api/items";
import { CreateItemsParams, EditItemParams } from "../../../api/interfaces/items.interface";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import { toast } from "../../ui/toast";
import {
  ProductItemInterface,
  VariantForm,
  Image,
  InfoBlockInterface,
  Media,
  MediaType,
} from "../../../assets/interfaces";
import { ManageVariants } from "./";
import { useModals } from "../../hooks/useModals";
import VariantsExplainerModal from "../create-products/variants-explainer";
import { actionIsAllowed, SCOPES } from "../../../assets/js/utils/permissions";
import authContext from "../../../contexts/auth-context";
import { convertItemToProduct, getProductThumbnail } from "../../../assets/js/utils/utils";
import MangageProductQuantity from "../create-products/manage-product-quantity";
import DeleteOptionsConfirmation from "./delete-options";
import { Product } from "@/pages/products/create";
import DataAccordion from "@/components/ui/data-accordion";
import InfoBlocksExplainer from "../create-products/info-blocks-explainer";
import useVideoTranscode, { ffmpegContext } from "@/components/hooks/useVideoTranscode";
import VideoUploadCard from "@/components/products/create-products/video-upload-card";
import SelectVideoThumbnail from "@/components/products/modals/process-video/select-video-thumbnail";
import TrimProductVideo from "@/components/products/modals/process-video/trim-video";
import VideoResourcesLoader from "@/components/products/modals/video-resources-loader";
import ManageBlocksInProducts from "../info-blocks/manage-blocks-in-products";
import ProcessVideoModal from "@/components/products/modals/process-video";
import SelectVideo from "@/components/products/create-products/select-video";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  item: ProductItemInterface;
  categories: any[];
  updateItem?: (item: ProductItemInterface) => void;
  updateItemList?: (item: ProductItemInterface) => void;
  isDuplication?: boolean;
  templates?: Product["variants"][];
  infoBlocks: InfoBlockInterface[];
  blocksLoading: boolean;
  blocksError: any;
  setInfoBlocks?: (blocks: InfoBlockInterface[]) => void;
}

const EditProductModal: React.FC<Props> = ({
  show,
  isDuplication = false,
  updateItemList,
  toggle,
  item,
  categories,
  updateItem,
  templates,
  infoBlocks,
  setInfoBlocks,
  blocksLoading,
  blocksError,
}) => {
  const { subscription, store } = authContext.useContainer();
  // const [infoBlocks, setInfoBlocks] = useState<InfoBlockInterface[]>(blocks || []);
  const [showPriceUnit, setShowPriceUnit] = useState(item.discount_price !== undefined);
  const [showCategoriesModal, setShowCategoriesModal] = useState(false);

  const bodyRef = useRef<HTMLDivElement>(null);
  const { modals, toggleModal } = useModals([
    "categories",
    "variants",
    "v_explainer",
    "delete_options",
    "loader",
    "process_video",
  ]);
  const { isLoading, makeRequest, error, response } = useRequest<EditItemParams>(EditItems);
  //const [product, setProduct] = useState<Product>();
  const createReq = useRequest<CreateItemsParams>(CreateItems);
  const allowVariants = actionIsAllowed({
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_OPTIONS,
    plan: subscription?.plan?.type ?? "STARTER",
  });
  const [errMessage, setErrMessage] = useState("");

  /**
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   */
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [videoTrim, setVideoTrim] = useState<any>({ start: 0, end: 30 });

  useEffect(() => {
    form.setValues({ ...convertItemToProduct(item) });
  }, [item]);

  const { ffmpegRef, ffmpegLoading, canTranscode, loadProgress } = ffmpegContext.useContainer();
  const { removeVideoProgress, retryVideoTask, videoProgresses, transcodeVideo } = useVideoTranscode({
    cb: (_, videoKey, url, blob) => {
      if (url) {
        const index = form.values.videos.findIndex((v) => v.meta.id === videoKey);
        if (index >= 0) {
          const videosCopy = [...(form.values.videos ?? [])];
          const newFile = new File([blob], (videosCopy?.[index]?.file as File)?.name ?? "Video");

          videosCopy[index].url = url;
          videosCopy[index].file = newFile;

          form.setFieldValue("videos", videosCopy);
        }
      }
    },
    ffmpegRef,
  });

  const handleOpenVideoModal = (m: string, i: number) => {
    if (ffmpegLoading) {
      toggleModal("loader");
      return;
    }
    toggleModal(m);
    setCurrentVideoIndex(i);
  };

  const saveMedias = (medias: Media[]) => {
    form.setFieldValue("images", [...(form.values.images ?? []), ...medias.filter((m) => m.type === MediaType.IMAGE)]);
    const videos = [...(form.values.videos ?? []), ...medias.filter((m) => m.type === MediaType.VIDEO)];
    form.setFieldValue("videos", videos);
  };

  const removeProductVideo = (index: number, videoKey: string) => {
    const productCopy = { ...form.values };
    if (!productCopy?.videos?.[index]?.file && videoProgresses?.[form.values.id]?.[videoKey])
      removeVideoProgress(form.values.id, videoKey);

    productCopy.videos = productCopy.videos.filter((v, i) => i != index);
    form.setValues(productCopy);
  };

  /**
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   */

  const form = useFormik<Product>({
    initialValues: {
      ...convertItemToProduct(item),
    },
    validationSchema,
    onSubmit: async (values) => {
      setErrMessage("");

      if (values.variants?.options) {
        values.variants.options = values.variants?.options.map((o) => ({ ...o, discount_price: null })) ?? [];
      }
      const payload = {
        ...values,
        info_blocks: values.info_blocks.map((block) => block.id),
        images: values.images.map((i) => i.url),
        price: Number(values.price),
        videos: values.videos?.map((v) => ({ name: v.name, url: v.url, thumbnail: v.meta?.thumbnail?.url })),
        //variants: !allowVariants && {},
      };

      if (payload.images.length < 1) {
        setErrMessage("Please add at least one product image");
        bodyRef.current.scrollTop = 0;
        return;
      }

      if (isDuplication) {
        const [res] = await createReq.makeRequest({
          store: item.store,
          items: [{ ...payload, info_blocks: payload.info_blocks.map((block) => block) }],
        });

        if (res) {
          const duplicatedProduct = { ...res.data[0], category: categories.find((c) => res.data[0].category === c.id) };
          updateItemList(duplicatedProduct);
          bodyRef.current.scrollTop = 0;
          toggle(false);
        }

        return;
      }

      const [response, error] = await makeRequest({ id: item.id, item: payload as any });
      bodyRef.current.scrollTop = 0;

      if (error) {
        return;
      }

      const updatedProduct = { ...response.data, info_blocks: values.info_blocks, category: categories.find((c) => response.data.category === c.id) };
      updateItem(updatedProduct);
      toggle(false);
    },
  });

  const product = form.values;
  const productImageUploading = product.images.some((i) => i.isUploading);

  const saveImages = (images: Image[]) => {
    form.setFieldValue("images", images);
  };

  const changeThumbnail = (e: React.MouseEvent<HTMLElement, MouseEvent>, id: number) => {
    form.setFieldValue("thumbnail", id);
  };

  const removePickedImage = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id) => {
    e.stopPropagation();

    if (id === product.thumbnail) {
      toast.error({
        title: "Error!",
        message: `Cannot delete thumbnail image.`,
      });
      return;
    }

    const productCopy = { ...product };
    productCopy.thumbnail = product.thumbnail > id ? product.thumbnail - 1 : product.thumbnail;
    productCopy.images.splice(id, 1);
    form.setValues(productCopy);
  };

  const updateVariants = (variants: VariantForm) => {
    form.setFieldValue("variants", variants);
    form.handleSubmit();
  };

  const deleteOptions = () => {
    if (modals.delete_options.show) {
      toggleModal("delete_options");
    }

    form.setFieldValue("variants", { ...form.values.variants, options: [] });
  };

  const formContent = {
    title: isDuplication ? "" : "Edit",
    success: isDuplication ? "created" : "updated",
    button: isDuplication ? "Save Item" : "Save Updates",
    buttonIsLoading: isDuplication ? "Saving Item..." : "Saving Updates...",
  };

  const canUploadVideos = actionIsAllowed({
    planPermission: SCOPES.PRODUCTS.UPLOAD_VIDEOS,
    plan: subscription?.plan?.type ?? "STARTER",
  });

  return (
    <>
      <Modal {...{ show, toggle }} title={`${formContent.title} Product - ${form.values.name}`} size="midi">
        <form className="flex flex-col flex-auto overflow-hidden" onSubmit={form.handleSubmit}>
          <ModalBody setRef={(ref) => (bodyRef.current = ref.current)}>
            <ErrorLabel error={errMessage ?? error?.message ?? createReq.error?.message} />
            <SuccessLabel message={response ?? createReq.response ? `Item successfully ${formContent.success}!` : ""} />
            <PickProductMedia
              {...{
                product: form.values,
                changeThumbnail,
                removePickedMedia: removePickedImage,
                saveMedias: saveMedias,
                thumbnail: product.thumbnail,
                saveImages: saveImages,
                canProcessVideos: canTranscode,
              }}
            />
            {form.values?.videos?.length > 0 && (
              <DataAccordion
                isClosed={false}
                className="border-b border-grey-divider rounded-none"
                title={
                  <span className="text-base font-bold font-body tracking-normal">
                    <span className="font-display">Product Videos</span>
                  </span>
                }
              >
                <div className="space-y-2.5 pt-2.5">
                  {form.values.videos.map((v, i) => (
                    <VideoUploadCard
                      key={i}
                      retryTask={() => retryVideoTask(product.id, v.meta.id)}
                      videoProgress={videoProgresses?.value?.[product.id]?.[v.meta.id]}
                      openVideoModal={(m) => handleOpenVideoModal(m, i)}
                      removeVideo={() => removeProductVideo(i, v.key)}
                      video={v}
                      error={form.touched.videos && (form.errors?.videos?.[i] ? "Select a thumbnail" : undefined)}
                    />
                  ))}
                  <SelectVideo
                    product={form.values}
                    saveMedias={saveMedias}
                    canUploadVideos={canUploadVideos}
                    canProcessVideos={canTranscode}
                  />
                </div>
              </DataAccordion>
            )}
            {form.values?.videos?.length < 1 && (
              <div className="mt-5 border-t border-grey-divider pt-3.5">
                <SelectVideo
                  product={form.values}
                  saveMedias={saveMedias}
                  canUploadVideos={canUploadVideos}
                  canProcessVideos={canTranscode}
                />
              </div>
            )}
            <InputField label="Product Name" {...getFieldvalues("name", form)} />
            <InputField
              label={`Product Price in ${getProductsCurrency()}`}
              min={1}
              {...getFieldvalues("price", form)}
            />
            <SelectDropdown
              label="Select product category"
              options={[...categories].map((c) => ({ text: `${c.emoji} ${c.name}`, value: c.id }))}
              emptyLabel="No categories to show"
              action={{ label: "Create new categories +", onClick: () => setShowCategoriesModal(true) }}
              {...getFieldvalues("category", form)}
            />
            <TextArea label="Product Description" {...getFieldvalues("description", form)} />
            {/* {allowVariants && ( */}
            <div className="mt-3 flex items-center justify-between">
              <div className="flex items-center">
                <AppBtn color="neutral" size="sm" onClick={() => toggleModal("variants")} disabled={!allowVariants}>
                  {form.values.variants?.options.length > 0 ? `Update Options` : "Add Options"}
                </AppBtn>
                <span className="text-dark text-1xs inline-block ml-1.5 font-medium">
                  {form.values.variants?.options.length} Options added.
                </span>
              </div>
              {form.values.variants?.options.length > 0 && (
                <RoundActionBtn
                  icon="delete"
                  className="text-accent-red-500"
                  onClick={() => toggleModal("delete_options")}
                />
              )}
            </div>
            <div className="mt-3.5 border-t border-grey-divider">
              <div className="flex items-center justify-between bg-grey-fields-100 rounded-10 py-2 px-2.5 mt-3.5">
                <div className="flex items-center">
                  <figure className="h-6 w-6 bg-accent-yellow-500 flex items-center justify-center text-white rounded-full">
                    {/* prettier-ignore */}
                    <svg width="55%" viewBox="0 0 24 24" fill="none">
                      <path d="M18.0204 12.33L16.8004 11.11C16.5104 10.86 16.3404 10.49 16.3304 10.08C16.3104 9.63 16.4904 9.18 16.8204 8.85L18.0204 7.65C19.0604 6.61 19.4504 5.61 19.1204 4.82C18.8004 4.04 17.8104 3.61 16.3504 3.61H5.90039V2.75C5.90039 2.34 5.56039 2 5.15039 2C4.74039 2 4.40039 2.34 4.40039 2.75V21.25C4.40039 21.66 4.74039 22 5.15039 22C5.56039 22 5.90039 21.66 5.90039 21.25V16.37H16.3504C17.7904 16.37 18.7604 15.93 19.0904 15.14C19.4204 14.35 19.0404 13.36 18.0204 12.33Z" fill="currentColor"/>
                    </svg>
                  </figure>

                  <span className="text-dark text-sm font-medium inline-block ml-2">What are product options?</span>
                </div>
                <button
                  className="flex items-center text-primary-500 font-medium text-1xs"
                  onClick={() => toggleModal("v_explainer")}
                  type="button"
                >
                  Learn More
                  {/* prettier-ignore */}
                  <svg className="w-[9px] ml-1" viewBox="0 0 10 10" fill="none">
                    <path d="M1.24264 0.24265V1.73818L7.16643 1.74348L0.71231 8.1976L1.77297 9.25826L8.22709 2.80414L8.23239 8.72793H9.72792V0.24265H1.24264Z" fill="#332089"/>
                  </svg>
                </button>
              </div>
            </div>
            <div className="mt-5 border-t border-grey-divider pt-3.75" onClick={(e) => e.preventDefault()}>
              <MangageProductQuantity store={store} form={form} />
            </div>
            <ManageBlocksInProducts
              form={form}
              infoBlocks={infoBlocks}
              blocksLoading={blocksLoading}
              setInfoBlocks={setInfoBlocks}
              blocksError={blocksError}
            />
            <div className="mt-5 border-t border-grey-divider pt-3.5" onClick={(e) => e.preventDefault()}>
              <DataAccordion
                isClosed={true}
                title={
                  <span className="text-base font-bold font-body tracking-normal">
                    <span className="font-display">Optional Product Settings</span>
                  </span>
                }
              >
                <div className="mt-3.5">
                  <InputField
                    label={`Discount Price ${getProductsCurrency()} (No commas)`}
                    type="number"
                    {...getFieldvalues("discount_price", form)}
                    inputMode="numeric"
                  />
                  <InputField
                    label={`How much did you buy/make this product?`}
                    type="number"
                    {...getFieldvalues("cost_price", form)}
                    inputMode="numeric"
                  />
                  <InputField
                    type="number"
                    label="What's the min. quantity customers can buy?"
                    {...getFieldvalues("minimum_order_quantity", form)}
                    inputMode="numeric"
                  />
                  <DatePickerInput label="Expiry date (Food, Drugs, e.t.c)" {...getFieldvalues("expiry_date", form)} />
                </div>
              </DataAccordion>
            </div>
          </ModalBody>
          <ModalFooter>
            <AppBtn isBlock type="submit" disabled={isLoading || productImageUploading} size="lg">
              {productImageUploading && "Uploading images..."}
              {!productImageUploading && (isLoading ? formContent.buttonIsLoading : formContent.button)}
            </AppBtn>
          </ModalFooter>
        </form>
      </Modal>
      <ProductCategoriesModal show={modals.categories.show} toggle={() => toggleModal("categories")} />
      <ManageVariants
        show={modals.variants.show}
        toggle={() => toggleModal("variants")}
        title=""
        product={form.values}
        saveVariants={updateVariants}
        templates={templates}
      />
      <VariantsExplainerModal title="" show={modals.v_explainer.show} toggle={() => toggleModal("v_explainer")} />

      {form.values.videos?.length > 0 && (
        <>
          <ProcessVideoModal
            currentVideo={form.values.videos?.[currentVideoIndex]}
            onTrim={(start, end) => {
              setVideoTrim({ start, end });
            }}
            show={modals.process_video.show}
            toggle={() => toggleModal("process_video")}
            setVideo={(video) => {
              const videosCopy = [...form.values.videos];
              videosCopy[currentVideoIndex] = video;
              form.setFieldValue("videos", videosCopy);
            }}
            callback={() => {
              if (!getProductThumbnail(form.values)) {
                form.setFieldValue("thumbnail_type", "video");
              }
            }}
            transcodeVideo={(dims) => {
              transcodeVideo({
                dimensions: dims,
                trim: videoTrim,
                taskId: form.values?.id,
                videoKey: form.values.videos[currentVideoIndex].meta.id,
                file: form.values.videos[currentVideoIndex].file as any,
                meta: {
                  videoIndex: currentVideoIndex,
                  productIndex: 0,
                },
              });
            }}
          />
        </>
      )}

      <VideoResourcesLoader
        toggle={() => toggleModal("loader")}
        show={modals.loader.show}
        loadProgress={loadProgress}
      />
      <DeleteOptionsConfirmation
        show={modals.delete_options.show}
        toggle={() => toggleModal("delete_options")}
        deleteOptions={deleteOptions}
      />
    </>
  );
};

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Item name is required"),
  description: Yup.string().required("Item description is required"),
  price: Yup.string()
    .required("Item price is required")
    .test("digits", "Item price should be a number", (value) => !Number.isNaN(Number(value))),
  discount_price: Yup.number()
    .optional()
    .test(
      "islesser",
      "Discount price should be lesser than item price",
      (value, ctx) => value === undefined || Number(ctx.parent.price) > Number(value)
    )
    .min(1, "Price must be greater than 0")
    .integer("Price must be a number"),
});

export default EditProductModal;
