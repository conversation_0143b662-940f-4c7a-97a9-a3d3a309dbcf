import React, { useEffect, useState } from "react";
import { ProductItemInterface } from "../../../assets/interfaces";
import { getItemThumbnail } from "../../../assets/js/utils/utils";
import LazyImage from "../../lazy-image";
import Checkbox from "../../ui/form-elements/checkbox";
import SearchBar from "../../ui/search-bar";

interface SelectProductsProps {
  items: ProductItemInterface[];
  selectedItems: string[];
  setSelectedItems: (items: string[]) => void;
}

const SelectProducts: React.FC<SelectProductsProps> = ({ items, selectedItems, setSelectedItems }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredItems, setFilteredItems] = useState<ProductItemInterface[]>([]);
  const hasSelectedAll = selectedItems.length == filteredItems.length;

  /* SEARCH LOGIC */
  useEffect(() => {
    if (searchQuery === "") {
      setFilteredItems(items);
      return;
    }

    searchQuery.trim();
    const splitQueries = searchQuery.split(" ");

    const filteredItems = items.filter((item) => {
      let match = false;
      splitQueries.map((query) => {
        if (item.name.toLocaleLowerCase().includes(query)) match = true;
      });

      return match;
    });

    setFilteredItems(filteredItems);
  }, [searchQuery, items]);

  const isSelectedItem = (id: string) => selectedItems.includes(id);

  /* ITEM TOGGLE LOGIC */
  const toggleItem = (item: ProductItemInterface) => {
    const selectedIndex = selectedItems.indexOf(item.id);
    const selectedCopy = [...selectedItems];

    if (selectedIndex > -1) {
      selectedCopy.splice(selectedIndex, 1);
    } else {
      selectedCopy.push(item.id);
    }
    setSelectedItems(selectedCopy);
  };

  const selectAll = () => {
    let newSelected = [];

    if (!hasSelectedAll) {
      newSelected = items.map((i) => i.id);
    }

    setSelectedItems(newSelected);
  };

  return (
    <div>
      <div className="">Select products you want to import</div>
      <div className="sticky -top-7 z-50 bg-white pt-3">
        <SearchBar {...{ searchQuery, setSearchQuery }} />
        <div className="flex items-center justify-between mt-1">
          <div className="flex items-center w-[fit-content] mt-1 pb-2">
            <Checkbox
              onChange={selectAll}
              checked={hasSelectedAll}
              name="select_all"
              className="mr-1"
              id="select_all"
              // neutral
            ></Checkbox>
            <span className="text-sm text-gray-500">Select All</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4">
        {filteredItems.map((item, index) => (
          <div
            key={index}
            className="w-fit flex items-center py-4 pr-4 border-b-[1px] border-gray-100 last:border-0 cursor-pointer"
            onClick={() => toggleItem(item)}
          >
            <figure className=" flex-shrink-0 h-9 w-9 rounded-5 overflow-hidden mr-3 relative">
              <LazyImage
                src={getItemThumbnail(item)}
                className="h-full w-full object-cover rounded-5 relative z-10"
                alt={item.name}
              />
            </figure>
            <span className="w-[60%] mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis">{item.name}</span>
            <Checkbox
              onChange={() => toggleItem(item)}
              checked={isSelectedItem(item.id)}
              id={item.id}
              name={item.name}
              // neutral
              className="ml-auto"
              round
            ></Checkbox>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SelectProducts;
