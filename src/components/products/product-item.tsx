import React from "react";
import { toAppUrl, toCurrency } from "../../assets/js/utils/functions";
import { getItemThumbnail } from "../../assets/js/utils/utils";
import LazyImage from "../lazy-image";
import { TableCell, TableRow } from "../ui/table";
import Toggle from "../ui/toggle";
import { toast, ToastWithPromiseOptions } from "../ui/toast";
import { ProductItemInterface } from "../../assets/interfaces";
import IconButton from "../ui/icon-button";
import router from "next/router";
import { RoundActionBtn } from "../ui/buttons";
import Badge, { BadgeColor } from "../ui/badge";
import { useRequest } from "@/api/utils";
import { ConvertImagesToStrings } from "@/api";
import useShare from "../hooks/useShare";
import Dropdown, { DropdownItem } from "../ui/dropdown-new";
import Link from "next/link";
import authContext from "@/contexts/auth-context";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";

interface Props {
  product: ProductItemInterface;
  handleAvailabilityToggle: (available: boolean) => void;
  handleFeatureUpdate: (is_featured: boolean) => void;
  toggleModal: (key: string) => void;
  isStats?: boolean;
  available: boolean;
  itemsHaveQuantity?: boolean;
  copyLink: VoidFunction;
  share: () => Promise<void>;
  selectAffiliate: () => void;
  canManageAffiliates: boolean;
}

const ProductItem: React.FC<Props> = ({
  product,
  handleAvailabilityToggle,
  toggleModal,
  isStats,
  available,
  itemsHaveQuantity,
  copyLink,
  share,
  handleFeatureUpdate,
  selectAffiliate,
  canManageAffiliates,
}) => {
  const { userRole } = authContext.useContainer();
  const {
    name,
    description,
    thumbnail,
    price,
    category,
    views = 0,
    images,
    variants,
    slug,
    is_always_available,
    discount_price,
    quantity,
    is_featured,
    total_orders,
  } = product;

  const canEditProducts = actionIsAllowed({
    userRole,
    permission: SCOPES.PRODUCTS.EDIT_PRODUCTS,
  });

  const handlePaymentLinkClick = () => {
    // Route to payment link form with prefilled data
    router.push({
      pathname: "/payments/payment-links/create",
      query: {
        amount: discount_price || price,
        narration: `Payment for ${name}`,
      },
    });
  };

  // const { share } = useShare({
  //   title: name,
  //   text: `${name}\nPrice: ${toCurrency(discount_price ? discount_price : price)}\nClick here to buy: ${toAppUrl(
  //     `/products/${slug}`,
  //     true,
  //     true
  //   )}`,
  //   images,
  // });

  const dropdownItems: DropdownItem[] = [
    {
      text: is_featured ? "Item Is Featured" : "Feature Item",
      onClick: () => handleFeatureUpdate(!is_featured),
      icon: (
        <RoundActionBtn size="sm" className={is_featured ? "text-accent-green-500" : ""}>
          {/* prettier-ignore */}
          <svg width="50%" viewBox="0 0 24 24" fill="none">
            <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </RoundActionBtn>
      ),
      className: is_featured ? "text-accent-green-500" : "",
    },
    {
      text: "Create Payment Link",
      onClick: handlePaymentLinkClick,
      icon: <RoundActionBtn size="sm" icon="link" />,
    },
    {
      text: "Copy Link",
      link: undefined,
      onClick: copyLink,
      icon: <RoundActionBtn size="sm" icon="copy" />,
    },
    canManageAffiliates
      ? {
          text: "Copy Affiliate Link",
          link: undefined,
          onClick: selectAffiliate,
          icon: <RoundActionBtn size="sm" icon="copy" />,
        }
      : null,
    {
      text: "Duplicate Item",
      link: undefined,
      onClick: () => {
        toggleModal("duplicate");
      },
      icon: <RoundActionBtn size="sm" icon="copy_doc" />,
    },
    {
      text: "Share Item",
      link: undefined,
      onClick: share,
      icon: <RoundActionBtn size="sm" icon="route" />,
    },
    canEditProducts
      ? {
          text: "Delete Item",
          link: undefined,
          onClick: () => {
            toggleModal("delete");
          },
          icon: <RoundActionBtn size="sm" icon="delete" className="text-accent-red-500" />,
          className: "text-accent-red-500",
        }
      : null,
  ].filter(Boolean) as DropdownItem[];

  return (
    <TableRow onClick={() => router.push(`/products/${slug}`)}>
      <TableCell stopBubble>
        <Link href={`/products/${slug}`}>
          <a className="flex items-center no-underline">
            <figure className="h-7.5 w-7.5 rounded-5 overflow-hidden mr-2.5 relative">
              <LazyImage
                src={getItemThumbnail(product)}
                className="h-full w-full object-cover rounded-5 relative z-10"
                alt={name}
              />
            </figure>
            <span className="text-black text-sm inline-block font-medium max-w-[120px] sm:max-w-[180px] overflow-hidden overflow-ellipsis whitespace-nowrap">
              {name}
            </span>
            {is_featured && (
              <div title="Featured Item" className="ml-1.25">
                {/* prettier-ignore */}
                <svg className="w-5 text-accent-yellow-500"viewBox="0 0 24 24" fill="none" >
                  <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM15.53 13.23L10.35 16.82C9.59 17.35 9.15 17.04 9.37 16.15L10.32 12.31L8.67 11.9C7.92 11.72 7.83 11.2 8.46 10.76L13.64 7.17C14.4 6.64 14.84 6.95 14.62 7.84L13.67 11.68L15.32 12.09C16.07 12.28 16.16 12.79 15.53 13.23Z" fill="currentColor" />
                </svg>
              </div>
            )}
          </a>
        </Link>
      </TableCell>
      {isStats && (
        <TableCell className="font-medium" stopBubble>
          <Link href={`/products/${slug}`}>
            <a className="no-underline">{total_orders}</a>
          </Link>
        </TableCell>
      )}
      {!isStats && itemsHaveQuantity && (
        <TableCell className=" hidden sm:table-cell">
          <Link href={`/products/${slug}`}>
            <a className="no-underline">
              {quantity > -1 || is_always_available ? (
                <QuantityDisplay is_always_available={is_always_available} quantity={quantity} />
              ) : (
                "-"
              )}
            </a>
          </Link>
        </TableCell>
      )}
      {!isStats && !itemsHaveQuantity && (
        <TableCell className=" hidden sm:table-cell">
          <Link href={`/products/${slug}`}>
            <a className="no-underline">{category ? `${category.emoji} ${category.name}` : "-"}</a>
          </Link>
        </TableCell>
      )}
      <TableCell className=" hidden sm:table-cell">
        <Link href={`/products/${slug}`}>
          <a className="no-underline">
            <span className="text-black-secondary font-medium">{toCurrency(price)}</span>
          </a>
        </Link>
      </TableCell>

      <TableCell className=" hidden sm:table-cell">
        <Link href={`/products/${slug}`}>
          <a className="no-underline">
            {variants && variants?.options?.length > 0 ? (
              <button onClick={() => toggleModal("variants")}>
                <Badge size="sm" text={`${variants?.options.length} options`} color="dark" />
              </button>
            ) : (
              "-"
            )}
          </a>
        </Link>
      </TableCell>
      <TableCell className={!isStats ? "" : "hidden sm:table-cell"} stopBubble>
        <div className="flex items-center space-x-2">
          <RoundActionBtn grey icon="copy" onClick={copyLink} />
          {/* <RoundActionBtn grey icon="route" onClick={share} /> */}
          {canEditProducts && <RoundActionBtn grey icon="edit" onClick={() => toggleModal("edit")} />}
          {/* <RoundActionBtn grey icon="copy_doc" onClick={() => toggleModal("duplicate")} /> */}
          {/* <RoundActionBtn grey icon="delete" onClick={() => toggleModal("delete")} /> */}
          <div className="relative">
            <Dropdown items={dropdownItems} vPosition="BOTTOM" gap={false}>
              <RoundActionBtn
                grey
                icon="options"
                onClick={() => toggleModal("edit")}
                className="z-[999] dropdown-toggle !text-black-secondary"
              />
            </Dropdown>
          </div>
        </div>
      </TableCell>
      <TableCell stopBubble>
        <Toggle intialState={available} onChange={handleAvailabilityToggle} />
      </TableCell>
    </TableRow>
  );
};

interface IProps {
  is_always_available?: boolean;
  quantity?: number;
}

export const QuantityDisplay: React.FC<IProps> = ({ is_always_available, quantity }) => {
  const badgeText = is_always_available ? "Infinite" : quantity === 0 ? "OUT OF STOCK" : `${quantity} IN STOCK`;
  const badgeColor: BadgeColor = is_always_available ? "green" : quantity === 0 ? "red" : "dark";

  return <Badge text={badgeText} color={badgeColor} size="sm" />;
};

export default ProductItem;
