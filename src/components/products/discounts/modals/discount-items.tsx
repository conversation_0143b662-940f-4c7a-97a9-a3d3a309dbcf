import { toCurrency } from "@/assets/js/utils/functions";
import { getItemThumbnail } from "@/assets/js/utils/utils";
import { DiscountItemInterface, ProductItemInterface } from "@/assets/interfaces";
import LazyImage from "@/components/lazy-image";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { useEffect, useState } from "react";
import { useRequest } from "@/api/utils";
import { GetDiscountItems } from "@/api";
import { GetDiscountItemParams } from "@/api/interfaces";
import useFluxState from "@/components/hooks/useFluxState";
import ContentState from "@/components/ui/content-state";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  openEditModal: () => void;
  discount: DiscountItemInterface;
}

const DiscountItems: React.FC<Props> = ({ show, toggle, openEditModal, discount }) => {
  const { isLoading, makeRequest, error, response } = useRequest<GetDiscountItemParams>(GetDiscountItems);
  const [items, setItems] = useFluxState<ProductItemInterface[]>(
    ((response?.data as ProductItemInterface[]) ?? []).map((i) => {
      const discount_amount = Math.min(
        (discount?.percentage / 100) * i.price,
        discount.discount_cap ? discount.discount_cap : Number.POSITIVE_INFINITY
      );

      return {
        ...i,
        discount_price: Math.ceil(i.price - discount_amount),
      };
    }),
    [response]
  );

  useEffect(() => {
    if (show && !response) {
      getItems();
    }
  }, [show]);

  const getItems = async () => {
    const [res, err] = await makeRequest({ id: discount?.id });
  };

  return (
    <Modal {...{ show, toggle }} title="Discount items" bgClose={false}>
      <ModalBody className="relative !pt-0">
        {(isLoading || error || items.length === 0) && (
          <ContentState
            title="No Items to show"
            loadingText="Loading Items..."
            errorTitle="Couldn't fetch items"
            errorMessage="Something went wrong & we couldn't fetch items for this discount"
            errorAction={
              <AppBtn size="sm" onClick={getItems}>
                Reload Items
              </AppBtn>
            }
            error={error}
            isEmpty={items.length === 0}
            isLoading={isLoading}
            small
          />
        )}
        {!isLoading && !error && items.length > 0 && (
          <div>
            {items.map((item, index) => (
              <div
                key={index}
                className="w-full flex items-center justify-between py-4 pr-4 border-b-[1px] border-grey-border border-opacity-50 last:border-0"
              >
                <div className="flex items-center">
                  <figure className="h-11.25 w-11.25 rounded-10 overflow-hidden mr-3 relative">
                    <LazyImage
                      src={getItemThumbnail(item)}
                      className="h-full w-full object-cover rounded-5 relative z-10"
                      alt={item.name}
                    />
                  </figure>
                  <div className="">
                    <h4 className="text-sm font-body text-dark mb-0.5 font-medium">{item.name}</h4>
                    <span className="text-1xs font-semibold text-black-secondary">{toCurrency(item.price)}</span>
                  </div>
                </div>
                <div className="">
                  <h4 className="text-sm font-body text-dark mb-0.5">Discount Price</h4>
                  <span className="text-1xs font-semibold text-black-secondary">{toCurrency(item.discount_price)}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <AppBtn onClick={openEditModal} isBlock disabled={isLoading || !!error || items.length === 0} size="lg">
          Edit Discount
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default DiscountItems;
