import { toCurrency } from "@/assets/js/utils/functions";
import { DiscountItemInterface, ProductItemInterface } from "@/assets/interfaces";
import { checkIfExpired, getDateRangeText } from "@/assets/js/utils/utils";
import { useModals } from "@/components/hooks/useModals";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import Badge from "@/components/ui/badge";
import DiscountItems from "./discount-items";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  openEditModal: () => void;
  storeItems: ProductItemInterface[];
  value: DiscountItemInterface;
}

const DiscountDetailsModal: React.FC<Props> = ({ show, toggle, value, storeItems, openEditModal }) => {
  const { percentage, label, start_date, end_date, items, id, discount_cap, active } = value;
  const { modals, toggleModal } = useModals(["products"]);
  // const discountItems = storeItems.filter((item) => items.includes(item.id));

  return (
    <>
      {" "}
      <Modal {...{ show, toggle }} title="Discount Details" size="midi">
        <ModalBody>
          <div className="flex items-center justify-between border-b border-grey-border border-opacity-50 pb-4">
            <div className="mt-2">
              <h4 className="text-base sm:text-lg font-semibold -mb-1"> {label} </h4>
              <span className="text-1xs sm:text-sm text-dark">Discount Label</span>
            </div>
            <div className="bg-accent-green-500 rounded-full h-11.5 w-11.5 text-white flex items-center justify-center">
              {discountDetailsIcons.id}
            </div>
          </div>
          <div className="flex items-center justify-between border-b border-grey-border border-opacity-50 py-4">
            <div className="mt-2">
              <h4 className="text-base sm:text-lg font-semibold -mb-1"> {`${percentage}%`} </h4>
              <span className="text-1xs sm:text-sm text-dark">Discount Percent</span>
            </div>
            <div className="bg-accent-yellow-500 rounded-full h-11.5 w-11.5 text-white flex items-center justify-center">
              {discountDetailsIcons.percentage}
            </div>
          </div>
          {discount_cap && (
            <div className="flex items-center justify-between border-b border-grey-border border-opacity-50 py-4">
              <div className="mt-2">
                <h4 className="text-base sm:text-lg font-semibold -mb-1"> {`${toCurrency(discount_cap)}`} </h4>

                <span className="text-1xs sm:text-sm text-dark">Discount Cap</span>
              </div>
              <div className="bg-accent-red-500 rounded-full h-11.5 w-11.5 text-white flex items-center justify-center">
                {discountDetailsIcons.percentage}
              </div>
            </div>
          )}

          <div className="py-4">
            <div className="flex items-center justify-between  py-2">
              <div className="flex items-center">
                <div className="text-grey-subtext p-1.5 rounded-full bg-grey-fields-200">
                  {discountDetailsIcons.status}
                </div>{" "}
                <span className="ml-2 text-gray-600 text-1xs sm:text-sm">Status</span>
              </div>
              <Badge
                text={checkIfExpired(end_date) ? "Expired" : active ? "Active" : "Inactive"}
                color={checkIfExpired(end_date) ? "red" : active ? "green" : "red"}
              ></Badge>
            </div>

            <div className="flex items-center justify-between  py-2">
              <div className="flex items-center">
                <div className="text-grey-subtext p-1.5 rounded-full bg-grey-fields-200">
                  {discountDetailsIcons.date}
                </div>
                <span className=" ml-2 text-gray-600 text-1xs sm:text-sm">Start & End Date</span>
              </div>
              <span className={`text-black-secondary font-medium text-1xs sm:text-sm`}>
                {getDateRangeText(new Date(start_date), new Date(end_date), "ddmm")}
              </span>
            </div>

            <div className="flex items-center justify-between py-2">
              <div className="flex items-center">
                <div className="text-grey-subtext p-1.5 rounded-full bg-grey-fields-200">
                  {discountDetailsIcons.products}
                </div>
                <span className="ml-2 text-gray-600 text-1xs sm:text-sm">No of products applied</span>
              </div>
              <div className="flex items-center cursor-pointer select-none" onClick={() => toggleModal("products")}>
                <span className={`mr-1.5 font-semibold text-primary-500 text-1xs sm:text-sm`}>
                  {items.length} products
                </span>
                {/* prettier-ignore */}
                <svg viewBox="0 0 14 15" fill="none" className="w-3">
                  <path d="M1 7.5L13 7.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M7 13.5L13 7.5L7 1.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <AppBtn onClick={openEditModal} isBlock size="lg">
            Edit discount
          </AppBtn>
        </ModalFooter>
      </Modal>
      <DiscountItems
        show={modals.products.show}
        openEditModal={openEditModal}
        toggle={() => toggleModal("products")}
        discount={value}
      ></DiscountItems>
    </>
  );
};

export const discountDetailsIcons = {
  id: (
    <>
      {/* prettier-ignore */}
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" >
        <path d="M21.5299 10.8699L20.0099 9.34988C19.7499 9.08988 19.5399 8.57988 19.5399 8.21988V6.05988C19.5399 5.17988 18.8199 4.45988 17.9399 4.45988H15.7899C15.4299 4.45988 14.9199 4.24988 14.6599 3.98988L13.1399 2.46988C12.5199 1.84988 11.4999 1.84988 10.8799 2.46988L9.33988 3.98988C9.08988 4.24988 8.57988 4.45988 8.20988 4.45988H6.05988C5.17988 4.45988 4.45988 5.17988 4.45988 6.05988V8.20988C4.45988 8.56988 4.24988 9.07988 3.98988 9.33988L2.46988 10.8599C1.84988 11.4799 1.84988 12.4999 2.46988 13.1199L3.98988 14.6399C4.24988 14.8999 4.45988 15.4099 4.45988 15.7699V17.9199C4.45988 18.7999 5.17988 19.5199 6.05988 19.5199H8.20988C8.56988 19.5199 9.07988 19.7299 9.33988 19.9899L10.8599 21.5099C11.4799 22.1299 12.4999 22.1299 13.1199 21.5099L14.6399 19.9899C14.8999 19.7299 15.4099 19.5199 15.7699 19.5199H17.9199C18.7999 19.5199 19.5199 18.7999 19.5199 17.9199V15.7699C19.5199 15.4099 19.7299 14.8999 19.9899 14.6399L21.5099 13.1199C22.1599 12.5099 22.1599 11.4899 21.5299 10.8699ZM7.99988 8.99988C7.99988 8.44988 8.44988 7.99988 8.99988 7.99988C9.54988 7.99988 9.99988 8.44988 9.99988 8.99988C9.99988 9.54988 9.55988 9.99988 8.99988 9.99988C8.44988 9.99988 7.99988 9.54988 7.99988 8.99988ZM9.52988 15.5299C9.37988 15.6799 9.18988 15.7499 8.99988 15.7499C8.80988 15.7499 8.61988 15.6799 8.46988 15.5299C8.17988 15.2399 8.17988 14.7599 8.46988 14.4699L14.4699 8.46988C14.7599 8.17988 15.2399 8.17988 15.5299 8.46988C15.8199 8.75988 15.8199 9.23988 15.5299 9.52988L9.52988 15.5299ZM14.9999 15.9999C14.4399 15.9999 13.9899 15.5499 13.9899 14.9999C13.9899 14.4499 14.4399 13.9999 14.9899 13.9999C15.5399 13.9999 15.9899 14.4499 15.9899 14.9999C15.9899 15.5499 15.5499 15.9999 14.9999 15.9999Z" fill="white"/>
      </svg>
    </>
  ),
  percentage: (
    <>
      {/* prettier-ignore */}
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM6.73 5.66C7.54 5.66 8.21 6.32 8.21 7.14C8.21 7.95 7.55 8.62 6.73 8.62C5.92 8.62 5.25 7.96 5.25 7.14C5.25 6.32 5.91 5.66 6.73 5.66ZM6.85 13.8C6.7 13.95 6.51 14.02 6.32 14.02C6.13 14.02 5.94 13.95 5.79 13.8C5.5 13.51 5.5 13.03 5.79 12.74L12.34 6.19C12.63 5.9 13.11 5.9 13.4 6.19C13.69 6.48 13.69 6.96 13.4 7.25L6.85 13.8ZM13.27 14.34C12.46 14.34 11.79 13.68 11.79 12.86C11.79 12.05 12.45 11.38 13.27 11.38C14.08 11.38 14.75 12.04 14.75 12.86C14.75 13.68 14.09 14.34 13.27 14.34Z" fill="white"/>
      </svg>
    </>
  ),
  money: (
    <>
      {/* prettier-ignore */}
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M17 4H7C4 4 2 5.5 2 9V12.56C2 12.93 2.38 13.16 2.71 13.01C3.69 12.56 4.82 12.39 6.01 12.6C8.64 13.07 10.57 15.51 10.5 18.18C10.49 18.6 10.43 19.01 10.32 19.41C10.24 19.72 10.49 20.01 10.81 20.01H17C20 20.01 22 18.51 22 15.01V9C22 5.5 20 4 17 4ZM12 14.5C10.62 14.5 9.5 13.38 9.5 12C9.5 10.62 10.62 9.5 12 9.5C13.38 9.5 14.5 10.62 14.5 12C14.5 13.38 13.38 14.5 12 14.5ZM19.25 14C19.25 14.41 18.91 14.75 18.5 14.75C18.09 14.75 17.75 14.41 17.75 14V10C17.75 9.59 18.09 9.25 18.5 9.25C18.91 9.25 19.25 9.59 19.25 10V14Z" fill="white"/>
        <path d="M5 14C2.79 14 1 15.79 1 18C1 18.75 1.21 19.46 1.58 20.06C2.27 21.22 3.54 22 5 22C6.46 22 7.73 21.22 8.42 20.06C8.79 19.46 9 18.75 9 18C9 15.79 7.21 14 5 14ZM6.97 17.67L4.84 19.64C4.7 19.77 4.51 19.84 4.33 19.84C4.14 19.84 3.95 19.77 3.8 19.62L2.81 18.63C2.52 18.34 2.52 17.86 2.81 17.57C3.1 17.28 3.58 17.28 3.87 17.57L4.35 18.05L5.95 16.57C6.25 16.29 6.73 16.31 7.01 16.61C7.29 16.91 7.27 17.39 6.97 17.67Z" fill="white"/>
      </svg>
    </>
  ),
  status: (
    <>
      {/* prettier-ignore */}
      <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
        <path d="M1.53125 9.35645C2.2 11.5064 4 13.1627 6.2375 13.6189" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M1.28125 6.8625C1.6 3.70625 4.2625 1.25 7.5 1.25C10.7375 1.25 13.4 3.7125 13.7187 6.8625" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.75586 13.6252C10.9871 13.1689 12.7809 11.5314 13.4621 9.3877" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    </>
  ),
  date: (
    <>
      {/* prettier-ignore */}
      <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
        <path d="M5 1.25V3.125" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M10 1.25V3.125" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M2.1875 5.68115H12.8125" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M13.125 5.3125V10.625C13.125 12.5 12.1875 13.75 10 13.75H5C2.8125 13.75 1.875 12.5 1.875 10.625V5.3125C1.875 3.4375 2.8125 2.1875 5 2.1875H10C12.1875 2.1875 13.125 3.4375 13.125 5.3125Z" stroke="#656565" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M9.80907 8.5625H9.81468" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M9.80907 10.4375H9.81468" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M7.49754 8.5625H7.50316" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M7.49754 10.4375H7.50316" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M5.18407 8.5625H5.18968" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M5.18407 10.4375H5.18968" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    </>
  ),
  products: (
    <>
      {/* prettier-ignore */}
      <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
        <path d="M1.9812 4.6499L7.49995 7.84365L12.9812 4.66865" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M7.5 13.5062V7.8374" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M6.20615 1.54981L2.86865 3.39981C2.1124 3.81856 1.49365 4.86856 1.49365 5.73106V9.26231C1.49365 10.1248 2.1124 11.1748 2.86865 11.5936L6.20615 13.4498C6.91865 13.8436 8.0874 13.8436 8.7999 13.4498L12.1374 11.5936C12.8937 11.1748 13.5124 10.1248 13.5124 9.26231V5.73106C13.5124 4.86856 12.8937 3.81856 12.1374 3.39981L8.7999 1.54356C8.08115 1.14981 6.91865 1.14981 6.20615 1.54981Z" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    </>
  ),
  quantity: (
    <>
      {/* prettier-ignore */}
      <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
        <path d="M10.8335 13.1654H4.1668C2.1668 13.1654 0.833496 12.1654 0.833496 9.832V5.1654C0.833496 2.832 2.1668 1.83203 4.1668 1.83203H10.8335C12.8335 1.83203 14.1668 2.832 14.1668 5.1654V9.832C14.1668 12.1654 12.8335 13.1654 10.8335 13.1654Z" stroke="currentColor"/>
        <path d="M3.5 4.83203V10.1654" stroke="currentColor"/>
        <path d="M5.5 4.83203V7.49873" stroke="currentColor"/>
        <path d="M5.5 9.5V10.1667" stroke="currentColor"/>
        <path d="M9.5 4.83203V5.49873" stroke="currentColor"/>
        <path d="M7.5 4.83203V10.1654" stroke="currentColor"/>
        <path d="M9.5 7.5V10.1667" stroke="currentColor"/>
        <path d="M11.5 4.83203V10.1654" stroke="currentColor"/>
      </svg>
    </>
  ),
};

export default DiscountDetailsModal;
