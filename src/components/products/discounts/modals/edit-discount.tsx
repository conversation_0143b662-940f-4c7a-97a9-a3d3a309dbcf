import { useState } from "react";
import { DiscountItemInterface, ProductItemInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import DiscountsForm from "../discounts-form";
import { emit } from "@/components/hooks/useListener";
import { AppEvent } from "@/assets/js/utils/event-types";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  storeItems: ProductItemInterface[];
  value: DiscountItemInterface;
  updateDiscount: (item: DiscountItemInterface) => void;
}

const EditDiscountModal: React.FC<Props> = ({ show, toggle, value, updateDiscount }) => {
  const [updating, setUpdating] = useState(false);

  const initialValues = {
    ...value,
    discount_cap: value.discount_cap ?? undefined,
    start_date: new Date(value.start_date),
    end_date: value.end_date ? new Date(value.end_date) : undefined,
    duration: value?.end_date ? "range" : "indefinite",
  };

  return (
    <Modal {...{ show, toggle }} title="Edit discount">
      <ModalBody>
        <DiscountsForm {...{ initialValues, updateDiscount, setUpdating }} />
      </ModalBody>
      <ModalFooter>
        <AppBtn
          onClick={() => {
            emit(AppEvent.SUBMIT_DISCOUNT);
          }}
          isBlock
          disabled={updating}
          size="lg"
        >
          Save Updates
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default EditDiscountModal;
