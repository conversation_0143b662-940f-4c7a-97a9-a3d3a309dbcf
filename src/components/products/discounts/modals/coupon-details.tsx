import { toCurrency } from "@/assets/js/utils/functions";
import { CouponItemInterface } from "@/assets/interfaces";
import { checkIfExpired, formatDateString } from "@/assets/js/utils/utils";
import { useModals } from "@/components/hooks/useModals";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import { discountDetailsIcons } from "./discount-details";
import Badge from "@/components/ui/badge";
import OrderList from "@/components/orders/modals/order-list";
import Portal from "@/components/portal";
import { useFetcher } from "@/api/utils";
import { GetCouponOrders } from "@/api";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  openEditModal: () => void;
  openCouponOrdersModal: () => void;
  value: CouponItemInterface;
}

const CouponDetailsModal: React.FC<Props> = ({ show, toggle, value, openEditModal, openCouponOrdersModal }) => {
  const {
    percentage,
    coupon_code,
    type,
    id,
    discount_cap,
    active,
    end_date,
    discount_amount,
    quantity,
    minimum_order_amount,
  } = value;
  const { modals, toggleModal } = useModals(["order_list"]);
  const couponOrdersReq = useFetcher(GetCouponOrders, { coupon_code });
  const orders = couponOrdersReq?.response?.data ?? [];

  return (
    <>
      <Modal {...{ show, toggle }} title="Coupon details" size="midi">
        <ModalBody>
          <div className="flex items-center justify-between border-b border-grey-border border-opacity-50 pb-4">
            <div className="">
              <h4 className="text-base sm:text-lg font-semibold -mb-1">{coupon_code}</h4>
              <span className="text-1xs sm:text-sm text-gray-600">Coupon Code </span>
            </div>
            <div className="bg-accent-red-500 rounded-full h-11.5 w-11.5 flex text-white items-center justify-center">
              {discountDetailsIcons.id}
            </div>
          </div>
          {type == "percentage" && (
            <div className="flex items-center justify-between border-b border-grey-border border-opacity-50 py-4">
              <div className="">
                <h4 className="text-base sm:text-lg font-semibold -mb-1"> {`${percentage}%`} </h4>
                <span className="text-1xs sm:text-sm text-gray-600">Discount Percent</span>
              </div>
              <div className="bg-accent-green-500 rounded-full h-11.5 w-11.5 flex text-white items-center justify-center">
                {discountDetailsIcons.percentage}
              </div>
            </div>
          )}
          {discount_cap && (
            <div className="flex items-center justify-between border-b border-grey-border border-opacity-50 py-4">
              <div className="">
                <h4 className="text-base sm:text-lg font-semibold -mb-1"> {`${toCurrency(discount_cap)}`} </h4>
                <span className="text-1xs sm:text-sm text-gray-600">Coupon Cap</span>
              </div>
              <div className="bg-accent-orange-500 rounded-full h-11.5 w-11.5 flex text-white items-center justify-center">
                {discountDetailsIcons.percentage}
              </div>
            </div>
          )}
          {type == "fixed" && (
            <div className="flex items-center justify-between border-b border-grey-border border-opacity-50 py-4">
              <div className="">
                <h4 className="text-base sm:text-lg font-semibold -mb-1"> {`${toCurrency(discount_amount)}`} </h4>
                <span className="text-1xs sm:text-sm text-gray-600">Discount Amount</span>
              </div>
              <div className="bg-accent-green-500 rounded-full h-11.5 w-11.5 flex text-white items-center justify-center">
                {discountDetailsIcons.percentage}
              </div>
            </div>
          )}

          {minimum_order_amount && (
            <div className="flex items-center justify-between border-b border-grey-border border-opacity-50 py-4">
              <div className="">
                <h4 className="text-base sm:text-lg font-semibold -mb-1"> {`${toCurrency(minimum_order_amount)}`} </h4>
                <span className="text-1xs sm:text-sm text-gray-600">Mininmum Order Amount</span>
              </div>
              <div className="bg-accent-orange-500 rounded-full h-11.5 w-11.5 flex text-white items-center justify-center">
                {discountDetailsIcons.money}
              </div>
            </div>
          )}

          <div className="py-4">
            <div className="flex items-center justify-between  py-2">
              <div className="flex items-center">
                <div className="text-grey-subtext p-1.5 rounded-full bg-grey-fields-200">
                  {discountDetailsIcons.status}
                </div>
                <span className="ml-2 text-dark text-1xs sm:text-sm">Status</span>
              </div>
              <Badge
                text={checkIfExpired(end_date) ? "Expired" : active ? "Active" : "Inactive"}
                color={checkIfExpired(end_date) ? "red" : active ? "green" : "red"}
              ></Badge>
            </div>

            <div className="flex items-center justify-between  py-2">
              <div className="flex items-center">
                <div className="text-grey-subtext p-1.5 rounded-full bg-grey-fields-200">
                  {discountDetailsIcons.date}
                </div>
                <span className="ml-2 text-dark text-1xs sm:text-sm">Expiry Date</span>
              </div>
              <span className={`text-black-secondary font-medium text-1xs sm:text-sm`}>
                {formatDateString(new Date(end_date))}{" "}
                {/* {checkIfExpired(end_date) ? <Badge text="Expired" color="red"></Badge> : null */}
              </span>
            </div>

            <div className="flex items-center justify-between  py-2">
              <div className="flex items-center">
                <div className="text-grey-subtext p-1.5 rounded-full bg-grey-fields-200">
                  {discountDetailsIcons.quantity}
                </div>
                <span className="ml-2 text-dark text-1xs sm:text-sm">Quantity left</span>
              </div>
              <div className="flex items-center cursor-pointer select-none" onClick={openEditModal}>
                <span className={`pt-1 font-medium text-black-secondary text-1xs sm:text-sm`}>{quantity} left</span>
              </div>
            </div>

            {orders?.length > 0 && (
              <div className="flex items-center justify-between  py-2">
                <div className="flex items-center">
                  <div className="text-grey-subtext p-1.5 rounded-full bg-grey-fields-200">
                    {discountDetailsIcons.products}
                  </div>
                  <span className="ml-2 text-gray-600 text-1xs sm:text-sm">Orders</span>
                </div>
                <button
                  className="flex items-center cursor-pointer select-none"
                  onClick={() => toggleModal("order_list")}
                >
                  <span className={`mr-2 font-semibold text-primary-500 text-1xs sm:text-sm`}>
                    {orders.length} Order{orders?.length > 1 ? "s" : ""}
                  </span>
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 14 15" fill="none" className="w-3">
                    <path d="M1 7.5L13 7.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M7 13.5L13 7.5L7 1.5" stroke="#332098" strokeWidth="1.75" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <AppBtn onClick={openEditModal} isBlock size="lg">
            Edit coupon
          </AppBtn>
        </ModalFooter>
        <Portal>
          {orders?.length > 0 && (
            <OrderList show={modals.order_list.show} toggle={() => toggleModal("order_list")} orders={orders} />
          )}
        </Portal>
      </Modal>
    </>
  );
};

export default CouponDetailsModal;
