import { useState } from "react";
import { CouponItemInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import CouponForm from "../coupon-form";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  value: CouponItemInterface;
  updateCoupon: (item: CouponItemInterface) => void;
}

const EditCouponModal: React.FC<Props> = ({ show, toggle, value, updateCoupon }) => {
  const { percentage, coupon_code, end_date, type, id, discount_cap, discount_amount, quantity, minimum_order_amount } =
    value;
  const [updating, setUpdating] = useState(false);

  const initialValues = {
    coupon_code,
    type,
    percentage: percentage ?? 0,
    discount_cap: discount_cap ?? undefined,
    discount_amount: discount_amount ?? 0,
    quantity,
    expiryDate: "",
    id,
    end_date: new Date(end_date),
    minimum_order_amount,
  };

  return (
    <Modal {...{ show, toggle }} title={`Edit coupon - ${value.coupon_code}`}>
      <ModalBody>
        <CouponForm {...{ setUpdating, initialValues, updateCoupon }} />
      </ModalBody>
      <ModalFooter>
        <AppBtn
          onClick={() => {
            const btn = document.querySelector("#coupon_btn") as HTMLElement;
            btn && btn.click();
          }}
          isBlock
          disabled={updating}
        >
          Save Updates
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default EditCouponModal;
