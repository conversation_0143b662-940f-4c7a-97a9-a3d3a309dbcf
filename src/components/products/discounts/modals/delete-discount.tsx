import { useRequest } from "@/api/utils";
import { DeleteDiscount, DeleteItem } from "@/api/items";
import { DeleteDiscountParams } from "@/api/interfaces/items.interface";
import { AppBtn } from "@/components/ui/buttons";
import ErrorLabel from "@/components/ui/error-label";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import SuccessLabel from "@/components/ui/success-label";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  discountId: string;
  deleteDiscount: () => void;
}

const DeleteDiscountModal: React.FC<Props> = ({ show, toggle, discountId, deleteDiscount }) => {
  const { makeRequest, isLoading, error, response, clearResponse } = useRequest<DeleteDiscountParams>(DeleteDiscount);

  const handleItemDelete = async () => {
    const [res, err] = await makeRequest({ id: discountId });
    if (res) {
      toggle(false);
      deleteDiscount();
    }
  };

  return (
    <Modal
      {...{
        show,
        toggle: (state) => {
          clearResponse();
          toggle(state);
        },
      }}
      title="Delete Discount"
      size="sm"
    >
      <ModalBody>
        <ErrorLabel error={error?.message} />
        <SuccessLabel message={response ? "Discount deleted successfully!" : ""} />
        <div className="text-center">
          <h5 className="text-black text-base font-semibold">Do you want to delete this Discount?</h5>
          <p className="text-sm text-grey-subtext mt-1 max-w-xs mx-auto">
            You won’t see this discount again if you delete it, set it to inactive instead if it is out of use.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="danger" onClick={handleItemDelete} disabled={isLoading}>
          {isLoading ? "Deleting Discount..." : "Delete Discount"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default DeleteDiscountModal;
