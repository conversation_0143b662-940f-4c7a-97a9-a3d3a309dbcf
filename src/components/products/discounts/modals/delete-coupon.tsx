import { DeleteCouponParams } from "@/api/interfaces/items.interface";
import { DeleteCoupon } from "@/api/items";
import { useRequest } from "@/api/utils";
import { AppBtn } from "@/components/ui/buttons";
import ErrorLabel from "@/components/ui/error-label";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import SuccessLabel from "@/components/ui/success-label";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  couponId: string;
  deleteCoupon: () => void;
}

const DeleteCouponModal: React.FC<Props> = ({ show, toggle, couponId, deleteCoupon }) => {
  const { makeRequest, isLoading, error, response } = useRequest<DeleteCouponParams>(DeleteCoupon);

  const handleCouponItemDelete = async () => {
    const [res, err] = await makeRequest({ id: couponId });
    if (res) {
      toggle(false);
      deleteCoupon();
    }
  };

  return (
    <Modal {...{ show, toggle }} title="Delete Coupon" size="sm">
      <ModalBody>
        <ErrorLabel error={error?.message} />
        <SuccessLabel message={response ? "Coupon deleted successfully!" : ""} />
        <div className="text-center">
          <h5 className="text-black text-base font-semibold">Do you want to delete this Coupon?</h5>
          <p className="text-sm text-grey-subtext mt-1 max-w-xs mx-auto">
            You won’t see this Coupon again if you delete it, set it to inactive instead if it is out of use.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="danger" onClick={handleCouponItemDelete} disabled={isLoading}>
          {isLoading ? "Deleting Coupon..." : "Delete Coupon"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default DeleteCouponModal;
