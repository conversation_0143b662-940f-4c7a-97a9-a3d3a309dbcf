import { useEffect, useState } from "react";
import { GetCoupons } from "../../../api/items";
import { useFetcher } from "../../../api/utils";
import { CouponItemInterface } from "../../../assets/interfaces";
import { reloadPage } from "../../../assets/js/utils/functions";
import { actionIsAllowed, SCOPES } from "../../../assets/js/utils/permissions";
import authContext from "../../../contexts/auth-context";
import ClearSearch from "../../clear-search";
import { useModals } from "../../hooks/useModals";
import usePagination from "../../hooks/usePagination";
import useScreenSize from "../../hooks/useScreenSize";
import useSearchParams from "../../hooks/useSearchParams";
import Portal from "../../portal";
import { AppBtn } from "../../ui/buttons";
import ContentState from "../../ui/content-state";
import ErrorBox from "../../ui/error";
import { productPageIcons } from "../../ui/layouts/product";
import Pagination from "../../ui/pagination";
import Table, { TableBody, TableHead, TableHeadItem } from "../../ui/table";
import { CouponItemMobile } from "./coupon-mobile";
import CouponDetailsModal from "./modals/coupon-details";
import DeleteCouponModal from "./modals/delete-coupon";
import EditCouponModal from "./modals/edit-coupon";
import CouponItem from "./coupon-item";

const Coupons = () => {
  const { search } = useSearchParams(["search"]);
  const PER_PAGE = 10;
  const { currentPage, goNext, goPrevious, setPage } = usePagination();
  const { response, isLoading, error } = useFetcher(GetCoupons, {
    filter: { search: search ? search : "" },
    page: currentPage,
    per_page: PER_PAGE,
    sort: "asc",
  });

  const { subscription } = authContext.useContainer();
  const [coupons, setCoupons] = useState<CouponItemInterface[]>([]);
  const [currentCoupon, setCurrentCoupon] = useState<number>(undefined);
  const { modals, toggleModal } = useModals(["edit", "delete", "coupon_details"]);
  const { width, isSmall } = useScreenSize();

  useEffect(() => {
    if (response) {
      const data = response?.data.items as CouponItemInterface[];
      setCoupons(data);
    }
  }, [response]);

  const deleteCoupon = (coupon: CouponItemInterface) => {
    setCoupons(coupons.filter((c) => c.id !== coupon.id));
  };

  const updateCoupons = (update: CouponItemInterface) => {
    const index = coupons.findIndex((value) => update.id === value.id);
    const couponsCopy = [...coupons];

    couponsCopy[index] = update;
    setCoupons(couponsCopy);
  };

  const handleCouponAction = (action: "click" | "edit" | "delete" | "toggle", coupon: CouponItemInterface) => {
    const couponIndex = coupons.findIndex((c) => c.id === coupon.id);
    setCurrentCoupon(couponIndex);

    switch (action) {
      case "click":
        toggleModal("coupon_details");
        break;
      case "delete":
        toggleModal("delete");
        break;
      case "edit":
        toggleModal("edit");
        break;
      case "toggle":
        const couponCopy = { ...coupons[currentCoupon] };
        couponCopy.active = !couponCopy.active;
        updateCoupons(couponCopy);
        break;
    }
  };

  let canManageCoupons = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_COUPONS,
  });

  if (!canManageCoupons) {
    return (
      <ErrorBox title="Upgrade required" message="Upgrade to the basic or business plus plan to create coupons">
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  if (isLoading || error || !response?.data || coupons.length < 1) {
    return (
      <>
        <ClearSearch search={search} />
        <ContentState
          isLoading={isLoading}
          loadingText="Loading coupons"
          errorTitle="Failed to load coupons"
          error={error}
          errorMessage="We couldn't load your coupons please click the button to retry"
          errorAction={
            <AppBtn size="md" onClick={reloadPage}>
              Reload Coupons
            </AppBtn>
          }
          isEmpty={coupons.length < 1}
          emptyIcon={<div className="w-8.75 h-8.75 text-grey-muted">{productPageIcons.coupon}</div>}
          title="No Coupons to Show"
          description="Create a coupon"
        >
          <AppBtn size="md" className="max-w-[240px] m-auto" href={`/products/create-coupon`}>
            Create Coupon
          </AppBtn>
        </ContentState>
      </>
    );
  }

  return (
    <>
      {!isLoading &&
        coupons.length > 0 &&
        (!isSmall ? (
          <div>
            <ClearSearch search={search} />
            <Table>
              <TableHead>
                {/* <TableHeadItem className=" hidden sm:table-cell">Coupon ID</TableHeadItem> */}
                <TableHeadItem className=" sm:table-cell">Coupon code</TableHeadItem>
                <TableHeadItem className=" hidden sm:table-cell">Coupon Value</TableHeadItem>
                <TableHeadItem className=" hidden sm:table-cell">Qty</TableHeadItem>
                <TableHeadItem className=" hidden sm:table-cell">Expiry</TableHeadItem>
                <TableHeadItem className=" sm:table-cell">Options</TableHeadItem>
                <TableHeadItem className=" sm:table-cell"></TableHeadItem>
              </TableHead>
              <TableBody>
                {coupons.map((coupon) => {
                  return (
                    <CouponItem
                      key={coupon.id}
                      coupon={coupon}
                      onAction={(action) => handleCouponAction(action, coupon)}
                    />
                  );
                })}
              </TableBody>
            </Table>
          </div>
        ) : (
          <ul>
            {coupons &&
              coupons.map((discount, index) => (
                <CouponItemMobile
                  data={discount}
                  key={index}
                  index={index}
                  onAction={(action) => handleCouponAction(action, discount)}
                />
              ))}
          </ul>
        ))}
      {/* <Pagination
        data={response}
        {...{ currentPage, goNext, goPrevious, per_page: PER_PAGE, length: coupons.length, label: "coupons" }}
      /> */}

      <Pagination
        data={response?.data}
        {...{ currentPage, setPage, goNext, length: coupons.length, label: "coupons", goPrevious, per_page: PER_PAGE }}
      />
      {currentCoupon !== undefined && (
        <Portal>
          <CouponDetailsModal
            show={modals.coupon_details.show}
            toggle={() => toggleModal("coupon_details")}
            openEditModal={() => toggleModal("edit")}
            openCouponOrdersModal={() => toggleModal("coupon_orders")}
            value={coupons[currentCoupon]}
          />
          <EditCouponModal
            show={modals.edit.show}
            toggle={() => toggleModal("edit")}
            value={coupons[currentCoupon]}
            updateCoupon={(coupon) => {
              toggleModal("edit");
              updateCoupons(coupon);
            }}
          />
          <DeleteCouponModal
            show={modals.delete.show}
            toggle={() => toggleModal("delete")}
            couponId={coupons[currentCoupon]?.id}
            deleteCoupon={() => deleteCoupon(coupons[currentCoupon])}
          />
        </Portal>
      )}
    </>
  );
};
export default Coupons;
