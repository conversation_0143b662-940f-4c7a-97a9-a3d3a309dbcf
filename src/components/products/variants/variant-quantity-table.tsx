import { useState } from "react";
import { VariantForm, VariantItem } from "../../../assets/interfaces";
import { removeUnderscores, toCurrency, genarateStringFromVariantValues } from "../../../assets/js/utils/functions";
import LazyImage from "../../lazy-image";
import Badge from "../../ui/badge";
import { StepNumberInput } from "../../ui/form-elements/step-number-input";
import Table, { TableHead, TableHeadItem, TableBody, TableRow, TableCell } from "../../ui/table";
import Toggle from "../../ui/toggle";

interface Props {
  variants: VariantForm;
  updateVariants: (options: VariantForm) => void;
}
export const VariantQuantityTable: React.FC<Props> = ({ variants, updateVariants }) => {
  const options = variants?.options;
  const isImageVariant = variants.type === "images";
  const types = !isImageVariant && Object?.keys(options[0]?.values);
  const extraOption = options[0]?.values ? Object.keys(options[0]?.values)[0] : null;

  const updateOptions = (value: VariantItem, index: number) => {
    const optionsCopy = [...options];
    optionsCopy[index] = value;
    updateVariants({ ...variants, options: optionsCopy });
  };

  return (
    <ul className="flex flex-col space-y-2.5 mt-4 ">
      {options &&
        options.map((o, index) => (
          <li
            className="flex flex-col bg-grey-fields-200 border border-grey-border border-opacity-40 rounded-15 p-3.5 divide-y divide-grey-border divide-opacity-50"
            key={index}
          >
            {!isImageVariant && (
              <div className="flex flex-col">
                <span className="text-dark text-sm">{genarateStringFromVariantValues(o?.values)}</span>
                <span className="text-1xs font-semibold text-black-secondary">{toCurrency(o.price)}</span>
              </div>
            )}

            {isImageVariant && (
              <div className="flex items-center">
                <figure className="h-9 w-9 rounded-5 overflow-hidden bg-grey-ash relative">
                  {o.image && <LazyImage src={o.image} alt="" className="w-full h-full object-cover" />}
                </figure>
                <div className="ml-2.5 flex flex-col">
                  {o?.values && isImageVariant && (
                    <span className="text-dark text-1xs inline-block mb-0.5">
                      {removeUnderscores(extraOption)} {o.values[extraOption]}
                    </span>
                  )}
                  <span className={`font-semibold text-black-secondary ${extraOption ? "text-xs" : "text-1xs"}`}>
                    {toCurrency(o.price)}
                  </span>
                </div>
              </div>
            )}
            <div className="flex items-center justify-between mt-3 pt-3">
              <Badge
                color={o?.quantity > -1 && o?.quantity < 1 ? "red" : "green"}
                greyBg={false}
                size="sm"
                text={o?.quantity > -1 && o?.quantity < 1 ? "Unavailable" : "Available"}
              />
              <StepNumberInput
                min={0}
                value={o.quantity ?? 0}
                onChange={(quantity) => updateOptions({ ...o, quantity }, index)}
              />
            </div>
          </li>
        ))}
    </ul>
  );

  return (
    <div>
      <Table className="mt-5">
        <TableHead>
          {isImageVariant ? (
            <TableHeadItem>Image</TableHeadItem>
          ) : (
            <>{types && types?.map((t, index) => <TableHeadItem key={index}>{removeUnderscores(t)}</TableHeadItem>)}</>
          )}
          {extraOption && isImageVariant && <TableHeadItem>{removeUnderscores(extraOption)}</TableHeadItem>}
          <TableHeadItem>Price</TableHeadItem>
          <TableHeadItem>Quantity</TableHeadItem>
        </TableHead>
        <TableBody>
          {options &&
            options.map((o, index) => (
              <TableRow key={index}>
                {isImageVariant ? (
                  <TableCell className="text-1xs">
                    <figure className="h-8 w-8 rounded-5 overflow-hidden bg-grey-ash">
                      {o.image && <img crossOrigin="anonymous" src={o.image} alt="" className="w-full h-full object-cover" />}
                    </figure>
                  </TableCell>
                ) : (
                  <>
                    {types &&
                      types?.map((t, _index) => (
                        <TableCell className="text-black-secondary" key={_index}>
                          {o.values[t]}
                        </TableCell>
                      ))}
                  </>
                )}

                {o?.values && isImageVariant && <TableCell>{o.values[extraOption]}</TableCell>}
                <TableCell stopBubble className="text-black-secondary">
                  {toCurrency(o.price)}
                </TableCell>
                <TableCell onClick={(e) => e.stopPropagation()}>
                  <StepNumberInput
                    min={0}
                    value={o.quantity ?? 0}
                    onChange={(quantity) => updateOptions({ ...o, quantity }, index)}
                  />
                </TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </div>
  );
};
