import useMediaProcessor from "@/components/hooks/useMediaProcessor";
import { ProductUploadStep } from "@/components/hooks/useProductController";
import LazyImage from "@/components/lazy-image";
import { FormEvent, useEffect, useRef } from "react";
import { Media, MediaType } from "../../../assets/interfaces";
import { generateSimpleUUID } from "../../../assets/js/utils/functions";
import { ProductForm } from "../../../pages/products/create";
import Badge from "../../ui/badge";
import { AppBtn } from "../../ui/buttons";

interface Props {
  setForm: (form: ProductForm) => void;
  setCurrentStep: (step: ProductUploadStep) => void;
  form: ProductForm;
  currentStep: ProductUploadStep;
  maxUploadable: number;
  pastProgress: ProductForm;
  medias: Media[];
  setMedias: React.Dispatch<React.SetStateAction<Media[]>>;
  canProcessVideos?: boolean; //browser compatibility
  canUploadVideos?: boolean; //plan permission for videos
}

const AddMedia: React.FC<Props> = ({
  setForm,
  setCurrentStep,
  form,
  currentStep,
  maxUploadable,
  pastProgress,
  medias = [],
  setMedias,
  canProcessVideos = true,
  canUploadVideos = true,
}) => {
  const mediaPicker = useRef<HTMLInputElement>(null);
  const uploadEnabled = medias.length < maxUploadable;
  const areImagesUploading = medias
    .filter((m) => m.type === MediaType.IMAGE)
    .some((m) => m.isUploading || m.uploadProgress < 100);
  const excludedMediaTypes = canProcessVideos && canUploadVideos ? [] : [MediaType.VIDEO];

  const { processFiles } = useMediaProcessor((m) => setMedias(m), medias, maxUploadable);

  useEffect(() => {
    if (currentStep !== "media" && medias.length > form.products.length) {
      // setImages(form.products.map((p) => p.images[0]));
    }
  }, [form]);

  useEffect(() => {
    mediaPicker.current.value = "";
  }, [medias]);

  const removePickedMedia = (index) => {
    const newMedias = [...medias];
    newMedias.splice(index, 1);

    setMedias(newMedias);
  };

  const handleFormSubmit = (e: FormEvent) => {
    e.preventDefault();

    const productsWithInitialMedias = medias.map((media) => {
      const prevData = form.products.find(
        (item) =>
          item?.images?.findIndex((i) => i.src === media.src) !== -1 ||
          item?.videos?.findIndex((v) => v.src === media.src) !== -1
      );

      const productMedia =
        media.type === MediaType.VIDEO ? { videos: [media], images: [] } : { images: [media], videos: [] };

      return prevData
        ? prevData
        : {
            ...productMedia,
            name: "",
            price: "",
            category: "",
            description: "",
            thumbnail: 0,
            thumbnail_type: "image",
            price_unit: "",
            variants: {
              type: "",
              options: [],
            },
            id: generateSimpleUUID(),
            info_blocks: [],
          };
    });

    setForm({ ...form, products: productsWithInitialMedias });
    setCurrentStep("details");
  };

  const loadProgress = () => {
    if (pastProgress && form.products.length === 0) {
      setForm(pastProgress);
      // setImages(pastProgress.products.map(({ images }) => ({ ...images[0], src: images[0].url })));
    }
  };

  return (
    <form className="flex flex-col items-center text-center" onSubmit={handleFormSubmit}>
      <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 m-auto flex items-center justify-center text-white">
        {/* prettier-ignore */}
        <svg className="w-[30px] sm:w-8" viewBox="0 0 30 30" fill="none">
          <path d="M26.2125 1.25H22.5375C21.45 1.25 20.65 1.7 20.2875 2.5C20.0875 2.8625 20 3.2875 20 3.7875V7.4625C20 9.05 20.95 10 22.5375 10H26.2125C26.7125 10 27.1375 9.9125 27.5 9.7125C28.3 9.35 28.75 8.55 28.75 7.4625V3.7875C28.75 2.2 27.8 1.25 26.2125 1.25ZM27.3875 6.1625C27.2625 6.2875 27.075 6.375 26.875 6.3875H25.1125V7.025L25.125 8.125C25.1125 8.3375 25.0375 8.5125 24.8875 8.6625C24.7625 8.7875 24.575 8.875 24.375 8.875C23.9625 8.875 23.625 8.5375 23.625 8.125V6.375L21.875 6.3875C21.4625 6.3875 21.125 6.0375 21.125 5.625C21.125 5.2125 21.4625 4.875 21.875 4.875L22.975 4.8875H23.625V3.1375C23.625 2.725 23.9625 2.375 24.375 2.375C24.7875 2.375 25.125 2.725 25.125 3.1375L25.1125 4.025V4.875H26.875C27.2875 4.875 27.625 5.2125 27.625 5.625C27.6125 5.8375 27.525 6.0125 27.3875 6.1625Z" fill="currentColor"/>
          <path d="M11.2499 12.975C12.8929 12.975 14.2249 11.6431 14.2249 10C14.2249 8.35698 12.8929 7.02502 11.2499 7.02502C9.60686 7.02502 8.2749 8.35698 8.2749 10C8.2749 11.6431 9.60686 12.975 11.2499 12.975Z" fill="currentColor"/>
          <path d="M26.2125 10H25.625V15.7625L25.4625 15.625C24.4875 14.7875 22.9125 14.7875 21.9375 15.625L16.7375 20.0875C15.7625 20.925 14.1875 20.925 13.2125 20.0875L12.7875 19.7375C11.9 18.9625 10.4875 18.8875 9.4875 19.5625L4.8125 22.7C4.5375 22 4.375 21.1875 4.375 20.2375V9.7625C4.375 6.2375 6.2375 4.375 9.7625 4.375H20V3.7875C20 3.2875 20.0875 2.8625 20.2875 2.5H9.7625C5.2125 2.5 2.5 5.2125 2.5 9.7625V20.2375C2.5 21.6 2.7375 22.7875 3.2 23.7875C4.275 26.1625 6.575 27.5 9.7625 27.5H20.2375C24.7875 27.5 27.5 24.7875 27.5 20.2375V9.7125C27.1375 9.9125 26.7125 10 26.2125 10Z" fill="currentColor"/>
        </svg>
      </figure>
      <h2 className="text-center font-light text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto mt-3.5 !leading-tight">
        Add media,
        <br />
        <b className="font-bold">for multiple products</b>
      </h2>
      <div className="bg-grey-fields-100 rounded-10 p-3 sm:p-3.5 mt-5 flex flex-col items-start">
        <Badge color="red" text="Important" greyBg={false} className="mb-2" />
        <p className="text-black-secondary text-1xs text-left !leading-snug">
          Select one media file (image or video) for each product you want to add.Videos must be no longer than 15
          seconds and no larger than 50MB.
        </p>
      </div>
      <div className="w-full">
        <input
          type="file"
          ref={mediaPicker}
          name="product-medias"
          multiple
          accept={
            canProcessVideos && canUploadVideos ? "video/mp4,webm,ogg,video/quicktime,image/*,.heic" : "image/*,.heic"
          }
          id="product-medias"
          className="hidden"
          onChange={(e) => processFiles(e.target.files, excludedMediaTypes)}
        />
        {medias.length < 1 && !areImagesUploading && (
          <button
            className={`relative no-outline w-24 h-28 rounded-10 border border-dashed border-grey-subtext disabled:!border-opacity-30 disabled:hover:border-grey-subtext disabled:text-grey-subtext disabled:!text-opacity-30 text-black-secondary hover:border-primary-500 hover:text-primary-500 transition-all image-picker bg-white disabled:cursor-not-allowed mt-15 mb-18`}
            onClick={() => mediaPicker.current.click()}
            type="button"
            disabled={!uploadEnabled}
          >
            <div className="h-full w-full flex items-center justify-center bg-white z-10 relative rounded-10">
              {/* prettier-ignore */}
              <svg width="30" height="30" viewBox="0 0 30 30" fill="none">
                <path d="M15 6.25V23.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6.24976 15H23.7498" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </div>
          </button>
        )}

        {medias.length > 0 && (
          <div className="grid items-start w-full grid-cols-2 gap-6 sm:gap-6 my-8.75 sm:my-10">
            {medias.map(({ name, src, isUploading, url, type, uploadProgress, error }, index) => (
              <div key={index} className="flex items-center">
                <figure
                  className="w-[55px] h-[55px] sm:w-15 sm:h-15 relative group cursor-pointer flex items-center justify-center flex-shrink-0 border border-grey-border border-opacity-20 rounded-10 overflow-hidden"
                  key={index}
                >
                  {src ? (
                    <>
                      {type === MediaType.IMAGE && (
                        <LazyImage crossOrigin="anonymous" src={src} alt={name} className="w-full h-full object-cover" />
                      )}
                      {type === MediaType.VIDEO && (
                        <video src={src} controls={false} className="w-full h-full object-cover" playsInline />
                      )}
                    </>
                  ) : (
                    <div className="h-full w-full rounded-10 bg-grey-loader animate-pulse flex items-center justify-center">
                      <div className="spinner spinner--sm"></div>
                    </div>
                  )}
                </figure>
                <div className="flex flex-col items-start ml-2.5 flex-1">
                  <div className="flex items-center mb-1.5 sm:mb-2.5 w-full justify-between">
                    <h4 className="inline-block text-black-placeholder text-sm font-bold">Product {index + 1}</h4>
                    {!areImagesUploading && (
                      <button
                        className="h-[22px] w-[22px] bg-grey-fields-100 flex items-center justify-center transform rounded-full transition-all text-dark hover:text-accent-red-500"
                        onClick={() => removePickedMedia(index)}
                        type="button"
                      >
                        {/* prettier-ignore */}
                        <svg width="60%" viewBox="0 0 15 15" fill="none">
                          <path d="M11.25 3.75L3.75 11.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                          <path d="M3.75 3.75L11.25 11.25" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </button>
                    )}
                  </div>
                  {isUploading && (
                    <div
                      className={`mt-1.5 h-1.5 w-[90%] rounded-10 overflow-hidden ${
                        error ? "bg-accent-red-500 bg-opacity-10" : "bg-grey-fields-100"
                      }`}
                    >
                      <div
                        className={`h-full transition-all duration-200 ease-out ${
                          error ? "bg-accent-red-500" : "bg-accent-green-500"
                        }`}
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  )}
                  {!isUploading && (
                    <div className="mt-1.5 w-[90%] h-1.5 bg-grey-fields-200 bg-opacity-80 rounded-10"></div>
                  )}
                  <div className="mt-1.5 w-[50%] h-1.5 bg-grey-fields-200 rounded-10 bg-opacity-80"></div>
                </div>
              </div>
            ))}

            {uploadEnabled && (
              <button
                className="no-outline w-full h-[55px] sm:h-15 rounded-10 border border-dashed border-grey-border border-opacity-50 text-black-secondary hover:text-primary-500 transition-all flex items-center bg-grey-fields-200 bg-opacity-40 px-3.5"
                onClick={() => mediaPicker.current.click()}
                type="button"
              >
                <figure className="h-7.5 w-7.5 rounded-full flex items-center justify-center bg-white">
                  {/* prettier-ignore */}
                  <svg width="60%" viewBox="0 0 30 30" fill="none">
                    <path d="M15 6.25V23.75" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M6.24976 15H23.7498" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </figure>
                <span className="font-medium inline-block ml-2 text-sm">Add Product</span>
              </button>
            )}
          </div>
        )}
      </div>
      <AppBtn isBlock disabled={medias.length < 1 || areImagesUploading} type="submit" size="lg">
        {areImagesUploading ? "Saving images..." : "Proceed to add details"}
      </AppBtn>
      <AppBtn color="neutral" isBlock className="mt-4" onClick={() => setCurrentStep("method")} size="lg">
        Change Upload Method
      </AppBtn>
      {/* {pastProgress !== null && form.products.length === 0 && (
        <AppBtn color="neutral" isBlock className="mt-4" onClick={loadProgress} size="lg">
          Load products from last progress
        </AppBtn>
      )} */}
    </form>
  );
};

export default AddMedia;
