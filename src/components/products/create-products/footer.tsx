import classNames from "classnames";
import { FormikProps } from "formik";
import React from "react";
import { boolean } from "yup/lib/locale";
import { InvoiceForm } from "../../../assets/interfaces/invoices";
import { ProductForm } from "../../../pages/products/create";
import Badge from "../../ui/badge";
import { AppBtn } from "../../ui/buttons";

interface IProps {
  form: ProductForm;
  showFooter: boolean;
  index: number;
  productImageUploading: boolean;
  isLoading: boolean;
  back: VoidFunction;
  next: VoidFunction;
}

const ProductUploadFooter: React.FC<IProps> = ({
  form,
  showFooter,
  index,
  isLoading,
  productImageUploading,
  back,
  next,
}) => {
  const classes = classNames(
    "absolute w-full bg-white bottom-0 border-t border-grey-border border-opacity-50 transform transition-all ease-in-out duration-300 flex flex-col sm:flex-row items-center justify-between sm:space-x-8 lg:space-x-12.5 py-4 sm:py-5 px-5 sm:px-5 lg:px-7.5 z-[100]",
    {
      "-bottom-full opacity-0": !showFooter,
      "bottom-0 opacity-100": showFooter,
    }
  );

  return (
    <div className={classes} style={{ transitionDelay: "250ms" }}>
      {form.products.length > 1 && (
        <div className="flex-1 text-left w-full sm:max-w-[400px]">
          <Badge
            text={`${index + 1} of ${form.products.length} Products`}
            color="dark"
            className="hidden sm:inline-flex"
          />
          <div className="flex flex-col items-end mb-2 sm:mb-0">
            <Badge
              text={`${index + 1} of ${form.products.length} Products`}
              color="dark"
              className="mb-1.5 sm:hidden"
            />
            <div className="w-full h-2.5 bg-grey-fields-100 rounded-2xl sm:mt-1.5">
              <div
                className="bg-accent-green-500 h-full rounded-3xl transition-all ease-out duration-200"
                style={{ width: `${((index + 1) / form.products.length) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}
      <div className="w-full sm:max-w-[300px] md:max-w-[350px] flex items-center space-x-2.5 sm:space-x-3.5 ml-auto">
        <div className="flex-1 w-full">
          <AppBtn color="neutral" isBlock onClick={back} disabled={isLoading}>
            {index === 0 ? "Back to Selection" : "Previous Product"}
          </AppBtn>
        </div>
        <div className="flex-1 w-full">
          <AppBtn isBlock type="submit" disabled={isLoading || productImageUploading} onClick={next}>
            {productImageUploading && "Uploading images..."}
            {isLoading && "Uploading items..."}
            {!isLoading &&
              !productImageUploading &&
              (index === form.products.length - 1 ? "Upload Products" : "Next Product")}
          </AppBtn>
        </div>
      </div>
    </div>
  );
};

export default ProductUploadFooter;
