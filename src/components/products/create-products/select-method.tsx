import { ProductCreateMethod } from "@/components/hooks/useProductController";
import classNames from "classnames";
import router from "next/router";
import { useMemo } from "react";

interface Props {
  selectMethod: (method: ProductCreateMethod, subMethod?: ProductCreateMethod) => void;
  hasMultipleStores?: boolean;
  uploadMethods: { [key: string]: boolean };
}

const SelectProductMethod: React.FC<Props> = ({ selectMethod, hasMultipleStores = false, uploadMethods }) => {
  const canUploadFromInstagram = uploadMethods[ProductCreateMethod.INSTAGRAM];

  const options = useMemo(() => {
    return [
      {
        title: "Add Manually",
        description: "Manually provide product information",
        icon: (
          <>
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 24 24" fill="none">
              <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM10.95 17.51C10.66 17.8 10.11 18.08 9.71 18.14L7.25 18.49C7.16 18.5 7.07 18.51 6.98 18.51C6.57 18.51 6.19 18.37 5.92 18.1C5.59 17.77 5.45 17.29 5.53 16.76L5.88 14.3C5.94 13.89 6.21 13.35 6.51 13.06L10.97 8.6C11.05 8.81 11.13 9.02 11.24 9.26C11.34 9.47 11.45 9.69 11.57 9.89C11.67 10.06 11.78 10.22 11.87 10.34C11.98 10.51 12.11 10.67 12.19 10.76C12.24 10.83 12.28 10.88 12.3 10.9C12.55 11.2 12.84 11.48 13.09 11.69C13.16 11.76 13.2 11.8 13.22 11.81C13.37 11.93 13.52 12.05 13.65 12.14C13.81 12.26 13.97 12.37 14.14 12.46C14.34 12.58 14.56 12.69 14.78 12.8C15.01 12.9 15.22 12.99 15.43 13.06L10.95 17.51ZM17.37 11.09L16.45 12.02C16.39 12.08 16.31 12.11 16.23 12.11C16.2 12.11 16.16 12.11 16.14 12.1C14.11 11.52 12.49 9.9 11.91 7.87C11.88 7.76 11.91 7.64 11.99 7.57L12.92 6.64C14.44 5.12 15.89 5.15 17.38 6.64C18.14 7.4 18.51 8.13 18.51 8.89C18.5 9.61 18.13 10.33 17.37 11.09Z" fill="currentColor"/>
            </svg>
          </>
        ),
        key: ProductCreateMethod.MANUAL,
        show: true,
        disable: !uploadMethods[ProductCreateMethod.MANUAL],
        color: "text-accent-orange-500",
      },
      {
        title: "Import From Menu Image",
        description: "Extract items from an image of your restaurant's menu",
        icon: (
          <>
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 24 24" fill="none">
              <path d="M16 4.25C16 5.49 14.99 6.5 13.75 6.5H10.25C9.63 6.5 9.07 6.25 8.66 5.84C8.25 5.43 8 4.87 8 4.25C8 3.01 9.01 2 10.25 2H13.75C14.37 2 14.93 2.25 15.34 2.66C15.75 3.07 16 3.63 16 4.25Z" fill="currentColor"/>
              <path d="M18.83 5.02874C18.6 4.83874 18.34 4.68874 18.06 4.57874C17.77 4.46874 17.48 4.69874 17.42 4.99874C17.08 6.70874 15.57 7.99874 13.75 7.99874H10.25C9.25 7.99874 8.31 7.60874 7.6 6.89874C7.08 6.37874 6.72 5.71874 6.58 5.00874C6.52 4.70874 6.22 4.46874 5.93 4.58874C4.77 5.05874 4 6.11874 4 8.24874V17.9987C4 20.9987 5.79 21.9987 8 21.9987H16C18.21 21.9987 20 20.9987 20 17.9987V8.24874C20 6.61874 19.55 5.61874 18.83 5.02874ZM8 12.2487H12C12.41 12.2487 12.75 12.5887 12.75 12.9987C12.75 13.4087 12.41 13.7487 12 13.7487H8C7.59 13.7487 7.25 13.4087 7.25 12.9987C7.25 12.5887 7.59 12.2487 8 12.2487ZM16 17.7487H8C7.59 17.7487 7.25 17.4087 7.25 16.9987C7.25 16.5887 7.59 16.2487 8 16.2487H16C16.41 16.2487 16.75 16.5887 16.75 16.9987C16.75 17.4087 16.41 17.7487 16 17.7487Z" fill="currentColor"/>
            </svg>
          </>
        ),
        show: uploadMethods[ProductCreateMethod.MENU_IMAGE],
        disable: !uploadMethods[ProductCreateMethod.MENU_IMAGE],
        key: ProductCreateMethod.MENU_IMAGE,
        color: "text-accent-red-500",
      },
      {
        title: "Import from Store",
        description: "Import products from another store you own on Catlog",
        icon: (
          <>
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 24 24" fill="none">
              <path d="M22.3596 8.27L22.0696 5.5C21.6496 2.48 20.2796 1.25 17.3497 1.25H14.9896H13.5097H10.4697H8.98965H6.58965C3.64965 1.25 2.28965 2.48 1.85965 5.53L1.58965 8.28C1.48965 9.35 1.77965 10.39 2.40965 11.2C3.16965 12.19 4.33965 12.75 5.63965 12.75C6.89965 12.75 8.10965 12.12 8.86965 11.11C9.54965 12.12 10.7097 12.75 11.9997 12.75C13.2896 12.75 14.4197 12.15 15.1096 11.15C15.8797 12.14 17.0696 12.75 18.3096 12.75C19.6396 12.75 20.8396 12.16 21.5896 11.12C22.1896 10.32 22.4597 9.31 22.3596 8.27Z" fill="currentColor"/>
              <path d="M11.3491 16.6602C10.0791 16.7902 9.11914 17.8702 9.11914 19.1502V21.8902C9.11914 22.1602 9.33914 22.3802 9.60914 22.3802H14.3791C14.6491 22.3802 14.8691 22.1602 14.8691 21.8902V19.5002C14.8791 17.4102 13.6491 16.4202 11.3491 16.6602Z" fill="currentColor"/>
              <path d="M21.3709 14.3981V17.3781C21.3709 20.1381 19.1309 22.3781 16.3709 22.3781C16.1009 22.3781 15.8809 22.1581 15.8809 21.8881V19.4981C15.8809 18.2181 15.4909 17.2181 14.7309 16.5381C14.0609 15.9281 13.1509 15.6281 12.0209 15.6281C11.7709 15.6281 11.5209 15.6381 11.2509 15.6681C9.47086 15.8481 8.12086 17.3481 8.12086 19.1481V21.8881C8.12086 22.1581 7.90086 22.3781 7.63086 22.3781C4.87086 22.3781 2.63086 20.1381 2.63086 17.3781V14.4181C2.63086 13.7181 3.32086 13.2481 3.97086 13.4781C4.24086 13.5681 4.51086 13.6381 4.79086 13.6781C4.91086 13.6981 5.04086 13.7181 5.16086 13.7181C5.32086 13.7381 5.48086 13.7481 5.64086 13.7481C6.80086 13.7481 7.94086 13.3181 8.84086 12.5781C9.70086 13.3181 10.8209 13.7481 12.0009 13.7481C13.1909 13.7481 14.2909 13.3381 15.1509 12.5981C16.0509 13.3281 17.1709 13.7481 18.3109 13.7481C18.4909 13.7481 18.6709 13.7381 18.8409 13.7181C18.9609 13.7081 19.0709 13.6981 19.1809 13.6781C19.4909 13.6381 19.7709 13.5481 20.0509 13.4581C20.7009 13.2381 21.3709 13.7181 21.3709 14.3981Z" fill="currentColor"/>
            </svg>
          </>
        ),
        show: hasMultipleStores,
        disable: !uploadMethods[ProductCreateMethod.STORE],
        key: ProductCreateMethod.STORE,
        color: "text-accent-yellow-500",
      },
      {
        title: "Import from Instagram",
        description: canUploadFromInstagram
          ? "Import products from your instagram posts"
          : "Upgrade to a paid plan to continue importing products from Instagram",
        icon: (
          <>
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 24 24" fill="none">
              <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM12 15.88C9.86 15.88 8.12 14.14 8.12 12C8.12 9.86 9.86 8.12 12 8.12C14.14 8.12 15.88 9.86 15.88 12C15.88 14.14 14.14 15.88 12 15.88ZM17.92 6.88C17.87 7 17.8 7.11 17.71 7.21C17.61 7.3 17.5 7.37 17.38 7.42C17.26 7.47 17.13 7.5 17 7.5C16.73 7.5 16.48 7.4 16.29 7.21C16.2 7.11 16.13 7 16.08 6.88C16.03 6.76 16 6.63 16 6.5C16 6.37 16.03 6.24 16.08 6.12C16.13 5.99 16.2 5.89 16.29 5.79C16.52 5.56 16.87 5.45 17.19 5.52C17.26 5.53 17.32 5.55 17.38 5.58C17.44 5.6 17.5 5.63 17.56 5.67C17.61 5.7 17.66 5.75 17.71 5.79C17.8 5.89 17.87 5.99 17.92 6.12C17.97 6.24 18 6.37 18 6.5C18 6.63 17.97 6.76 17.92 6.88Z" fill="currentColor"/>
            </svg>
          </>
        ),
        disable: !canUploadFromInstagram,
        show: true,
        key: ProductCreateMethod.INSTAGRAM,
        color: "text-accent-red-500",
        extraAction: !canUploadFromInstagram && {
          label: "Upgrade Subscription",
          onClick: () => router.push("/my-store/change-plan"),
        },
      },
      {
        title: "Import from Chowdeck",
        description: "Import items from your Chowdeck account",
        icon: (
          <>
            {/* prettier-ignore */}
            <svg className="text-green-500" width="21" height="24" fill="none">
              <path d="M12.49 13.226c-.696.363-1.498.458-2.259.268a3.243 3.243 0 0 1-1.877-1.302 3.402 3.402 0 0 1 .2-4.237 3.25 3.25 0 0 1 4.22-.633c1.353.847 2.86 1.41 4.432 1.654 1.251.176 2.52.23 3.794.338C20.848 5.331 17.43.22 11.337.006a9.978 9.978 0 0 0-5.375 1.407 10.152 10.152 0 0 0-3.823 4.076 10.316 10.316 0 0 0 .825 10.777l-.166-.126L0 24l6.44-4.74a9.978 9.978 0 0 0 4.214 1.122c5.662.233 9.74-4.242 10.243-8.435a.704.704 0 0 0-.159-.055c-2.86-.265-5.627 0-8.247 1.335" fill="currentColor" data-darkreader-inline-fill="" >
              </path>
            </svg>
          </>
        ),
        show: uploadMethods[ProductCreateMethod.CHOWDECK],
        disable: !uploadMethods[ProductCreateMethod.CHOWDECK],
        key: ProductCreateMethod.CHOWDECK,
        color: "text-accent-orange-500",
      },
    ];
  }, [hasMultipleStores]);

  return (
    <div>
      <div className="mb-7.5">
        <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 m-auto flex items-center justify-center text-white">
          {/* prettier-ignore */}
          <svg className="w-[50%]" viewBox="0 0 24 24" fill="none">
            <path d="M18.3398 14.13L16.1598 12.38C15.8498 12.14 15.4598 12 15.0598 12H12.7498V9H17.2198C18.1898 9 18.9698 8.22 18.9698 7.25V3.75C18.9698 2.78 18.1898 2 17.2198 2H8.95984C8.56984 2 8.17984 2.14 7.86984 2.38L5.67984 4.13C4.80984 4.83 4.80984 6.17 5.67984 6.87L7.86984 8.62C8.17984 8.86 8.56984 9 8.95984 9H11.2498V12H6.79984C5.82984 12 5.04984 12.78 5.04984 13.75V17.25C5.04984 18.22 5.82984 19 6.79984 19H11.2498V21.25H8.99984C8.58984 21.25 8.24984 21.59 8.24984 22C8.24984 22.41 8.58984 22.75 8.99984 22.75H14.9998C15.4098 22.75 15.7498 22.41 15.7498 22C15.7498 21.59 15.4098 21.25 14.9998 21.25H12.7498V19H15.0598C15.4598 19 15.8498 18.86 16.1598 18.62L18.3398 16.87C19.2198 16.17 19.2198 14.83 18.3398 14.13Z" fill="currentColor"/>
          </svg>
        </figure>
        <h2 className="text-center font-bold text-black text-2lg sm:text-[28px] lg:text-[30px] mx-auto mt-3.5 !leading-tight">
          How would you like <br /> to add products?
        </h2>
      </div>

      <div className="w-full mx-auto max-w-[440px] bg-grey-fields-100 bg-opacity-50 rounded-20">
        {options
          .filter((o) => o.show)
          .map((o, idx) => (
            <div key={idx} className="px-5 py-3.75 border-b last:border-b-0 border-grey-divider">
              <div
                onClick={() => !o.disable && selectMethod(o.key)}
                className={classNames("hover:bg-grey-light flex cursor-pointer justify-between w-full  items-center", {
                  "cursor-not-allowed": o.disable,
                })}
              >
                <div className="flex gap-2.5 sm:gap-3.75 ">
                  <div
                    className={classNames(
                      "rounded-full h-11.25 w-11.25 bg-white flex items-center justify-center shadow-card flex-shrink-0",
                      o.color,
                      { "opacity-50": o.disable }
                    )}
                  >
                    {o.icon}
                  </div>
                  <div>
                    <div
                      className={classNames({
                        "opacity-50": o.disable,
                      })}
                    >
                      <h2 className="font-bold text-1sm sm:text-base text-black">{o.title}</h2>
                      <p className="text-xs sm:text-1xs text-black-placeholder mt-1">{o.description}</p>
                    </div>

                    {o.extraAction && (
                      <div>
                        <button
                          className={`flex items-center text-primary-500 mt-2`}
                          onClick={(e) => {
                            e.stopPropagation();
                            o.extraAction.onClick();
                          }}
                        >
                          <span className="text-xs text-primary-500 font-semibold uppercase">
                            {o.extraAction.label}
                          </span>
                          {/* prettier-ignore */}
                          <svg className="w-4" viewBox="0 0 15 16" fill="none">
                            <path d="M3.96484 11.5355L11.0359 4.46446" stroke="#332089" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
                            <path d="M11.0352 11.5355L11.0352 4.46446L3.96409 4.46445" stroke="#332089" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                <div className="pl-5 lg:pl-12 text-grey-muted">
                  {/* prettier-ignore */}
                  <svg width="16" viewBox="0 0 24 24" fill="none">
                    <path d="M8.91003 19.9201L15.43 13.4001C16.2 12.6301 16.2 11.3701 15.43 10.6001L8.91003 4.08008" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};
export default SelectProductMethod;
