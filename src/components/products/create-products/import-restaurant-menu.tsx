import { ExtractMenuItemsFromImage } from "@/api";
import { useRequest } from "@/api/utils";
import { generateSimpleUUID, toBase64 } from "@/assets/js/utils/functions";
import handleImageSelectionFromFile from "@/assets/js/utils/image-selection";
import useProgress from "@/components/hooks/useProgess";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import ProgressModal from "@/components/ui/progress-modal";
import { Product, ProductForm } from "@/pages/products/create";
import { useEffect, useRef, useState } from "react";
import { Image } from "../../../assets/interfaces";
import ErrorLabel from "@/components/ui/error-label";
import ImagePicker from "@/components/ui/form-elements/image-picker";

interface ExtractionPayload {
  name: string;
  description: string;
  price: number;
}
interface Props {
  show: boolean;
  toggle: VoidFunction;
  onComplete: VoidFunction;
  setForm: (products: ProductForm) => void;
}
const ImportRestaurantMenu: React.FC<Props> = ({ show, toggle, onComplete, setForm }) => {
  const { currentProgressStep: currentProgressStep, progressSteps, setStepIsLoading, setStepComplete } = useProgress([
    { key: "EXTRACT", label: "Extracting items from menu...", isLoading: false, complete: false },
  ]);
  const [image, setImage] = useState<string>(null);

  const { makeRequest, error, isLoading } = useRequest<{
    menu_image: string;
  }>(ExtractMenuItemsFromImage);

  useEffect(() => {
    if (isLoading) {
      setStepIsLoading();
    }
  }, [isLoading]);

  const createBlankProducts = (payload: ExtractionPayload[]) => {
    const products: Product[] = [];
    for (let i = 0; i < payload.length; i++) {
      products.push({
        category: "",
        description: payload[i].description,
        images: [
          {
            file: null,
            lastModified: Date.now(),
            name: "placeholder",
            src: "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/rice.png",
            url: "https://res.cloudinary.com/catlog/image/upload/v1713111825/food-preset-images/rice.png",
            isUploading: false,
            isPlaceholder: true,
          },
        ],
        name: payload[i].name,
        price: payload[i].price.toString(),
        thumbnail: 0,
        hasImages: false,
        variants: { options: [], type: "custom" },
        id: generateSimpleUUID(),
      });
    }
    setForm({ products });
    onComplete();
    toggle();
  };

  const uploadAndExtractItems = async () => {
    const [res, err] = await makeRequest({ menu_image: image });
    if (res) {
      setStepComplete();
      const payload = res.data as ExtractionPayload[];
      createBlankProducts(payload);
      toggle();
      return;
    }
  };

  return (
    <Modal show={show} toggle={toggle} title="Import from Menu Image" size="midi">
      <ModalBody>
        <div className="flex flex-col">
          <ErrorLabel error={error?.message} />
          {!isLoading && (
            <>
              <h5 className="text-sm font-medium text-black-secondary mb-2.5">
                Upload a menu image to extract products from
              </h5>
              <ImagePicker {...{ image, setImage }} />
            </>
          )}

          {isLoading && (
            <ProgressModal stepCount={2} isEmbedded steps={progressSteps} currentStep={currentProgressStep} />
          )}
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn disabled={!image || isLoading} onClick={uploadAndExtractItems} isBlock color="primary">
          Extract Products
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};
export default ImportRestaurantMenu;
