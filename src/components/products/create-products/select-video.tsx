import React, { useMemo, useRef } from "react";
import { AddCircle, Warning2 } from "iconsax-react";
import useMediaProcessor from "@/components/hooks/useMediaProcessor";
import { MediaType } from "@/assets/interfaces";
import { Media } from "@/assets/interfaces";
import { Product } from "@/pages/products/create";
import { useRouter } from "next/router";
interface Props {
  product: Product;
  saveMedias: (media: Media[]) => void;
  canUploadVideos: boolean;
  canProcessVideos: boolean;
}

const SelectVideo = ({ product, saveMedias, canUploadVideos, canProcessVideos }: Props) => {
  const router = useRouter();
  const imagePicker = useRef<HTMLInputElement>(null);

  const productMedias: Media[] = useMemo(() => {
    const imgMedias = (product.images ?? [])?.map((i) => ({ ...i, type: MediaType.IMAGE }));
    const videoMedias = (product.videos ?? [])?.map((i) => ({ ...i, type: MediaType.VIDEO }));
    return [...imgMedias, ...videoMedias];
  }, [product]);

  const { processFiles } = useMediaProcessor(
    (m) => {
      saveMedias(m);
    },
    productMedias,
    null,
    false
  );

  const handleClick = () => {
    if (canUploadVideos && canProcessVideos) {
      imagePicker.current?.click();
    } else {
      router.push("/my-store/change-plan");
    }
  };

  return (
    <>
      <div
        className="w-full pt-[22%] border border-dashed border-grey-border rounded-15 flex flex-col items-center justify-center relative hover:border-primary-500 transition-all duration-300 ease-out cursor-pointer"
        onClick={handleClick}
      >
        <div className="h-full w-full absolute inset-0 flex items-center justify-center flex-col">
          <div className="flex items-center">
            <AddCircle size={20} variant="Outline" className="text-placeholder" />
            <p className="text-1xs text-dark font-medium ml-0.5 mt-0.5">Add Video</p>
          </div>
          {canUploadVideos ? (
            <p className="text-xs text-placeholder mt-1.5">MP4,MOV,OGG,WEBM allowed, max 100MB</p>
          ) : (
            <p className="text-xs text-placeholder mt-1.5">
              Please upgrade to the business+ plan to add product videos.{" "}
              <span className="text-primary-500 font-semibold">Upgrade Plan</span>
            </p>
          )}

          {canUploadVideos && !canProcessVideos && (
            <div className="p-2.5 bg-opacity-20 mt-3.75 rounded-md gap-2.5 bg-accent-orange-100 text-accent-orange-500 text-xs flex items-center justify-center">
              <Warning2 size={15} className="text-accent-orange-500" />
              You cannot process videos on this browser! Please use a desktop computer.
            </div>
          )}
        </div>
      </div>
      <input
        type="file"
        ref={imagePicker}
        name="product-medias"
        multiple
        accept={"video/mp4,webm,ogg,video/quicktime"}
        id="product-media-picker"
        className="hidden"
        onChange={(e) => processFiles(e.target.files, [])}
      />
    </>
  );
};

export default SelectVideo;
