import React from "react";
import Modal, { ModalBody, ModalProps } from "../../ui/modal";

interface Props extends ModalProps {}

const VariantsExplainerModal: React.FC<Props> = ({ show, toggle }) => {
  return (
    <Modal {...{ show, toggle }} title="What are product options?" size="midi">
      <ModalBody>
        <div className="text-dark">
          Product options is a simple way to show your customers the options of products you have available. On catlog
          you can add options in two ways.
          <br />
          <br />
          1. With Images
          <br />
          2. With Texts (custom)
          <br />
          <br />
          Image variants are useful when the product options are visual, say you sell air force 1 sneakers - airforce
          1&apos;s do not all come in simple plain colors so it makes sense to show customers the actual designs as
          pictures.
          <br /> <br />
          Custom variants are useful when you want to show options of size (M, L), colors (Blue, Green) or Memory Size
          for phones (32GB, 64GB) e.t.c
          <br />
          <br />
          <b>This video shows how to add options for your products</b>
          <br /> <br />
          <iframe
            width="100%"
            height="250px"
            src="https://www.youtube.com/embed/_gfoPYrH5W4"
            title="YouTube video player"
            frameBorder={0}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
          <br />
          <br />
          <b>This video shows how customers get to buy from your store</b>
          <br />
          <br />
          <iframe
            width="100%"
            height="250px"
            src="https://www.youtube.com/embed/T0YV523z00o"
            title="YouTube video player"
            frameBorder={0}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default VariantsExplainerModal;
