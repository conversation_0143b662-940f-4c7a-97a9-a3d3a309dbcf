import { emit } from "@/components/hooks/useListener";
import useProductController, { ProductCreateMethod } from "@/components/hooks/useProductController";
import useVideoTranscode, { ffmpegContext, VideoStepStatus } from "@/components/hooks/useVideoTranscode";
import AddMedia from "@/components/products/create-products/add-media";
import InstagramImportManager from "@/components/products/create-products/instagram-import-manager";
import SelectManualMethod from "@/components/products/modals/select-manual-method";
import VideoResourcesLoader from "@/components/products/modals/video-resources-loader";
import React from "react";
import { AddProductsForm } from "..";
import { Category, MediaType, StoreInterface } from "../../../assets/interfaces";
import Portal from "../../portal";
import ImportProducts from "../import-products";
import ChowdeckImportModal from "../modals/chowdeck-import";
import VideoUploadProgress from "../modals/video-upload-progress";
import CreateBlankProducts from "./create-blank-products";
import AddProductsError from "./error";
import ImportRestaurantMenu from "./import-restaurant-menu";
import SelectProductMethod from "./select-method";
import AddProductsSuccess from "./success";
import ProductUploadTutorial from "./upload-tutorial";

interface Props {
  maxUploadable: number;
  store: StoreInterface;
  categories: Category[];
  success: {
    label: string;
    route: string;
  };
  isSetup?: boolean;
}

const ProductUploadController: React.FC<Props> = ({ maxUploadable, categories, success, isSetup = false }) => {
  const {
    allowChowdeckImports,
    canInstagramUpload,
    allowVariants,
    canUploadMenu,
    changeView,
    createReq,
    form,
    handleSelectMethod,
    maxInstagramUpload,
    medias,
    setMedias,
    modals,
    pastProgress,
    selectingMedia,
    submitForm,
    toggleModal,
    userStores,
    currentStep,
    setCurrentStep,
    uploadMethod,
    currentProduct,
    setForm,
    templates,
    loadModals,
    canUploadVideos,
    infoBlocksData,
  } = useProductController(isSetup);

  const { ffmpegRef, ffmpegLoading, canTranscode, loadProgress } = ffmpegContext.useContainer();
  const { removeVideoProgress, retryVideoTask, videoProgresses, transcodeVideo } = useVideoTranscode({
    ffmpegRef,
    cb: (taskId, videoKey, url, blob) => {
      emit("video-url", { taskId, videoKey, url, blob });
    },
  });

  const handleSubmit = () => {
    const videosAreProcessing = Object.values(videoProgresses.value).some((progressObj) =>
      Object.values(progressObj).some((taskProgress) => taskProgress.step !== VideoStepStatus.SUCCESS)
    );
    if (videosAreProcessing) {
      toggleModal("progresses");
      return;
    }
    submitForm();
  };

  const uploadMethods = {
    [ProductCreateMethod.MANUAL]: true,
    [ProductCreateMethod.CHOWDECK]: allowChowdeckImports,
    [ProductCreateMethod.STORE]: userStores.length > 1,
    [ProductCreateMethod.MENU_IMAGE]: canUploadMenu,
    [ProductCreateMethod.INSTAGRAM]: canInstagramUpload,
  };

  return (
    <>
      <div className="w-full mx-auto max-w-[400px] pb-15">
        {currentStep === "method" && (
          <SelectProductMethod
            hasMultipleStores={userStores?.length > 1}
            selectMethod={handleSelectMethod}
            uploadMethods={uploadMethods}
          />
        )}
        {uploadMethod === ProductCreateMethod.MANUAL && (
          <div className={`${selectingMedia ? "" : "hidden"}`}>
            <AddMedia
              currentStep={currentStep}
              form={form}
              medias={medias}
              maxUploadable={maxUploadable}
              pastProgress={pastProgress}
              setCurrentStep={setCurrentStep}
              setForm={setForm}
              setMedias={setMedias}
              canProcessVideos={canTranscode}
              canUploadVideos={canUploadVideos}
            />
          </div>
        )}

        {currentStep === "details" &&
          form.products.map((product, index) => (
            <div className={`${index === currentProduct ? "" : "hidden"}`} key={product.id}>
              <AddProductsForm
                index={index}
                setForm={setForm}
                form={form}
                changeView={changeView}
                submitForm={handleSubmit}
                isLoading={createReq.isLoading}
                categories={categories}
                allowVariants={allowVariants}
                isSetup={isSetup}
                currentIndex={currentProduct}
                product={product}
                variantTemplates={templates}
                toggleOuterModal={(m) => toggleModal(m)}
                videoProgresses={videoProgresses.value}
                removeVideoProgress={(_, k) => removeVideoProgress(product.id, k)}
                retryTranscode={retryVideoTask}
                transcodeVideo={transcodeVideo}
                canProcessVideos={canTranscode}
                videoResourcesLoaded={!ffmpegLoading}
                canUploadVideos={canUploadVideos}
                infoBlocksData={infoBlocksData}
              />
            </div>
          ))}
        {createReq.response && currentStep === "response" && (
          <AddProductsSuccess count={form.products.length} success={success} />
        )}
        {createReq.error && currentStep === "response" && (
          <AddProductsError
            errorCode={createReq.error?.statusCode}
            retryFunc={submitForm}
            goBack={() => setCurrentStep("details")}
            isLoading={createReq.isLoading}
          />
        )}
      </div>
      <Portal>
        <CreateBlankProducts
          setForm={setForm}
          toggle={() => toggleModal("count")}
          show={modals.count.show}
          onComplete={() => setCurrentStep("details")}
          maxUploadable={maxUploadable}
        />
        {selectingMedia && (
          <>
            <button
              className="fixed bottom-5 right-5 sm:bottom-10 sm:right-10 px-3 py-2 sm:px-4 sm:py-2.5 bg-accent-green-500 text-white font-semibold text-1xs sm:text-sm rounded-full flex items-center shadow-icon1 hover:shadow-none transition-all ease-out duration-300"
              onClick={() => toggleModal("tutorial")}
            >
              See quick tutorial
              {/* prettier-ignore */}
              <svg viewBox="0 0 20 20" fill="none" className="ml-1 w-3.5 sm:w-4">
                <path d="M7.58325 9.99999V8.76666C7.58325 7.17499 8.70825 6.53333 10.0833 7.32499L11.1499 7.94166L12.2166 8.55833C13.5916 9.34999 13.5916 10.65 12.2166 11.4417L11.1499 12.0583L10.0833 12.675C8.70825 13.4667 7.58325 12.8167 7.58325 11.2333V9.99999Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M10.0001 18.3334C14.6025 18.3334 18.3334 14.6024 18.3334 10C18.3334 5.39765 14.6025 1.66669 10.0001 1.66669C5.39771 1.66669 1.66675 5.39765 1.66675 10C1.66675 14.6024 5.39771 18.3334 10.0001 18.3334Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <ProductUploadTutorial
              toggle={() => toggleModal("tutorial")}
              proceed={() => toggleModal("tutorial")}
              show={modals.tutorial.show}
            />
          </>
        )}
        <InstagramImportManager
          active={uploadMethod === ProductCreateMethod.INSTAGRAM}
          {...{
            form,
            setForm,
            medias: medias,
            setMedias: setMedias,
            setCurrentStep,
            maxUploadable: Math.min(maxUploadable, maxInstagramUpload),
          }}
        />
        <VideoResourcesLoader
          toggle={() => toggleModal("loader")}
          show={modals.loader.show}
          loadProgress={loadProgress}
        />
        <ImportProducts toggle={() => toggleModal("import")} show={modals.import.show} success={success} />
        <ImportRestaurantMenu
          setForm={setForm}
          toggle={() => toggleModal("menu")}
          show={modals.menu.show}
          onComplete={() => setCurrentStep("details")}
        />
        {loadModals.chowdeck && (
          <ChowdeckImportModal toggle={() => toggleModal("chowdeck")} show={modals.chowdeck.show} />
        )}
        <VideoUploadProgress
          retryTranscode={retryVideoTask}
          // productForm={form}
          videoGroups={form.products.map((p) => ({
            taskId: p.id,
            videos: p.videos,
          }))}
          progresses={videoProgresses.value}
          show={modals.progresses.show}
          toggle={() => toggleModal("progresses")}
        />
        <SelectManualMethod
          pastProgress={pastProgress}
          show={modals.manual.show}
          toggleModal={() => toggleModal("manual")}
          setMedias={setMedias}
          setStep={setCurrentStep}
          openCountModal={() => toggleModal("count")}
          loadLastProgress={() => {
            setForm(pastProgress);
            setMedias(
              pastProgress.products.map(({ images }) => ({ ...images[0], src: images[0].url, type: MediaType.IMAGE }))
            ); //todo: @silas update this to use the correct media type
            setCurrentStep("media");
            toggleModal("manual");
          }}
          maxUploadable={maxUploadable}
          medias={medias}
          canProcessVideos={canTranscode}
        />
      </Portal>
    </>
  );
};

export default ProductUploadController;

/* 
  TODO
  - show when a device can't run video compression and disable video picking
  - show list of uncompleted video tasks
  - handle duplicates in single form view
  - add formik validation for thumbnail selection
  - trim videos 
  - handle Transcoding errors
  - disable video file processing when transcoding is disabled
*/
