import { is } from "date-fns/locale";
import React, { useCallback, useEffect, useRef, useState } from "react";
import useSlider from "../../hooks/useSlider";
import { AppBtn } from "../../ui/buttons";
import FullScreenModal from "../../ui/full-screen-modal";

interface IProps {
  show: boolean;
  toggle: () => void;
  proceed: () => void;
}

const ProductUploadTutorial: React.FC<IProps> = ({ show, toggle, proceed }) => {
  const { slide, slider, currentSlide, next, previous, hasNext, hasPrevious, switchSlides } = useSlider({
    slides: steps.length,
    gap: 0,
    sameWidthAsSlider: true,
  });

  const isLastStep = currentSlide === steps.length - 1;

  useEffect(() => {
    if (show) {
      switchSlides(0);
    }
  }, [show]);

  return (
    <FullScreenModal title="Product upload tutorial" {...{ show, toggle }}>
      <div className="px-5 sm:px-6.25 lg:px-7.5 py-14 sm:py-16 lg:py-20 ">
        <div className="max-w-[500px] mx-auto w-full">
          <ul className="grid w-full grid-cols-[repeat(3,100%)] overflow-hidden" ref={slider}>
            {steps.map((step, index) => (
              <Slide key={index} step={step} slide={slide} index={index} isActive={index === currentSlide && show} />
            ))}
          </ul>
          <div className="flex items-center space-x-2.5 mt-6.25 sm:mt-8">
            {hasPrevious && (
              <AppBtn size="lg" color="neutral" onClick={previous} className="flex-1">
                Previous
              </AppBtn>
            )}
            <AppBtn size="lg" onClick={isLastStep ? proceed : next} className="flex-1">
              {isLastStep ? "Alright got it!" : "Next"}
            </AppBtn>
          </div>
        </div>
      </div>
    </FullScreenModal>
  );
};

interface SlideProp {
  step: {
    title: string;
    description: string;
    color: string;
    video: string;
  };
  slide: React.MutableRefObject<HTMLDivElement>;
  index: number;
  isActive: boolean;
}

const Slide: React.FC<SlideProp> = ({ step, slide, index, isActive }) => {
  const { title, description, color, video } = step;
  const [isPlaying, setIsPlaying] = useState(true);
  const [videoEl, setVideoEl] = useState<HTMLVideoElement>(null);

  const newVideoRef = useCallback((node) => {
    if (node !== null) {
      setVideoEl(node);

      const stop = () => setIsPlaying(false);
      const start = () => setIsPlaying(true);

      node.addEventListener("pause", stop);
      node.addEventListener("ended", stop);
      node.addEventListener("play", start);
    }
  }, []);

  useEffect(() => {
    if (!videoEl) return;

    if (isActive) {
      setTimeout(() => {
        videoEl?.play();
      }, 800);
    } else {
      videoEl?.pause();
      videoEl.currentTime = 0;
    }
  }, [isActive, videoEl]);

  return (
    <div className="flex items-center flex-col" ref={slide}>
      <figure className={`h-12.5 w-12.5 sm:h-15 sm:w-15 rounded-full flex items-center justify-center ${color}`}>
        {/* prettier-ignore */}
        <svg width="50%" viewBox="0 0 28 28" fill="none">
        <path d="M23.9166 18.6667V21.5833C23.9166 23.835 22.0849 25.6667 19.8333 25.6667H8.16659C5.91492 25.6667 4.08325 23.835 4.08325 21.5833V20.825C4.08325 18.9933 5.57659 17.5 7.40825 17.5H22.7499C23.3916 17.5 23.9166 18.025 23.9166 18.6667Z" fill="white"/>
        <path d="M18.0833 2.3335H9.91659C5.24992 2.3335 4.08325 3.50016 4.08325 8.16683V17.0102C4.96992 16.2285 6.13659 15.7502 7.40825 15.7502H22.7499C23.3916 15.7502 23.9166 15.2252 23.9166 14.5835V8.16683C23.9166 3.50016 22.7499 2.3335 18.0833 2.3335ZM15.1666 12.5418H9.33325C8.85492 12.5418 8.45825 12.1452 8.45825 11.6668C8.45825 11.1885 8.85492 10.7918 9.33325 10.7918H15.1666C15.6449 10.7918 16.0416 11.1885 16.0416 11.6668C16.0416 12.1452 15.6449 12.5418 15.1666 12.5418ZM18.6666 8.4585H9.33325C8.85492 8.4585 8.45825 8.06183 8.45825 7.5835C8.45825 7.10516 8.85492 6.7085 9.33325 6.7085H18.6666C19.1449 6.7085 19.5416 7.10516 19.5416 7.5835C19.5416 8.06183 19.1449 8.4585 18.6666 8.4585Z" fill="white"/>
      </svg>
      </figure>
      <h4 className="text-lg sm:text-2lg font-bold text-black mt-3 sm:mt-3.75 capitalize">
        <span className="text-primary-500">Step {index + 1}</span> - {title}
      </h4>
      <figure
        className="w-full bg-grey-fields-100 mt-8 sm:mt-10 rounded-[18px] relative overflow-hidden"
        style={{ paddingTop: "60%" }}
      >
        <video
          crossOrigin="anonymous"
          src={video}
          className="h-full w-full absolute top-0 left-0"
          ref={newVideoRef}
          muted
          playsInline
        ></video>
        {!isPlaying && (
          <AppBtn
            className="!text-black shadow-card !font-semibold absolute z-20 right-5 bottom-5"
            color="white"
            size="sm"
            onClick={() => videoEl.play()}
          >
            Replay
          </AppBtn>
        )}
      </figure>
      <p className="text-center text-sm sm:text-base font-medium mt-3.75 text-black-secondary">{description}</p>
    </div>
  );
};

const steps = [
  {
    title: "Upload Images",
    description: "Add one image for each of the products you want to upload, then click “proceed”",
    video: "https://res.cloudinary.com/catlog/video/upload/v1669562824/product-tutorial/step-1.mp4",
    color: "bg-accent-red-500",
  },
  {
    title: "Add product details",
    description:
      "Fill in the information for the first image, you can add more images here too, then click on “next product”",
    video: "https://res.cloudinary.com/catlog/video/upload/v1669562824/product-tutorial/step-2.mp4",
    color: "bg-accent-green-500",
  },
  {
    title: "Add other details and submit",
    description: "Repeat step 2 for all the images you added, at the last image click on “upload products”",
    video: "https://res.cloudinary.com/catlog/video/upload/v1669562824/product-tutorial/step-3.mp4",
    color: "bg-accent-orange-500",
  },
];

export default ProductUploadTutorial;
