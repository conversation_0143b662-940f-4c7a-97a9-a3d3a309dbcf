import { getProductThumbnail } from "@/assets/js/utils/utils";
import classNames from "classnames";
import { FormikProps } from "formik";
import { useEffect, useRef } from "react";
import { StoreInterface, VariantForm } from "../../../assets/interfaces";
import { toCurrency } from "../../../assets/js/utils/functions";
import { Product } from "../../../pages/products/create";
import LazyImage from "../../lazy-image";
import Badge from "../../ui/badge";
import { AppBtn } from "../../ui/buttons";
import DataAccordion from "../../ui/data-accordion";
import Checkbox from "../../ui/form-elements/checkbox";
import { StepNumberInput } from "../../ui/form-elements/step-number-input";
import { VariantQuantityTable } from "../variants/variant-quantity-table";

interface MangageQuantityProps {
  form: FormikProps<Product>;
  store: StoreInterface;
  isCreating?: boolean;
}

const MangageProductQuantity: React.FC<MangageQuantityProps> = ({ form, store, isCreating }) => {
  const product = form.values;
  // const { quantity, variants: { options, type } = { options: [], type: "custom" } } = product;
  const { quantity, is_always_available } = product;
  const { options, type, is_template } = product?.variants ?? { options: [], type: "custom" };
  const isFirstRender = useRef(true);

  const {
    configuration: { direct_checkout_enabled },
    flags,
  } = store;

  const requiresQuantity = direct_checkout_enabled || flags?.uses_chowbot;

  useEffect(() => {
    if (requiresQuantity && !(quantity > 0) && !is_always_available && !isCreating) {
      form.setFieldValue("quantity", 10);
      form.setFieldValue("is_always_available", false);
    }
  }, [isCreating]);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    if (requiresQuantity) {
      let unitQuantity = Math.floor((product.quantity ?? 10) / options.length);
      let quantityAdded = 0;

      const optionsWithQuanitity = options.map((o, index) => {
        let quantityToAdd = unitQuantity;

        if (unitQuantity < 1 && quantity > 0 && quantityAdded < quantity) {
          quantityToAdd = 1;
        }

        if (index === options.length - 1) {
          quantityToAdd = quantity - quantityAdded;
        }

        o.quantity = quantityToAdd;
        quantityAdded += quantityToAdd;
        return o;
      });

      form.setFieldValue("variants", {
        type,
        options: optionsWithQuanitity,
        is_template,
      });
    }
  }, [options.length]);

  const updateVariantForm = (variants: VariantForm) => {
    form.setFieldValue("variants", variants);
    const totalQuantity = variants.options.reduce((p, c) => p + (c.quantity ?? 0), 0);
    form.setFieldValue("quantity", totalQuantity);
  };

  const resetQuantity = () => {
    const formValuesCopy = JSON.parse(JSON.stringify(product));

    delete formValuesCopy.quantity;
    delete formValuesCopy.is_always_available;

    if (formValuesCopy?.variants?.options?.length > 0) {
      formValuesCopy.variants.options = formValuesCopy.variants.options.map((o) => {
        const optionCopy = { ...o };

        delete optionCopy.quantity;

        return optionCopy;
      });
    }

    form.setValues(formValuesCopy);
  };

  const hasVariants = product.variants?.options?.length > 0;

  return (
    <>
      <DataAccordion
        isClosed={!requiresQuantity}
        title={
          <span className="text-base font-bold font-body tracking-normal">
            <span className="font-display">Manage product quantity</span>
            <Badge
              className="ml-2"
              text={requiresQuantity ? "REQUIRED" : "OPTIONAL"}
              color={requiresQuantity ? "red" : "dark"}
            ></Badge>
          </span>
        }
      >
        <div className="pt-6.25 text-left">
          <div className="">
            <div
              className={classNames("flex justify-between pb-5", {
                "items-center": !product.is_always_available && !hasVariants,
                "items-start": product.is_always_available || hasVariants,
              })}
            >
              <div className="flex-1 flex items-center overflow-hidden mr-2.5 w-full">
                <figure className="flex-shrink-0 h-[47px] w-[47px] rounded-8 overflow-hidden mr-3 relative">
                  <LazyImage
                    src={getProductThumbnail(product)}
                    className="h-full w-full object-cover rounded-md relative z-10"
                    alt={product.name ?? ""}
                  />
                </figure>
                <div className="flex-1 w-full overflow-hidden">
                  <div className="flex items-center mr-2.5 w-full justify-start">
                    <h4 className="text-black-secondary text-sm sm:text-1sm mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis block font-body max-w-full mb-0.5">
                      {product.name}
                    </h4>
                  </div>
                  <p className="font-semibold text-xs sm:text-1xs text-black-secondary inline-block">
                    {toCurrency(product.price)}
                  </p>
                </div>
              </div>
              {!product.is_always_available && !hasVariants && (
                <StepNumberInput
                  min={0}
                  onChange={(value) => {
                    form.setFieldValue("quantity", value);
                    form.setFieldValue("is_always_available", false);
                  }}
                  value={product.quantity ?? 0}
                />
              )}
              {(product.is_always_available || hasVariants) && (
                <span className="text-xs text-dark font-medium inline-flex items-center px-2.5 py-1.5 rounded-5 bg-grey-fields-100">
                  {product?.is_always_available
                    ? "Always Available"
                    : product?.quantity > -1
                    ? `${product?.quantity} available`
                    : "Quantity not set"}
                </span>
              )}
            </div>
            <div className="flex items-center justify-between">
              <div
                className="inline-flex items-center gap-2.5 cursor-pointer"
                onClick={() => form.setFieldValue("is_always_available", !Boolean(product?.is_always_available))}
              >
                <Checkbox
                  onChange={() => form.setFieldValue("is_always_available", !Boolean(product?.is_always_available))}
                  checked={product?.is_always_available}
                  name={"mark"}
                  round
                  small
                />
                <span className="text-1xs sm:text-sm text-dark font-medium">Always available</span>
              </div>
              {(product.quantity > -1 || product?.is_always_available) && !requiresQuantity && (
                <AppBtn color="neutral" className="!text-accent-red-500 !text-1xs" size="sm" onClick={resetQuantity}>
                  Remove Quantity
                </AppBtn>
              )}
            </div>
          </div>

          {options?.length > 0 && !product?.is_always_available && (
            <div className="border-t border-grey-border border-opacity-50 mt-3.5 pt-3.5">
              <h3 className="font-bold text-black-secondary text-1sm">Provide quantities for each option</h3>
              <div className="">
                <VariantQuantityTable
                  variants={product.variants}
                  updateVariants={(variants) => updateVariantForm(variants)}
                />
              </div>
            </div>
          )}
        </div>
      </DataAccordion>
    </>
  );
};
export default MangageProductQuantity;
