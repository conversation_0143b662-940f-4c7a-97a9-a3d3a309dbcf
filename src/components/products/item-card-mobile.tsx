import { toAppUrl, toCurrency } from "../../assets/js/utils/functions";
import { getItemThumbnail } from "../../assets/js/utils/utils";
import { ProductItemInterface } from "../../assets/interfaces";
import Dropdown, { DropdownItem } from "../ui/dropdown-new";
import { toast, ToastWithPromiseOptions } from "../ui/toast";
import Toggle from "../ui/toggle";
import router from "next/router";
import Badge from "../ui/badge";
import { QuantityDisplay } from "./product-item";
import { RoundActionBtn } from "../ui/buttons";
import LazyImage from "../lazy-image";
import useShare from "../hooks/useShare";
import authContext from "@/contexts/auth-context";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";
import { useModals } from "../hooks/useModals";
import SelectAffiliate from "./modals/select-affiliate";

interface Props {
  product: ProductItemInterface;
  handleAvailabilityToggle: (available: boolean) => void;
  handleFeatureUpdate: (is_featured: boolean) => void;
  toggleModal: (key: string) => void;
  isStats?: boolean;
  available: boolean;
  itemsHaveQuantity: boolean;
  copyLink: VoidFunction;
  share: () => Promise<void>;
  selectAffiliate: () => void;
  canManageAffiliates: boolean;
}

const ItemMobile: React.FC<Props> = ({
  product,
  handleAvailabilityToggle,
  toggleModal,
  isStats,
  available,
  itemsHaveQuantity,
  copyLink,
  share,
  handleFeatureUpdate,
  selectAffiliate,
  canManageAffiliates,
}) => {
  const { userRole } = authContext.useContainer();
  const {
    name,
    description,
    thumbnail,
    price,
    category,
    views = 0,
    images,
    variants,
    slug,
    is_always_available,
    quantity,
    discount_price,
    is_featured,
    total_orders,
  } = product;

  const canEditProducts = actionIsAllowed({
    userRole,
    permission: SCOPES.PRODUCTS.EDIT_PRODUCTS,
  });

  const { modals, toggleModal: toggleAffiliateModal } = useModals(["select_affiliate"]);

  const handlePaymentLinkClick = () => {
    // Route to payment link form with prefilled data
    router.push({
      pathname: "/payments/payment-links/create",
      query: {
        amount: discount_price || price,
        narration: `Payment for ${name}`,
      },
    });
  };

  const dropdownItems: DropdownItem[] = [
    {
      text: is_featured ? "Item Is Featured" : "Feature Item",
      onClick: () => handleFeatureUpdate(!is_featured),
      icon: (
        <RoundActionBtn size="sm">
          {/* prettier-ignore */}
          <svg width="50%" viewBox="0 0 24 24" fill="none" className={is_featured ? "text-accent-green-500" : ""}>
            <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </RoundActionBtn>
      ),
      className: is_featured ? "text-accent-green-500" : "",
    },
    {
      text: "Copy Link",
      link: undefined,
      onClick: copyLink,
      icon: <RoundActionBtn size="sm" icon="copy" />,
    },
    {
      text: "Copy Affiliate Link",
      link: undefined,
      onClick: selectAffiliate,
      icon: <RoundActionBtn size="sm" icon="copy" />,
    },
    canManageAffiliates
      ? {
          text: "Create Payment Link",
          onClick: handlePaymentLinkClick,
          icon: <RoundActionBtn size="sm" icon="link" />,
        }
      : null,
    canEditProducts
      ? {
          text: "Edit Item",
          link: undefined,
          onClick: () => {
            toggleModal("edit");
          },
          icon: <RoundActionBtn size="sm" icon="edit" />,
        }
      : null,
    {
      text: "Duplicate Item",
      link: undefined,
      onClick: () => {
        toggleModal("duplicate");
      },
      icon: <RoundActionBtn size="sm" icon="copy_doc" />,
    },
    {
      text: "Share Item",
      link: undefined,
      onClick: share,
      icon: <RoundActionBtn size="sm" icon="route" />,
    },
    canEditProducts
      ? {
          text: "Delete Item",
          link: undefined,
          onClick: () => {
            toggleModal("delete");
          },
          icon: <RoundActionBtn size="sm" icon="delete" className="text-accent-red-500" />,
          className: "text-accent-red-500",
        }
      : null,
    {
      text: "",
      link: undefined,
      onClick: () => {
        // nothing here
      },
      icon:
        //prettier-ignore
        <Toggle
          intialState={available}
          onChange={handleAvailabilityToggle}
          size="sm"
          onLabel="Available"
          offLabel="Unavailable"
        />,
    },
  ].filter((i) => !!i) as DropdownItem[];

  return (
    <>
      <li
        onClick={() => router.push(`/products/${slug}`)}
        className="rounded-15 border border-grey-border border-opacity-50 p-3 mb-2.5 cursor-pointer hover:bg-grey-fields-100 hover:bg-opacity-30 transition-all ease-out"
      >
        <div className="relative flex items-center">
          <figure className="w-14 h-14 rounded-10 overflow-hidden flex-shrink-0 relative">
            <LazyImage src={getItemThumbnail(product)} alt={name} className="w-full h-full object-cover" />
            {is_featured && (
              <div title="Featured Item" className="absolute left-1 bottom-1">
                {/* prettier-ignore */}
                <svg className="w-5 text-accent-yellow-500"viewBox="0 0 24 24" fill="none" >
                  <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM15.53 13.23L10.35 16.82C9.59 17.35 9.15 17.04 9.37 16.15L10.32 12.31L8.67 11.9C7.92 11.72 7.83 11.2 8.46 10.76L13.64 7.17C14.4 6.64 14.84 6.95 14.62 7.84L13.67 11.68L15.32 12.09C16.07 12.28 16.16 12.79 15.53 13.23Z" fill="currentColor" />
                </svg>
              </div>
            )}
          </figure>
          <div className="flex flex-col items-start ml-2.5 flex-1 overflow-hidden">
            <div className="mb-1.5 absolute -bottom-1 right-0">
              {isStats && <Badge text={`${total_orders} orders`} color="orange" />}

              {!isStats && !itemsHaveQuantity && (
                <Badge
                  text={`${variants?.options?.length ?? 0} options`}
                  color={variants?.options?.length > 0 ? "yellow" : "dark"}
                  uppercase={false}
                />
              )}

              {!isStats && itemsHaveQuantity && (is_always_available || quantity > -1) && (
                <QuantityDisplay {...{ is_always_available, quantity }} />
              )}
            </div>
            <span className="text-1sm text-dark font-medium block w-full overflow-hidden whitespace-nowrap overflow-ellipsis mb-2.5">
              {name}
            </span>
            <span className="text-1xs text-black font-semibold">{toCurrency(price)}</span>
          </div>
          <div className="absolute top-0 right-0">
            <Dropdown items={dropdownItems} vPosition="BOTTOM" gap={false}>
              <button className="z-[999] dropdown-toggle text-black-400 p-2 -mt-2 -mr-2">
                {/* prettier-ignore */}
                <svg className="w-5 h-5" viewBox="0 0 20 20" fill="none">
                  <g opacity="0.5">
                    <path d="M4.16667 8.73837C3.25 8.73837 2.5 9.48837 2.5 10.405C2.5 11.3217 3.25 12.0717 4.16667 12.0717C5.08333 12.0717 5.83333 11.3217 5.83333 10.405C5.83333 9.48837 5.08333 8.73837 4.16667 8.73837Z" fill="currentColor" />
                    <path d="M15.8337 8.73837C14.917 8.73837 14.167 9.48837 14.167 10.405C14.167 11.3217 14.917 12.0717 15.8337 12.0717C16.7503 12.0717 17.5003 11.3217 17.5003 10.405C17.5003 9.48837 16.7503 8.73837 15.8337 8.73837Z" fill="currentColor" />
                    <path d="M9.99967 8.73837C9.08301 8.73837 8.33301 9.48837 8.33301 10.405C8.33301 11.3217 9.08301 12.0717 9.99967 12.0717C10.9163 12.0717 11.6663 11.3217 11.6663 10.405C11.6663 9.48837 10.9163 8.73837 9.99967 8.73837Z" fill="currentColor" />
                  </g>
                </svg>
              </button>
            </Dropdown>
          </div>
        </div>
      </li>
    </>
  );
};

export default ItemMobile;
