import { toCurrency } from "@/assets/js/utils/functions";
import LazyImage from "../../lazy-image";
import Checkbox from "@/components/ui/form-elements/checkbox";
import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface SortItemProps {
  name: string;
  price: number;
  imageUrl: string;
  checked: boolean;
  id: string;
  onCheck?: () => void;
}

const SortItem: React.FC<SortItemProps> = ({ id, name, price, imageUrl, checked, onCheck }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging, isSorting } = useSortable({
    id,
    animateLayoutChanges: () => false,
  });
  const style: React.CSSProperties = {
    transition,
    opacity: isDragging ? 1 : 1,
    zIndex: isDragging ? 1000 : isSorting ? 999 : "auto",
    transformOrigin: isDragging ? "center center" : "none",
    transform: isDragging ? `${CSS.Translate.toString(transform)} rotate(3.2deg)` : CSS.Translate.toString(transform),
    touchAction: "none"
  };

  return (
    <div className="relative">
      {isDragging && (
        <div className="absolute inset-0 bg-grey-light border border-dashed border-grey-border rounded-15 z-0"></div>
      )}
      {isSorting && (
        <div className="absolute inset-0 bg-grey-light border border-dashed border-grey-border rounded-15 z-0"></div>
      )}
      <div
        className="flex gap bg-grey-fields-100 items-center rounded-15 cursor-grab relative grabbing-handle "
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
      >
        <div className="flex items-center gap-2 justify-center h-full">
          {/* prettier-ignore */}
          <svg width={24} height={24} viewBox="0 0 18 18" fill="none" className="text-black-placeholder mx-2" >
            <path d="M11.5 14.5C12.052 14.5 12.5 14.052 12.5 13.5L12.5 13C12.5 12.448 12.052 12 11.5 12L11 12C10.448 12 10 12.448 10 13L10 13.5C10 14.052 10.448 14.5 11 14.5L11.5 14.5Z" fill="currentColor" />
            <path d="M11.5 10.25C12.052 10.25 12.5 9.802 12.5 9.25L12.5 8.75C12.5 8.198 12.052 7.75 11.5 7.75L11 7.75C10.448 7.75 10 8.198 10 8.75L10 9.25C10 9.802 10.448 10.25 11 10.25L11.5 10.25Z" fill="currentColor" />
            <path d="M12.5 5C12.5 5.552 12.052 6 11.5 6L11 6C10.448 6 10 5.552 10 5L10 4.5C10 3.948 10.448 3.5 11 3.5L11.5 3.5C12.052 3.5 12.5 3.948 12.5 4.5L12.5 5Z" fill="currentColor" />
            <path d="M7 14.5C7.552 14.5 8 14.052 8 13.5L8 13C8 12.448 7.552 12 7 12L6.5 12C5.948 12 5.5 12.448 5.5 13L5.5 13.5C5.5 14.052 5.948 14.5 6.5 14.5L7 14.5Z" fill="currentColor" />
            <path d="M8 9.25C8 9.802 7.552 10.25 7 10.25L6.5 10.25C5.948 10.25 5.5 9.802 5.5 9.25L5.5 8.75C5.5 8.198 5.948 7.75 6.5 7.75L7 7.75C7.552 7.75 8 8.198 8 8.75L8 9.25Z" fill="currentColor" />
            <path d="M7 6C7.552 6 8 5.552 8 5L8 4.5C8 3.948 7.552 3.5 7 3.5L6.5 3.5C5.948 3.5 5.5 3.948 5.5 4.5L5.5 5C5.5 5.552 5.948 6 6.5 6L7 6Z" fill="currentColor" />
            </svg>
        </div>

        <div className="flex justify-between items-start border rounded-15 border-gray-border bg-white px-3 py-2.5 sm:py-3 sm:px-4 flex-1 my-0">
          <div className="relative flex items-center select-none">
            <figure className="h-10 w-10 sm:w-14 sm:h-14 rounded-10 overflow-hidden flex-shrink-0 relative">
              <LazyImage src={imageUrl} alt={name} className="w-full h-full object-cover bg-gray-100" />
            </figure>
            <div className="select-none	flex max-w-[158px] flex-col items-start ml-2.5 flex-1 overflow-hidden">
              <span className="text-1xs sm:text-1sm text-dark font-medium block w-full overflow-hidden whitespace-nowrap overflow-ellipsis mb-0.75 sm:mb-1.5">
                {name}
              </span>
              <span className="text-xs sm:text-1xs text-black font-semibold">{toCurrency(price)}</span>
            </div>
          </div>
          <div className=""  onTouchStart={(e) => {
                e.stopPropagation();
                onCheck && onCheck();
              }} onClick={onCheck}>
            <Checkbox
              name={name}
              large
              size={30}
              checked={checked}
              neutral
              onChange={() => onCheck}
              className="ml-auto mt-1"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SortItem;
