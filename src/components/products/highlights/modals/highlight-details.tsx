import { HighlightInterface, HighlightVideo, ProductItemInterface, Video } from "@/assets/interfaces";
import { getItemThumbnail } from "@/assets/js/utils/utils";
import { useModals } from "@/components/hooks/useModals";
import LazyImage from "@/components/lazy-image";
import Portal from "@/components/portal";
import { AppBtn } from "@/components/ui/buttons";
import { productPageIcons } from "@/components/ui/layouts/product";
import { ModalBody, ModalFooter } from "@/components/ui/modal";
import Modal from "@/components/ui/modal";
import PlayVideoModal from "@/components/ui/play-video";
import { VideoCircle } from "iconsax-react";
import React, { useState } from "react";

interface HighlightDetailsModalProps {
  show: boolean;
  toggle: (state: boolean) => void;
  highlight: HighlightInterface;
  openEditModal: () => void;
}

const HighlightDetailsModal = ({ show, toggle, highlight, openEditModal }: HighlightDetailsModalProps) => {
  const { modals, toggleModal } = useModals(["video"]);
  const [video, setVideo] = useState<HighlightVideo | null>(null);

  return (
    <>
      <Modal title="Highlight Details" toggle={toggle} show={show} size="midi">
        <ModalBody>
          <div className="border-b border-grey-divider pb-3.75">
            <div className="flex items-center">
              <figure className="h-12.5 w-12.5 rounded-full mr-2 bg-grey-fields-100 flex items-center justify-center text-accent-red-500">
                <div className="w-1/2">{productPageIcons.productHighlights}</div>
              </figure>
              <div>
                <h2 className="text-2xl font-bold">{highlight.title}</h2>
                <p className="text-sm text-dark">
                  {highlight.videos.length} videos,{" "}
                  {highlight.videos.reduce((acc, video) => acc + video.products.length, 0)} products
                </p>
              </div>
            </div>
          </div>
          <div className="mt-3.75">
            <h3 className="text-lg font-bold">Videos</h3>
            <div className="flex flex-col gap-2 mt-2.5">
              {highlight.videos.map((video, index) => {
                const products = video.products as ProductItemInterface[];
                return (
                  <div className="w-full rounded-15 bg-grey-fields-100" key={index}>
                    <div className="p-2.5 w-full">
                      <div className="flex justify-between items-start w-full">
                        <div className="flex flex-1 mr-3.75 gap-2.5 ">
                          <figure
                            onClick={() => {
                              setVideo(video);
                              toggleModal("video");
                            }}
                            className="w-[45px] h-[45px] relative group cursor-pointer flex items-center justify-center flex-shrink-0 border border-grey-border border-opacity-20 rounded-10 overflow-hidden"
                            style={
                              video?.thumbnail
                                ? {
                                    backgroundImage: `url(${video?.thumbnail})`,
                                    backgroundSize: "cover",
                                    backgroundPosition: "center",
                                  }
                                : {}
                            }
                          >
                            <video
                              src={video.url}
                              controls={false}
                              className="w-full h-full object-cover"
                              playsInline
                            />
                            <div className="absolute top-0 left-0 right-0 bottom-0 w-full h-full bg-black bg-opacity-20"></div>
                            <div className="absolute left-0 right-0 mx-auto p-1.25 bg-white bg-opacity-20 w-[fit-content] h-[fit-content] rounded-full">
                              <VideoCircle size={20} className="text-white" />
                            </div>
                          </figure>
                          <div className="text-left w-full">
                            <span className="block overflow-hidden overflow-ellipsis whitespace-nowrap max-w-[60%] text-sm text-black-secondary font-semibold">
                              Video {index + 1}.mp4
                            </span>
                            <div className="flex items-center mt-1">
                              <div className="h-5 w-5 rounded-full overflow-hidden mr-1 relative">
                                <LazyImage
                                  src={getItemThumbnail(products[0])}
                                  className="w-full h-full object-cover"
                                  alt={products[0].name}
                                  loaderClasses="rounded-full h-full w-full"
                                />
                              </div>
                              <span className="text-xs text-black-placeholder text-left">
                                {products[0].name} {products.length > 1 && `and ${products.length - 1} other products`}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <AppBtn
            isBlock
            color="primary"
            onClick={() => {
              toggle(false);
              openEditModal();
            }}
            size="lg"
          >
            Edit Highlight
          </AppBtn>
        </ModalFooter>
      </Modal>
      <Portal>
        <PlayVideoModal
          title={video?.url?.split("/").pop() || "video"}
          video={video?.url}
          show={modals.video.show}
          toggle={() => toggleModal("video")}
        ></PlayVideoModal>
      </Portal>
    </>
  );
};

export default HighlightDetailsModal;
