// import EmojiPicker from "emoji-picker-react";
import <PERSON><PERSON>, { <PERSON>dal<PERSON>ody, ModalFooter } from "@/components/ui/modal";
import React from "react";
import { HighlightInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";

interface DelProps {
  show: boolean;
  toggle: (status: boolean) => void;
  block: HighlightInterface;
  removeBlock: (customer: HighlightInterface) => void;
}

const DeleteHighlight: React.FC<DelProps> = ({ show, toggle, block, removeBlock }) => {
  // const { makeRequest, isLoading, error, response } = useRequest<DeleteCustomerParams>(DeleteCustomer);

  const handleItemDelete = async () => {
    //   const [res, err] = await makeRequest({ id: block.id });
    //   if (!err) {
    //     toggle(false);
    //     removeBlock(block);
    //   }
  };

  return (
    <Modal {...{ show, toggle }} title="Delete Product Highlight" size="sm">
      <ModalBody>
        {/* <ErrorLabel error={error?.message} /> */}
        {/* <SuccessLabel message={response ? "Block deleted successfully!" : ""} /> */}
        <div className="text-center py-3.5">
          <h4 className="text-black text-base font-semibold">Do you want to delete this highlight?</h4>
          <p className="text-sm text-grey-subtext mt-1 max-w-xs mx-auto">
            This highlight would be completely removed from the list.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="danger" onClick={handleItemDelete} disabled={false /*isLoading*/} size="lg">
          Delete Highlight
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default DeleteHighlight;
