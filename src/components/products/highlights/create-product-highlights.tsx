// import EmojiPicker from "emoji-picker-react";
import {
  generateSimpleUUID,
  getFieldvalues,
  handleImageSelectionFromFile,
  toCurrency,
} from "@/assets/js/utils/functions";
import useMediaProcessor from "@/components/hooks/useMediaProcessor";
import useVideoTranscode, { ffmpegContext } from "@/components/hooks/useVideoTranscode";
import VideoUploadCard from "@/components/products/create-products/video-upload-card";
import { VideoDimensions } from "@/components/products/modals/process-video/select-video-thumbnail";
import { VideoTrimmer } from "@/components/products/modals/process-video/trim-video";
import { useFormik } from "formik";
import React, { useEffect, useRef, useState } from "react";
import * as Yup from "yup";
import { CreateStoreCategoriesParams, DeleteStoreCategoryParams } from "@/api/interfaces/stores.interface";
import { CreateStoreCategories, DeleteStoreCategory } from "@/api/stores";
import { useRequest } from "@/api/utils";
import { Category, FILE_TYPES, Image as ImageType, MediaType, ProductItemInterface, Video } from "@/assets/interfaces";
import authContext from "@/contexts/auth-context";
import { useModals } from "@/components/hooks/useModals";
import Portal from "../../portal";
import { AppBtn, RoundActionBtn } from "@/components/ui/buttons";
import ErrorLabel from "@/components/ui/error-label";
import { InputField } from "@/components/ui/form-elements";
import SuccessLabel from "@/components/ui/success-label";
import ProductsSelectorModal from "../modals/select-products-modal";
import { getProductItemThumbnail } from "@/assets/js/utils/utils";
import LazyImage from "@/components/lazy-image";

interface Props {
  submitBtnRef: React.MutableRefObject<HTMLButtonElement>;
  storeId?: string;
  setIsLoading?: (val: boolean) => void;
  onSuccess?: () => void;
}

interface FormProps {
  title: string;
  videos: { video: Video; products: ProductItemInterface[]; id: string; dimensions?: VideoDimensions }[];
}

const CreateProductHighlights: React.FC<Props> = ({ submitBtnRef, storeId, setIsLoading, onSuccess }) => {
  const { categories: storeCategories, store, updateStore } = authContext.useContainer();
  const { modals, toggleModal } = useModals(["select_items"]);

  const form = useFormik<FormProps>({
    initialValues: {
      title: "",
      videos: [],
    },
    validationSchema: Yup.object({
      title: Yup.string().required("Please provide block title"),
      type: Yup.string().required("Please select block type"),
      images: Yup.array()
        .of(Yup.string())
        .when("type", {
          is: "images",
          then: Yup.array().min(1, "Please select at least one image").required("Please select images"),
          otherwise: Yup.array().notRequired(),
        }),
      content: Yup.string().when("type", {
        is: "text",
        then: Yup.string().required("Please provide block content"),
        otherwise: Yup.string().notRequired(),
      }),
    }),
    onSubmit: async (values) => {
      console.log(values);
      // const [response, error] = await makeRequest({id: store.id });
      // if (error) {
      //   if (error.fields && Object.keys(error.fields).length > 0) {
      //     form.setErrors(error.fields);
      //   }
      // } else {
      //   updateStore({ ...response.data });
      //   // router.push("/setup/add-products");
      // }
    },
  });
  const mediaPicker = useRef<HTMLInputElement>(null);
  const { processFiles } = useMediaProcessor(
    ([{ type, ...m }]) => {
      const videosCopy = form.values.videos;
      videosCopy.push({
        video: { ...m },
        products: [],
        id: generateSimpleUUID(),
      });
      form.setFieldValue("videos", videosCopy);
    },
    [],
    null
  );

  const { ffmpegRef, ffmpegLoading, canTranscode, loadProgress } = ffmpegContext.useContainer();
  const { removeVideoProgress, retryVideoTask, transcodeVideo, videoProgresses } = useVideoTranscode({
    cb: (task, video, url) => {},
    ffmpegRef,
    skipUpload: true,
  });

  const [trim, setTrim] = useState<any>();
  const setDimensions = (index: number, dims: VideoDimensions) => {
    const videosCopy = form.values.videos;
    videosCopy[index].dimensions = dims;
    form.setFieldValue("videos", videosCopy);
  };

  const uploadVideo = (index: number) => {
    const data = form.values.videos[index];
    transcodeVideo({
      dimensions: data.dimensions,
      file: data.video.file as File,
      taskId: data.id,
      trim: trim,
      videoKey: data.video.meta.id,
      meta: {
        videoIndex: index,
      },
      type: FILE_TYPES.HIGHLIGHTS,
    });
  };

  const removeVideo = (index: number) => {
    const videosCopy = form.values.videos;
    const videoDataCopy = { ...videosCopy[index] };

    removeVideoProgress(videoDataCopy.id, videoDataCopy.video.meta.id);
    videosCopy.splice(index);
    form.setFieldValue("videos", videosCopy);
  };

  /**
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *
   *  */

  const [currentVideo, setCurrentVideo] = useState<number>(null);
  const [categories, setCategories] = useState<Category[]>([...storeCategories, { id: "", name: "", emoji: "" }]);
  const {
    isLoading,
    makeRequest,
    error,
    response: successResponse,
  } = useRequest<CreateStoreCategoriesParams>(CreateStoreCategories);
  const {
    response: delRes,
    makeRequest: delRequest,
    error: delError,
  } = useRequest<DeleteStoreCategoryParams>(DeleteStoreCategory);
  const [errorText, setErrorText] = useState<string>(null);
  const videoPicker = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setIsLoading && setIsLoading(isLoading);
  }, [isLoading]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (categories.some((category) => !category.name)) {
      setErrorText("Category names cannot be empty, delete empty categories and submit again");
      return;
    }

    const [response, error] = await makeRequest({ id: storeId || store.id, categories });

    if (!error) {
      updateStoreCategories(response.data);
      onSuccess && onSuccess();

      return;
    }

    setErrorText(error.message);
  };

  function updateStoreCategories(categories: Category[]) {
    updateStore({ categories });
  }

  function openSelectItems(e: React.MouseEvent<HTMLButtonElement>, index: number) {
    setCurrentVideo(index);
    toggleModal("select_items");
  }

  function removeProduct(e: React.MouseEvent<HTMLButtonElement>, videoIndex: number, productId: string) {
    const updatedVideos = [...form.values.videos];
    const updatedProducts = updatedVideos[videoIndex].products.filter((product) => product.id !== productId);
    updatedVideos[videoIndex].products = updatedProducts;
    form.setFieldValue("videos", updatedVideos);
  }

  return (
    <form onSubmit={handleSubmit} className="w-full pb-20">
      <ErrorLabel error={errorText || error?.message} />
      <SuccessLabel message={successResponse ? "Store categories updated successfully!" : ""} />
      <InputField label={`Highlight Title`} {...getFieldvalues("title", form)} />

      {form.values.videos.map((data, index) => {
        const videoProgress = videoProgresses?.value?.[data.id]?.[data.video.meta.id];

        return (
          <div className="relative h-full mt-3.75" key={index}>
            <div className="flex flex-col gap-2 bg-grey-fields-100 items-center rounded-10 relative h-full">
              <div className="flex justify-between items-center w-full px-3 pt-2.5">
                <h2 className="text-black-placeholder text-sm font-semibold">Video {index + 1}</h2>
              </div>

              <div className="flex-1 w-full h-full border rounded-10 border-gray-border border-opacity-50 bg-white p-3 my-0">
                {!videoProgress ? (
                  <div>
                    <VideoTrimmer
                      setDimensions={(d) => setDimensions(index, d)}
                      setDuration={() => {}}
                      onChange={(start, end) => setTrim({ start, end })}
                      maxDuration={30}
                      src={data.video.src}
                    />
                    <div className="flex gap-2.5 justify-between mt-2.5">
                      <AppBtn
                        type="button"
                        onClick={() => removeVideo(index)}
                        size="md"
                        color="neutral"
                        className="flex-1 text-accent-red-500"
                      >
                        Remove
                      </AppBtn>
                      <AppBtn
                        type="button"
                        onClick={() => uploadVideo(index)}
                        size="md"
                        color="neutral"
                        className="flex-1"
                      >
                        Save Video
                      </AppBtn>
                    </div>
                  </div>
                ) : (
                  <VideoUploadCard
                    key={index}
                    retryTask={() => retryVideoTask(data.id, data.video.meta.id)}
                    videoProgress={videoProgress}
                    openVideoModal={(m) => null}
                    removeVideo={() => removeVideo(index)}
                    video={data.video}
                  />
                )}
                <div className="">
                  {data.products.length < 1 && (
                    <div className="w-full flex gap-2 my-3.75 bg-accent-yellow-pastel text-black-muted text-1xs font-normal py-2.5 px-3 rounded-10">
                      {/* prettier ignore */}
                      <svg width={16} height={16} viewBox="0 0 16 16" fill="none">
                        <path
                          d="M8.00016 15.1673C4.04683 15.1673 0.833496 11.954 0.833496 8.00065C0.833496 4.04732 4.04683 0.833984 8.00016 0.833984C11.9535 0.833984 15.1668 4.04732 15.1668 8.00065C15.1668 11.954 11.9535 15.1673 8.00016 15.1673ZM8.00016 1.83398C4.60016 1.83398 1.8335 4.60065 1.8335 8.00065C1.8335 11.4007 4.60016 14.1673 8.00016 14.1673C11.4002 14.1673 14.1668 11.4007 14.1668 8.00065C14.1668 4.60065 11.4002 1.83398 8.00016 1.83398Z"
                          fill="currentColor"
                        />
                        <path
                          d="M8 9.16732C7.72667 9.16732 7.5 8.94065 7.5 8.66732V5.33398C7.5 5.06065 7.72667 4.83398 8 4.83398C8.27333 4.83398 8.5 5.06065 8.5 5.33398V8.66732C8.5 8.94065 8.27333 9.16732 8 9.16732Z"
                          fill="currentColor"
                        />
                        <path
                          d="M8.00016 11.3339C7.9135 11.3339 7.82683 11.3139 7.74683 11.2806C7.66683 11.2472 7.5935 11.2006 7.52683 11.1406C7.46683 11.0739 7.42016 11.0072 7.38683 10.9206C7.3535 10.8406 7.3335 10.7539 7.3335 10.6672C7.3335 10.5806 7.3535 10.4939 7.38683 10.4139C7.42016 10.3339 7.46683 10.2606 7.52683 10.1939C7.5935 10.1339 7.66683 10.0872 7.74683 10.0539C7.90683 9.98724 8.0935 9.98724 8.2535 10.0539C8.3335 10.0872 8.40683 10.1339 8.4735 10.1939C8.5335 10.2606 8.58016 10.3339 8.6135 10.4139C8.64683 10.4939 8.66683 10.5806 8.66683 10.6672C8.66683 10.7539 8.64683 10.8406 8.6135 10.9206C8.58016 11.0072 8.5335 11.0739 8.4735 11.1406C8.40683 11.2006 8.3335 11.2472 8.2535 11.2806C8.1735 11.3139 8.08683 11.3339 8.00016 11.3339Z"
                          fill="currentColor"
                        />
                      </svg>
                      <span className="flex-1 inline-block text-xs sm:text-1xs">
                        Link products in the video so customers can buy easily.
                      </span>
                    </div>
                  )}
                  {data?.products.length > 0 && (
                    <div className="mt-3.75 mb-1 grid grid-cols-2 gap-2">
                      {data.products.map((product, _index) => (
                        <div
                          key={_index}
                          className="flex bg-grey-fields-200 rounded-15 px-2 md:px-2 py-2 md:py-2 items-center rounded-t-10 border-grey-border border-opacity-50 justify-between gap-x-0.5"
                        >
                          <div className="py-1 flex items-stretch overflow-hidden">
                            <figure className="h-[34px] w-[34px] sm:h-10 sm:w-10 rounded-10 overflow-hidden flex-shrink-0">
                              <LazyImage
                                src={getProductItemThumbnail(product)}
                                alt=""
                                className="h-full w-full object-cover"
                              />
                            </figure>
                            <div className="ml-2 flex flex-col gap-y-0.5 flex-1 overflow-hidden justify-start items-start">
                              <h5 className="text-black-muted font-display font-bold overflow-hidden text-xxs sm:text-xs max-w-full whitespace-nowrap overflow-ellipsis ">
                                {product.name}{" "}
                              </h5>
                              <p className="text-black font-bold overflow-hidden text-xxs sm:text-xxs max-w-full whitespace-nowrap overflow-ellipsis ">
                                {toCurrency(product.price)}{" "}
                              </p>
                            </div>
                          </div>
                          <button
                            className="flex items-center justify-center  rounded-full disabled:text-grey-muted group flex-shrink-0 bg-white w-7.5 h-7.5 text-red-500"
                            type="button"
                            onClick={(e) => removeProduct(e, index, product.id)}
                          >
                            <svg
                              width={15}
                              height={16}
                              viewBox="0 0 15 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M11.25 4.25L3.75 11.75M3.75 4.25L11.25 11.75"
                                stroke="#BF0637"
                                strokeWidth="1.3"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                  <button
                    type="button"
                    className="flex items-center justify-center font-medium whitespace-nowrap bg-white hover:text-primary-700 text-primary-700 h-9 mt-1 text-1xs sm:text-sm rounded-[8px] px-0 font-action !outline-none transition-all cursor-pointer box-border group flex-shrink-0"
                    onClick={(e) => openSelectItems(e, index)}
                  >
                    + Add Products
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
      })}

      <input
        type="file"
        ref={mediaPicker}
        name="product-medias"
        accept={"video/mp4,webm,ogg"}
        id="product-medias"
        className="hidden"
        onChange={(e) => processFiles(e.target.files)}
      />

      <div className=" mt-5">
        <AppBtn size="md" color="neutral" className="w-full" onClick={() => mediaPicker.current?.click()}>
          + Add New Video to Highlight
        </AppBtn>
      </div>
      <button type="submit" className="hidden" ref={submitBtnRef}></button>

      <Portal>
        {currentVideo !== null && (
          <ProductsSelectorModal
            storeId={store?.id}
            toggle={() => toggleModal("select_items")}
            show={modals.select_items.show}
            selectItem={(products: ProductItemInterface[]) => {
              form.setFieldValue(
                `videos[${currentVideo}].products`, // Directly update the products array of the current video
                products
              );
            }}
            selected={form.values.videos[currentVideo]?.products.map((item) => item.id) || []} // Pass selected product IDs for the current video
            isItemSelected={(id) => form.values.videos[currentVideo]?.products.some((item) => item.id === id) || false}
            formatAsCurrency={toCurrency}
          />
        )}
      </Portal>
    </form>
  );
};

export default CreateProductHighlights;
