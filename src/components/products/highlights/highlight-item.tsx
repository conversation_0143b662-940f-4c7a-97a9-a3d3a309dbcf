import { HighlightInterface } from "@/assets/interfaces";
import LazyImage from "@/components/lazy-image";
import { AppBtn, RoundActionBtn } from "@/components/ui/buttons";
import Dropdown, { DropdownItem } from "@/components/ui/dropdown-new";
import Toggle from "@/components/ui/toggle";
import { VideoSquare, ExportSquare } from "iconsax-react";
import { useRequest } from "@/api/utils";
import { UpdateHighlight } from "@/api/items";
import { toast } from "@/components/ui/toast";
import { useRouter } from "next/router";
import { toAppUrl } from "@/assets/js/utils/functions";
import useCopyClipboard from "@/components/hooks/useCopyClipboard";
interface HighlightItemProps {
  data?: HighlightInterface;
  editHighlight: () => void;
  deleteHighlight: () => void;
  openDetails: () => void;
  updateHighlight: (highlight: HighlightInterface) => void;
}

const HighLightItem: React.FC<HighlightItemProps> = ({
  data,
  editHighlight,
  deleteHighlight,
  openDetails,
  updateHighlight,
}) => {
  const router = useRouter();
  const updateHighlightReq = useRequest(UpdateHighlight);

  const highlightLink = toAppUrl(`highlights/${data.slug}`);
  const [isCopied, copy] = useCopyClipboard(highlightLink, { successDuration: 500 });

  const handleToggle = async (active: boolean) => {
    const request = async () => {
      const [res, err] = await updateHighlightReq.makeRequest({ id: data.id, active });

      if (res) {
        updateHighlight({ ...data, active });
        return Promise.resolve(res);
      }

      return Promise.reject(err);
    };

    toast.promise(request, toastOpts(request));
  };

  const dropdownItems: DropdownItem[] = [
    // {
    //   text: "Add New Video",
    //   link: "href",
    //   icon: (
    //     <RoundActionBtn size="sm">
    //       <VideoSquare strokeWidth={1} size={16} />
    //     </RoundActionBtn>
    //   ),
    // },
    {
      text: "Share Highlight",
      link: "/products/bulk-update-quantities",
      icon: (
        <RoundActionBtn size="sm">
          <ExportSquare strokeWidth={1} size={15} />
        </RoundActionBtn>
      ),
    },
    {
      text: "Copy Link",
      icon: <RoundActionBtn size="sm" icon="copy" />,
      onClick: () => copy(),
    },
    {
      text: "Edit Highlight",
      onClick: () => editHighlight(),
      icon: <RoundActionBtn size="sm" icon="edit" />,
    },
    {
      text: "Delete Highlight",
      onClick: () => deleteHighlight(),
      icon: <RoundActionBtn size="sm" className="text-accent-red-500" icon="delete" />,
    },
  ];

  return (
    <div
      className="relative h-full border border-grey-border border-opacity-50 rounded-15 bg-white cursor-pointer"
      onClick={openDetails}
    >
      <div className="flex flex-col gap-2 items-center rounded-10 relative h-full">
        <figure className="w-full h-40 relative sm:h-[180px] rounded-10 overflow-hidden m-2 px-2">
          <LazyImage
            src={data.videos[0].thumbnail}
            alt={data.title}
            className="w-full h-full object-cover rounded-10 transition-all duration-300 ease-out"
            loaderClasses="rounded-10"
            style={data.active ? {} : { filter: "grayscale(100%)" }}
          />
        </figure>
        <div className="flex justify-between items-center w-full px-3">
          <h2 className="text-black font-semibold text-base">{data.title}</h2>
          <Toggle size="sm" intialState={data.active} onChange={handleToggle} className="flex-shrink-0" />
        </div>
        <div className="flex justify-between items-center w-full px-3">
          <span className="text-black-placeholder font-medium text-1xs">
            {data.videos.length} Videos •{" "}
            {data.videos.map((video) => video.products?.length || 0).reduce((sum, length) => sum + length, 0)} Products
          </span>
        </div>
        <div
          className="flex justify-between items-center w-full px-3 mt-1 mb-3.5 pt-2.5 border-t border-grey-border border-opacity-50"
          onClick={(e) => e.stopPropagation()}
        >
          <AppBtn
            color="neutral"
            size="sm"
            className="px-4.5 mr-2"
            onClick={() => router.push(toAppUrl(`highlights/${data.slug}`))}
          >
            Preview Highlight
          </AppBtn>

          <Dropdown items={dropdownItems} vPosition="BOTTOM" gap={false} size="lg">
            <RoundActionBtn
              color="danger"
              size="md"
              className="w-8 h-8 flex-shrink-0 dropdown-toggle"
              icon={"options"}
            />
          </Dropdown>
        </div>
      </div>
    </div>
  );
};

const toastOpts = (retry: Function) => ({
  loading: {
    title: "Updating highlight",
    message: "Please wait...",
  },
  success: {
    title: "Successful",
    message: "Highlight updated!",
  },
  error: {
    title: "Something went wrong",
    message: "Something went wrong! Please retry",
    retry,
  },
});

export default HighLightItem;
