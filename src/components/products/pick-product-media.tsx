import useComputeThumbnails from "@/components/hooks/useComputeThumbnails";
import useMediaProcessor from "@/components/hooks/useMediaProcessor";
import LazyImage from "@/components/lazy-image";
import React, { useMemo, useRef } from "react";
import { FILE_TYPES, Image, Media, MediaType } from "../../assets/interfaces";
import { Product } from "../../pages/products/create";
import useImageUploads from "../hooks/useImageUploads";

interface Props {
  product: Product;
  saveMedias: (media: Media[]) => void;
  saveImages: (media: Image[]) => void; // after upload
  changeThumbnail: (e: React.MouseEvent<HTMLElement, MouseEvent>, idx: number, type: string) => void;
  removePickedMedia: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, idx: number, type: string) => void;
  selectPlaceholder?: VoidFunction;
  canProcessVideos?: boolean;
}

const PickProductMedia = ({
  product,
  saveMedias,
  saveImages,
  changeThumbnail,
  selectPlaceholder,
  removePickedMedia,
  canProcessVideos = false,
}: Props) => {
  const imagePicker = useRef<HTMLInputElement>(null);
  useImageUploads(product.images, FILE_TYPES.ITEMS, saveImages);

  const excludedMediaTypes = canProcessVideos ? [] : [MediaType.VIDEO];

  const productMedias: Media[] = useMemo(() => {
    const imgMedias = (product.images ?? [])?.map((i) => ({ ...i, type: MediaType.IMAGE }));
    const videoMedias = (product.videos ?? [])?.map((i) => ({ ...i, type: MediaType.VIDEO }));
    return [...imgMedias, ...videoMedias];
  }, [product]);

  const { processFiles } = useMediaProcessor(
    (m) => {
      saveMedias(m);
    },
    productMedias,
    null,
    false
  );

  const { imageThumbnails, videoThumbnails } = useComputeThumbnails(product);

  const isThumbnail = (type: string, idx: number) => {
    return product.thumbnail_type === type && product.thumbnail === idx;
  };

  return (
    <div className="text-left">
      <div className="grid items-start w-full grid-cols-[repeat(auto-fit,60px)] gap-2.5">
        <input
          type="file"
          ref={imagePicker}
          name="product-medias"
          multiple
          accept={canProcessVideos ? "video/mp4,webm,ogg,video/quicktime,image/*,.heic" : "image/*,.heic"}
          id="product-media-picker"
          className="hidden"
          onChange={(e) => processFiles(e.target.files, excludedMediaTypes)}
        />

        {[...imageThumbnails, ...videoThumbnails].map(
          ({ name, src, url, meta, isUploading, uploadProgress, error }, index) => (
            <div key={index}>
              <figure
                className="w-full h-15 relative group cursor-pointer"
                onClick={(e) => changeThumbnail(e, meta.index, meta.type)}
                key={index}
              >
                {src || url ? (
                  <LazyImage src={src || url} alt={name} className="w-full h-full object-cover rounded-10" />
                ) : (
                  <div className="h-full w-full rounded-10 bg-grey-loader animate-pulse flex items-center justify-center">
                    <div className="spinner spinner--sm"></div>
                  </div>
                )}
                <div
                  className={`bg-primary-900 bg-opacity-50 flex items-center justify-center absolute h-full w-full inset-0 z-10 rounded-10 transition-all duration-100 ease-out ${
                    isThumbnail(meta.type, meta.index) ? "opacity-100" : "opacity-0 pointer-events-none"
                  }`}
                >
                  {/* prettier-ignore */}
                  <svg width="20" viewBox="0 0 32 35" fill="none" className="transform -translate-y-0.5 text-white">
                    <path d="M4.18766 22.4105L13.7894 29.9576L28.4843 11.882" stroke="currentColor" strokeWidth="5" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
                {!isThumbnail(meta.type, meta.index) && (
                  <button
                    className="h-6 w-6 bg-black bg-opacity-80 flex items-center justify-center absolute transform -top-2 -right-2 rounded-full transition-all opacity-0 group-hover:opacity-100 z-30"
                    onClick={(e) => removePickedMedia(e, meta.index, meta.type)}
                    type="button"
                  >
                    {/* prettier-ignore */}
                    <svg width="12" height="12" viewBox="0 0 15 15" fill="none">
                      <path d="M11.25 3.75L3.75 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M3.75 3.75L11.25 11.25" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                )}
              </figure>
              {(isUploading || uploadProgress > 0) && (
                <div
                  className={`mt-1.5 h-1 w-full rounded-10 overflow-hidden ${
                    error ? "bg-accent-red-500 bg-opacity-10" : "bg-grey-divider"
                  }`}
                >
                  <div
                    className={`h-full transition-all duration-200 ease-out ${
                      error ? "bg-accent-red-500" : "bg-accent-green-500"
                    }`}
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              )}
            </div>
          )
        )}

        <button
          className="no-outline w-full h-15 rounded-10 border border-dashed border-grey-subtext text-grey-subtext hover:border-primary-500 hover:text-primary-500 transition-all flex items-center justify-center"
          onClick={() => imagePicker.current.click()}
          type="button"
        >
          {/* prettier-ignore */}
          <svg width="20" viewBox="0 0 30 30" fill="none">
            <path d="M15 6.25V23.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M6.24976 15H23.7498" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>
      </div>
      <div className="flex items-center gap-2.5 mt-3 mb-2">
        {product.images[0]?.isPlaceholder ? (
          <button
            type="button"
            onClick={selectPlaceholder}
            className="text-primary-500 text-xs text-left py-1.5 px-2 bg-grey-fields-100 rounded-5 inline-flex font-medium"
          >
            Click here to pick a new placeholder
          </button>
        ) : (
          <p className="text-grey-subtext text-xs text-left py-1.5 px-2 bg-grey-fields-100 rounded-5 inline-flex">
            Click on any image to change thumbnail
          </p>
        )}
      </div>
    </div>
  );
};

export default PickProductMedia;
