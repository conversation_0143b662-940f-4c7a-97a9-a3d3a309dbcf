import { Dispatch, SetStateAction, useRef, useState } from "react";

function useRefState<T extends Object>(currentState: T): [{ value: T; hash: number }, Dispatch<SetStateAction<T>>] {
  const stateRef = useRef<T>(currentState);
  const [stateHash, setStateHash] = useState<number>(0);

  const setState = (state: T) => {
    if (typeof state === "object") {
      Object.assign(stateRef.current, state);
      setStateHash(Math.random());
      return;
    }
    throw "invalid type";
  };

  return [{ value: stateRef.current, hash: stateHash }, setState];
}

export default useRefState;
