import { useState } from "react";

export interface UseStepsData<T = string> {
  step: T;
  changeStep: (step: T) => void;
  stepIndex: number;
  isActive: (s: T) => boolean;
  steps: T[];
  next: () => void;
  previous: () => void;
  canNext: boolean;
  canPrevious: boolean;
}

const useSteps = <T = string>(steps: T[], active: number): UseStepsData<T> => {
  const [step, setStep] = useState<T>(steps[active]);
  const stepIndex = steps.indexOf(step);
  const canNext = stepIndex < steps.length - 1;
  const canPrevious = stepIndex > 0;

  const isActive = (s: T) => step === s;

  const handleNext = () => {
    if (canNext) {
      setStep(steps[stepIndex + 1]);
    }
  };

  const handlePrevious = () => {
    if (canPrevious) {
      setStep(steps[stepIndex - 1]);
    }
  };

  // const changeStep = (step: string) => {
  //   const index = steps.findIndex((s) => s === step);
  //   if (index > -1) {
  //     setStep(step);
  //   }
  // }

  return {
    step,
    changeStep: setStep,
    stepIndex,
    isActive,
    steps,
    next: handleNext,
    previous: handlePrevious,
    canNext,
    canPrevious,
  };
};

export default useSteps;
