import { useEffect } from "react";
import { uploadClient } from "../../api/client";
import { toast } from "../ui/toast";
import Toaster from "react-hot-toast";
import { FILE_TYPES, Image } from "../../assets/interfaces";
import { generateSimpleUUID } from "@/assets/js/utils/functions";

const useImageUploads = (images: Image[], type: FILE_TYPES, setImages: (images: Image[]) => void, deps?: any[]) => {
  const imagesClone = [...images];

  const getItemIndex = (name) => images.findIndex((i) => i.name === name);

  const progressCallback = (value, image: Image) => {
    const index = getItemIndex(image.name);

    if (index > -1) {
      imagesClone[index] = { ...image, isUploading: true, uploadProgress: value };
      setImages([...imagesClone]);
    }
  };

  const callback = (data: any, err, image: Image, req: XMLHttpRequest, retry: () => void) => {
    const index = getItemIndex(image.name);

    if (index > -1) {
      if (err) {
        req.abort();
        //omo, i no sabi wetin to do
        Toaster.dismiss();

        toast.error({
          title: "Upload failed",
          message: `${image.name} couldn't be uploaded, please retry.`,
          actionFunc: () => (retry ? retry() : {}),
          actionText: "Retry",
        });

        //show a toast with specific error message
        imagesClone[index] = { ...image, isUploading: false, error: true };
        setImages([...imagesClone]);

        return;
      }

      const res = JSON.parse(data.response);

      imagesClone[index] = { ...image, isUploading: false, uploadProgress: 100, url: res.data?.link };
      setImages([...imagesClone]);
    }
  };

  useEffect(() => {
    //really test image uploads

    if (images.length < 1 || images.every((image) => image.src && image.url)) {
      return;
    }
    const imagesToUpload = images.filter(({ url, isUploading }) => !isUploading && !url);

    if (imagesToUpload.length < 1) {
      return;
    }

    for (const image of imagesToUpload) {
      uploadClient(
        { file: image.file, endpoint: "file", type },
        (value) => progressCallback(value, image),
        (data: any, err, req: XMLHttpRequest, retry) => callback(data, err, image, req, retry)
      );

      const index = getItemIndex(image.name);

      if (index > -1) {
        imagesClone[index] = { ...image, isUploading: true };
      }
    }

    setImages(imagesClone);
  }, [deps ?? images]);
};

export function uploadImageBlob(img: Blob | File, setImage: (img: Image) => void, onComplete?: VoidFunction) {
  const initialImage = {
    src: URL.createObjectURL(img),
    name: (img as File).name ?? "image",
    lastModified: (img as File)?.lastModified ?? Date.now(),
    file: img,
    isUploading: false,
    uploadProgress: 0,
    url: "",
    key: `${(img as File).name ?? "image"}-${Date.now()}`,
    meta: {
      id: generateSimpleUUID(),
    },
  };

  uploadClient(
    { file: img, endpoint: "file", type: FILE_TYPES.ITEMS },
    (value) => {
      setImage({ ...initialImage, isUploading: true, uploadProgress: value });
    },
    (data: any, err, req: XMLHttpRequest, retry) => {
      if (err) {
        req.abort();

        toast.error({
          title: "Upload failed",
          message: `${initialImage.name} couldn't be uploaded, please retry.`,
          actionFunc: () => (retry ? retry() : {}),
          actionText: "Retry",
        });

        //show a toast with specific error message
        setImage({ ...initialImage, isUploading: false, error: true });
        return;
      }
      const res = JSON.parse(data.response);
      setImage({ ...initialImage, isUploading: false, uploadProgress: 100, url: res.data?.link });
      onComplete?.();
    }
  );
}

export default useImageUploads;
