import { Image, Media, MediaType } from "@/assets/interfaces";
import { useListener } from "@/components/hooks/useListener";
import { useModals } from "@/components/hooks/useModals";
import { ProductFormProps } from "@/components/products/create-products/form";
import { toast } from "@/components/ui/toast";
import authContext from "@/contexts/auth-context";
import { Product } from "@/pages/products/create";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import * as Yup from "yup";
import useVideoTrim from "./useVideoTrim";

function useProductForm(props: ProductFormProps) {
  const {
    product,
    setForm,
    form,
    index,
    changeView,
    submitForm,
    toggleOuterModal,
    removeVideoProgress,
    videoResourcesLoaded = false,
  } = props;

  const { modals, toggleModal } = useModals([
    "categories",
    "images",
    "variants",
    "v_explainer",
    "placeholders",
    "video",
    "trim",
    "thumbnail",
    "process_video",
  ]);
  const { store } = authContext.useContainer();
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);

  const thisForm = useFormik<Product>({
    initialValues: {
      ...product,
      // is_always_available: false,
    },
    validationSchema: validationSchema(store.configuration.direct_checkout_enabled || store?.flags?.uses_chowbot),
    onSubmit: async (values) => {
      updateForm(values);
      if (index === form.products.length - 1) {
        console.log("submitting");
        submitForm();
        return;
      }
      changeView("forward");
    },
  });

  const { videoTrim, setVideoTrim } = useVideoTrim(thisForm.values.videos);

  useEffect(() => {
    // console.log("updating form")
    thisForm.setValues({ ...product });
  }, [product]);

  // console.log(thisForm.errors);

  const productImageUploading = thisForm.values.images.some((i) => i.isUploading);
  const variantsCount = thisForm.values.variants.options.length;

  useEffect(() => {
    const handleBeforeUnload = (e) => {
      e = e || window.event;

      // For IE and Firefox prior to version 4
      if (e) {
        e.returnValue = "Sure?";
      }

      // For Safari
      return "Sure?";
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, []);

  useListener<{
    taskId: string;
    videoKey: string;
    url: string;
    blob: Blob;
  }>(
    `video-url`,
    ({ taskId, videoKey, url, blob }) => {
      if (url && thisForm.values.id === taskId) {
        const index = thisForm.values.videos.findIndex((v) => v.meta.id === videoKey);
        if (index >= 0) {
          const videosCopy = [...thisForm.values.videos];
          const newFile = new File([blob], (videosCopy?.[index]?.file as File)?.name ?? "Video");

          videosCopy[index].url = url;
          videosCopy[index].file = newFile;

          thisForm.setFieldValue("videos", videosCopy);
        }
      }
    },
    [index, thisForm]
  );

  const saveMedias = (medias: Media[]) => {
    thisForm.setFieldValue("images", [
      ...(thisForm.values.images ?? []),
      ...medias.filter((m) => m.type === MediaType.IMAGE),
    ]);

    const videos = [...(thisForm.values.videos ?? []), ...medias.filter((m) => m.type === MediaType.VIDEO)];

    thisForm.setFieldValue("videos", videos);
    updateForm({ ...thisForm.values, videos });
  };

  const saveImages = (images: Image[]) => {
    thisForm.setFieldValue("images", images);
  };

  const removePickedMedia = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, idx: number, type: string) => {
    e.stopPropagation();

    if (idx === thisForm.values.thumbnail && thisForm.values.thumbnail_type === type) {
      toast.error({
        title: "Error!",
        message: `Cannot delete thumbnail image.`,
      });
      return;
    }

    const mediasCopy = type === "image" ? [...thisForm.values.images] : [...thisForm.values.videos];
    const newThumbnail = thisForm.values.thumbnail > idx ? thisForm.values.thumbnail - 1 : thisForm.values.thumbnail;
    mediasCopy.splice(idx, 1);
    thisForm.setFieldValue(type === "image" ? "images" : "videos", mediasCopy);
    thisForm.setFieldValue("thumbnail", newThumbnail);
  };

  const removeProductVideo = (index: number, videoKey: string) => {
    const productCopy = { ...thisForm.values };
    productCopy.videos = productCopy.videos.filter((v, i) => i != index);

    removeVideoProgress(index, videoKey);
    thisForm.setValues(productCopy);
  };

  const changeThumbnail = (e: React.MouseEvent<HTMLElement, MouseEvent>, id: number, type: string) => {
    e.stopPropagation();
    e.preventDefault();
    thisForm.setFieldValue("thumbnail", id);
    thisForm.setFieldValue("thumbnail_type", type);
  };

  const removeProduct = (index: number) => {
    const formCopyProducts = []; //deep copy
    Object.assign(formCopyProducts, form.products);

    formCopyProducts.splice(index, 1);
    setForm({ ...form, products: formCopyProducts });

    removeVideoProgress(index);
    toast.success({ title: "Item removed", message: "Item has been removed" });
  };

  const deleteToastOptions = {
    title: "Are you sure?",
    message: "Deleting this item would remove it from the form",
    actionText: "Delete",
    actionFunc: () => removeProduct(index),
  };

  const openVariantForm = () => {
    if (!thisForm.values.price) {
      toast.error({
        title: "Price not added",
        message: "Please add a name and price before adding options",
      });
      return;
    }

    toggleModal("variants");
  };

  const updateForm = (values) => {
    const formCopy = { ...form };
    formCopy.products[index] = {
      ...values,
    };
    setForm({ ...formCopy });
  };

  const scrollPageToTop = () => {
    document.querySelector(".page-content").scrollTo(0, 0);
  };

  const handleOpenVideoModal = (m: string, i: number) => {
    if (!videoResourcesLoaded) {
      toggleOuterModal("loader");
      return;
    }
    toggleModal(m);
    setCurrentVideoIndex(i);
  };

  return {
    thisForm,
    handleOpenVideoModal,
    scrollPageToTop,
    openVariantForm,
    changeThumbnail,
    removePickedMedia,
    removeProductVideo,
    saveImages,
    saveMedias,
    currentVideoIndex,
    modals,
    toggleModal,
    productImageUploading,
    variantsCount,
    deleteToastOptions,
    product,
    store,
    updateForm,
    videoTrim,
    setVideoTrim,
  };
}

const validationSchema = (direct_checkout_enabled: boolean) =>
  Yup?.object()?.shape({
    name: Yup.string().required("Item name is required"),
    description: Yup.string().required("Item description is required"),
    price: Yup.string()
      .required("Item price is required")
      .test("digits", "Item price should be a number", (value) => !Number.isNaN(Number(value))),
    discount_price: Yup.number()
      .optional()
      .test(
        "islesser",
        "Discount price should be lesser than item price",
        (value, ctx) => value === undefined || Number(ctx.parent.price) > Number(value)
      )
      .min(1, "Price must be greater than 0")
      .integer("Price must be a number"),
    is_always_available: direct_checkout_enabled && Yup.boolean().optional(),
    minimum_order_quantity: Yup.number().optional().min(1, "Minimum order must be greater than 0"),
    cost_price: Yup.number().optional().min(1, "Minimum order must be greater than 0"),
    quantity:
      direct_checkout_enabled &&
      Yup.number().when("is_always_available", {
        is: false,
        then: Yup.number().required("Quantity must be provided"),
        otherwise: Yup.number().optional(),
      }),
    videos: Yup.array().of(
      Yup.object().shape({
        meta: Yup.object().shape({
          thumbnail: Yup.object().shape({ url: Yup.string().required("Select a thumbnail to proceed") }),
        }),
      })
    ),
  });

export default useProductForm;
