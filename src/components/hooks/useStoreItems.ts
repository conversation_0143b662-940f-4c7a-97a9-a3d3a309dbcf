import { useEffect, useState } from "react";
import { GetAllItems, GetItems } from "../../api";
import { GetAllItemsParams, GetItemsParams } from "../../api/interfaces";
import { useFetcher } from "../../api/utils";
import { ProductItemInterface, StrippedItem } from "../../assets/interfaces";

//Fetches all store items
const useStoreItems = (type?: "storefront" | "custom") => {
  const [items, setItems] = useState<StrippedItem[]>([]);
  const fetchItemsReq = useFetcher<GetAllItemsParams>(GetAllItems, { type });
  const { response } = fetchItemsReq;

  useEffect(() => {
    if (response) {
      setItems([...response?.data?.items]);
    }
  }, [response]);

  const addNewItem = (item: StrippedItem) => {
    setItems([...items, item]);
  };

  return {
    items,
    addNewItem,
    fetchItemsReq,
  };
};

export const useStorefrontItems = (
  storeId: string,
  filter?: GetItemsParams["filter"],
  preloadedItems?: ProductItemInterface[]
) => {
  const [items, setItems] = useState<ProductItemInterface[]>([]);
  const getItemRequest = useFetcher<GetItemsParams>(GetItems, {
    filter: { store: storeId, ...filter },
    per_page: Number.MAX_SAFE_INTEGER,
  });
  const { response } = getItemRequest;

  useEffect(() => {
    if (response) {
      setItems([...response?.data?.items]);
    }
  }, [response]);

  const getItem = (id: string) => {
    //uses items from preloaded data if available
    if (items.length === 0 && preloadedItems && preloadedItems.length > 0) {
      return preloadedItems.find((item) => item.id === id);
    }

    return items.find((item) => item.id === id);
  };

  return {
    items,
    fetchItemsReq: getItemRequest,
    getItem,
  };
};

export default useStoreItems;
