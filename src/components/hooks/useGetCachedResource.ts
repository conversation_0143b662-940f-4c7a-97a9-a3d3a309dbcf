import { findInCollection, initCollection, pushToCollection, stringToHash } from "@/assets/js/utils/indexdb";
import { useEffect, useState } from "react";

const DB_NAME = "RESOURCES";
const OBJECT_STORE_NAME = "BLOBS";

export function useGetCachedBlobUrl(url: string) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>();
  const [resource, setResource] = useState<String | null>(null);

  async function getCachedBlobUrl(url: string) {
    const blob = await getResource(url);
    if (blob) {
      setResource(blob ? URL.createObjectURL(blob) : null);
    } else {
      setError("Failed to load resource");
    }
    setIsLoading(false);
  }

  useEffect(() => {
    initCollection({
      dbName: DB_NAME,
      objectStoreNames: [OBJECT_STORE_NAME],
      keyPath: "id",
      indexes: [{ keyPath: "id", name: "id", unique: true }],
    });

    getCachedBlobUrl(url);
  }, []);

  return { resource, error, isLoading };
}

export async function getCachedBlobUrl(url: string, skipCache = false) {
  if(skipCache){
     return await fetch(url).then(res => res.blob()).then(blob => URL.createObjectURL(blob))
  }
  initCollection({
    dbName: DB_NAME,
    objectStoreNames: [OBJECT_STORE_NAME],
    keyPath: "id",
    indexes: [{ keyPath: "id", name: "id", unique: true }],
  });

  const blob = await getResource(url);
  if (blob) {
    return URL.createObjectURL(blob);
  }
  throw new Error("Failed to load resource");
}

async function getResource(url: string) {
  let resource: Blob | null = null;
  const hash = stringToHash(url);

  const cachedResource = await findInCollection<{ id: string; data: Blob }>(DB_NAME, OBJECT_STORE_NAME, hash);
  if (cachedResource) {
    resource = cachedResource.data;
  } else {
    const res = await fetch(url);
    const data = await res.blob();

    if (data) {
      await pushToCollection(DB_NAME, OBJECT_STORE_NAME, {
        id: hash,
        data,
      });
      resource = data;
    }
  }

  return resource;
}
