import { uploadClient } from "@/api/client";
import { FILE_TYPES } from "@/assets/interfaces";
import { delay, secondsToHHMMSS } from "@/assets/js/utils/functions";
import { getCachedBlobUrl } from "@/components/hooks/useGetCachedResource";
import useProgress from "@/components/hooks/useProgess";
import useRefState from "@/components/hooks/useRefState";
import { VideoDimensions } from "@/components/products/modals/process-video/trim-video";
import { toast } from "@/components/ui/toast";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { fetchFile } from "@ffmpeg/util";
import { MutableRefObject, useEffect, useRef, useState } from "react";
import { createContainer } from "unstated-next";

export enum VideoStepStatus {
  UPLOADING = "UPLOADING",
  SUCCESS = "SUCCESS",
  COMPRESSING = "COMPRESSING",
  FETCHING = "FETCHING",
  ERROR = "ERROR",
  WAITING = "WAITING",
}

export interface VideoProgress {
  isActive: boolean;
  step: VideoStepStatus;
  progress: number;
  meta?: any;
}

export interface VideoProgresses {
  [taskId: string]: { [videoKey: string]: VideoProgress };
}

export interface VideoTranscodeTask {
  taskId: string;
  videoKey: string;
  file: File;
  type?: FILE_TYPES;
  dimensions: VideoDimensions;
  trim: {
    start: number;
    end: number;
  };
  meta?: any;
}

export interface Props {
  cb: (taskId: string, videoKey: string, url: string, blob: Blob) => void;
  skipUpload?: boolean;
  ffmpegRef: MutableRefObject<FFmpeg>;
}

export interface ContextProps {
  onFail: VoidFunction;
  onLoad: VoidFunction;
}

function useFfmpegContext() {
  const [ffmpegLoading, setFfmpegLoading] = useState(false);
  const [canTranscode, setCanTranscode] = useState(true);
  const ffmpegRef = useRef<FFmpeg>();

  const loadProgress = useProgress([
    {
      key: "LOADING",
      label: "Downloading transcoding resources. Please wait...",
      isLoading: false,
      complete: false,
      useSpinner: true,
    },
  ]);

  useEffect(() => {
    console.log("loading ffmpeg");
    ffmpegRef.current = new FFmpeg();
    initializeTranscoder();
  }, []);

  const initializeTranscoder = async () => {
    if (!clientIsSupported()) {
      setFfmpegLoading(false);
      setCanTranscode(false);
      return;
    }

    ffmpegRef.current.on("log", () => {
    });

    loadProgress.setStepIsLoading();
    setFfmpegLoading(true);

    try {
      await getCachedBlobUrl(`${window.location.origin}/ffmpeg-core.js`);
      await ffmpegRef.current.load({
        coreURL: "/ffmpeg-core.js",
        wasmURL: "/ffmpeg-core.wasm",
        workerURL: "/ffmpeg-core.worker.js",
      });
    } catch (error) {
      setFfmpegLoading(false);
      setCanTranscode(false);

      toast.error({
        title: "Error",
        message: error.toString(),
      });
    }
    loadProgress.setStepComplete();
    setFfmpegLoading(false);
  };

  const deInitializeTranscoder = () => {
    ffmpegRef.current.terminate();
  };

  return {
    ffmpegRef,
    loadProgress,
    ffmpegLoading,
    canTranscode,
    deInitializeTranscoder,
  };
}

// export interface Props {
//   cb: (taskId: string, videoKey: string, url: string) => void;
//   skipUpload?: boolean;
//   onFail?: VoidFunction;
//   onLoad?: VoidFunction;
//   ffmpegRef?: MutableRefObject<FFmpeg>;
// }

export interface Props {
  cb: (taskId: string, videoKey: string, url: string, blob: Blob) => void;
  skipUpload?: boolean;
  ffmpegRef: MutableRefObject<FFmpeg>;
}

function useVideoTranscode({ cb, ffmpegRef, skipUpload = false }: Props) {
  const [videoProgressesRef, setVideoProgresses] = useRefState<VideoProgresses>({});

  const videoTaskQueue = useRef<VideoTranscodeTask[]>([]);
  const failedTaskQueue = useRef<VideoTranscodeTask[]>([]);

  const updateVideoProgress = (
    taskId: string,
    videoKey: string,
    step: VideoStepStatus,
    progress: number,
    meta?: any
  ) => {
    const progresses = {
      ...videoProgressesRef.value,
      [taskId]: {
        ...(videoProgressesRef.value?.[taskId] ?? {}),
        [videoKey]: {
          progress,
          step: step,
          meta: meta ? meta : videoProgressesRef.value?.[taskId]?.[videoKey]?.meta ?? {},
        } as VideoProgress,
      },
    };
    setVideoProgresses(progresses);
  };

  const removeVideoProgress = (taskId: string, videoKey: string) => {
    const videoProgressesCopy = { ...videoProgressesRef.value };
    delete videoProgressesCopy[taskId][videoKey];
    setVideoProgresses(videoProgressesCopy);
  };

  // Methods
  const clientTranscodeVideo = async (
    dimensions: VideoDimensions,
    taskId: string,
    videoKey: string,
    trim: VideoTranscodeTask["trim"],
    file: File,
    fileType = FILE_TYPES.ITEMS
  ) => {
    //compress video
    let lastTimeoutId = null;

    if (file) {
      updateVideoProgress(taskId, videoKey, VideoStepStatus.COMPRESSING, null);

      // transcode video
      const blob = await clientTranscode(file, ffmpegRef.current, dimensions, trim, ({ progress }) => {
        progress = Math.ceil(progress * 100);

        const isComplete = progress >= 100;

        // debounce the oga
        if (lastTimeoutId && !isComplete) return;

        lastTimeoutId = setTimeout(() => {
          if (lastTimeoutId) clearTimeout(lastTimeoutId);

          updateVideoProgress(
            taskId,
            videoKey,
            isComplete ? VideoStepStatus.UPLOADING : VideoStepStatus.COMPRESSING,
            isComplete ? null : progress
          );

          lastTimeoutId = null;
        }, 200);
      });

      let url = "";

      transcodeNext();

      if (skipUpload === false) {
        //upload video
        const res = await new Promise<any>((resolve, reject) => {
          uploadClient(
            { file: blob, endpoint: "file/video", type: fileType },
            (value) => {
              if (value === 100) value = 90;
              updateVideoProgress(taskId, videoKey, VideoStepStatus.UPLOADING, value);
            },
            (data: any, err, req: XMLHttpRequest, retry) => {
              if (data) {
                resolve(data);
              } else reject(err);
            }
          );
        });

        url = JSON.parse(res.response)?.data?.link;
      }

      await delay(1000);
      updateVideoProgress(taskId, videoKey, VideoStepStatus.SUCCESS, 100);
      cb(taskId, videoKey, url, blob);
    }
  };

  const transcodeNext = async () => {
    const queue = videoTaskQueue.current;
    if (queue.length > 0) {
      const task = videoTaskQueue.current.shift();
      const { dimensions, taskId, videoKey, trim, file, type } = task;
      const useClient = clientIsSupported();

      if (useClient) {
        try {
          await clientTranscodeVideo(dimensions, taskId, videoKey, trim, file, type);
        } catch (e) {
          updateVideoProgress(taskId, videoKey, VideoStepStatus.ERROR, null);
          failedTaskQueue.current.push(task);
        }
      }
    }
  };

  const transcodeVideo = async (task: VideoTranscodeTask) => {
    videoTaskQueue.current.push(task);
    updateVideoProgress(task.taskId, task.videoKey, VideoStepStatus.WAITING, null, task.meta);
    if (videoTaskQueue.current.length === 1) transcodeNext();
  };

  const retryVideoTask = (taskId: string, videoKey: string) => {
    const failedTaskIndex = failedTaskQueue.current.findIndex((t) => t.taskId === taskId && t.videoKey === videoKey);
    if (failedTaskIndex >= 0) {
      const [task] = failedTaskQueue.current.splice(failedTaskIndex);
      if (task) {
        transcodeVideo(task);
      }
    }
  };

  return {
    videoProgresses: videoProgressesRef,
    transcodeVideo,
    retryVideoTask,
    removeVideoProgress,
  };
}

export default useVideoTranscode;

///CURRENT CODE
// export interface Props {
//   cb: (taskId: string, videoKey: string, url: string) => void;
//   skipUpload?: boolean;
//   onFail?: VoidFunction;
//   onLoad?: VoidFunction;
// }

// function useVideoTranscode({ cb, onFail, onLoad, skipUpload = false }: Props) {
//   const [videoProgressesRef, setVideoProgresses] = useRefState<VideoProgresses>({});
//   const ffmpegRef = useRef(new FFmpeg());
//   const videoTaskQueue = useRef<VideoTranscodeTask[]>([]);
//   const failedTaskQueue = useRef<VideoTranscodeTask[]>([]);
//   const [videoResourcesLoaded, setVideoResourcesLoaded] = useState(false);
//   const [canTranscode, setCanTranscode] = useState(false);

//   const loadProgress = useProgress([
//     {
//       key: "LOADING",
//       label: "Downloading transcoding resources. Please wait...",
//       isLoading: false,
//       complete: false,
//       useSpinner: true,
//     },
//   ]);
//   useEffect(() => {
//     console.log("loading ffmpeg")
//     ffmpegRef.current = new FFmpeg();
//     initializeTranscoder();
//   }, []);

//   const initializeTranscoder = async () => {
//     if (!clientIsSupported()) {
//       handleLoadFailed();
//       return;
//     }

//     ffmpegRef.current.on("log", ({ message }) => {
//       // console.log(message);
//     });

//     loadProgress.setStepIsLoading();
//     setFfmpegLoading(true);

//     try {
//       const d = await getCachedBlobUrl(`${window.location.origin}/ffmpeg-core.js`);
//       console.log(d);
//       await ffmpegRef.current.load({
//         coreURL: "/ffmpeg-core.js",
//         wasmURL: "/ffmpeg-core.wasm",
//         workerURL: "/ffmpeg-core.worker.js",
//       });
//     } catch (error) {
//       handleLoadFailed();
//       toast.error({
//         title: "Error",
//         message: error.toString(),
//       });
//     }
//     loadProgress.setStepComplete();
//     console.log("finished loading");
//     handleLoadSuccess();
//   };

//   const handleLoadFailed = () => {
//     setCanTranscode(false);
//     onFail?.();
//   };

//   const handleLoadSuccess = () => {
//     setCanTranscode(true);
//     setVideoResourcesLoaded(true);
//     onLoad?.();
//   };

//   const deInitializeTranscoder = () => {
//     ffmpegRef.current.terminate();
//   };

//   return {
//     ffmpegRef,
//     loadProgress,
//     ffmpegLoading,
//     canTranscode,
//     deInitializeTranscoder,
//     videoResourcesLoaded
//   };
// }

// function useVideoTranscode({ cb, ffmpegRef, skipUpload = false }: Props) {
//   const [videoProgressesRef, setVideoProgresses] = useRefState<VideoProgresses>({});

//   const videoTaskQueue = useRef<VideoTranscodeTask[]>([]);
//   const failedTaskQueue = useRef<VideoTranscodeTask[]>([]);

//   const updateVideoProgress = (
//     taskId: string,
//     videoKey: string,
//     step: VideoStepStatus,
//     progress: number,
//     meta?: any
//   ) => {
//     const progresses = {
//       ...videoProgressesRef.value,
//       [taskId]: {
//         ...(videoProgressesRef.value?.[taskId] ?? {}),
//         [videoKey]: {
//           progress,
//           step: step,
//           meta: meta ? meta : videoProgressesRef.value?.[taskId]?.[videoKey]?.meta ?? {},
//         } as VideoProgress,
//       },
//     };
//     setVideoProgresses(progresses);
//   };

//   const removeVideoProgress = (taskId: string, videoKey: string) => {
//     try {
//       const videoProgressesCopy = { ...videoProgressesRef.value };
//       delete videoProgressesCopy[taskId][videoKey];
//       setVideoProgresses(videoProgressesCopy);
//     } catch (error) {
//       //do nothing
//       // console.log({ error });
//     }
//   };

//   console.log({ videoProgressesRef });

//   // Methods
//   const clientTranscodeVideo = async (
//     dimensions: VideoDimensions,
//     taskId: string,
//     videoKey: string,
//     trim: VideoTranscodeTask["trim"],
//     file: File
//   ) => {
//     //compress video
//     let lastTimeoutId = null;

//     if (file) {
//       updateVideoProgress(taskId, videoKey, VideoStepStatus.COMPRESSING, null);

//       // transcode video
//       const blob = await clientTranscode(file, ffmpegRef.current, dimensions, trim, ({ progress }) => {
//         progress = Math.ceil(progress * 100);

//         const isComplete = progress >= 100;

//         // debounce the oga
//         if (lastTimeoutId && !isComplete) return;

//         lastTimeoutId = setTimeout(() => {
//           if (lastTimeoutId) clearTimeout(lastTimeoutId);

//           updateVideoProgress(
//             taskId,
//             videoKey,
//             isComplete ? VideoStepStatus.UPLOADING : VideoStepStatus.COMPRESSING,
//             isComplete ? null : progress
//           );

//           lastTimeoutId = null;
//         }, 200);
//       });

//       let url = "";

//       if (skipUpload === false) {
//         //upload video
//         const res = await new Promise<any>((resolve, reject) => {
//           uploadClient(
//             { file: blob, endpoint: "file/video" },
//             (value) => {
//               if (value === 100) value = 90;
//               updateVideoProgress(taskId, videoKey, VideoStepStatus.UPLOADING, value);
//             },
//             (data: any, err, req: XMLHttpRequest, retry) => {
//               if (data) {
//                 resolve(data);
//               } else reject(err);
//             }
//           );
//         });

//         url = JSON.parse(res.response)?.data?.link;
//       }

//       await delay(1000);
//       updateVideoProgress(taskId, videoKey, VideoStepStatus.SUCCESS, 100);
//       cb(taskId, videoKey, url);
//     }
//   };

//   const transcodeNext = async () => {
//     const queue = videoTaskQueue.current;
//     if (queue.length > 0) {
//       const { dimensions, taskId, videoKey, trim, file } = queue[0];
//       console.log("handling task:", queue[0]);
//       const useClient = clientIsSupported();

//       if (useClient) {
//         try {
//           await clientTranscodeVideo(dimensions, taskId, videoKey, trim, file);
//           videoTaskQueue.current.shift();
//         } catch (e) {
//           updateVideoProgress(taskId, videoKey, VideoStepStatus.ERROR, null);
//           const failedTask = videoTaskQueue.current.shift();
//           failedTaskQueue.current.push(failedTask);
//         }
//         await transcodeNext();
//       }
//     }
//   };

//   const transcodeVideo = async (task: VideoTranscodeTask) => {
//     videoTaskQueue.current.push(task);
//     updateVideoProgress(task.taskId, task.videoKey, VideoStepStatus.WAITING, null, task.meta);
//     if (videoTaskQueue.current.length === 1) transcodeNext();
//   };

//   const retryVideoTask = (taskId: string, videoKey: string) => {
//     const failedTaskIndex = failedTaskQueue.current.findIndex((t) => t.taskId === taskId && t.videoKey === videoKey);
//     if (failedTaskIndex >= 0) {
//       const [task] = failedTaskQueue.current.splice(failedTaskIndex);
//       if (task) {
//         transcodeVideo(task);
//       }
//     }
//   };

//   return {
//     videoProgresses: videoProgressesRef,
//     transcodeVideo,
//     retryVideoTask,
//     removeVideoProgress,
// <<<<<<< HEAD
//     loadProgress,
//     canTranscode,
//     videoResourcesLoaded,
// =======
// >>>>>>> origin/new-storefront-dashbooard
//   };
// }

// export default useVideoTranscode;

const clientTranscode = async (
  videoFile: File | Blob,
  ffmpeg: FFmpeg,
  dimensions: VideoDimensions,
  trim: VideoTranscodeTask["trim"],
  progressCallback: ({ progress }: { progress: number }) => void
) => {
  if (ffmpeg.loaded) {
    ffmpeg.on("progress", progressCallback);
    const outputFile = "output.mp4";
    const fixedWidth = 960;
    const isLandscape = dimensions.width > dimensions.height;
    const maxLength = Math.max(dimensions.width, dimensions.height);
    const ratio = dimensions.width / dimensions.height;
    let width = dimensions.width;
    let height = dimensions.height;

    if (maxLength > 960) {
      width = isLandscape ? fixedWidth : fixedWidth * ratio;
      height = isLandscape ? fixedWidth / ratio : fixedWidth;
    }

    await ffmpeg.writeFile((videoFile as any).name ?? "video", await fetchFile(videoFile));

    const start = secondsToHHMMSS(trim.start);
    const durationSeconds = trim.end - trim.start;
    const duration = secondsToHHMMSS(durationSeconds);

    // prettier-ignore
    const status =await ffmpeg.exec([
      "-ss", start,
      "-i", (videoFile as any).name ?? "video",
      "-vf", `scale=${width}:${height}`,
      "-r", "24",
      "-preset", "slow",
      "-c:v", "libx264",
      "-c:a", "copy",
      "-b:a", "128k",
      "-t", duration,
      outputFile,
    ]);

    ffmpeg.off("progress", progressCallback);
    progressCallback({ progress: 1 });

    if (status === 0) {
      const data: any = await ffmpeg.readFile(outputFile);
      return new Blob([data.buffer], { type: "video/mp4" });
    } else {
      throw new Error("Error occurred processing video");
    }
  }
};

export const clientIsSupported = () => {
  let useClient = true;

  if (typeof SharedArrayBuffer == "undefined") useClient = false;

  if (!("indexedDB" in window)) useClient = false;
  if (!(typeof WebAssembly === "object" && typeof WebAssembly.instantiate === "function")) useClient = false;
  return useClient;
};

export const ffmpegContext = createContainer(useFfmpegContext);

/*

  Videos are chosen and stored in their raw file state in product form
    on select device as source
      open file picker
      show preview

  Videos from ig are converted to files and stored.
  After video edits (thumbnail, length, trim)
      the video transcoding service is given a task with the product index in the form and the video index
  The state of transcoding is stored in an internal state and exported
  After the video is uploaded the product form is updated


  const serverTranscode = async (
    videoFile: File,
    progressCallback: (progress: number) => void,
    uploadFinished: VoidFunction
  ) => {
    const res = await new Promise<any>((resolve, reject) => {
      let currentStep;
      uploadClient(
        { file: videoFile, endpoint: "utils/transcode-video" },
        (value) => {
          progressCallback(value);
          if (value === 100) uploadFinished();
        },
        (data: any, err, req: XMLHttpRequest, retry) => {
          if (data) {
            resolve(data);
            progressCallback(100);
          } else reject(err);
        }
      );
    });
    const url = JSON.parse(res.response)?.data?.link;
    return url;
  };

  const serverTranscodeProductVideo = async (currentProduct: number) => {
    updateVideoProgress(currentProduct, ProductVideoProgressStep.UPLOADING, null, false);
    const currentVideo = form.products[currentProduct].video;
    let isUploading = true;
    const url = await serverTranscode(
      currentVideo.file,
      (progress) => {
        if (isUploading) updateVideoProgress(currentProduct, ProductVideoProgressStep.UPLOADING, progress, true);
        else updateVideoProgress(currentProduct, ProductVideoProgressStep.COMPRESSING, progress, true);
      },
      () => {
        updateVideoProgress(currentProduct, ProductVideoProgressStep.COMPRESSING, null, false);
        isUploading = false;
      }
    );
    if (url) {
      emit(`video-url-${currentProduct}`, url);
    }
    updateVideoProgress(currentProduct, ProductVideoProgressStep.SUCCESS, 100, true);
  };

  if (url) {
        emit(`video-url-${currentProduct}`, url);
  }

  window.open(URL.createObjectURL(blob), "_blank");
*/
