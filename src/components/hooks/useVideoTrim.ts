import { Video } from "@/assets/interfaces";
import { useEffect, useState } from "react";

const useVideoTrim = (videos: Video[]) => {
  const [videoTrim, setVideoTrim] = useState<Array<{ start: number; end: number }>>([]);

  useEffect(() => {
    // Initialize videoTrim array when videos change
    if (videos?.length) {
      setVideoTrim((prevTrim) => {
        const newTrim = [...prevTrim];
        // If we have more videos than trim values, add new ones
        while (newTrim.length < videos.length) {
          newTrim.push({ start: 0, end: 45 });
        }
        // If we have more trim values than videos, remove extras
        if (newTrim.length > videos.length) {
          newTrim.splice(videos.length);
        }
        return newTrim;
      });
    }
  }, [videos?.length]);

  return { videoTrim, setVideoTrim };
};

export default useVideoTrim;
