import { useEffect, useRef, useState } from "react";

interface LazyImageProps extends React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement> {
  src: string;
  showLoader?: boolean;
  loaderClasses?: string;
}

const LazyImage: React.FC<LazyImageProps> = ({ src, showLoader = true, loaderClasses, ...props }) => {
  const imageRef = useRef<HTMLImageElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);

  const [resourceBlobUrl, setResourceBlobUrl] = useState<string>();

  useEffect(() => {
    const fn = async () => {
      const res = await fetch(`/api/res?url=${encodeURIComponent(src)}`);
      const b = await res.blob();
      setResourceBlobUrl(URL.createObjectURL(b));
    };
    if (src?.startsWith?.("/") || src?.startsWith?.("blob:")) {
      setResourceBlobUrl(src);
      return;
    }
    fn();
  }, []);

  useEffect(() => {
    const imageObserver = new IntersectionObserver(
      (entries, observer) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.unobserve(imageRef.current);
          }
        });
      },
      { threshold: 0.1 }
    );

    imageObserver.observe(imageRef.current);

    return () => imageObserver.disconnect();
  }, []);

  function getImage() {
    if (isVisible) {
      return resourceBlobUrl;
    }
    return "";
  }

  return (
    <>
      {!hasLoaded && showLoader && (
        <div className={`bg-grey-loader h-full w-full absolute top-0 animate-pulse ${loaderClasses}`}></div>
      )}
      <img
        // crossOrigin="anonymous"
        src={getImage()}
        {...props}
        style={!hasLoaded ? { opacity: 0 } : { ...props.style }}
        ref={imageRef}
        onLoad={() => setHasLoaded(true)}
      />
    </>
  );
};

export default LazyImage;
