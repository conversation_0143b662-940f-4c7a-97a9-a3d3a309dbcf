import React from "react";
import FullScreenModal from "../ui/full-screen-modal";
import { ModalProps } from "../ui/modal";

interface Props {
  show: boolean;
  toggle: () => void;
  storeLink: string;
}

const PreviewStoreModal: React.FC<Props> = ({ show, toggle, storeLink }) => {
  return (
    <FullScreenModal title="Your Store" {...{ show, toggle }}>
      <div className="h-full w-full relative overflow-hidden flex-1">
        <div className="absolute top-0 left-0 w-full h-full bg-white flex items-center justify-center">
          <div className="flex items-center">
            <span className="spinner spinner--sm text-primary-500"></span>
            <span className="text-dark text-1s ml-2">Loading Preview...</span>
          </div>
        </div>
        <iframe className="w-full h-full relative z-10 bg-white" id="storelink-iframe" src={storeLink}></iframe>
      </div>
    </FullScreenModal>
  );
};

export default PreviewStoreModal;
