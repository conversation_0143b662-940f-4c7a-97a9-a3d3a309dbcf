import { formatTime } from "@/assets/js/utils/functions";
import React, { useEffect, useRef, useState } from "react";

interface WalkthroughVideoWithRewardProps {
  videoSrc: string;
  onRewardEarned: () => void;
}

const WalkthroughVideoWithReward: React.FC<WalkthroughVideoWithRewardProps> = ({ videoSrc, onRewardEarned }) => {
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const watchTimerRef = useRef<NodeJS.Timeout | null>(null);
  const previousTimeRef = useRef<number>(0);
  const previousPlaybackRateRef = useRef<number>(1);

  // State
  const [watchedTime, setWatchedTime] = useState<number>(0);
  const [warningMessage, setWarningMessage] = useState<{ message: string; show: boolean }>({
    message: "",
    show: false,
  });

  // Config
  const minWatchPercentage = 95; // Must watch at least 95%
  const checkIntervalMs = 500; // Update watched time every second

  const startWatchTimer = () => {
    if (!videoRef.current) return;
    if (watchTimerRef.current) clearInterval(watchTimerRef.current);

    watchTimerRef.current = setInterval(() => {
      setWatchedTime((prev) => prev + 0.5);
    }, checkIntervalMs);
  };

  const stopWatchTimer = (videoEnded: boolean = false) => {
    if (watchTimerRef.current) {
      clearInterval(watchTimerRef.current);
      watchTimerRef.current = null;
    }

    if (videoEnded) setWatchedTime(0);
  };

  const showWarning = (message: string) => {
    setWarningMessage({
      message,
      show: true,
    });

    setTimeout(() => {
      setWarningMessage({
        message: "",
        show: false,
      });
    }, 5000);
  };

  const handleEnded = () => {
    if (!videoRef.current) return;

    const totalDuration = videoRef.current.duration;
    const requiredWatchTime = (minWatchPercentage / 100) * totalDuration;

    if (watchedTime >= requiredWatchTime) {
      // setShowReward(true);
      // setRewardMessage("Congratulations! You've earned your bonus!");
      onRewardEarned();
    } else {
      showWarning(`You've only watched ${formatTime(watchedTime)}. Please watch the full video`);
    }

    stopWatchTimer();
  };

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => {
      startWatchTimer();
    };

    const handlePause = () => {
      stopWatchTimer();
    };

    // Stop counting if video is buffering
    const handleWaiting = () => {
      stopWatchTimer();
    };

    // Resume when video starts playing again
    const handlePlaying = () => {
      startWatchTimer();
    };

    const handleTimeUpdate = () => {
      if (!videoRef.current) return;

      // If seeking forward by more than 1 seconds, show a warning
      if (videoRef.current.currentTime - previousTimeRef.current >= 1) {
        showWarning("Please watch the video without skipping.");
      }

      previousTimeRef.current = videoRef.current.currentTime;
    };

    const handleVolumeChange = () => {
      if (video.muted || video.volume === 0) {
        showWarning("Please keep the volume on to earn your reward.");
        stopWatchTimer();
      } else if (!watchTimerRef.current) {
        startWatchTimer();
      }
    };

    const handlePlaybackRateChange = () => {
      if (!video) return;
      if (video.playbackRate > 1) {
        showWarning("Please watch the video at normal speed.");
        video.playbackRate = 1; // Reset speed to normal
      }
      previousPlaybackRateRef.current = video.playbackRate;
    };

    const handleVisibilityChange = () => {
      if (document.hidden && video && !video.paused) {
        video.pause();
        showWarning("Please keep this page active while watching the video.");
      }
    };

    video.addEventListener("play", handlePlay);
    video.addEventListener("pause", handlePause);
    video.addEventListener("waiting", handleWaiting);
    video.addEventListener("playing", handlePlaying);
    video.addEventListener("timeupdate", handleTimeUpdate);
    video.addEventListener("volumechange", handleVolumeChange);
    video.addEventListener("ratechange", handlePlaybackRateChange);
    video.addEventListener("ended", handleEnded);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      video.removeEventListener("play", handlePlay);
      video.removeEventListener("pause", handlePause);
      video.removeEventListener("waiting", handleWaiting);
      video.removeEventListener("playing", handlePlaying);
      video.addEventListener("timeupdate", handleTimeUpdate);
      video.removeEventListener("volumechange", handleVolumeChange);
      video.removeEventListener("ratechange", handlePlaybackRateChange);
      video.removeEventListener("ended", handleEnded);
      document.removeEventListener("visibilitychange", handleVisibilityChange);

      // stopWatchTimer();
    };
  }, [watchedTime, warningMessage]);

  return (
    <div className="w-full max-w-xl mx-auto mt-5">
      {warningMessage.show && (
        <div className="bg-red-600 bg-opacity-70 text-white p-2 rounded text-1xs font-medium w-full mb-2.5">
          {warningMessage.message}
        </div>
      )}
      <div className="relative w-full">
        <video
          ref={videoRef}
          src={videoSrc}
          controls
          className="w-full rounded-lg shadow-card border border-grey-divider"
          playsInline
        />
      </div>
    </div>
  );
};

export default WalkthroughVideoWithReward;
