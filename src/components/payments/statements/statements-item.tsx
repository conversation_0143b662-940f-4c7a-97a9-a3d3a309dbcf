import Badge from "@/components/ui/badge";
import { PaymentStatement } from "../../../assets/interfaces/invoices";
import { formatDateString } from "../../../assets/js/utils/utils";
import RoundActionButton from "../../ui/buttons/round-action-btn";
import { TableCell, TableRow } from "../../ui/table";

interface Props {
  item: PaymentStatement;
  onAction?: (action: "click" | "download" | "copy-link" | "toggle-pin") => void;
  isLoadingPdf: boolean;
  canShowPin: boolean;
}

const StatementItem: React.FC<Props> = ({ item, onAction, isLoadingPdf, canShowPin }) => {
  return (
    <TableRow onClick={() => onAction("click")}>
      {/* <TableCell className="font-medium">{item.id}</TableCell> */}
      <TableCell>
        {formatDateString(new Date(item.period?.start_date))} - {formatDateString(new Date(item.period?.end_date))}{" "}
      </TableCell>
      <TableCell>
        <Badge color="orange" text={item?.wallet?.currency} className="text-white" />
      </TableCell>
      <TableCell stopBubble>
        <div className="flex items-center gap-2.5">
          <span>{canShowPin ? item.pin : "••••••"}</span>
          <RoundActionButton onClick={() => onAction("toggle-pin")} icon={canShowPin ? "copy_text" : "show"} />
        </div>
      </TableCell>
      <TableCell className="space-x-2.5 flex items-center" stopBubble>
        <RoundActionButton onClick={() => onAction("copy-link")} icon="link" />
        {isLoadingPdf ? (
          <div className="spinner text-primary-500"></div>
        ) : (
          <RoundActionButton onClick={() => onAction("download")} icon="download" />
        )}
      </TableCell>
      <TableCell> {formatDateString(new Date(item.created_at))} </TableCell>
    </TableRow>
  );
};

export default StatementItem;
