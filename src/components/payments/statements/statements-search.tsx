import useScreenSize from "@/components/hooks/useScreenSize";
import AppSearchBar from "@/components/ui/app-search-bar";
import { AppBtn } from "@/components/ui/buttons";
import { useState } from "react";

interface Props {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  openRangePicker: VoidFunction
}
const StatementsSearch: React.FC<Props> = ({ searchQuery, setSearchQuery,openRangePicker }) => {
  const { width, isSmall } = useScreenSize();
  const [fullViewOnMobile, setFullViewOnMobile] = useState(false);
  return (
    <div className="flex items-center justify-between py-5">
      <h1 className="text-base md:text-xl font-bold">All statements</h1>
      <div className="flex items-center gap-2">
        <AppSearchBar
          placeholder="Search Statements by id"
          {...{ searchQuery, setSearchQuery, fullViewOnMobile, setFullViewOnMobile }}
        />
        {!isSmall && (
          <AppBtn size="md" onClick={openRangePicker}>
            New statement
          </AppBtn>
        )}
        {isSmall && (
          <AppBtn size="md" onClick={openRangePicker} className="!rounded-full !p-0 !h-9 !w-9">
            {/* prettier-ignore */}
            <svg width={17} height={17} viewBox="0 0 17 17" fill="none" >
                <path d="M4.25 8.5h8.5M8.5 12.75v-8.5" stroke="#fff" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          </AppBtn>
        )}
      </div>
    </div>
  );
};
export default StatementsSearch;
