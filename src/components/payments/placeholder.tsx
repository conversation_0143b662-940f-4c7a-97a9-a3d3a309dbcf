import classNames from "classnames";
import React, { useMemo } from "react";
import { GetStoreKYC } from "../../api";
import { useFetcher } from "../../api/utils";
import { COUNTRIES, KYCInfo } from "../../assets/interfaces";
import { validateData } from "../../pages/payments/kyc";
import { useModals } from "../hooks/useModals";
import { AppBtn } from "../ui/buttons";
import DashboardLayout from "../ui/layouts/dashboard";
import WalletContext from "@/contexts/wallet-context";
import { COUNTRY_CURRENCY_MAP, ONBOARDING_CREDITS, ONBOARDING_STEPS_WITH_REWARDS } from "@/assets/js/utils/constants";
import authContext from "@/contexts/auth-context";
import dayjs from "dayjs";

interface InvoicesPlaceholderProps {
  country: COUNTRIES;
}

const InvoicesPlaceholder: React.FC<InvoicesPlaceholderProps> = ({ country }) => {
  const { user } = authContext.useContainer();
  const { wallets } = WalletContext.useContainer();
  const { response, error, isLoading } = useFetcher(GetStoreKYC);
  const kycInfo: KYCInfo = response?.data;
  const showKYCProgress = kycInfo && kycInfo.first_name;

  const firstWallet = wallets[0];
  const showWalletLimitNotice = firstWallet && !firstWallet?.has_completed_kyc && firstWallet?.limits;

  const storeCurrency = COUNTRY_CURRENCY_MAP[country];
  const bannerNote = useMemo(() => {
    const signupDate = dayjs(user.created_at);
    const dayDiff = dayjs().diff(signupDate, "days");

    const threeDaysFromSignup = signupDate.add(3, "days").format("Do MMMM");
    const kycCompletedBonus = `${storeCurrency} ${
      ONBOARDING_CREDITS[ONBOARDING_STEPS_WITH_REWARDS.COMPLETE_KYC][storeCurrency] / 100
    }`;

    if (dayDiff <= 3) {
      return `Complete KYC before ${threeDaysFromSignup} & get ${kycCompletedBonus} off your subscription`;
    }

    return "Please complete your KYC to increase your collection limits";
  }, [storeCurrency, user.created_at]);

  const setupProgress = showKYCProgress
    ? [
        {
          title: "Basic Info",
          status: validateData(kycInfo).BASIC,
        },
        country === COUNTRIES.NG
          ? {
              title: "BVN",
              status: validateData(kycInfo).BVN,
            }
          : {
              title: "Phone Verification",
              status: validateData(kycInfo).PHONE,
            },
        {
          title: "ID Card",
          status: validateData(kycInfo).ID,
        },
        {
          title: "Address",
          status: validateData(kycInfo).ADDRESS,
        },
      ]
    : [];

  const percentageCompletion = (setupProgress.filter((p) => p.status).length / setupProgress.length) * 100;

  return (
    <DashboardLayout title="Enable Payments" padding={false}>
      {showKYCProgress && (
        <div className="flex flex-col items-center w-full h-full overflow-y-auto py-15 text-center px-5 sm:px-6.25 lg:px-7.5">
          <div className="flex flex-col items-center w-full">
            <div className="flex flex-col items-center text-center">
              <figure className="w-17.5 h-17.5 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 flex items-center justify-center">
                {/* prettier-ignore */}
                <svg className="w-8 sm:w-10" viewBox="0 0 40 40" fill="none">
                  <path d="M26.6673 7.08331C26.6673 9.14998 24.984 10.8333 22.9173 10.8333H17.084C16.0507 10.8333 15.1173 10.4166 14.434 9.73331C13.7507 9.04998 13.334 8.11665 13.334 7.08331C13.334 5.01665 15.0173 3.33331 17.084 3.33331H22.9173C23.9507 3.33331 24.884 3.74998 25.5673 4.43331C26.2507 5.11665 26.6673 6.04998 26.6673 7.08331Z" fill="white"/>
                  <path d="M31.3827 8.38333C30.9993 8.06667 30.566 7.81667 30.0993 7.63333C29.616 7.45 29.1327 7.83333 29.0327 8.33333C28.466 11.1833 25.9493 13.3333 22.916 13.3333H17.0827C15.416 13.3333 13.8493 12.6833 12.666 11.5C11.7993 10.6333 11.1993 9.53333 10.966 8.35C10.866 7.85 10.366 7.45 9.88268 7.65C7.94935 8.43333 6.66602 10.2 6.66602 13.75V30C6.66602 35 9.64935 36.6667 13.3327 36.6667H26.666C30.3493 36.6667 33.3327 35 33.3327 30V13.75C33.3327 11.0333 32.5827 9.36667 31.3827 8.38333ZM13.3327 20.4167H19.9993C20.6827 20.4167 21.2493 20.9833 21.2493 21.6667C21.2493 22.35 20.6827 22.9167 19.9993 22.9167H13.3327C12.6493 22.9167 12.0827 22.35 12.0827 21.6667C12.0827 20.9833 12.6493 20.4167 13.3327 20.4167ZM26.666 29.5833H13.3327C12.6493 29.5833 12.0827 29.0167 12.0827 28.3333C12.0827 27.65 12.6493 27.0833 13.3327 27.0833H26.666C27.3493 27.0833 27.916 27.65 27.916 28.3333C27.916 29.0167 27.3493 29.5833 26.666 29.5833Z" fill="white"/>
                </svg>
              </figure>
              <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold mt-3.75">Setup in progress</h2>
              <p className="text-sm text-dark mt-1.25 max-w-[250px] mx-auto">Pick up where you left</p>
            </div>

            <div className="mt-7.5 w-full mx-auto max-w-[450px]">
              <div className="w-full border border-grey-border border-opacity-50 rounded-15">
                <div className="w-full">
                  <div className="flex items-center justify-between px-3.75 sm:px-5 py-2 sm:py-2.5">
                    <h4 className="text-base sm:text-lg text-black font-bold">Progress</h4>
                    <span className="text-placeholder text-1xs text-sm font-medium">
                      {percentageCompletion}% Complete
                    </span>
                  </div>
                  <div className="w-full h-1.5 bg-grey-fields-100">
                    <div
                      className="h-full rounded-r-15 bg-accent-green-500"
                      style={{ width: `${percentageCompletion}%` }}
                    ></div>
                  </div>
                </div>
                <ul className="p-3.5 sm:px-5 sm:py-4 divide-y divide-grey-border divide-opacity-50">
                  {setupProgress.map((s, index) => (
                    <li
                      className="flex flex-center py-3.5 sm:py-4 first:pt-1.5 last:pb-1.5 justify-between"
                      key={index}
                    >
                      <span className="text-sm sm:text-1sm font-medium text-black-secondary">{s.title}</span>
                      <figure className="h-4.5 w-4.5 sm:w-5 sm:h-5 rounded-full bg-grey-fields-100">
                        {s.status && ( // prettier-ignore
                          <svg width="100%" viewBox="0 0 18 18" fill="none">
                            <rect width="18" height="18" rx="9" fill="#39B588" />
                            <path
                              d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z"
                              fill="white"
                            />
                          </svg>
                        )}

                        {!s.status && ( // prettier-ignore
                          <svg width="100%" className="text-grey-border-dark" viewBox="0 0 24 24" fill="none">
                            <path
                              d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM11.25 8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V13C12.75 13.41 12.41 13.75 12 13.75C11.59 13.75 11.25 13.41 11.25 13V8ZM12.92 16.38C12.87 16.51 12.8 16.61 12.71 16.71C12.61 16.8 12.5 16.87 12.38 16.92C12.26 16.97 12.13 17 12 17C11.87 17 11.74 16.97 11.62 16.92C11.5 16.87 11.39 16.8 11.29 16.71C11.2 16.61 11.13 16.51 11.08 16.38C11.03 16.26 11 16.13 11 16C11 15.87 11.03 15.74 11.08 15.62C11.13 15.5 11.2 15.39 11.29 15.29C11.39 15.2 11.5 15.13 11.62 15.08C11.86 14.98 12.14 14.98 12.38 15.08C12.5 15.13 12.61 15.2 12.71 15.29C12.8 15.39 12.87 15.5 12.92 15.62C12.97 15.74 13 15.87 13 16C13 16.13 12.97 16.26 12.92 16.38Z"
                              fill="currentColor"
                            />
                          </svg>
                        )}
                      </figure>
                    </li>
                  ))}
                </ul>
              </div>
              <AppBtn size="lg" className="mt-5" href="/payments/kyc">
                Finish setting up
                {/* prettier-ignore */}
                <svg className="w-5 ml-1.25 transform transition-transform ease-out duration-150 group-hover:translate-x-0.5" viewBox="0 0 25 25" fill="none">
                  <path d="M5.59375 12.4209H19.5938" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M12.5938 5.4209L19.5938 12.4209L12.5938 19.4209" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </AppBtn>
            </div>
          </div>
        </div>
      )}
      {!showKYCProgress && (
        <>
          {showWalletLimitNotice && (
            <div className="bg-grey-fields-100 text-black-muted text-1xs px-3.5 py-2.5 border-b border-grey-divider text-center font-medium">
              {bannerNote}
            </div>
          )}
          <div className="flex flex-col items-center w-full h-full overflow-y-auto py-15 text-center px-5 sm:px-6.25 lg:px-7.5">
            <div className="flex items-center flex-col max-w-[280px] sm:max-w-[360px]">
              <figure className="h-25 w-25 sm:h-30 sm:w-30">
                <img src="/images/kyc-approved.png" alt="Payments Illustration" className="w-full" />
              </figure>
              {!showWalletLimitNotice ? (
                <h1 className="text-2xl sm:text-3xl lg:text-4lg-small text-black font-light mt-0">
                  Collect Payments <br /> <b className="font-bold"> With Catlog</b>
                </h1>
              ) : (
                <h1 className="text-2xl sm:text-3xl lg:text-4lg-small text-black font-light mt-0">
                  Verify your Identity To <br /> <b className="font-bold">Increase Your Payment Limit</b>
                  {/* Increase your <br />  */}
                </h1>
              )}
              <p className="text-dark text-1sm sm:text-base mt-2.5">{copies[country]?.description}</p>
              <AppBtn size="lg" className="mt-6 sm:mt-7.5" href="/payments/kyc">
                {showWalletLimitNotice ? "Complete KYC" : "Start collecting payments"}
                {/* prettier-ignore */}
                <svg className="w-5 ml-1.25 transform transition-transform ease-out duration-150 group-hover:translate-x-0.5" viewBox="0 0 25 25" fill="none">
                  <path d="M5.59375 12.4209H19.5938" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M12.5938 5.4209L19.5938 12.4209L12.5938 19.4209" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </AppBtn>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 w-full max-w-[1020px] gap-5 lg:gap-7.5 mt-10 md:mt-12.5">
              {copies[country]?.cards.map((card, index) => (
                <article
                  className={classNames(
                    "flex flex-col items-center py-10 md:py-11.25 px-6.25 sm:px-7.5 w-full bg-grey-fields-100 rounded-15 max-w-[480px] lg:max-w-[370px] mx-auto",
                    card.color
                  )}
                  key={index}
                >
                  <figure className={`h-16.25 w-16.25 rounded-full flex items-center justify-center bg-white`}>
                    {card.icon}
                  </figure>
                  <h4 className="text-lg text-black font-bold mt-4 sm:mt-5">{card.title}</h4>
                  <p className="mt-2.5 text-dark text-1xs sm:text-sm">{card.description}</p>
                </article>
              ))}
            </div>
          </div>
        </>
      )}
    </DashboardLayout>
  );
};

const icons = {
  cardCheck:
    // prettier-ignore
    <svg width="55%" viewBox="0 0 24 24" fill="none">
      <path d="M19 15C16.79 15 15 16.79 15 19C15 19.75 15.21 20.46 15.58 21.06C16.27 22.22 17.54 23 19 23C20.46 23 21.73 22.22 22.42 21.06C22.79 20.46 23 19.75 23 19C23 16.79 21.21 15 19 15ZM21.07 18.57L18.94 20.54C18.8 20.67 18.61 20.74 18.43 20.74C18.24 20.74 18.05 20.67 17.9 20.52L16.91 19.53C16.62 19.24 16.62 18.76 16.91 18.47C17.2 18.18 17.68 18.18 17.97 18.47L18.45 18.95L20.05 17.47C20.35 17.19 20.83 17.21 21.11 17.51C21.39 17.81 21.37 18.28 21.07 18.57Z" fill="currentColor"/>
      <path d="M22 7.5399V8.9999H2V7.5399C2 5.2499 3.86002 3.3999 6.15002 3.3999H17.85C20.14 3.3999 22 5.2499 22 7.5399Z" fill="currentColor"/>
      <path opacity="0.4" d="M2 9V16.46C2 18.75 3.85001 20.6 6.14001 20.6H12.4C12.98 20.6 13.48 20.11 13.43 19.53C13.29 18 13.78 16.34 15.14 15.02C15.7 14.47 16.39 14.05 17.14 13.81C18.39 13.41 19.6 13.46 20.67 13.82C21.32 14.04 22 13.57 22 12.88V9H2ZM8 17.25H6C5.59 17.25 5.25 16.91 5.25 16.5C5.25 16.09 5.59 15.75 6 15.75H8C8.41 15.75 8.75 16.09 8.75 16.5C8.75 16.91 8.41 17.25 8 17.25Z" fill="currentColor"/>
      <path d="M8.75 16.5C8.75 16.91 8.41 17.25 8 17.25H6C5.59 17.25 5.25 16.91 5.25 16.5C5.25 16.09 5.59 15.75 6 15.75H8C8.41 15.75 8.75 16.09 8.75 16.5Z" fill="currentColor"/>
    </svg>,
  wallet:
    // prettier-ignore
    <svg width="55%" viewBox="0 0 24 24" fill="none">
      <path opacity="0.8" d="M12.8992 2.52009L12.8692 2.59009L9.96922 9.32009H7.11922C6.43922 9.32009 5.79922 9.45009 5.19922 9.71009L6.94922 5.53009L6.98922 5.44009L7.04922 5.28009C7.07922 5.21009 7.09922 5.15009 7.12922 5.10009C8.43922 2.07009 9.91922 1.38009 12.8992 2.52009Z" fill="currentColor"/>
      <path d="M18.2907 9.52002C17.8407 9.39002 17.3707 9.32002 16.8807 9.32002H9.9707L12.8707 2.59002L12.9007 2.52002C13.0407 2.57002 13.1907 2.64002 13.3407 2.69002L15.5507 3.62002C16.7807 4.13002 17.6407 4.66002 18.1707 5.30002C18.2607 5.42002 18.3407 5.53002 18.4207 5.66002C18.5107 5.80002 18.5807 5.94002 18.6207 6.09002C18.6607 6.18002 18.6907 6.26002 18.7107 6.35002C18.9707 7.20002 18.8107 8.23002 18.2907 9.52002Z" fill="currentColor"/>
      <path opacity="0.4" d="M21.7602 14.1998V16.1498C21.7602 16.3498 21.7502 16.5498 21.7402 16.7398C21.5502 20.2398 19.6002 21.9998 15.9002 21.9998H8.10023C7.85023 21.9998 7.62023 21.9798 7.39023 21.9498C4.21023 21.7398 2.51023 20.0398 2.29023 16.8598C2.26023 16.6198 2.24023 16.3898 2.24023 16.1498V14.1998C2.24023 12.1898 3.46023 10.4598 5.20023 9.70982C5.80023 9.44982 6.44023 9.31982 7.12023 9.31982H16.8802C17.3702 9.31982 17.8402 9.38982 18.2902 9.51982C20.2902 10.1298 21.7602 11.9898 21.7602 14.1998Z" fill="currentColor"/>
      <path opacity="0.6" d="M6.95023 5.52979L5.20023 9.70978C3.46023 10.4598 2.24023 12.1898 2.24023 14.1998V11.2698C2.24023 8.42979 4.26023 6.05979 6.95023 5.52979Z" fill="currentColor"/>
      <path opacity="0.6" d="M21.7591 11.2698V14.1998C21.7591 11.9898 20.2891 10.1298 18.2891 9.51984C18.8091 8.22984 18.9691 7.19984 18.7091 6.34984C18.6891 6.25984 18.6591 6.17984 18.6191 6.08984C20.4891 7.05984 21.7591 9.02984 21.7591 11.2698Z" fill="currentColor"/>
      <path d="M14.5 16.75H9.5C9.09 16.75 8.75 16.41 8.75 16C8.75 15.59 9.09 15.25 9.5 15.25H14.5C14.91 15.25 15.25 15.59 15.25 16C15.25 16.41 14.91 16.75 14.5 16.75Z" fill="currentColor"/>
    </svg>,
  clock:
    // prettier-ignore
    <svg width="55%" viewBox="0 0 24 24" fill="none">
      <path opacity="0.4" d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="currentColor"/>
      <path d="M15.7106 15.9298C15.5806 15.9298 15.4506 15.8998 15.3306 15.8198L12.2306 13.9698C11.4606 13.5098 10.8906 12.4998 10.8906 11.6098V7.50977C10.8906 7.09977 11.2306 6.75977 11.6406 6.75977C12.0506 6.75977 12.3906 7.09977 12.3906 7.50977V11.6098C12.3906 11.9698 12.6906 12.4998 13.0006 12.6798L16.1006 14.5298C16.4606 14.7398 16.5706 15.1998 16.3606 15.5598C16.2106 15.7998 15.9606 15.9298 15.7106 15.9298Z" fill="currentColor"/>
    </svg>,
  invoice:
    // prettier-ignore
    <svg width="55%" viewBox="0 0 24 24" fill="none">
      <path d="M22 6V8.42C22 10 21 11 19.42 11H16V4.01C16 2.9 16.91 2 18.02 2C19.11 2.01 20.11 2.45 20.83 3.17C21.55 3.9 22 4.9 22 6Z" fill="currentColor"/>
      <path opacity="0.4" d="M2 7V21C2 21.83 2.94001 22.3 3.60001 21.8L5.31 20.52C5.71 20.22 6.27 20.26 6.63 20.62L8.28999 22.29C8.67999 22.68 9.32001 22.68 9.71001 22.29L11.39 20.61C11.74 20.26 12.3 20.22 12.69 20.52L14.4 21.8C15.06 22.29 16 21.82 16 21V4C16 2.9 16.9 2 18 2H7H6C3 2 2 3.79 2 6V7Z" fill="currentColor"/>
    </svg>,
};

const copies = {
  [COUNTRIES.NG]: {
    description: "Get a free bank account, collect payment in multiple ways & create professional invoices",
    cards: [
      {
        title: "Flexible Payment Options",
        description: "Give your customers multiple ways to pay you - with tranfers, cards, ussd & even split payments.",
        icon: icons.cardCheck,
        color: "text-accent-yellow-500 bg-accent-yellow-pastel",
      },
      {
        title: "Dedicated Business Account",
        description: "You get a dedicated business account number - this helps you separate your business finance.",
        icon: icons.wallet,
        color: "text-accent-orange-500 bg-accent-orange-pastel",
      },
      {
        title: "Instant Withdrawals",
        description: "Withdraw funds anytime you like and get them deposited into your bank accounts instantly.",
        icon: icons.clock,
        color: "text-accent-red-500 bg-accent-red-pastel",
      },
    ],
  },
  [COUNTRIES.GH]: {
    description: "Accept MoMo and card payments seamlessly on your storefront and via inovice links",
    cards: [
      {
        title: "Flexible Payment Options",
        description:
          "Provide your customers with multiple payment options - through cards or their mobile money wallets.",
        icon: icons.cardCheck,
        color: "text-accent-yellow-500 bg-accent-yellow-pastel",
      },
      {
        title: "Get Paid with Invoice links",
        description: "Easily create professional invoices while offering your customers convenient payment options",
        icon: icons.invoice,
        color: "text-accent-orange-500 bg-accent-orange-pastel",
      },
      {
        title: "Instant Withdrawals",
        description:
          "Withdraw funds anytime and enjoy instant deposits into your mobile money wallets whenever you need them",
        icon: icons.clock,
        color: "text-accent-red-500 bg-accent-red-pastel",
      },
    ],
  },
  [COUNTRIES.KE]: {
    description: "Accept M-PESA and card payments on your storefront and via invoice links",
    cards: [
      {
        title: "Flexible Payment Options",
        description: "Give your customers multiple ways to pay you - with cards or M-PESA.",
        icon: icons.cardCheck,
        color: "text-accent-yellow-500 bg-accent-yellow-pastel",
      },
      {
        title: "Get Paid with Invoice links",
        description: "Easily create professional invoices while offering your customers convenient payment options",
        icon: icons.invoice,
        color: "text-accent-orange-500 bg-accent-orange-pastel",
      },
      {
        title: "Instant Withdrawals",
        description:
          "Withdraw funds anytime you like and get them deposited into your bank or M-PESA wallet instantly.",
        icon: icons.clock,
        color: "text-accent-red-500 bg-accent-red-pastel",
      },
    ],
  },
  [COUNTRIES.ZA]: {
    description: "Accept EFT and card payments on your storefront and via invoice links",
    cards: [
      {
        title: "Flexible Payment Options",
        description: "Give your customers multiple ways to pay you - with cards or EFT.",
        icon: icons.cardCheck,
        color: "text-accent-yellow-500 bg-accent-yellow-pastel",
      },
      {
        title: "Get Paid with Invoice links",
        description: "Easily create professional invoices while offering your customers convenient payment options",
        icon: icons.invoice,
        color: "text-accent-orange-500 bg-accent-orange-pastel",
      },
      {
        title: "Instant Withdrawals",
        description: "Withdraw funds anytime you like and get them deposited into your bank account instantly.",
        icon: icons.clock,
        color: "text-accent-red-500 bg-accent-red-pastel",
      },
    ],
  },
};

// const cards = [
//   {
//     title: "Flexible Payment Options",
//     description: "Give your customers multiple ways to pay you - with tranfers, cards, ussd & even split payments.",
//     icon:
//       // prettier-ignore
//       <svg width="55%" viewBox="0 0 24 24" fill="none">
//         <path d="M19 15C16.79 15 15 16.79 15 19C15 19.75 15.21 20.46 15.58 21.06C16.27 22.22 17.54 23 19 23C20.46 23 21.73 22.22 22.42 21.06C22.79 20.46 23 19.75 23 19C23 16.79 21.21 15 19 15ZM21.07 18.57L18.94 20.54C18.8 20.67 18.61 20.74 18.43 20.74C18.24 20.74 18.05 20.67 17.9 20.52L16.91 19.53C16.62 19.24 16.62 18.76 16.91 18.47C17.2 18.18 17.68 18.18 17.97 18.47L18.45 18.95L20.05 17.47C20.35 17.19 20.83 17.21 21.11 17.51C21.39 17.81 21.37 18.28 21.07 18.57Z" fill="currentColor"/>
//         <path d="M22 7.5399V8.9999H2V7.5399C2 5.2499 3.86002 3.3999 6.15002 3.3999H17.85C20.14 3.3999 22 5.2499 22 7.5399Z" fill="currentColor"/>
//         <path opacity="0.4" d="M2 9V16.46C2 18.75 3.85001 20.6 6.14001 20.6H12.4C12.98 20.6 13.48 20.11 13.43 19.53C13.29 18 13.78 16.34 15.14 15.02C15.7 14.47 16.39 14.05 17.14 13.81C18.39 13.41 19.6 13.46 20.67 13.82C21.32 14.04 22 13.57 22 12.88V9H2ZM8 17.25H6C5.59 17.25 5.25 16.91 5.25 16.5C5.25 16.09 5.59 15.75 6 15.75H8C8.41 15.75 8.75 16.09 8.75 16.5C8.75 16.91 8.41 17.25 8 17.25Z" fill="currentColor"/>
//         <path d="M8.75 16.5C8.75 16.91 8.41 17.25 8 17.25H6C5.59 17.25 5.25 16.91 5.25 16.5C5.25 16.09 5.59 15.75 6 15.75H8C8.41 15.75 8.75 16.09 8.75 16.5Z" fill="currentColor"/>
//       </svg>,
//     color: "text-accent-yellow-500 bg-accent-yellow-pastel",
//   },
//   {
//     title: "Dedicated Business Account",
//     description: "You get a dedicated business account number - this helps you separate your business finance.",
//     icon:
//       // prettier-ignore
//       <svg width="55%" viewBox="0 0 24 24" fill="none">
//         <path opacity="0.8" d="M12.8992 2.52009L12.8692 2.59009L9.96922 9.32009H7.11922C6.43922 9.32009 5.79922 9.45009 5.19922 9.71009L6.94922 5.53009L6.98922 5.44009L7.04922 5.28009C7.07922 5.21009 7.09922 5.15009 7.12922 5.10009C8.43922 2.07009 9.91922 1.38009 12.8992 2.52009Z" fill="currentColor"/>
//         <path d="M18.2907 9.52002C17.8407 9.39002 17.3707 9.32002 16.8807 9.32002H9.9707L12.8707 2.59002L12.9007 2.52002C13.0407 2.57002 13.1907 2.64002 13.3407 2.69002L15.5507 3.62002C16.7807 4.13002 17.6407 4.66002 18.1707 5.30002C18.2607 5.42002 18.3407 5.53002 18.4207 5.66002C18.5107 5.80002 18.5807 5.94002 18.6207 6.09002C18.6607 6.18002 18.6907 6.26002 18.7107 6.35002C18.9707 7.20002 18.8107 8.23002 18.2907 9.52002Z" fill="currentColor"/>
//         <path opacity="0.4" d="M21.7602 14.1998V16.1498C21.7602 16.3498 21.7502 16.5498 21.7402 16.7398C21.5502 20.2398 19.6002 21.9998 15.9002 21.9998H8.10023C7.85023 21.9998 7.62023 21.9798 7.39023 21.9498C4.21023 21.7398 2.51023 20.0398 2.29023 16.8598C2.26023 16.6198 2.24023 16.3898 2.24023 16.1498V14.1998C2.24023 12.1898 3.46023 10.4598 5.20023 9.70982C5.80023 9.44982 6.44023 9.31982 7.12023 9.31982H16.8802C17.3702 9.31982 17.8402 9.38982 18.2902 9.51982C20.2902 10.1298 21.7602 11.9898 21.7602 14.1998Z" fill="currentColor"/>
//         <path opacity="0.6" d="M6.95023 5.52979L5.20023 9.70978C3.46023 10.4598 2.24023 12.1898 2.24023 14.1998V11.2698C2.24023 8.42979 4.26023 6.05979 6.95023 5.52979Z" fill="currentColor"/>
//         <path opacity="0.6" d="M21.7591 11.2698V14.1998C21.7591 11.9898 20.2891 10.1298 18.2891 9.51984C18.8091 8.22984 18.9691 7.19984 18.7091 6.34984C18.6891 6.25984 18.6591 6.17984 18.6191 6.08984C20.4891 7.05984 21.7591 9.02984 21.7591 11.2698Z" fill="currentColor"/>
//         <path d="M14.5 16.75H9.5C9.09 16.75 8.75 16.41 8.75 16C8.75 15.59 9.09 15.25 9.5 15.25H14.5C14.91 15.25 15.25 15.59 15.25 16C15.25 16.41 14.91 16.75 14.5 16.75Z" fill="currentColor"/>
//       </svg>,
//     color: "text-accent-orange-500 bg-accent-orange-pastel",
//   },
//   {
//     title: "Instant Withdrawals",
//     description: "Withdraw funds anytime you like and get them deposited into your bank accounts instantly.",
//     icon:
//       // prettier-ignore
//       <svg width="55%" viewBox="0 0 24 24" fill="none">
//         <path opacity="0.4" d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="currentColor"/>
//         <path d="M15.7106 15.9298C15.5806 15.9298 15.4506 15.8998 15.3306 15.8198L12.2306 13.9698C11.4606 13.5098 10.8906 12.4998 10.8906 11.6098V7.50977C10.8906 7.09977 11.2306 6.75977 11.6406 6.75977C12.0506 6.75977 12.3906 7.09977 12.3906 7.50977V11.6098C12.3906 11.9698 12.6906 12.4998 13.0006 12.6798L16.1006 14.5298C16.4606 14.7398 16.5706 15.1998 16.3606 15.5598C16.2106 15.7998 15.9606 15.9298 15.7106 15.9298Z" fill="currentColor"/>
//       </svg>,
//     color: "text-accent-red-500 bg-accent-red-pastel",
//   },
// ];

export default InvoicesPlaceholder;
