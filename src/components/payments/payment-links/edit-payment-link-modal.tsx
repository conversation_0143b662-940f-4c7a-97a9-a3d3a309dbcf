import React, { useEffect, useState } from "react";
import {
  InvoiceInterface,
  INVOICE_FEE_TYPES,
  PaymentLinkForm as PaymentLinkFormType,
} from "@/assets/interfaces/invoices";
import { AppBtn } from "@/components/ui/buttons";
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "@/components/ui/modal";
import { CURRENCIES } from "@/assets/interfaces";
import PaymentLinkForm from "./form";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  invoice: InvoiceInterface;
  updateInvoices: (invoice: InvoiceInterface) => void;
}

const EditPaymentLink: React.FC<Props> = ({ show, toggle, invoice, updateInvoices }) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const invoiceForm: PaymentLinkFormType = {
    id: invoice.id,
    narration: invoice.title,
    customer: invoice.receiver.id,
    amount: invoice.total_amount,
    currency: invoice?.currency ?? CURRENCIES.NGN,
  };

  useEffect(() => {
    if (!show) {
      setIsSubmitted(false);
      setIsSubmitting(false);
    }
  }, [show]);

  const submitForm = () => {
    const btn = document.getElementById("payment_link_btn");

    if (btn) {
      btn.click();
      setIsSubmitting(true);
    }
  };

  const handleSubmitted = (invoice: InvoiceInterface) => {
    setIsSubmitting(false);
    setIsSubmitted(true);
    updateInvoices(invoice);
  };

  return (
    <Modal {...{ show, toggle }} title="Edit Payment Link" size="midi">
      <ModalBody>
        <PaymentLinkForm initialValues={invoiceForm} updateInvoice={handleSubmitted} />
      </ModalBody>
      <ModalFooter>
        <AppBtn size="lg" isBlock disabled={isSubmitting} onClick={isSubmitted ? () => toggle(false) : submitForm}>
          {isSubmitting ? "Saving Updates..." : isSubmitted ? "Close" : "Save Updates"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export default EditPaymentLink;
