import { useFormik } from "formik";
import { useEffect, useState } from "react";
import * as Yup from "yup";
import { useFetcher, useRequest } from "../../../api/utils";
import { getFieldvalues, getProductsCurrency } from "../../../assets/js/utils/functions";
import { CURRENCIES, CustomerInterface } from "../../../assets/interfaces";
import { AppBtn } from "../../ui/buttons";
import { InputField, SelectDropdown, SelectWithModal } from "../../ui/form-elements";
import ErrorLabel from "../../ui/error-label";
import SuccessLabel from "../../ui/success-label";
import { CreatePaymentLinkParams, GetCustomersParams } from "@/api/interfaces";
import { CreatePaymentLink, GetCustomers, GetCustomersBasic, UpdatePaymentLink } from "@/api";
import { InvoiceInterface, PaymentLinkForm as PaymentLinkFormType } from "@/assets/interfaces/invoices";
import authContext from "@/contexts/auth-context";
import { CURRENCY_OPTIONS } from "@/assets/js/utils/constants";
import { CustomerOptionRender } from "../invoices/create/basic-info";
import AddCustomerModal from "@/components/orders/modals/add-customer";
import Portal from "@/components/portal";
import { useModals } from "@/components/hooks/useModals";
import { DropdownOptionInterface } from "@/components/ui/form-elements/select-dropdown";
import PaymentLinkSuccess from "./success";
import ContentWithCopy from "@/components/ui/content-with-copy";
import { useRouter } from "next/router";

interface Props {
  initialValues?: any;
  updateInvoice?: (item: InvoiceInterface) => void;
  setUpdating?: (state: boolean) => void;
}

type CouponType = {
  type: string;
  discount_cap: number;
  percentage: number;
  coupon_code: string;
  quantity: number;
  discount_amount: number;
  end_date: Date | string;
  active: boolean;
};

const PaymentLinkForm: React.FC<Props> = ({ initialValues, updateInvoice, setUpdating }) => {
  const router = useRouter();
  const { store } = authContext.useContainer();
  const [customers, setCustomers] = useState<DropdownOptionInterface[]>([]);
  const { modals, toggleModal } = useModals(["add_customer"]);

  const createRequest = useRequest<CreatePaymentLinkParams>(CreatePaymentLink);
  const updateRequest = useRequest<CreatePaymentLinkParams>(UpdatePaymentLink);

  const isEditForm = initialValues !== undefined;
  const currentReq = initialValues ? updateRequest : createRequest;

  const { response, error, isLoading, makeRequest } = useFetcher<GetCustomersParams>(GetCustomersBasic, {
    per_page: Number.MAX_SAFE_INTEGER,
  });
  // Get query parameters
  const { amount, narration } = router.query;

  useEffect(() => {
    if (response?.data?.data) {
      setCustomers(
        response.data.data.map((c) => ({
          text: c.name ?? "Unknown Customer",
          value: c.id,
          meta: { phone: c.phone, email: c?.email },
        }))
      );
    }
  }, [response]);

  const form = useFormik<PaymentLinkFormType>({
    initialValues: initialValues ?? {
      amount: amount ? Number(amount) : 0,
      narration: narration || "",
      currency: store?.currencies?.products,
      customer: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      if (isEditForm) values["id"] = initialValues.id;
      const [res, err] = await currentReq.makeRequest(values);

      if (isEditForm) {
        updateInvoice(res?.data);
      }
    },
  });

  const currencyOpts = CURRENCY_OPTIONS.filter((c) => store?.currencies?.storefront.includes(c.value));
  const addCustomer = (customer: CustomerInterface) => {
    form.setFieldValue("customer", customer.id);
    setCustomers([...customers, { text: customer.name, value: customer.id }]);
  };

  if (currentReq.response && !isEditForm) {
    return <PaymentLinkSuccess isEditForm={isEditForm} createdInvoice={currentReq.response.data} />;
  }

  return (
    <form onSubmit={form.handleSubmit} className="w-full mx-auto max-w-[450px]">
      {!isEditForm && (
        <>
          <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 m-auto flex items-center justify-center text-white">
            <div className="w-[30px] sm:w-8">
              {/* prettier-ignore */}
              <svg width="w-full" viewBox="0 0 24 24" fill="none">
                <path d="M7.24989 12C7.24989 11.59 7.58989 11.25 7.99989 11.25H10.9999V7.5C10.9999 6.95 10.5499 6.5 9.99989 6.5H7.76989C4.61989 6.5 1.87989 9.08 1.99989 12.22C2.05989 13.65 2.65989 14.94 3.60989 15.89C4.60989 16.88 5.97989 17.5 7.49989 17.5H9.99989C10.5499 17.5 10.9999 17.05 10.9999 16.5V12.75H7.99989C7.58989 12.75 7.24989 12.41 7.24989 12Z" fill="currentColor"/>
                <path d="M20.39 8.11C19.39 7.12 18.02 6.5 16.5 6.5H14C13.45 6.5 13 6.95 13 7.5V11.25H16C16.41 11.25 16.75 11.59 16.75 12C16.75 12.41 16.41 12.75 16 12.75H13V16.5C13 17.05 13.45 17.5 14 17.5H16.23C19.38 17.5 22.12 14.92 21.99 11.78C21.94 10.35 21.33 9.06 20.39 8.11Z" fill="currentColor"/>
                <path d="M13 11.25H11V12.75H13V11.25Z" fill="currentColor"/>
              </svg>
            </div>
          </figure>
          <h2 className="text-center font-light text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto mt-3.5 !leading-tight mb-7.5">
            Get Paid,
            <br />
            <b className="font-bold">With Payment Links</b>
          </h2>
        </>
      )}
      <ErrorLabel error={currentReq?.error?.message} />
      <SuccessLabel message={currentReq.response && isEditForm ? "Payment link updated successfully!" : ""} />

      <InputField label="What is this payment for?" {...getFieldvalues("narration", form)} />
      <SelectDropdown
        label="Invoice Currency"
        {...getFieldvalues("currency", form)}
        options={currencyOpts}
        disabled={currencyOpts.length < 2}
      />
      <SelectWithModal
        label="Who is this for"
        options={customers}
        {...getFieldvalues("customer", form)}
        action={{ onClick: () => toggleModal("add_customer"), label: "Create new customer" }}
        hasSearch
        OptionRender={CustomerOptionRender}
        isLoadingData={isLoading}
        searchLabel="Search Customers"
      />
      <InputField
        label={`Amount to be paid in (${form?.values?.currency})`}
        type="text"
        min={1}
        {...getFieldvalues("amount", form, "number")}
        inputMode="numeric"
      />

      <AppBtn
        disabled={isEditForm ? false : createRequest.isLoading}
        id="payment_link_btn"
        isBlock
        className={`${isEditForm && "hidden"} mt-5`}
        type="submit"
        size="lg"
      >
        Create Payment link
      </AppBtn>
      <Portal>
        <AddCustomerModal
          show={modals.add_customer.show}
          toggle={() => toggleModal("add_customer")}
          addCustomer={addCustomer}
        />
      </Portal>
    </form>
  );
};

const validationSchema = Yup.object().shape({
  customer: Yup.string().required("Please select a customer"),
  narration: Yup.string().required("Please provide a narration"),
  amount: Yup.number()
    .required("Amount is required")
    .min(1, "Amount must be greater than 0")
    .typeError("Amount must be a number"),
  currency: Yup.string()
    .required("Please select a currency")
    .oneOf(
      [CURRENCIES.EUR, CURRENCIES.USD, CURRENCIES.GBP, CURRENCIES.NGN, CURRENCIES.GHC, CURRENCIES.ZAR, CURRENCIES.KES],
      "Please select a valid currency"
    ),
});

export default PaymentLinkForm;
