import router from "next/router";
import { useEffect, useRef, useState } from "react";
import { DownloadInvoicePdf, GetInvoices } from "../../../api/invoices";
import { useFetcher } from "../../../api/utils";
import { InvoiceInterface, INVOICE_STATUSES, INVOICE_TYPES } from "../../../assets/interfaces/invoices";
import { toAppUrl } from "../../../assets/js/utils/functions";
import authContext from "../../../contexts/auth-context";
import ClearSearch from "../../clear-search";
import useCopyClipboard from "@/components/hooks/useCopyClipboard";
import useFluxState from "@/components/hooks/useFluxState";
import { useModals } from "@/components/hooks/useModals";
import usePagination from "@/components/hooks/usePagination";
import usePDFDownloads from "@/components/hooks/usePDFDownloads";
import useScreenSize from "@/components/hooks/useScreenSize";
import useSearchParams from "@/components/hooks/useSearchParams";
import Portal from "../../portal";
import AppSearchBar from "@/components/ui/app-search-bar";
import { AppBtn } from "@/components/ui/buttons";
import Pagination from "@/components/ui/pagination";
import Table, { TableBody, TableHead, TableHeadItem } from "@/components/ui/table";
import DeleteInvoiceItemModal from "../invoices/modals/delete";
import EditInvoiceModal from "../invoices/modals/edit";
import UpdateStatusModal from "../invoices/modals/update-status";
import InvoiceContentState from "../invoices/dashboard/invoice-content-state";
import InvoiceInfo from "../invoices/dashboard/invoice-info";
import { InvoiceItemMobile } from "../invoices/dashboard/invoice-item-mobile";
import InvoiceItems from "../invoices/dashboard/invoice-items";
import { GetInvoicesParams } from "@/api/interfaces";
import { DownloadReceiptPDF } from "@/api";
import useClickOutside from "@/components/hooks/useClickOutside";
import EditPaymentLink from "./edit-payment-link-modal";

interface Props {}

const PER_PAGE = 10;

const PaymentLinksTable: React.FC<Props> = ({}) => {
  const { storeId } = authContext.useContainer();
  const { width, isSmall } = useScreenSize();
  const { search } = useSearchParams(["search"]);
  const { currentPage, goNext, goPrevious, setPage } = usePagination();

  const searchBar = useRef(null);
  const [error, setError] = useState(undefined);
  const [searchQuery, setSearchQuery] = useState("");
  const [fullViewOnMobile, setFullViewOnMobile] = useState(false);
  const [currentInvoiceIndex, setCurrentInvoiceIndex] = useState<number>(0);
  const { modals, toggleModal } = useModals(["info", "delete", "update", "edit"]);
  const [copied, copy] = useCopyClipboard(toAppUrl(null, true, true), { successDuration: 500 });

  useClickOutside(searchBar, () => {
    setFullViewOnMobile(false);
  });

  const invoiceRequest = useFetcher<GetInvoicesParams>(GetInvoices, {
    filter: { search: search ?? "", type: INVOICE_TYPES.PAYMENT_LINK },
    page: currentPage,
    per_page: PER_PAGE,
  });
  const [invoiceItems, setInvoiceItems] = useFluxState<InvoiceInterface[]>(
    invoiceRequest?.response?.data?.invoices ?? []
  );

  const currentInvoice = invoiceItems?.[currentInvoiceIndex];
  const pageNotReady = invoiceRequest.isLoading || invoiceRequest.error || invoiceItems.length < 1;

  const { isLoadingPdf, download } = usePDFDownloads({
    request: DownloadReceiptPDF,
    data: { receiptId: currentInvoice?.receipt },
    filename: `receipt-${currentInvoice?.receipt}`,
  });

  const onAction = (action: string, index: number) => {
    if (!invoiceItems[index]) return;

    setCurrentInvoiceIndex(index);
    switch (action) {
      // case "click":
      //   toggleModal("info");
      //   break;
      case "copy-link":
        copy(toAppUrl(`pay/${invoiceItems[index].invoice_id}?byCustomer=true`, true, true));
        break;
      case "delete":
        toggleModal("delete");
        break;
      case "edit":
        toggleModal("edit");
        break;
      case "download":
        download();
        break;
      default:
      //do nothing
    }
  };

  const updateInvoiceList = (invoice: InvoiceInterface) => {
    const invoicesCopy = [...invoiceItems];
    invoicesCopy[currentInvoiceIndex] = invoice;
    setInvoiceItems(invoicesCopy);
  };

  const deleteCurrentInvoice = () => {
    const invoicesCopy = [...invoiceItems];

    invoicesCopy.splice(currentInvoiceIndex, 1);

    if (modals.info.show) {
      toggleModal("info");
    }

    setInvoiceItems(invoicesCopy);
  };

  return (
    <div className="w-full pb-10">
      <div className="flex justify-between items-center pb-5 mt-3.75">
        <h4
          className={`text-black text-[17px] sm:text-lg lg:text-xl font-bold font-display ${
            fullViewOnMobile && "md:block hidden"
          }`}
        >
          All Payment Links
        </h4>
        <div ref={searchBar} className={`flex items-stretch ${fullViewOnMobile ? " flex-1 md:flex-none" : ""}`}>
          <AppSearchBar
            {...{
              placeholder: `Search by customer`,
              searchQuery,
              setSearchQuery,
              fullViewOnMobile,
              setFullViewOnMobile,
            }}
          />
          <div className={`ml-3 md:ml-3.5 ${fullViewOnMobile ? "hidden sm:block" : "block"}`}>
            {!isSmall && (
              <AppBtn size="md" onClick={() => router.push("/payments/payment-links/create")}>
                Create New Link
              </AppBtn>
            )}
            {isSmall && (
              <AppBtn
                size="md"
                onClick={() => router.push("/payments/payment-links/create")}
                className="!rounded-full !p-0 !h-9 !w-9"
              >
                {/* prettier-ignore */}
                <svg width={17} height={17} viewBox="0 0 17 17" fill="none" >
                <path d="M4.25 8.5h8.5M8.5 12.75v-8.5" stroke="#fff" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              </AppBtn>
            )}
          </div>
        </div>
      </div>
      <ClearSearch search={search} />
      {pageNotReady && (
        <InvoiceContentState
          request={invoiceRequest}
          isEmpty={invoiceItems.length < 1}
          type="Payment Link"
          path="/payments/payment-links/create"
        />
      )}

      {!pageNotReady && (
        <>
          {!isSmall && (
            <Table className="hidden sm:table">
              <TableHead>
                {/* <TableHeadItem>INVOICE ID</TableHeadItem> */}
                <TableHeadItem>Title</TableHeadItem>
                <TableHeadItem>Customer</TableHeadItem>
                <TableHeadItem>AMOUNT</TableHeadItem>
                <TableHeadItem>STATUS</TableHeadItem>
                <TableHeadItem></TableHeadItem>
              </TableHead>
              <TableBody>
                {invoiceItems.map((item, index) => {
                  return (
                    <InvoiceItems
                      item={item}
                      isLoadingPdf={currentInvoiceIndex === index && isLoadingPdf}
                      key={index}
                      onAction={(action: string) => onAction(action, index)}
                    />
                  );
                })}
              </TableBody>
            </Table>
          )}
          {isSmall && (
            <ul className="block md:hidden">
              {invoiceItems.map((item, index) => {
                return (
                  <InvoiceItemMobile
                    key={index}
                    index={index}
                    item={item}
                    onAction={(action: string) => onAction(action, index)}
                  />
                );
              })}
            </ul>
          )}
          <Pagination
            {...{
              goNext,
              goPrevious,
              per_page: PER_PAGE,
              label: "payment links",
              data: invoiceRequest?.response,
              currentPage,
              length: invoiceItems?.length,
              setPage,
            }}
          />
        </>
      )}
      <Portal>
        {currentInvoice && (
          <DeleteInvoiceItemModal
            deleteInvoice={deleteCurrentInvoice}
            invoice={currentInvoice}
            toggle={() => toggleModal("delete")}
            show={modals.delete.show}
          />
        )}
        {currentInvoice && (
          <EditPaymentLink
            invoice={currentInvoice}
            toggle={() => toggleModal("edit")}
            show={modals.edit.show}
            updateInvoices={updateInvoiceList}
          />
        )}
      </Portal>
    </div>
  );
};

export default PaymentLinksTable;
