import { InvoiceInterface } from "@/assets/interfaces/invoices";
import { toAppUrl } from "@/assets/js/utils/functions";
import useCopyClipboard from "@/components/hooks/useCopyClipboard";
import { useModals } from "@/components/hooks/useModals";
import SuccessAnimation from "@/components/ui/success-animation";
import classNames from "classnames";
import React from "react";
import { icons } from "../invoices/create/success";
import { AppBtn } from "@/components/ui/buttons";
import Portal from "@/components/portal";
import SendModal from "../invoices/modals/send";

interface PaymentLinkSuccessProps {
  isEditForm?: boolean;
  createdInvoice: InvoiceInterface;
}

const PaymentLinkSuccess: React.FC<PaymentLinkSuccessProps> = ({ isEditForm, createdInvoice }) => {
  const paymentLink = toAppUrl(`pay/${createdInvoice.invoice_id}?byCustomer=true`);
  const [copied, copy] = useCopyClipboard(paymentLink, { successDuration: 500 });
  const { modals, toggleModal } = useModals(["send"]);

  return (
    <div className="h-full overflow-y-auto py-12.5 sm:py-20 px-6.25 lg:px-7.5 flex items-center justify-center">
      <div className={classNames("flex flex-col items-center w-full", { "-mt-20": !isEditForm, "mt-5": isEditForm })}>
        <figure className="mb-5 sm:mb-7.5">
          <SuccessAnimation />
        </figure>
        {!isEditForm && (
          <h3 className="text-black font-medium text-2xl sm:text-3lg lg:text-3xl text-center">
            Your <b className="font-bold">Payment Link</b> <br />
            has been created.
          </h3>
        )}

        {isEditForm && (
          <h3 className="text-black font-medium text-2xl sm:text-3lg lg:text-3xl text-center">
            Your <b className="font-bold">Payment Link</b> <br />
            has been updated.
          </h3>
        )}

        {!isEditForm && (
          <div className="w-full max-w-[350px] flex flex-col items-center">
            <ul className="w-full border border-grey-border border-opacity-50 rounded-20 py-1.25 px-3.75 sm:px-5 flex flex-col divide-y divide-grey-border divide-opacity-50 mt-5 sm:mt-7.5">
              <li className="w-full">
                <button
                  className="flex items-center py-3 hover:bg-grey-fields-100 transition ease-out duration-150 w-full hover:bg-opacity-50"
                  onClick={() => copy()}
                >
                  <figure className="h-7.5 w-7.5 rounded-full bg-accent-yellow-500 bg-opacity-10 flex items-center justify-center text-accent-yellow-500">
                    {icons.copy}
                  </figure>
                  <span className="inline-block text-sm text-dark font-medium ml-2.5">Copy payment link</span>
                </button>
              </li>
              <li className="w-full">
                <button
                  onClick={() => {
                    toggleModal("send");
                  }}
                  className="flex items-center py-3 hover:bg-grey-fields-100 transition ease-out duration-150 w-full hover:bg-opacity-50"
                >
                  <figure className="h-7.5 w-7.5 rounded-full bg-accent-red-500 bg-opacity-10 flex items-center justify-center text-accent-red-500">
                    {icons.send}
                  </figure>
                  <span className="inline-block text-sm text-dark font-medium ml-2.5">Send Payment Link</span>
                </button>
              </li>
            </ul>

            <AppBtn
              href="/payments/payment-links"
              className="mt-5 sm:mt-7.5 !px-8 !inline-flex"
              isBlock={true}
              size="lg"
            >
              See all payment links
            </AppBtn>
          </div>
        )}
      </div>
      <Portal>
        <SendModal
          invoice={createdInvoice}
          toggle={() => toggleModal("send")}
          show={modals.send.show}
          title="Payment Link"
          paymentLink={paymentLink}
        ></SendModal>
      </Portal>
    </div>
  );
};

export default PaymentLinkSuccess;
