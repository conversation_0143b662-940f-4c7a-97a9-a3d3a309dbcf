import React from "react";
import { AppBtn } from "../../ui/buttons";
import Modal, { <PERSON>dal<PERSON><PERSON>, ModalFooter, ModalProps } from "../../ui/modal";

interface Props extends ModalProps {
  complete: () => void;
}

const CopyKycInfoConfirmationModal: React.FC<Props> = ({ show, toggle, complete }) => {
  return (
    <Modal {...{ show, toggle }} title="Confirm Update" size="sm" className="z-[1000]">
      <form className="flex flex-col flex-auto overflow-hidden">
        <ModalBody>
          <div className="text-center">
            <p className="text-sm text-grey-subtext mt-1 max-w-[360px] mx-auto">
              Are you sure you want to proceed with this store info? This action is not reversible
            </p>
          </div>
        </ModalBody>
        <ModalFooter>
          <AppBtn isBlock onClick={complete}>
            Continue
          </AppBtn>
        </ModalFooter>
      </form>
    </Modal>
  );
};

export default CopyKycInfoConfirmationModal;
