import React, { useState } from "react";
import { CopyKycInfo } from "../../../api";
import { useRequest } from "../../../api/utils";
import { KYCInfo, KYC_STATUSES } from "../../../assets/interfaces";
import { useModals } from "../../hooks/useModals";
import Badge, { BadgeColor } from "../../ui/badge";
import { toast } from "../../ui/toast";
import CopyKycInfoConfirmationModal from "./copy-kyc-confirmation-modal";

interface OptionsProps {
  setShowOptions: (state: boolean) => void;
  relatedKycData: KYCInfo[];
  kycInfo: KYCInfo;
}

const KycOptions: React.FC<OptionsProps> = ({ setShowOptions, relatedKycData, kycInfo }) => {
  const { modals, toggleModal } = useModals(["copy_confirmation"]);
  const [selectedKyc, setSelectedKyc] = useState(null);
  const { response, makeRequest, error, isLoading } = useRequest(CopyKycInfo);

  const handleContinueClick = (kycId: string) => {
    if (kycInfo?.id === kycId) {
      setShowOptions(false);
      return;
    }

    setSelectedKyc(kycId);
    toggleModal("copy_confirmation");
  };

  const copyKyc = async () => {
    async function copyInfoReq() {
      const [res, err] = await makeRequest({
        kyc_id: selectedKyc,
      }); //gets items without quantities if any

      if (err) {
        return Promise.reject(err);
      }

      setTimeout(() => {
        window.location.reload();
      }, 600);

      toggleModal("copy_confirmation");
      return Promise.resolve(res);
    }

    toast.promise(() => copyInfoReq(), toastOpts);
  };

  return (
    <>
      <div className="h-full relative w-full overflow-hidden px-5 sm:px-6.25 lg:px-7.5">
        <div className="w-full h-full max-w-[550px] overflow-y-auto mx-auto py-12.5 sm:pt-16 lg:pt-20 text-center">
          <div className="flex flex-col items-center">
            <figure className="h-[70px] w-[70px] sm:h-20 sm:w-20 bg-accent-orange-500 flex items-center justify-center rounded-full">
              {/* prettier-ignore */}
              <svg className="w-7.5 sm:w-9 text-white" viewBox="0 0 14 14" fill="none">
                <path d="M10.5 1.16675H3.5C2.53167 1.16675 1.75 1.94258 1.75 2.89925V9.26341C1.75 10.2201 2.53167 10.9959 3.5 10.9959H3.94333C4.41 10.9959 4.85333 11.1767 5.18 11.5034L6.1775 12.4892C6.6325 12.9384 7.37333 12.9384 7.82833 12.4892L8.82583 11.5034C9.1525 11.1767 9.60167 10.9959 10.0625 10.9959H10.5C11.4683 10.9959 12.25 10.2201 12.25 9.26341V2.89925C12.25 1.94258 11.4683 1.16675 10.5 1.16675ZM7 3.35425C7.7525 3.35425 8.35917 3.96091 8.35917 4.71341C8.35917 5.46591 7.7525 6.07258 7 6.07258C6.2475 6.07258 5.64083 5.46008 5.64083 4.71341C5.64083 3.96091 6.2475 3.35425 7 3.35425ZM8.56333 8.78508H5.43667C4.96417 8.78508 4.69 8.26008 4.9525 7.86925C5.34917 7.28008 6.11917 6.88341 7 6.88341C7.88083 6.88341 8.65083 7.28008 9.0475 7.86925C9.31 8.26008 9.03 8.78508 8.56333 8.78508Z" fill="white"/>
              </svg>
            </figure>
            <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-light mt-2.5">
              {" "}
              How will you, <br /> <b className="font-bold">Like to continue?</b>
            </h2>
            <span className="text-1sm text-base text-dark inline-block mt-1.5">
              KYC information from other stores you own
            </span>
          </div>
          <div className="mt-9 sm:mt-10 max-w-[400px] mx-auto">
            <ul className="space-y-5">
              {relatedKycData?.map((data) => (
                <>
                  <div className="border border-grey-border border-opacity-50 rounded-15 p-3.5 sm:p-3.75">
                    <div className="border-b border-grey-border border-opacity-50 pb-3 flex items-start justify-between">
                      <div className="w-[75%]">
                        <h4 className="text-black text-lg font-display font-bold text-left">
                          {data.first_name} {data.last_name}
                        </h4>
                        <span className="flex items-center text-dark mt-2 text-sm">
                          <b className="font-medium inline-block mr-1">BVN:</b>
                          {data?.bvn ? (
                            <span className="text-black-secondary">**** {data.bvn.slice(-4)}</span>
                          ) : (
                            <span className="text-placeholder">No BVN</span>
                          )}
                        </span>
                      </div>
                      <Badge color={statusBadgeColorMap[data.status]} text={data.status} />
                    </div>
                    <div className="pt-3 flex items-center justify-end">
                      <button
                        className="text-primary-500 font-semibold inline-flex items-center text-sm py-0.5"
                        onClick={() => handleContinueClick(data?.id)}
                      >
                        Continue with this info
                        {/* prettier-ignore */}
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="ml-0.5">
                          <path d="M3.33331 8H7.99998H12.6666" stroke="currentColor" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M8 3.33334L12.6667 8L8 12.6667" stroke="currentColor" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </>
              ))}

              {!kycInfo && (
                <div className="border-t border-grey-border border-opacity-30 pt-5">
                  <button
                    className="border border-grey-border border-opacity-50 rounded-15 p-3.5 sm:p-3.75 flex items-center justify-center w-full"
                    onClick={() => setShowOptions(false)}
                  >
                    <div className="text-primary-500 font-semibold inline-flex items-center text-sm py-0.5">
                      Use new BVN info
                      {/* prettier-ignore */}
                      <svg width="12" viewBox="0 0 12 12" fill="none" className="ml-1">
                      <path d="M6 1V11" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M1 6H11" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    </div>
                  </button>
                </div>
              )}
            </ul>
          </div>
        </div>
      </div>
      <CopyKycInfoConfirmationModal
        title="Copy Info"
        show={modals.copy_confirmation.show}
        toggle={() => toggleModal("copy_confirmation")}
        complete={() => copyKyc()}
      />
    </>
  );
};

export const statusBadgeColorMap: { [key: string]: BadgeColor } = {
  [KYC_STATUSES.APPROVED]: "green",
  [KYC_STATUSES.PENDING]: "yellow",
  [KYC_STATUSES.IN_PROGRESS]: "orange",
  [KYC_STATUSES.DENIED]: "red",
};

const toastOpts = {
  loading: {
    title: "Copying KYC information",
    message: "Please wait...",
  },
  success: {
    title: "Successful",
    message: "Copied Info! Reloading page...",
  },
  error: {
    title: "Something went wrong",
    message: "Something went wrong! Please reload page & retry",
  },
};

export default KycOptions;
