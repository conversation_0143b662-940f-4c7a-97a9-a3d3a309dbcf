import React from "react";
import { AppBtn } from "../../../ui/buttons";

interface IProps {
  isActive: boolean;
  next: () => void;
}

const IDWidgetInfoScreen: React.FC<IProps> = ({ isActive, next }) => {
  return (
    <div className="h-full py-7 flex flex-col items-center justify-between px-7.5">
      {isActive && (
        <>
          <div className="mt-12 flex items-center flex-col">
            <figure className="h-[85px] w-[85px] sm:h-25 sm:w-25 bg-accent-red-500 flex items-center justify-center rounded-full">
              {/* TODO: ANIMATE THESE PATHS LATER */}
              {/* prettier-ignore */}
              <svg className="w-9 sm:w-11" viewBox="0 0 44 44" fill="none">
                <path d="M19.197 26.3815C21.283 26.3815 22.9806 24.6842 22.9806 22.5978L22.9803 15.3115C22.9803 14.77 22.5414 14.3307 21.9995 14.3307C21.458 14.3307 21.0188 14.7696 21.0188 15.3115V22.5978C21.0188 23.6025 20.2017 24.4195 19.1971 24.4195C18.6555 24.4195 18.2163 24.8584 18.2163 25.4003C18.2163 25.9425 18.6552 26.3814 19.1971 26.3814L19.197 26.3815Z" fill="white"/>
                <path d="M11.0005 16.0155V18.8178C11.0005 19.4752 11.5339 20.0086 12.1914 20.0086C12.8489 20.0086 13.3823 19.4753 13.3823 18.8178V16.0155C13.3823 15.358 12.8489 14.8246 12.1914 14.8246C11.5339 14.8246 11.0005 15.358 11.0005 16.0155V16.0155Z" fill="white"/>
                <path d="M31.809 20.0094C32.4665 20.0094 32.9999 19.4761 32.9999 18.8185V16.0163C32.9999 15.3588 32.4666 14.8254 31.809 14.8254C31.1515 14.8254 30.6182 15.3588 30.6182 16.0163V18.8185C30.6182 19.476 31.1515 20.0094 31.809 20.0094V20.0094Z" fill="white"/>
                <path d="M30.4025 31.2401C30.0564 30.8238 29.4381 30.7665 29.0214 31.1129C24.7958 34.6251 18.6621 34.6251 14.4358 31.1129C14.0194 30.7668 13.4009 30.8241 13.0547 31.2401C12.7086 31.6565 12.7652 32.2751 13.1823 32.6213C15.6584 34.6793 18.6935 35.7082 21.7289 35.7082C24.7641 35.7082 27.7995 34.679 30.2756 32.6213C30.6917 32.2751 30.749 31.6568 30.4021 31.2401H30.4025Z" fill="white"/>
                <path d="M0.981239 15.974C1.52278 15.974 1.96199 15.5351 1.96199 14.9933V10.0961C1.96199 5.61101 5.61095 1.96205 10.0961 1.96205H14.9939C15.5354 1.96205 15.9747 1.52316 15.9747 0.981299C15.9747 0.439757 15.5358 0.000549316 14.9939 0.000549316H10.0961C4.52919 0.000549316 0.000488281 4.52969 0.000488281 10.0961V14.9933C0.000829283 15.5348 0.440072 15.9737 0.981588 15.9737L0.981239 15.974Z" fill="white"/>
                <path d="M43.0183 28.0254C42.4768 28.0254 42.0376 28.4643 42.0376 29.0061V33.904C42.0376 38.3891 38.3886 42.0381 33.9035 42.0381H29.0057C28.4641 42.0381 28.0249 42.477 28.0249 43.0188C28.0249 43.5604 28.4638 43.9996 29.0057 43.9996H33.9035C39.4704 43.9996 43.9991 39.4704 43.9991 33.904V29.0065C43.9987 28.4646 43.5598 28.0254 43.0183 28.0254Z" fill="white"/>
                <path d="M14.9939 42.0383H10.0961C5.61095 42.0383 1.96199 38.3893 1.96199 33.9042V29.0064C1.96199 28.4648 1.5231 28.0256 0.981238 28.0256C0.439695 28.0256 0.000488281 28.4645 0.000488281 29.0064V33.9042C0.000488281 39.4711 4.52963 43.9998 10.0961 43.9998H14.9939C15.5354 43.9998 15.9747 43.5609 15.9747 43.019C15.9743 42.4768 15.5354 42.038 14.9936 42.038L14.9939 42.0383Z" fill="white"/>
                <path d="M33.9054 0.000113473H29.0076C28.4661 0.000113473 28.0269 0.439007 28.0269 0.980864C28.0269 1.52241 28.4657 1.96161 29.0076 1.96161H33.9054C38.3906 1.96161 42.0395 5.61057 42.0395 10.0957V14.9928C42.0395 15.5344 42.4784 15.9736 43.0203 15.9736C43.5618 15.9736 44.001 15.5347 44.001 14.9928V10.0964C44.001 4.52916 39.4719 -6.10352e-05 33.9054 -6.10352e-05V0.000113473Z" fill="white"/>
              </svg>
            </figure>
            <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold mt-2.5">Upload ID</h2>

            <ul className="flex flex-col space-y-3.5 mt-10 py-6 px-3 bg-grey-fields-200 rounded-10">
              {instructions.map((i, index) => (
                <li className="flex items-stretch" key={index}>
                  {/* prettier-ignore */}
                  <div className="h-full w-8 rounded-lg bg-grey-fields-100 mr-2.5 flex items-start pt-1 justify-center flex-shrink-0">
                    <svg className="w-4.5" viewBox="0 0 18 18" fill="none">
                      <rect width="18" height="18" rx="9" fill="#39B588"/>
                      <path d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z" fill="white"/>
                    </svg>
                  </div>
                  <span className="text-black text-sm inline-block max-w-[320px] sm:max-w-[280px]">{i}</span>
                </li>
              ))}
            </ul>
          </div>
          <AppBtn isBlock onClick={next} size="lg">
            Continue
          </AppBtn>
        </>
      )}
    </div>
  );
};

const instructions = [
  "You should be in a well lit environment to get started",
  "Avoid wearing anything that hides your face, e.g. glasses or caps",
  "All parts of your identity card should show clearly",
];

export default IDWidgetInfoScreen;
