import classNames from "classnames";
import { FormikProps } from "formik";
import React, { useEffect, useRef, useState } from "react";
import { ManuallyVerifyID } from "../../../../api";
import { VerifyIDParams } from "../../../../api/interfaces/store.kyc.interface";
import { RequestInterface, useRequest } from "../../../../api/utils";
import { KYCInfo } from "../../../../assets/interfaces";
import scroll from "../../../../assets/js/utils/scroll";
import { emit } from "../../../hooks/useListener";
import { useModals } from "../../../hooks/useModals";
import useSteps from "../../../hooks/useSteps";
import ConfirmationModal from "./confirmation-modal";
import IDWidgetInfoScreen from "./info-screen";
import IDWidgetProcessingScreen from "./processing";
import IDWidgetSelfieScreen from "./selfie-screen";
import IDWidgetStatusScreen from "./status";
import IDWidgetUploadScreen from "./upload-id-screen";

interface IDWidgetProps {
  show: boolean;
  toggle: () => void;
  form: FormikProps<VerifyIDParams>;
  verifyIDReq: RequestInterface<VerifyIDParams>;
  handleSuccess: (kyc: KYCInfo) => void;
}

const IDUploadWidget: React.FC<IDWidgetProps> = ({ show, toggle, form, verifyIDReq, handleSuccess }) => {
  const [saveManually, setSaveManually] = useState(false);
  const manualIdVerificationReq = useRequest(ManuallyVerifyID);
  const { isLoading, makeRequest, response, error } = saveManually ? manualIdVerificationReq : verifyIDReq;
  const [showWidget, setShowWidget] = useState(show);
  const [isClosing, setIsClosing] = useState(false);
  const { modals, toggleModal } = useModals(["confirmation"]);
  const screensWrapperRef = useRef<HTMLDivElement>(null);
  const { step, steps, isActive, stepIndex, changeStep, next, previous } = useSteps(
    ["INFO", "SELFIE", "ID_UPLOAD", "PROCESSING", "STATUS"],
    0
  );

  const showBackBtn = stepIndex > 0 && step !== "PROCESSING" && step !== "STATUS";

  useEffect(() => {
    if (!show) {
      setIsClosing(true);

      setTimeout(() => {
        setShowWidget(show);
        setIsClosing(false);
      }, 700);
    } else {
      setShowWidget(show);
    }
  }, [show]);

  useEffect(() => {
    //Handles scrolling the page sections
    if (screensWrapperRef.current) {
      const screensWrapper = screensWrapperRef.current;
      const wrapperScrollLeft = screensWrapper.scrollLeft;
      const screenWidth = screensWrapper.children[0].clientWidth;

      scroll({
        e: screensWrapper,
        time: 200,
        amount: screenWidth * stepIndex - wrapperScrollLeft,
        start: wrapperScrollLeft,
        pos: "left",
      });
    }

    //HANDLE REPOSISTIONING ON SCREEN RESIZE
  }, [step, stepIndex]);

  useEffect(() => {
    if ((response || error) && !isLoading) {
      changeStep("STATUS");
    }

    if (response && response?.data) {
      handleSuccess(response.data);
    }
  }, [response, error, isLoading]);

  const verifyId = () => {
    verifyIDReq.clearResponse();
    form.handleSubmit();
    changeStep("PROCESSING");
  };

  const retry = () => {
    verifyIDReq.makeRequest(form.values);
    changeStep("PROCESSING");
  };

  const retryManually = async () => {
    setSaveManually(true);
    changeStep("PROCESSING");
    const [res, err] = await manualIdVerificationReq.makeRequest(form.values);

    if (err) {
      setSaveManually(false);
    }

    if (res) {
      verifyIDReq.clearResponse();
    }
  };

  const restart = () => {
    form.setFieldValue("photo_id", "");
    form.setFieldValue("selfie", "");

    emit("clear-id-data");
    toggle();

    changeStep("INFO");
  };

  const saveIDInfo = (photo_id: string, filename: string) => {
    form.setFieldValue("photo_id", photo_id);
    form.setFieldValue("filename", filename);
  };

  const wrapperClasses = classNames(
    "fixed inset-0 bg-black bg-opacity-50 z-[9999] blur-bg flex  items-center justify-center",
    {
      hidden: !showWidget,
      "modal-wrapper-anim block": showWidget,
      "modal-wrapper-anim-rev": isClosing,
    }
  );

  const bodyClasses = classNames(
    "bg-white w-full h-full sm:max-w-[425px] sm:h-[620px] sm:rounded-15 relative transform overflow-hidden",
    {
      "modal-body-anim": showWidget,
      "modal-body-anim-rev": !showWidget || isClosing,
    }
  );

  return (
    <div className={wrapperClasses}>
      <div className={bodyClasses}>
        {/* Widget Controls */}
        <div className="flex items-center justify-between absolute right-3.75 top-3.75 left-3.75 z-50">
          {showBackBtn && (
            <button
              className="p-1.25 rounded-5 transition-all duration-150 bg-transparent hover:bg-grey-fields-100"
              onClick={previous}
            >
              {/* prettier-ignore */}
              <svg className="w-6.25" viewBox="0 0 24 24" fill="none">
                <path d="M19 12H5" stroke="#8E8E8E" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 19L5 12L12 5" stroke="#8E8E8E" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          )}
          <button
            className="p-1.25 rounded-5 transition-all duration-150 bg-transparent hover:bg-grey-fields-100 ml-auto"
            onClick={() => toggleModal("confirmation")}
          >
            {/* prettier-ignore */}
            <svg className="w-6.25" viewBox="0 0 25 25" fill="none">
            <rect x="0.5" y="0.5" width="24" height="24" rx="12" stroke="#8E8E8E"/>
            <path d="M16.25 8.75L8.75 16.25" stroke="#8E8E8E" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.75 8.75L16.25 16.25" stroke="#8E8E8E" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          </button>
        </div>
        <div className="h-full grid grid-cols-[repeat(5,100%)] overflow-hidden" id="wrapper-xx" ref={screensWrapperRef}>
          <IDWidgetInfoScreen isActive={isActive("INFO")} next={next} />
          <IDWidgetSelfieScreen
            isActive={isActive("SELFIE")}
            next={next}
            saveSelfie={(selfie: string) => form.setFieldValue("selfie", selfie)}
          />
          <IDWidgetUploadScreen isActive={isActive("ID_UPLOAD")} next={verifyId} savePhotoId={saveIDInfo} form={form} />
          <IDWidgetProcessingScreen isActive={isActive("PROCESSING")} next={next} />
          <IDWidgetStatusScreen
            isActive={isActive("STATUS")}
            restart={restart}
            response={response}
            error={error}
            verifyId={retry}
            closeModal={toggle}
            retryManually={retryManually}
          />
        </div>
        <ConfirmationModal
          onConfirm={() => {
            toggleModal("confirmation");
            restart();
          }}
          show={modals.confirmation.show}
          toggle={() => toggleModal("confirmation")}
        />
      </div>
    </div>
  );
};

export default IDUploadWidget;
