import React, { useEffect, useRef, useState } from "react";
import { handleImageSelectionFromFile } from "../../../../assets/js/utils/functions";
import { convertHeicToJpeg, generateKey, handleImageResizeOnCanvas } from "../../../../assets/js/utils/image-selection";
import { AppBtn } from "../../../ui/buttons";
import { toast } from "../../../ui/toast";
import { Image as ImageType } from "../../../../assets/interfaces";
import { FormikProps } from "formik";
import { useListener } from "../../../hooks/useListener";

interface IProps {
  isActive: boolean;
  next: () => void;
  savePhotoId: (photo_id: string, filename: string) => void;
  form: FormikProps<any>;
}

const IDWidgetUploadScreen: React.FC<IProps> = ({ isActive, next, savePhotoId, form }) => {
  const [image, setImage] = useState<ImageType>(null);
  const pickerRef = useRef<HTMLInputElement>(null);
  const dragAreaRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (image?.src) {
      savePhotoId(image?.src, image?.name);
    }
  }, [image]);

  useListener("clear-id-data", () => {
    setImage(null);
  });

  const openPicker = () => {
    setImage(null);
    pickerRef.current.click();
  };

  const removeImage = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setImage(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();

    const dragArea = dragAreaRef.current;
    dragArea.classList.add("border-primary-500");
  };

  const handleDragExit = (e: React.DragEvent) => {
    e.preventDefault();

    const dragArea = dragAreaRef.current;
    dragArea.classList.remove("border-primary-500");
  };

  const handleDrop = (e: React.DragEvent) => {
    const processFile = (file: File) => {
      if (!(file && file["type"].split("/")[0] === "image")) {
        toast.error({
          title: "Invalid Image",
          message: "File uploaded is not an image",
        });

        return;
      } else {
        processImage(file);
      }
    };

    e.preventDefault(); // Prevent default behavior (Prevent file from being opened)
    dragAreaRef.current.classList.remove("border-primary-500");

    if (e.dataTransfer.items) {
      // Use DataTransferItemList interface to access the file(s)
      // If dropped items aren't files, reject them
      if (e.dataTransfer.items[0].kind === "file") {
        const file = e.dataTransfer.items[0].getAsFile();

        processFile(file);
      }
      // }
    } else {
      // Use DataTransfer interface to access the file(s)
      const file = e.dataTransfer.files[0];
      processFile(file);
    }
  };

  const processImage = async (file: File) => {
    const fileIsHeic = file.name.toLowerCase().includes(".heic");

    if (fileIsHeic) {
      file = await convertHeicToJpeg(file);
    }

    const blobURL = URL.createObjectURL(file);
    const img = new Image();
    img.src = blobURL;

    img.addEventListener("error", () => {
      URL.revokeObjectURL(blobURL);
      // Handle the failure properly - come back to this important
      toast.error({
        title: "Something went wrong",
        message: "Cannot load image, please try again",
      });
    });

    img.addEventListener("load", async () => {
      const resizedImage = await handleImageResizeOnCanvas(img, 1000, blobURL);
      setImage({
        src: resizedImage.dataUrl,
        name: file.name,
        lastModified: file.lastModified,
        file: resizedImage.blob,
        isUploading: false,
        uploadProgress: 0,
        url: "",
        key: generateKey(file),
      });
    });
  };

  return (
    <div className="h-full py-7 flex flex-col items-center justify-between px-7.5">
      {isActive && (
        <>
          <div className="mt-8 flex items-center flex-col w-full">
            <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold mt-2.5">Add your ID</h2>
            <button
              className="w-full max-w-[3800px] mx-auto border border-grey-border border-dashed mt-10 rounded-15 cursor-pointer relative block transition-all ease-out"
              style={{ paddingTop: "55%" }}
              onClick={openPicker}
              onDragOver={handleDragOver}
              onDragLeave={handleDragExit}
              onDrop={handleDrop}
              ref={dragAreaRef}
              type="button"
            >
              <div className="absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center">
                {/* prettier-ignore */}
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                  <path opacity="0.4" d="M36.6997 28.0333L31.4831 15.8333C30.5331 13.6 29.1164 12.3333 27.4997 12.25C25.8997 12.1666 24.3497 13.2833 23.1664 15.4166L19.9997 21.1C19.3331 22.3 18.3831 23.0166 17.3497 23.1C16.2997 23.2 15.2497 22.65 14.3997 21.5666L14.0331 21.1C12.8497 19.6166 11.3831 18.9 9.88307 19.05C8.38307 19.2 7.09974 20.2333 6.24974 21.9166L3.36641 27.6666C2.33307 29.75 2.43307 32.1666 3.64974 34.1333C4.86641 36.1 6.98307 37.2833 9.29974 37.2833H30.5664C32.7997 37.2833 34.8831 36.1666 36.1164 34.3C37.3831 32.4333 37.5831 30.0833 36.6997 28.0333Z" fill="#656565" />
                  <path d="M11.6167 13.9666C14.7279 13.9666 17.2501 11.4445 17.2501 8.33328C17.2501 5.22208 14.7279 2.69995 11.6167 2.69995C8.50553 2.69995 5.9834 5.22208 5.9834 8.33328C5.9834 11.4445 8.50553 13.9666 11.6167 13.9666Z" fill="#656565" />
                </svg>
                <span className="inline-block text-xs text-dark mt-4 max-w-[200px] text-center">
                  Drag an image of your ID into this box or click to upload
                </span>
              </div>
              {image && (
                <figure className="absolute top-2.5 left-2.5 right-2.5 bottom-2.5 rounded-md overflow-hidden bg-white flex items-center justify-center">
                  <img src={image.src} alt="Selected ID" className="max-w-full max-h-full rounded-md" />
                  <button
                    className="bg-accent-red-500 flex items-center justify-center h-6 w-6 rounded-full absolute top-2.5 left-2.5"
                    onClick={removeImage}
                  >
                    {/* prettier-ignore */}
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                      <path d="M9 3L3 9" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M3 3L9 9" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>

                  <span className="flex absolute bottom-4 rounded-10 bg-black bg-opacity-30 blur-bg text-white text-xxs font-medium py-2 px-4">
                    Click to select new image
                  </span>
                </figure>
              )}
            </button>
            <input
              type="file"
              name="id-file"
              accept="image/*,.heic"
              className="hidden"
              onChange={
                (e) => processImage(e.target.files[0])
                // handleImageSelectionFromFile({
                //   e,
                //   images: image,
                //   saveImages: (image: ImageType) => setImage(image),
                // })
              }
              ref={pickerRef}
            />
          </div>

          <AppBtn isBlock onClick={next} disabled={!image?.src} size="lg">
            Submit
          </AppBtn>
        </>
      )}
    </div>
  );
};

export default IDWidgetUploadScreen;
