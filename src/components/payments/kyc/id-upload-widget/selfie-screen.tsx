import React, { useEffect, useRef, useState } from "react";
import { AppBtn } from "../../../ui/buttons";
import InfoCircleIcon from "../../../../assets/icons/new/info-circle-yellow.svg";
import FaceIcon from "./face-icon";
import { toast } from "../../../ui/toast";

interface IProps {
  isActive: boolean;
  next: () => void;
  saveSelfie: (selfie: string) => void;
}

const IDWidgetSelfieScreen: React.FC<IProps> = ({ isActive, next, saveSelfie }) => {
  const videoPlayerRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [hasCaptured, setHasCaptured] = useState(false);
  const [stream, setStream] = useState<MediaStream>(null);

  useEffect(() => {
    if (isActive && videoPlayerRef) {
      setHasCaptured(false);
      setTimeout(() => {
        getCameraAndStream();
      }, 3200);
    }
  }, [isActive, videoPlayerRef]);

  useEffect(() => {
    if (stream && !isActive) {
      stream.getTracks().forEach(function (track) {
        track.stop();
      });
    }
  }, [stream, isActive]);

  const getCameraAndStream = async () => {
    try {
      let stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });

      setStream(stream);

      if (videoPlayerRef.current) {
        videoPlayerRef.current.srcObject = stream;
      }

      setHasCaptured(false);
    } catch {
      toast.error({ title: "No camera access", message: "Please enable camera access" });
    }
  };

  const captureImage = () => {
    const canvas = canvasRef.current;
    const video = videoPlayerRef.current;

    if (canvas && video) {
      const { videoWidth, videoHeight } = video;
      const { width, height } = canvas;

      const heightRatio = videoHeight / height;
      const widthRatio = videoWidth / width;

      const ratio = Math.min(heightRatio, widthRatio);

      const [realHeight, realWidth] = [height * ratio, width * ratio];

      const [x, y] = [(videoWidth - realWidth) / 2, (videoHeight - realHeight) / 2];

      canvas.getContext("2d").drawImage(video, x, y, realWidth, realHeight, 0, 0, canvas.width, canvas.height);
      let image_data_url = canvas.toDataURL("image/jpeg");

      saveSelfie(image_data_url);

      // data url of the image

      stream.getTracks().forEach(function (track) {
        track.stop();
      });
      setHasCaptured(true);
    }

    //we need to find a way to get the actual size of the canvas off the video
    //the video heights & widths are usually different than actually set via CSS
    //get min ratio of the video height & width to the canvas height & width [this is essentially the higest number of times you can fit the canvas in the video without any overflow on the height & width]
    //multiply the canvas size by this ratio, what you get is essentially a scaled size of the canvas on the video
    //attempt to center the canvas on the video by dividing the rememenats of the subtraction of the widths & heights by 2
  };

  return (
    <div className="h-full py-7 flex flex-col items-center justify-between px-7.5">
      {isActive && (
        <>
          <div className="mt-8 flex items-center flex-col">
            <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold mt-2.5">Take a Selfie</h2>

            <figure className="w-[220px] h-[250px] rounded-[20px] bg-grey-fields-100 mt-8  relative flex items-center justify-center overflow-hidden">
              <FaceIcon />
              <div className="absolute top-0 left-0 w-full h-full z-50">
                <video
                  id="camera-video"
                  className={`h-full w-full object-cover rounded-[20px] ${hasCaptured ? "hidden" : "block"}`}
                  autoPlay
                  ref={videoPlayerRef}
                  style={{ transform: "rotateY(180deg)" }}
                  playsInline
                ></video>
                <canvas
                  id="canvas"
                  ref={canvasRef}
                  height={250}
                  width={220}
                  className={`w-full h-full rounded-[20px] ${hasCaptured ? "block" : "hidden"}`}
                  style={{ transform: "rotateY(180deg)" }}
                ></canvas>
              </div>
            </figure>
            <div className="bg-grey-fields-200 flex items-center justify-center p-2 rounded-10 mt-5">
              <InfoCircleIcon />
              <span className="text-xs text-black inline-block ml-1">
                Please ensure you&apos;re in a well lit environment
              </span>
            </div>
          </div>
          <div className="flex space-y-3 flex-col w-full">
            {hasCaptured && (
              <AppBtn isBlock onClick={getCameraAndStream} color="neutral" size="lg">
                Retake
              </AppBtn>
            )}
            {!hasCaptured && (
              <AppBtn isBlock onClick={captureImage} disabled={!stream} size="lg">
                Capture
              </AppBtn>
            )}

            {hasCaptured && (
              <AppBtn isBlock onClick={next} size="lg">
                Continue
              </AppBtn>
            )}
          </div>
        </>
      )}
    </div>
  );
};

const instructions = [
  "You should be in a well lit environment to get started",
  "Avoid wearing anything that hides your face, e.g. glasses or caps",
  "All parts of your identity card should show clearly",
];

export default IDWidgetSelfieScreen;
