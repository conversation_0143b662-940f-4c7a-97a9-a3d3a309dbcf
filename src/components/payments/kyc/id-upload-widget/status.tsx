import React from "react";
import { CustomErrorType } from "../../../../api/utils";
import { KYCInfo } from "../../../../assets/interfaces";
import { AppBtn } from "../../../ui/buttons";
import SuccessAnimation from "../../../ui/success-animation";

interface IProps {
  isActive: boolean;
  restart: () => void;
  retryManually: () => void;
  response: any;
  error: CustomErrorType;
  verifyId: VoidFunction;
  closeModal: VoidFunction;
}

const IDWidgetStatusScreen: React.FC<IProps> = ({
  isActive,
  restart,
  response,
  error,
  verifyId,
  closeModal,
  retryManually,
}) => {
  const errorsToShow = (error as any)?.errors ?? [error?.message] ?? defaultErrors;
  const isManualVerification = (response?.data as KYCInfo)?.verification_method === "MANUAL";

  const getButtonLabel = () => {
    if (response) {
      return "Continue";
    }

    if (error?.statusCode > 499) {
      return "Retry";
    }

    return "Restart";
  };

  const handleButtonClick = () => {
    if (response) {
      closeModal();
      return;
    }

    if (error?.statusCode > 499) {
      verifyId();
      return;
    }

    restart();
  };

  // const initiateManualVerification = async () => {
  //   const [res, err] = await updateKycToManual.makeRequest({});

  //   if (res) {
  //     Router.push("/payments/kyc/manual");
  //     return;
  //   }

  //   toast.error({
  //     title: "Something went wrong",
  //     message: "Please try again",
  //   });
  // };

  return (
    <div className="h-full py-7 flex flex-col items-center justify-between px-7.5 relative">
      {response && isActive && (
        <div className="h-full w-full grid place-items-center">
          <div className="flex flex-col items-center justify-center">
            <div className="bg-accent-green-500 rounded-full flex items-center justify-center text-white relative">
              <SuccessAnimation />
            </div>
            <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold mt-5 text-center">
              {isManualVerification ? (
                <>
                  ID Information <br /> Saved Successfully
                </>
              ) : (
                <>
                  Verification <br /> Successful
                </>
              )}
            </h2>
            <p className="text-dark text-sm text-center max-w-[200px] mt-2">
              {isManualVerification ? (
                <>Your identity has been saved</>
              ) : (
                <>We’ve successfully verified your identity</>
              )}
            </p>
          </div>
        </div>
      )}
      {error && isActive && (
        <div className="h-full w-full grid place-items-center">
          <div className="flex flex-col items-center justify-center w-full">
            <div className="h-20 w-20 sm:h-25 sm:w-25 bg-accent-red-500 rounded-full flex items-center justify-center text-white relative">
              <svg className="w-10 sm:w-12.5" viewBox="0 0 50 50" fill="none">
                <path
                  d="M24.9998 4.16675C13.5207 4.16675 4.1665 13.5209 4.1665 25.0001C4.1665 36.4793 13.5207 45.8334 24.9998 45.8334C36.479 45.8334 45.8332 36.4793 45.8332 25.0001C45.8332 13.5209 36.479 4.16675 24.9998 4.16675ZM31.9998 29.7918C32.604 30.3959 32.604 31.3959 31.9998 32.0001C31.6873 32.3126 31.2915 32.4584 30.8957 32.4584C30.4998 32.4584 30.104 32.3126 29.7915 32.0001L24.9998 27.2084L20.2082 32.0001C19.8957 32.3126 19.4998 32.4584 19.104 32.4584C18.7082 32.4584 18.3123 32.3126 17.9998 32.0001C17.3957 31.3959 17.3957 30.3959 17.9998 29.7918L22.7915 25.0001L17.9998 20.2084C17.3957 19.6042 17.3957 18.6042 17.9998 18.0001C18.604 17.3959 19.604 17.3959 20.2082 18.0001L24.9998 22.7917L29.7915 18.0001C30.3957 17.3959 31.3957 17.3959 31.9998 18.0001C32.604 18.6042 32.604 19.6042 31.9998 20.2084L27.2082 25.0001L31.9998 29.7918Z"
                  fill="white"
                />
              </svg>
            </div>
            <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold mt-5 text-center">
              Your ID <br /> Verification Failed
            </h2>
            <div className="mt-7 bg-grey-fields-100 rounded-10 px-3 py-4 sm:p-5 w-full">
              <span className="text-sm text-dark leading-snug inline-block">
                Your verification failed for one of the following reasons:
              </span>

              <ul className="mt-3 flex flex-col space-y-1">
                {errorsToShow.map((error, index) => (
                  <li className="flex items-center text-1xs text-black-muted" key={index}>
                    {/* prettier-ignore */}
                    <svg viewBox="0 0 18 19" fill="none" className="w-4.5">
                        <path d="M9 17.5625C4.5525 17.5625 0.9375 13.9475 0.9375 9.5C0.9375 5.0525 4.5525 1.4375 9 1.4375C13.4475 1.4375 17.0625 5.0525 17.0625 9.5C17.0625 13.9475 13.4475 17.5625 9 17.5625ZM9 2.5625C5.175 2.5625 2.0625 5.675 2.0625 9.5C2.0625 13.325 5.175 16.4375 9 16.4375C12.825 16.4375 15.9375 13.325 15.9375 9.5C15.9375 5.675 12.825 2.5625 9 2.5625Z" fill="#EF940F"/>
                        <path d="M9 10.8125C8.6925 10.8125 8.4375 10.5575 8.4375 10.25V6.5C8.4375 6.1925 8.6925 5.9375 9 5.9375C9.3075 5.9375 9.5625 6.1925 9.5625 6.5V10.25C9.5625 10.5575 9.3075 10.8125 9 10.8125Z" fill="#EF940F"/>
                        <path d="M9 13.2499C8.9025 13.2499 8.805 13.2274 8.715 13.1899C8.625 13.1524 8.5425 13.0999 8.4675 13.0324C8.4 12.9574 8.3475 12.8824 8.31 12.7849C8.2725 12.6949 8.25 12.5974 8.25 12.4999C8.25 12.4024 8.2725 12.3049 8.31 12.2149C8.3475 12.1249 8.4 12.0424 8.4675 11.9674C8.5425 11.8999 8.625 11.8474 8.715 11.8099C8.895 11.7349 9.105 11.7349 9.285 11.8099C9.375 11.8474 9.4575 11.8999 9.5325 11.9674C9.6 12.0424 9.6525 12.1249 9.69 12.2149C9.7275 12.3049 9.75 12.4024 9.75 12.4999C9.75 12.5974 9.7275 12.6949 9.69 12.7849C9.6525 12.8824 9.6 12.9574 9.5325 13.0324C9.4575 13.0999 9.375 13.1524 9.285 13.1899C9.195 13.2274 9.0975 13.2499 9 13.2499Z" fill="#EF940F"/>
                      </svg>
                    <span className="ml-2">{error}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="text-sm text-dark flex items-center justify-start mt-2.5 mb-1">
              <button
                className="text-primary-500 font-medium inline-flex items-center ml-0.5"
                type="button"
                onClick={retryManually}
              >
                Retry verification manually
                {/* prettier-ignore */}
                <svg className="w-3.5 mt-0.5" viewBox="0 0 15 16" fill="none">
                    <path d="M3.96484 11.5355L11.0359 4.46446" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M11.0352 11.5355L11.0352 4.46446L3.96409 4.46445" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
              </button>
            </div>
          </div>
        </div>
      )}
      <AppBtn isBlock onClick={handleButtonClick} className="mt-auto" size="lg">
        {getButtonLabel()}
      </AppBtn>
    </div>
  );
};

const defaultErrors = ["Something went wrong on our end"];

export default IDWidgetStatusScreen;
