import React from "react";
import { AppBtn } from "../../../ui/buttons";

interface IProps {
  isActive: boolean;
  next: () => void;
}

const IDWidgetProcessingScreen: React.FC<IProps> = ({ isActive, next }) => {
  return (
    <div className="h-full py-7 flex flex-col items-center justify-between px-7.5 relative">
      {isActive && (
        <div className="h-full w-full grid place-items-center">
          <div className="flex flex-col items-center justify-center">
            {/* prettier-ignore */}
            <div className="h-20 w-20 sm:h-25 sm:w-25 bg-accent-orange-500 rounded-full flex items-center justify-center text-white relative">
              <div className="spinner-x center">
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
                <div className="spinner-blade"></div>
              </div>
            </div>
            <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold mt-5">
              {/* 45% <span className="font-bold">Complete</span> */} Processing...
            </h2>
            <p className="text-dark text-sm text-center max-w-[200px] mt-2">
              Please wait! We&apos;re verifying your Identity
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default IDWidgetProcessingScreen;
