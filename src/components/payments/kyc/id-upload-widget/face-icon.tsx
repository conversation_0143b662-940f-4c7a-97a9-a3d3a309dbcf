import React from "react";

const FaceIcon = () => {
  return (
    //prettier-ignore
    <svg className="w-[75px] sm:w-20 text-grey-border" viewBox="0 0 100 100" fill="none">
      <path d="M62 3H72C87 3 97.0001 13 97.0001 28V38.0001" strokeWidth="5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" className="selfie-borders" stroke="currentColor"/>
      <path d="M3 38.0001V28C3 13 13 3 28 3H38.0001" strokeWidth="5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" stroke="currentColor" className="selfie-borders-rev"/>
      <path d="M62 97.0003H72C87 97.0003 97.0001 87.0003 97.0001 72.0003V62.0002" strokeWidth="5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" stroke="currentColor" className="selfie-borders"/>
      <path d="M3 62.0002V72.0003C3 87.0003 13 97.0003 28 97.0003H38.0001" strokeWidth="5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" stroke="currentColor" className="selfie-borders-rev"/>
      <path d="M25 33.5V40.5" strokeWidth="5" strokeLinecap="round" stroke="currentColor" className="selfie-eyes"/>
      <path d="M75 33.5V40.5" strokeWidth="5" strokeLinecap="round" stroke="currentColor" className="selfie-eyes"/>
      <path d="M52 33.5C52 39.8846 52 46.2692 52 52.6538C52 56.0594 49.2601 57.5 47 57.5" strokeWidth="5" strokeLinecap="round" stroke="currentColor" className="selfie-nose"/>
      <path d="M68 73C58.3259 82.1265 42.6284 82.1265 33 73" strokeWidth="5" strokeLinecap="round" strokeLinejoin="round" stroke="currentColor" className="selfie-mouth"/>
    </svg>
  );
};

export default FaceIcon;
