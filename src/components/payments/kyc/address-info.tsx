import { useFormik } from "formik";
import React, { useEffect, useMemo } from "react";
import { COUNTRIES, KYCInfo, Tier } from "../../../assets/interfaces";
import { arrayToInputValues, getFieldvalues } from "../../../assets/js/utils/functions";
import getStates from "../../../assets/js/utils/get-states";
import { getLgas } from "../../../assets/js/utils/lgas";
import { AppBtn } from "../../ui/buttons";
import { InputField, SelectDropdown } from "../../ui/form-elements";
import { TierBadge } from "./commons";
import * as Yup from "yup";
import { useRequest } from "../../../api/utils";
import { SaveAddress } from "../../../api/store.kyc";
import { SaveAddressParams } from "../../../api/interfaces/store.kyc.interface";
import { getDistricts } from "@/assets/js/utils/lgas-gh";
import authContext from "@/contexts/auth-context";
import { getSuburbsAndTownships } from "@/assets/js/utils/lgas-za";
import { getSubCounties } from "@/assets/js/utils/lgas-ke";

interface IProps {
  isActive: boolean;
  kycInfo: KYCInfo;
  next: () => void;
  setKycInfo: (kycInfo: KYCInfo) => void;
}

const KYCAddressInfo: React.FC<IProps> = ({ isActive, kycInfo, next, setKycInfo }) => {
  const { store } = authContext.useContainer();
  const { isLoading, makeRequest, error, response } = useRequest<SaveAddressParams>(SaveAddress);

  const kycCountry = kycInfo?.country ?? store?.country?.code;
  const states = getStates(kycCountry);

  const form = useFormik({
    initialValues: {
      address_line1: kycInfo?.address?.address_line1 ?? "",
      lga: kycInfo?.address?.lga ?? "",
      state: kycInfo?.address?.state || "",
      city: kycInfo?.address?.city ?? "",
    },
    onSubmit: async (values) => {
      const [res, err] = await makeRequest(values);

      if (res) {
        setKycInfo(res?.data);
        next();
      }
    },
    validationSchema,
  });

  useEffect(() => {
    if (JSON.stringify(kycInfo?.address) !== JSON.stringify(form.values)) {
      form.setValues({
        address_line1: kycInfo?.address?.address_line1 ?? "",
        lga: kycInfo?.address?.lga ?? "",
        state: kycInfo?.address?.state || "",
        city: kycInfo?.address?.city ?? "",
      });
    }
  }, [kycInfo]);

  const lgas = useMemo(() => lgaCountryMap[kycCountry](form.values.state), [kycInfo, form.values.state]);

  return (
    <div className="h-full overflow-y-auto">
      <div className="w-full h-full max-w-[550px] mx-auto py-12.5 sm:pt-16 lg:pt-20 text-center">
        {/* <TierBadge className="text-accent-green-500 bg-accent-green-500" tier={Tier.TIER_3} /> */}
        <figure className="h-[70px] w-[70px] sm:h-20 sm:w-20 bg-accent-green-500 flex items-center justify-center rounded-full mb-3.75 mx-auto">
          {/* prettier-ignore */}
          <svg className="w-7.5 sm:w-9 text-white" viewBox="0 0 24 24" fill="none">
          <path d="M20.6188 8.7C19.5788 4.07 15.5388 2 11.9988 2C11.9988 2 11.9988 2 11.9888 2C8.45877 2 4.42877 4.07 3.37877 8.69C2.19877 13.85 5.35877 18.22 8.21877 20.98C9.27877 22 10.6388 22.51 11.9988 22.51C13.3588 22.51 14.7188 22 15.7688 20.98C18.6288 18.22 21.7888 13.86 20.6188 8.7ZM15.2788 9.53L11.2788 13.53C11.1288 13.68 10.9388 13.75 10.7488 13.75C10.5588 13.75 10.3688 13.68 10.2188 13.53L8.71877 12.03C8.42877 11.74 8.42877 11.26 8.71877 10.97C9.00877 10.68 9.48877 10.68 9.77877 10.97L10.7488 11.94L14.2188 8.47C14.5088 8.18 14.9888 8.18 15.2788 8.47C15.5688 8.76 15.5688 9.24 15.2788 9.53Z" fill="currentColor"/>
        </svg>
        </figure>
        <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-light">
          Final Step <br />
          <b className="font-bold">What&apos;s Your Address?</b>
        </h2>
        <p className="text-sm text-dark mt-1.25 max-w-[250px] mx-auto">Your residential address</p>
        <form onSubmit={form.handleSubmit} className="max-w-[450px] mx-auto mt-6 sm:mt-7.5">
          <SelectDropdown
            label={`Select ${labelsMap[kycCountry].state}`}
            hasSearch={true}
            searchLabel={`Search ${labelsMap[kycCountry].state}`}
            options={arrayToInputValues(states)}
            {...getFieldvalues("state", form)}
          />
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3.75 sm:gap-5 mt-3.75">
            <div>
              <SelectDropdown
                label={`Select ${labelsMap[kycCountry].lga}`}
                hasSearch={true}
                searchLabel={`Search ${labelsMap[kycCountry].lga}`}
                options={arrayToInputValues(lgas)}
                name="lga"
                {...getFieldvalues("lga", form)}
              />
            </div>
            <div>
              <InputField label="Your City" {...getFieldvalues("city", form)} />
            </div>
          </div>
          <InputField label="Your street address" {...getFieldvalues("address_line1", form)} />
          <AppBtn isBlock size="lg" className="mt-6 sm:mt-7.5" type="submit" disabled={isLoading}>
            {isLoading ? "Loading..." : "Continue"}
          </AppBtn>
        </form>
      </div>
    </div>
  );
};

const lgaCountryMap = {
  [COUNTRIES.NG]: (state) => getLgas(state),
  [COUNTRIES.GH]: (state) => getDistricts(state),
  [COUNTRIES.ZA]: (state) => getSuburbsAndTownships(state),
  [COUNTRIES.KE]: (state) => getSubCounties(state),
};

const labelsMap = {
  [COUNTRIES.NG]: {
    state: "State",
    lga: "LGA",
  },
  [COUNTRIES.GH]: {
    state: "Region",
    lga: "District",
  },
  [COUNTRIES.ZA]: {
    state: "Province",
    lga: "Suburb/Township",
  },
  [COUNTRIES.KE]: {
    state: "County",
    lga: "Sub County",
  },
};

const validationSchema = (startVerfication: boolean) =>
  Yup.object().shape({
    address_line1: Yup.string().required("Street address is required"),
    lga: Yup.string().required("Local government is required"),
    state: Yup.string().required("State is required"),
    city: Yup.string().required("City is required"),
  });

export default KYCAddressInfo;
