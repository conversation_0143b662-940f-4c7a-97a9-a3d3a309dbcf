import React, { useEffect, useState } from "react";
import { COUNTRIES, KYCInfo, KYC_STATUSES } from "../../../assets/interfaces";
import { AppBtn } from "../../ui/buttons";
import ErrorIcon from "../../../assets/icons/statuses/error.svg";
import PendingIcon from "../../../assets/icons/statuses/pending.svg";
import KYCApproved from "./kyc-approved";
import { WHATSAPP_LINK } from "../../../assets/js/utils/constants";
import { useRequest } from "../../../api/utils";
import { RemoveIdInfo } from "../../../api";
import { toast } from "../../ui/toast";
import ChowbotSetupComplete from "@/components/get-started/chowbot-setup-complete";

interface IProps {
  kycInfo: KYCInfo;
  isSubmitting: boolean;
  error?: string;
  retry: VoidFunction;
  submitResponse: any;
  isChowbotSetup?: boolean;
}

const KYCStatus: React.FC<IProps> = ({ kycInfo, isSubmitting, error, retry, submitResponse, isChowbotSetup }) => {
  const removeIdReq = useRequest(RemoveIdInfo);
  const { isLoading, makeRequest, response } = removeIdReq;
  const status = kycInfo?.status;
  const notSubmittingOrError = !isSubmitting && !error;

  const removeIdandStatus = async () => {
    const [res, err] = await makeRequest({});

    if (err) {
      toast.error({
        title: "Something went wrong",
        message: "Re-initiating Kyc failed",
      });
      return;
    }

    window.location.reload();
  };

  return (
    <div className="w-full h-full overflow-y-auto py-15 flex flex-col items-center justify-center">
      {isSubmitting && (
        <div className="flex flex-col items-center">
          <div className="spinner spinner--md text-primary-500"></div>
          <span className="text-sm text-black-placeholder mt-2 text-center">Submitting...</span>
        </div>
      )}

      {error && (
        <div className="flex flex-col items-center">
          <figure className="w-[90px] md:w-25 mb-5 sm:mb-7.5 -mt-20">
            <ErrorIcon />
          </figure>
          <h3 className="text-black font-bold text-2xl sm:text-3lg lg:text-3xl text-center">Something went wrong!</h3>
          <p className="text-dark text-1xs sm:text-sm mt-2.5 max-w-[320px] text-center">{error}</p>
          <AppBtn onClick={retry} className="mt-5 sm:mt-7.5 !px-8" size="lg">
            Try Again
          </AppBtn>
        </div>
      )}

      {status === "DENIED" && (
        <div className="flex flex-col items-center">
          <figure className="w-[90px] md:w-25 mb-5 sm:mb-7.5 -mt-20">
            <ErrorIcon />
          </figure>
          <h3 className="text-black font-bold text-2xl sm:text-3lg lg:text-3xl text-center">Your request was denied</h3>
          <p className="text-dark text-1xs sm:text-sm mt-2.5 max-w-[320px] text-center">{kycInfo?.rejection_message}</p>
          <div className="flex items-center space-x-2.5">
            <AppBtn
              href={`${WHATSAPP_LINK}&text=Hi, my payments activation was denied. My store ID is ${kycInfo.store}`}
              className="mt-5 sm:mt-7.5 !px-8"
              size="lg"
              color="neutral"
            >
              Contact Support
            </AppBtn>
            <AppBtn className="mt-5 sm:mt-7.5 !px-8" size="lg" onClick={removeIdandStatus} disabled={isLoading}>
              {isLoading ? "Please wait..." : "Update Information"}
            </AppBtn>
          </div>
        </div>
      )}

      {notSubmittingOrError && status === "PENDING" && (
        <div className="flex flex-col items-center -mt-20">
          <figure className="w-[90px] md:w-25 mb-5 sm:mb-7.5">
            <PendingIcon />
          </figure>
          <h3 className="text-black font-bold text-2xl sm:text-3lg lg:text-3xl text-center">
            {copies[kycInfo.country].pendingTitle}
          </h3>
          <p className="text-dark text-1xs sm:text-sm mt-2.5 text-center max-w-[350px]">
            {copies[kycInfo.country].pendingSubText}
          </p>
          <AppBtn href="/dashboard" className="mt-5 sm:mt-7.5 !px-8" size="lg">
            Continue to dashboard
          </AppBtn>
        </div>
      )}

      {notSubmittingOrError && status === KYC_STATUSES.APPROVED && !isChowbotSetup && (
        <KYCApproved submitResponse={submitResponse} kycInfo={kycInfo} />
      )}

      {notSubmittingOrError && status === KYC_STATUSES.APPROVED && isChowbotSetup && <ChowbotSetupComplete />}
    </div>
  );
};

const copies = {
  [COUNTRIES.NG]: {
    pendingTitle: "Account creation in Progress",
    pendingSubText: `We're verifying your information and creating your account, you should get an email in a few hours`,
  },
  [COUNTRIES.GH]: {
    pendingTitle: "KYC Verification Progress",
    pendingSubText: `We're verifying the information you provided, you should get an email in a few hours`,
  },
  [COUNTRIES.KE]: {
    pendingTitle: "KYC Verification Progress",
    pendingSubText: `We're verifying the information you provided, you should get an email in a few hours`,
  },
  [COUNTRIES.ZA]: {
    pendingTitle: "KYC Verification Progress",
    pendingSubText: `We're verifying the information you provided, you should get an email in a few hours`,
  },
};

export default KYCStatus;
