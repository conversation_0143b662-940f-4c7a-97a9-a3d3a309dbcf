import React from "react";
import { SubmitKYC } from "../../../api/store.kyc";
import { useRequest } from "../../../api/utils";
import { COUNTRIES, KYCInfo } from "../../../assets/interfaces";
import LazyImage from "../../lazy-image";
import { AppBtn } from "../../ui/buttons";
import { removeCountryCode } from "@/assets/js/utils/functions";

interface IProps {
  kycInfo: KYCInfo;
  submitKyc: VoidFunction;
  previous: VoidFunction;
}

const KYCInfoSummary: React.FC<IProps> = ({ kycInfo, submitKyc, previous }) => {
  const information = [
    {
      title: "Basic Information",
      icon: icons.PERSON,
      color: "bg-primary-500",
      data: [
        {
          title: "First Name",
          value: kycInfo?.first_name,
        },
        {
          title: "Last Name",
          value: kycInfo?.last_name,
        },
      ],
    },
    kycInfo?.country === COUNTRIES.NG
      ? {
          title: "BVN Information",
          icon: icons.BVN,
          color: "bg-accent-green-500",
          data: [
            {
              title: "BVN",
              value: kycInfo?.bvn,
            },
            {
              title: "DOB",
              value: kycInfo?.dob,
            },
          ],
        }
      : {
          title: "Phone Information",
          icon: icons.PHONE,
          color: "bg-accent-green-500",
          data: [
            {
              title: "Phone",
              value: removeCountryCode(kycInfo?.phone ?? ""),
            },
            {
              title: "DOB",
              value: kycInfo?.dob,
            },
          ],
        },
    {
      title: "ID Information",
      icon: icons.ID,
      color: "bg-accent-yellow-500",
      data: [
        {
          title: "ID TYPE",
          value: idTypes[kycInfo?.identity?.type],
        },
        {
          title: "ID Number",
          value: kycInfo?.identity?.number,
        },
        {
          title: "ID IMAGE",
          value: kycInfo?.identity?.url,
          isImage: true,
        },
      ],
    },
    {
      title: "Address Information",
      icon: icons.ADDRESS,
      color: "bg-accent-orange-500",
      data: [
        {
          title: "City",
          value: kycInfo?.address?.city,
        },
        {
          title: "State",
          value: kycInfo?.address?.state,
        },
        {
          title: "LGA",
          value: kycInfo?.address?.lga,
        },
        {
          title: "street address",
          value: kycInfo?.address?.address_line1,
        },
      ],
    },
  ];

  return (
    <div className="w-full h-full overflow-y-auto">
      <div className="max-w-[480px] flex flex-col items-center pt-12.5 sm:pt:15 mx-auto w-full pb-16">
        <div className="flex flex-col items-center text-center">
          <figure className="w-17.5 h-17.5 sm:h-20 sm:w-20 rounded-full bg-accent-red-500 flex items-center justify-center">
            {/* prettier-ignore */}
            <svg className="w-8 sm:w-10" viewBox="0 0 40 40" fill="none">
              <path d="M26.6673 7.08331C26.6673 9.14998 24.984 10.8333 22.9173 10.8333H17.084C16.0507 10.8333 15.1173 10.4166 14.434 9.73331C13.7507 9.04998 13.334 8.11665 13.334 7.08331C13.334 5.01665 15.0173 3.33331 17.084 3.33331H22.9173C23.9507 3.33331 24.884 3.74998 25.5673 4.43331C26.2507 5.11665 26.6673 6.04998 26.6673 7.08331Z" fill="white"/>
              <path d="M31.3827 8.38333C30.9993 8.06667 30.566 7.81667 30.0993 7.63333C29.616 7.45 29.1327 7.83333 29.0327 8.33333C28.466 11.1833 25.9493 13.3333 22.916 13.3333H17.0827C15.416 13.3333 13.8493 12.6833 12.666 11.5C11.7993 10.6333 11.1993 9.53333 10.966 8.35C10.866 7.85 10.366 7.45 9.88268 7.65C7.94935 8.43333 6.66602 10.2 6.66602 13.75V30C6.66602 35 9.64935 36.6667 13.3327 36.6667H26.666C30.3493 36.6667 33.3327 35 33.3327 30V13.75C33.3327 11.0333 32.5827 9.36667 31.3827 8.38333ZM13.3327 20.4167H19.9993C20.6827 20.4167 21.2493 20.9833 21.2493 21.6667C21.2493 22.35 20.6827 22.9167 19.9993 22.9167H13.3327C12.6493 22.9167 12.0827 22.35 12.0827 21.6667C12.0827 20.9833 12.6493 20.4167 13.3327 20.4167ZM26.666 29.5833H13.3327C12.6493 29.5833 12.0827 29.0167 12.0827 28.3333C12.0827 27.65 12.6493 27.0833 13.3327 27.0833H26.666C27.3493 27.0833 27.916 27.65 27.916 28.3333C27.916 29.0167 27.3493 29.5833 26.666 29.5833Z" fill="white"/>
            </svg>
          </figure>
          <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold mt-3.75">
            Information <b className="font-bold">Summary</b>
          </h2>
          <p className="text-sm text-dark mt-1.25 max-w-[250px] mx-auto">
            Here’s a summary of the information you provided
          </p>
        </div>

        <div className="w-full bg-grey-fields-200 flex mt-5 sm:mt-7.5 px-6 sm:px-8 lg:px-12.5 py-5 rounded-10 flex-col">
          {information.map((i, index) => (
            <section className="w-full first:mt-0 mt-7.5" key={index}>
              <div className="flex items-center pb-2.5 border-b border-grey-border w-full">
                <figure className={`h-6 w-6 rounded-full flex items-center justify-center text-white ${i.color}`}>
                  {i.icon}
                </figure>
                <h4 className="ml-1.25 font-bold text-base">{i.title}</h4>
              </div>
              <div className="mt-3.75 grid grid-cols-2 gap-3.5 sm:gap-5">
                {i.data.map((d, _index) => (
                  <div key={_index}>
                    <span className="uppercase text-xxs text-dark font-medium">{d.title}</span>
                    {!d?.isImage && <h6 className="text-black text-1sm">{d.value}</h6>}
                    {d.isImage && (
                      <div className="relative w-full">
                        <LazyImage
                          src={d.value}
                          alt=""
                          className="w-full rounded-10"
                          loaderClasses="rounded-10 w-full"
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          ))}
        </div>

        <div className="mt-7.5 flex items-center w-full space-x-3.5">
          <AppBtn size="lg" color="neutral" className="flex-1" isBlock onClick={previous}>
            Edit
          </AppBtn>
          <AppBtn size="lg" className="flex-1" isBlock onClick={submitKyc}>
            Submit
          </AppBtn>
        </div>
      </div>
    </div>
  );
};

const icons = {
  PERSON:
    // prettier-ignore
    <svg className="w-3.5" viewBox="0 0 14 14" fill="none">
      <path d="M6.99935 1.16663C5.47102 1.16663 4.22852 2.40913 4.22852 3.93746C4.22852 5.43663 5.40102 6.64996 6.92935 6.70246C6.97602 6.69663 7.02268 6.69663 7.05768 6.70246C7.06935 6.70246 7.07518 6.70246 7.08685 6.70246C7.09268 6.70246 7.09268 6.70246 7.09852 6.70246C8.59185 6.64996 9.76435 5.43663 9.77018 3.93746C9.77018 2.40913 8.52768 1.16663 6.99935 1.16663Z" fill="white"/>
      <path d="M9.96258 8.25418C8.33508 7.16918 5.68091 7.16918 4.04174 8.25418C3.30091 8.75001 2.89258 9.42085 2.89258 10.1383C2.89258 10.8558 3.30091 11.5208 4.03591 12.0108C4.85258 12.5592 5.92591 12.8333 6.99924 12.8333C8.07258 12.8333 9.14591 12.5592 9.96258 12.0108C10.6976 11.515 11.1059 10.85 11.1059 10.1267C11.1001 9.40918 10.6976 8.74418 9.96258 8.25418Z" fill="white"/>
    </svg>,

  BVN:
    //prettier-ignore
    <svg className="w-3.5" viewBox="0 0 14 14" fill="none">
      <path d="M10.8145 2.40341L7.60615 1.20174C7.27365 1.07924 6.73115 1.07924 6.39865 1.20174L3.19031 2.40341C2.57198 2.63674 2.07031 3.36007 2.07031 4.01924V8.74424C2.07031 9.21674 2.37948 9.84091 2.75865 10.1209L5.96698 12.5184C6.53281 12.9442 7.46031 12.9442 8.02615 12.5184L11.2345 10.1209C11.6136 9.83507 11.9228 9.21674 11.9228 8.74424V4.01924C11.9286 3.36007 11.427 2.63674 10.8145 2.40341ZM9.02948 5.67007L6.52115 8.17841C6.43365 8.26591 6.32281 8.30674 6.21198 8.30674C6.10115 8.30674 5.99031 8.26591 5.90281 8.17841L4.96948 7.23341C4.80031 7.06424 4.80031 6.78424 4.96948 6.61507C5.13865 6.44591 5.41865 6.44591 5.58781 6.61507L6.21781 7.24507L8.41698 5.0459C8.58615 4.87674 8.86615 4.87674 9.03531 5.0459C9.20448 5.21507 9.20448 5.50091 9.02948 5.67007Z" fill="white"/>
    </svg>,
  ADDRESS:
    // prettier-ignore
    <svg className="w-3.5" viewBox="0 0 14 14" fill="none">
      <path d="M12.0286 4.92909C11.4161 2.23409 9.0653 1.02075 7.0003 1.02075C7.0003 1.02075 7.0003 1.02075 6.99447 1.02075C4.9353 1.02075 2.57863 2.22825 1.96613 4.92325C1.28363 7.93325 3.12697 10.4824 4.7953 12.0866C5.41363 12.6816 6.20697 12.9791 7.0003 12.9791C7.79363 12.9791 8.58697 12.6816 9.19947 12.0866C10.8678 10.4824 12.7111 7.93909 12.0286 4.92909ZM7.0003 7.85159C5.9853 7.85159 5.1628 7.02909 5.1628 6.01409C5.1628 4.99909 5.9853 4.17659 7.0003 4.17659C8.0153 4.17659 8.8378 4.99909 8.8378 6.01409C8.8378 7.02909 8.0153 7.85159 7.0003 7.85159Z" fill="white"/>
    </svg>,
  ID:
    // prettier-ignore
    <svg className="w-3.5" viewBox="0 0 14 14" fill="none">
      <path d="M10.5 1.16675H3.5C2.53167 1.16675 1.75 1.94258 1.75 2.89925V9.26341C1.75 10.2201 2.53167 10.9959 3.5 10.9959H3.94333C4.41 10.9959 4.85333 11.1767 5.18 11.5034L6.1775 12.4892C6.6325 12.9384 7.37333 12.9384 7.82833 12.4892L8.82583 11.5034C9.1525 11.1767 9.60167 10.9959 10.0625 10.9959H10.5C11.4683 10.9959 12.25 10.2201 12.25 9.26341V2.89925C12.25 1.94258 11.4683 1.16675 10.5 1.16675ZM7 3.35425C7.7525 3.35425 8.35917 3.96091 8.35917 4.71341C8.35917 5.46591 7.7525 6.07258 7 6.07258C6.2475 6.07258 5.64083 5.46008 5.64083 4.71341C5.64083 3.96091 6.2475 3.35425 7 3.35425ZM8.56333 8.78508H5.43667C4.96417 8.78508 4.69 8.26008 4.9525 7.86925C5.34917 7.28008 6.11917 6.88341 7 6.88341C7.88083 6.88341 8.65083 7.28008 9.0475 7.86925C9.31 8.26008 9.03 8.78508 8.56333 8.78508Z" fill="white"/>
    </svg>,
  PHONE:
    //prettier-ignore
    <svg className="w-3.5" viewBox="0 0 24 24" fill="none">
      <path d="M11.05 14.95L9.2 16.8C8.81 17.19 8.19 17.19 7.79 16.81C7.68 16.7 7.57 16.6 7.46 16.49C6.43 15.45 5.5 14.36 4.67 13.22C3.85 12.08 3.19 10.94 2.71 9.81C2.24 8.67 2 7.58 2 6.54C2 5.86 2.12 5.21 2.36 4.61C2.6 4 2.98 3.44 3.51 2.94C4.15 2.31 4.85 2 5.59 2C5.87 2 6.15 2.06 6.4 2.18C6.66 2.3 6.89 2.48 7.07 2.74L9.39 6.01C9.57 6.26 9.7 6.49 9.79 6.71C9.88 6.92 9.93 7.13 9.93 7.32C9.93 7.56 9.86 7.8 9.72 8.03C9.59 8.26 9.4 8.5 9.16 8.74L8.4 9.53C8.29 9.64 8.24 9.77 8.24 9.93C8.24 10.01 8.25 10.08 8.27 10.16C8.3 10.24 8.33 10.3 8.35 10.36C8.53 10.69 8.84 11.12 9.28 11.64C9.73 12.16 10.21 12.69 10.73 13.22C10.83 13.32 10.94 13.42 11.04 13.52C11.44 13.91 11.45 14.55 11.05 14.95Z" fill="currentColor"/>
      <path d="M21.9696 18.3291C21.9696 18.6091 21.9196 18.8991 21.8196 19.1791C21.7896 19.2591 21.7596 19.3391 21.7196 19.4191C21.5496 19.7791 21.3296 20.1191 21.0396 20.4391C20.5496 20.9791 20.0096 21.3691 19.3996 21.6191C19.3896 21.6191 19.3796 21.6291 19.3696 21.6291C18.7796 21.8691 18.1396 21.9991 17.4496 21.9991C16.4296 21.9991 15.3396 21.7591 14.1896 21.2691C13.0396 20.7791 11.8896 20.1191 10.7496 19.2891C10.3596 18.9991 9.96961 18.7091 9.59961 18.3991L12.8696 15.1291C13.1496 15.3391 13.3996 15.4991 13.6096 15.6091C13.6596 15.6291 13.7196 15.6591 13.7896 15.6891C13.8696 15.7191 13.9496 15.7291 14.0396 15.7291C14.2096 15.7291 14.3396 15.6691 14.4496 15.5591L15.2096 14.8091C15.4596 14.5591 15.6996 14.3691 15.9296 14.2491C16.1596 14.1091 16.3896 14.0391 16.6396 14.0391C16.8296 14.0391 17.0296 14.0791 17.2496 14.1691C17.4696 14.2591 17.6996 14.3891 17.9496 14.5591L21.2596 16.9091C21.5196 17.0891 21.6996 17.2991 21.8096 17.5491C21.9096 17.7991 21.9696 18.0491 21.9696 18.3291Z" fill="currentColor"/>
    </svg>,
};

const idTypes = {
  NIN: "NIN Slip",
  INTERNATIONAL_PASSPORT: "Intl. Passport",
  DRIVERS_LICENSE: "Drivers License",
  VOTERS_CARD: "Voters ID",
};

export default KYCInfoSummary;
