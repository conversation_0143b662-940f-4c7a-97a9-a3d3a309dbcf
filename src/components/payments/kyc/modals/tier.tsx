import { useEffect, useState } from "react";
import { Tier } from "../../../../assets/interfaces";
import Modal, { ModalBody } from "../../../ui/modal";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  tier: Tier;
}

const TierModal: React.FC<Props> = ({ show, toggle, tier }) => {
  const [openSection, setOpenSection] = useState<number>(0);

  useEffect(() => {
    setOpenSection(tierMap[tier]);
  }, [tier]);

  const handleItemClick = (id: number) => {
    if (openSection === id) {
      setOpenSection(null);
    } else {
      setOpenSection(id);
    }
  };

  const isOpen = (id: number) => id === openSection;

  return (
    <Modal {...{ show, toggle }} title="Tiers" size="midi">
      <ModalBody className="!px-0 !pb-0">
        <span className="block text-left text-1xs sm:text-sm text-black font-medium px-8 mb-4">
          Transaction Limitations for bank account & upgrade requirements
        </span>
        <div className="mt-5">
          {tiers.map((item, index) => (
            <div
              key={index}
              className={`text-left text-dark border-b last:border-none sm:last:rounded-b-15 ${
                isOpen(index) ? "px-8 bg-grey-fields-100" : "mx-8 border-opacity-50"
              }`}
            >
              <div className={`py-5 text-dark cursor-pointer ${index === 0 && !isOpen(index) ? "!pt-0" : ""}`}>
                <div
                  className="flex items-center justify-between cursor-pointer "
                  onClick={() => handleItemClick(index)}
                >
                  <div className="flex items-center">
                    <figure className={`rounded-full w-7.5 h-7.5 flex items-center justify-center ${item.color}`}>
                      {/* prettier-ignore */}
                      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" className="text-white">
                        <path d="M16.1702 8.05492L15.1502 6.86992C14.9552 6.64492 14.7977 6.22492 14.7977 5.92492V4.64992C14.7977 3.85492 14.1452 3.20242 13.3502 3.20242H12.0752C11.7827 3.20242 11.3552 3.04492 11.1302 2.84992L9.94516 1.82992C9.42766 1.38742 8.58016 1.38742 8.05516 1.82992L6.87766 2.85742C6.65266 3.04492 6.22516 3.20242 5.93266 3.20242H4.63516C3.84016 3.20242 3.18766 3.85492 3.18766 4.64992V5.93242C3.18766 6.22492 3.03016 6.64492 2.84266 6.86992L1.83016 8.06242C1.39516 8.57992 1.39516 9.41992 1.83016 9.93742L2.84266 11.1299C3.03016 11.3549 3.18766 11.7749 3.18766 12.0674V13.3499C3.18766 14.1449 3.84016 14.7974 4.63516 14.7974H5.93266C6.22516 14.7974 6.65266 14.9549 6.87766 15.1499L8.06266 16.1699C8.58016 16.6124 9.42766 16.6124 9.95266 16.1699L11.1377 15.1499C11.3627 14.9549 11.7827 14.7974 12.0827 14.7974H13.3577C14.1527 14.7974 14.8052 14.1449 14.8052 13.3499V12.0749C14.8052 11.7824 14.9627 11.3549 15.1577 11.1299L16.1777 9.94492C16.6127 9.42742 16.6127 8.57242 16.1702 8.05492ZM12.1202 7.58242L8.49766 11.2049C8.39266 11.3099 8.25016 11.3699 8.10016 11.3699C7.95016 11.3699 7.80766 11.3099 7.70266 11.2049L5.88766 9.38992C5.67016 9.17242 5.67016 8.81242 5.88766 8.59492C6.10516 8.37742 6.46516 8.37742 6.68266 8.59492L8.10016 10.0124L11.3252 6.78742C11.5427 6.56992 11.9027 6.56992 12.1202 6.78742C12.3377 7.00492 12.3377 7.36492 12.1202 7.58242Z" fill="currentColor" />
                      </svg>
                    </figure>
                    <h2 className="font-semibold text-1sm ml-2.5">{item.title}</h2>
                  </div>
                  <button
                    className={`bg-gray-100 rounded-full w-7 h-7 flex items-center justify-center ${
                      isOpen(index) ? " duration-300 rotate-[315deg]" : "rotate-0"
                    }`}
                  >
                    {/* prettier-ignore */}
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" className="text-dark">
                      <path d="M6 1V11" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M1 6H11" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                </div>
                <ul
                  className={`text-1xs sm:text-sm list-inside transition-all ease-out duration-300 ${
                    isOpen(index) ? "max-h-[400px] mt-2.5" : "max-h-0 overflow-hidden"
                  }`}
                >
                  <span className="block font-semibold mt-3.75 mb-2 list-none">Transaction limitations</span>
                  {item.limitations.map((content, index) => (
                    <li key={index} className="list-disc text-dark">
                      {content}
                    </li>
                  ))}
                  <span className="block font-semibold my-2">Requirements</span>
                  {item.requirements.map((content, index) => (
                    <li key={index} className="list-disc text-dark">
                      {content}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </ModalBody>
    </Modal>
  );
};

const tierMap = {
  [Tier.TIER_1]: 0,
  [Tier.TIER_2]: 1,
  [Tier.TIER_3]: 2,
};

export const tiers = [
  {
    title: "Tier 1",
    limitations: ["You can receive NGN 50,000 at a time", "Maximum account balance of NGN 300,000.00 only"],
    requirements: ["We would require your BVN", "Your Phone Number is required"],
    color: "bg-accent-red-500",
  },
  {
    title: "Tier 2",
    limitations: [
      "You can receive NGN 100,000 at a time",
      "You can send a max of NGN 100,000 at a time",
      "You can send a max of NGN 100,000 in a day",
      "Maximum account balance of NGN 500,000.00 only",
    ],
    requirements: [
      "We would require your BVN",
      "Your Phone Number is required",
      "We need a means of identification (Intl. passport/ Driver’s license/ Voter’s ID/ NIN)",
    ],
    color: "bg-accent-green-500",
  },
  {
    title: "Tier 3",
    limitations: [
      "You can receive Unlimited Amount at a time",
      "You can send a max of NGN 1,000,000 at a time",
      "You can send a max of NGN 5,000,000 in a day",
      "Account balance is Unlimited",
    ],
    requirements: [
      "We would require your BVN",
      "Your Phone Number is required",
      "We need a means of identification (Intl. passport/ Driver’s license/ Voter’s ID/ NIN)",
      "Your Residential Address Required",
    ],
    color: "bg-accent-yellow-500",
  },
];

export default TierModal;
