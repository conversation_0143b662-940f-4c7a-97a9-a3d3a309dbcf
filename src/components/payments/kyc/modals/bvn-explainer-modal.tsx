import Modal, { ModalBody, ModalProps } from "@/components/ui/modal";

interface Props extends ModalProps {}

export const BVNExplainerModal: React.FC<Props> = ({ show, toggle }) => {
  return (
    <Modal {...{ show, toggle }} title="Why your BVN is needed" size="sm">
      <ModalBody>
        <div className="text-dark text-sm">
          We are required by CBN to collect your BVN if you want to receive payments on your Catlog store. <br />
          <br /> Your BVN is similar to your NIN, it can only be used to verify your identity and cannot be used to
          access your bank account or make transactions.
          <br />
          <br />
          <h4 className="font-display font-bold text-black text-base">Here's how we'll use your BVN</h4>
          <ul className="space-y-3 max-w-md mx-auto mt-3 ml-0">
            {steps.map((step, index) => (
              <li className="flex items-start" key={index}>
                <figure className="h-4.5 w-4.5 bg-accent-green-500 flex items-center justify-center text-white rounded-full flex-shrink-0 mr-2 mt-0.5">
                  {/* prettier-ignore */}
                  <svg width="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="24" height="24" rx="12" fill="#39B588"/>
                    <path d="M10.2951 16.5C10.055 16.5 9.82691 16.3995 9.65883 16.2238L6.26113 12.6702C5.91296 12.3061 5.91296 11.7033 6.26113 11.3392C6.6093 10.9751 7.18559 10.9751 7.53377 11.3392L10.2951 14.2272L16.4662 7.77311C16.8144 7.40896 17.3907 7.40896 17.7389 7.77311C18.087 8.13725 18.087 8.73997 17.7389 9.10412L10.9315 16.2238C10.7634 16.3995 10.5353 16.5 10.2951 16.5Z" fill="white"/>
                  </svg>
                </figure>
                <span className="inline-block text-dark text-sm max-w-sm">{step}</span>
              </li>
            ))}
          </ul>
        </div>
      </ModalBody>
    </Modal>
  );
};

const steps = [
  "To verify your identity",
  "To prevent others from impersonating you",
  "To Create a bank account with your business name",
];
