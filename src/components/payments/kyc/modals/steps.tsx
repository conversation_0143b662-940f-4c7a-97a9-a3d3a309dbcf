import { useEffect, useState } from "react";
import Modal, { ModalBody } from "../../../ui/modal";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
}

const StepsModal: React.FC<Props> = ({ show, toggle }) => {
  return (
    <Modal {...{ show, toggle }} title="KYC steps" size="midi">
      <ModalBody className="!px-0 !pb-0">
        <div className="flex items-center justify-between text-dark border-grey-border border-b border-opacity-50 pb-5 px-5 sm:px-6.25">
          <span className="text-1sm font-semibold text-black">Your progress</span>
          <span className="text-sm font-medium">10% completed</span>
        </div>
        <StepsComponent />
      </ModalBody>
    </Modal>
  );
};

interface StepsProps {}

export const StepsComponent: React.FC<StepsProps> = ({}) => {
  const [steps, setSteps] = useState(stepsarray);
  useEffect(() => {
    setSteps(
      steps.map((step) => {
        return {
          ...step,
          status: status.sort(() => Math.random() - 0.5)[Math.floor(Math.random() * 3)],
        };
      })
    );
  }, []);
  return (
    <div className="mx-5 sm:mx-6.25">
      {steps.map((step, index) => {
        return (
          <div
            key={step.id}
            className="flex items-center justify-between py-4 sm:py-5 border-b border-opacity-50 last:border-none"
          >
            <div className="flex items-center">
              <span
                className={`w-7.5 h-7.5 flex items-center justify-center rounded-full text-sm font-semibold ${
                  statusAssets[step.status]?.color.bg
                } ${step.status !== "pending" ? "text-white" : "text-dark"}`}
              >
                {index + 1}
              </span>
              <span className="text-dark text-1xs sm:text-1sm font-medium ml-2.5">{step.title}</span>
            </div>
            <div className="flex items-center">
              {step.status !== "skipped" && <div>{statusAssets[step.status]?.icon}</div>}
              <span
                className={`${statusAssets[step.status]?.color.text} text-xs sm:text-sm font-medium pl-0.5 capitalize`}
              >
                {step.status}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
};

const status = ["completed", "ongoing", "pending", "skipped"];

const stepsarray = [
  {
    id: 1,
    title: "BVN Information",
    status: "completed", //status: "skipped"
  },
  {
    id: 2,
    title: "ID Information",
    status: "ongoing",
  },
  {
    id: 3,
    title: "Address Information",
    status: "pending",
  },
];

const statusAssets = {
  completed: {
    color: {
      text: "text-accent-green-500",
      bg: "bg-dark",
    },
    icon:
      // prettier-ignore
      <svg viewBox="0 0 16 16" fill="none" className="text-accent-green-500 w-3.5 sm:w-4">
        <path d="M13.3327 4L5.99935 11.3333L2.66602 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </svg>,
  },
  ongoing: {
    color: {
      text: "text-accent-purple-500",
      bg: "bg-black",
    },
    icon:
      // prettier-ignore
      <svg viewBox="0 0 12 16" fill="none" className="text-accent-purple-500 h-3.5 sm:h-4">
        <path d="M8.15937 1.33337H3.83937C1.33271 1.33337 1.13937 3.58671 2.49271 4.81337L9.50604 11.1867C10.8594 12.4134 10.666 14.6667 8.15937 14.6667H3.83937C1.33271 14.6667 1.13937 12.4134 2.49271 11.1867L5.99937 8.00004L9.50604 4.81337C10.8594 3.58671 10.666 1.33337 8.15937 1.33337Z" stroke="#332089" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      </svg>,
  },
  pending: {
    color: {
      text: "text-dark",
      bg: "bg-grey-fields-100",
    },
    icon:
      // prettier-ignore
      <svg viewBox="0 0 16 16" fill="none" className="text-dark w-3.5 sm:w-4">
        <path d="M14.6673 8.00004C14.6673 11.68 11.6807 14.6667 8.00065 14.6667C4.32065 14.6667 1.33398 11.68 1.33398 8.00004C1.33398 4.32004 4.32065 1.33337 8.00065 1.33337C11.6807 1.33337 14.6673 4.32004 14.6673 8.00004Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M10.4739 10.12L8.40724 8.88671C8.04724 8.67338 7.75391 8.16005 7.75391 7.74005V5.00671" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      </svg>,
  },
  skipped: {
    color: {
      text: "text-accent-red-500",
      bg: "bg-accent-red-500",
    },
  },
};

export default StepsModal;
