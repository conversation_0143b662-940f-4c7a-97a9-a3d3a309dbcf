import { useEffect, useState } from "react";
import { ResendBVNTokenParams, VerifyBVNParms, VerifyPhoneParams } from "../../../api/interfaces/store.kyc.interface";
import { ResendBVNToken } from "../../../api/store.kyc";
import { RequestInterface, useRequest } from "../../../api/utils";
import { timeToClock } from "../../../assets/js/utils/functions";
import { CodeInput } from "../../ui/form-elements";

const TokenForm: React.FC<{
  verifyTokenReq: RequestInterface<any>;
  resendTokenReq: RequestInterface<any>;
  value: string;
  fieldKey: string;
  setIsVerified: (status: boolean) => void;
}> = ({ verifyTokenReq, value, fieldKey, setIsVerified, resendTokenReq }) => {
  const RESEND_DELAY_MINS = 5;
  const [token, setToken] = useState("");
  const { isLoading, makeRequest, error, response, clearResponse } = verifyTokenReq;
  const [resendStatus, setResendStatus] = useState<"success" | "failed" | "pending">(null);
  const [lastResendTime, setLastResendTime] = useState<number>(null);
  const [nextResendTime, setNextResendTime] = useState<number>(null);
  const canResendToken = nextResendTime <= 0;

  useEffect(() => {
    if (!token) {
      return;
    }

    if (token.length === 6) {
      verifyToken();
    } else {
      clearResponse(); //not sure why we're doing this
    }
  }, [token]);

  useEffect(() => {
    const storedLastResendTime = localStorage.getItem("last-send-bvn-token");

    if (storedLastResendTime) {
      setLastResendTime(Number(storedLastResendTime));
    }
  }, []);

  useEffect(() => {
    if (!lastResendTime) {
      return;
    }

    const storedLastResendTime = localStorage.getItem(`last-send-bvn-token`);

    if (storedLastResendTime !== String(lastResendTime)) {
      localStorage.setItem(`last-send-bvn-token`, String(lastResendTime));
    }

    const interval = setInterval(() => {
      const nextResendTime = RESEND_DELAY_MINS * 60000 - (Date.now() - lastResendTime);
      setNextResendTime(nextResendTime);

      if (nextResendTime < 0) {
        clearInterval(interval);
        localStorage.removeItem(`last-send-bvn-token`);
      }
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [lastResendTime]);

  useEffect(() => {
    if (response) {
      setIsVerified(true);
    }
  }, [response]);

  const resend = async () => {
    setResendStatus("pending");
    const [res, error] = await resendTokenReq.makeRequest({ [fieldKey]: value });

    if (res) {
      setResendStatus("success");
      setLastResendTime(Date.now());
    } else {
      setResendStatus("failed");
    }

    setTimeout(() => {
      setResendStatus(null);
    }, 3000);
  };

  const verifyToken = async () => {
    const [res, error] = await makeRequest({ [fieldKey]: value, token });

    if (error) {
      setResendStatus("failed");

      setTimeout(() => {
        setResendStatus(null);
      }, 3000);
    }
  };

  return (
    <div className="mt-3">
      {/* {error && <div className="text-accent-red-500 text-xs font-semibold mt-1">{error?.message}</div>} */}
      <div className="flex items-center">
        <CodeInput onChange={setToken} length={6} disabled={isLoading} />
        {isLoading && <div className="spinner spinner--sm ml-1.5 flex-shrink-0 text-primary-500"></div>}
      </div>
      <div className="flex items-center mt-2">
        {!canResendToken && (
          <span className="text-dark text-1xs">
            Resend token in <b>{timeToClock(nextResendTime)}</b>
          </span>
        )}
        {canResendToken && (
          <button
            className="inline-flex items-center text-dark disabled:text-placeholder transition-colors hover:text-primary-500"
            onClick={resend}
            type="button"
            disabled={resendStatus === "pending" || isLoading}
          >
            <span className="inline-block text-1xs font-medium">Resend Code</span>
            {/* prettier-ignore */}
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" className="ml-0.5">
              <g clipPath="url(#clip0)">
                <path d="M11.5001 2V5H8.50012" stroke="currentColor" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M10.2451 7.50001C9.92005 8.41997 9.30484 9.20937 8.49214 9.74926C7.67944 10.2892 6.71329 10.5503 5.73927 10.4933C4.76525 10.4363 3.83614 10.0643 3.09194 9.43334C2.34774 8.80237 1.82877 7.94662 1.61324 6.99503C1.39772 6.04345 1.4973 5.0476 1.89699 4.15754C2.29669 3.26748 2.97483 2.53144 3.82924 2.06033C4.68365 1.58921 5.66803 1.40856 6.63404 1.54558C7.60006 1.6826 8.49537 2.12988 9.18506 2.82001L11.5001 5.00001" stroke="currentColor" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
              </g>
              <defs>
                <clipPath id="clip0">
                  <rect width="12" height="12" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </button>
        )}
        {resendStatus === "pending" && <span className="inline-flex ml-2 text-placeholder text-1xs">Sending...</span>}
        {resendStatus === "success" && (
          <span className="inline-flex ml-2 text-accent-green-500 text-1xs">Token resent</span>
        )}
        {resendStatus === "failed" && (
          <span className="inline-flex ml-2 text-accent-red-500 text-1xs">
            {resendTokenReq?.error?.message ?? error?.message ?? "Token resend failed"}
          </span>
        )}
      </div>
    </div>
  );
};

export default TokenForm;
