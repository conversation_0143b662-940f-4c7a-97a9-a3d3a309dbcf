import { useFormik } from "formik";
import React, { useEffect, useState } from "react";
import {
  LookupBVNParams,
  LookupPhoneParams,
  ResendPhoneTokenParams,
  UpdateDobParams,
  VerifyBVNParms,
  VerifyPhoneParams,
} from "../../../api/interfaces/store.kyc.interface";
import { LookupBVN, LookupPhone, ResendPhoneToken, UpdateDob, VerifyBVN, VerifyPhone } from "../../../api/store.kyc";
import { useRequest } from "../../../api/utils";
import {
  dateToDDMMYYYY,
  ddMMYYYYToDate,
  getFieldvalues,
  phoneObjectFromString,
  phoneObjectToString,
  removeCountryCode,
} from "../../../assets/js/utils/functions";
import { AppBtn } from "../../ui/buttons";
import { DatePickerInput, InputField, PhoneInput } from "../../ui/form-elements";
import { TierBadge } from "./commons";
import * as Yup from "yup";
import ErrorLabel from "../../ui/error-label";
import TokenForm from "./token-form";
import SuccessLabel from "../../ui/success-label";
import { KYCInfo, Tier } from "../../../assets/interfaces";
import { WHATSAPP_LINK } from "../../../assets/js/utils/constants";
import { phoneValidation } from "@/assets/js/utils/common-validations";
import dayjs from "dayjs";

interface IProps {
  isActive: boolean;
  kycInfo: KYCInfo;
  setKycInfo: (kycInfo: KYCInfo) => void;
  next: () => void;
  phone: string;
}

const KYCPhoneInfo: React.FC<IProps> = ({ isActive, kycInfo, setKycInfo, next, phone }) => {
  const [hasLookup, setHasLookup] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [showContactSupport, setShowContactSupport] = useState(false);
  const lookupPhoneReq = useRequest<LookupPhoneParams>(LookupPhone);
  const verifyPhoneReq = useRequest<VerifyPhoneParams>(VerifyPhone);
  const updateDobReq = useRequest<UpdateDobParams>(UpdateDob);
  const resendTokenReq = useRequest<ResendPhoneTokenParams>(ResendPhoneToken);

  const form = useFormik({
    initialValues: {
      phone: kycInfo?.phone !== "" ? phoneObjectFromString(kycInfo?.phone) : phoneObjectFromString(phone),
      dob: kycInfo?.dob ?? "",
    },
    enableReinitialize: true,
    onSubmit: async (values) => {
      if (isVerified && kycInfo?.dob !== form.values.dob) {
        const [res, err] = await updateDobReq.makeRequest({
          dob: values.dob,
        });

        if (res) {
          setKycInfo(res?.data);
          setHasLookup(true);
          next();
        }
      }

      if (!hasLookup && !isVerified) {
        const [res, err] = await lookupPhoneReq.makeRequest({
          phone: phoneObjectToString(values.phone),
          dob: values.dob,
        });
        if (res) {
          setHasLookup(true);
        }
        if (err && err.statusCode == 412) {
          setShowContactSupport(true);
        }
      }

      if (verifyPhoneReq.response) {
        setKycInfo(verifyPhoneReq.response?.data);
        // next();
      }

      if (isVerified) {
        next();
      }
    },
    validationSchema,
  });

  useEffect(() => {
    if (kycInfo?.phone_verified && kycInfo?.phone) {
      setIsVerified(true);
    }
  }, [kycInfo]);

  const getButtonLabel = () => {
    if (isVerified && form.values.dob === verifyPhoneReq?.response?.data?.dob) {
      return "Next";
    }

    if (isVerified && kycInfo?.dob !== form.values.dob) {
      return "Update Date of Birth";
    }

    if (isVerified) return "Next";

    if (!hasLookup && lookupPhoneReq.isLoading) {
      return "Please wait...";
    }

    if (!hasLookup) {
      return "Verify Phone Number";
    }

    if (hasLookup) {
      return "Next";
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="w-full max-w-[550px] mx-auto py-12.5 sm:pt-16 lg:pt-20 text-center !pb-48">
        {/* <TierBadge className="text-primary-500 bg-primary-500" tier={Tier.TIER_1} /> */}
        <figure className="h-[70px] w-[70px] sm:h-20 sm:w-20 bg-accent-green-500 flex items-center justify-center rounded-full mx-auto mb-3.75">
          {/* prettier-ignore */}
          <svg className="w-7.5 sm:w-9 text-white" viewBox="0 0 24 24" fill="none">
            <path d="M11.05 14.95L9.2 16.8C8.81 17.19 8.19 17.19 7.79 16.81C7.68 16.7 7.57 16.6 7.46 16.49C6.43 15.45 5.5 14.36 4.67 13.22C3.85 12.08 3.19 10.94 2.71 9.81C2.24 8.67 2 7.58 2 6.54C2 5.86 2.12 5.21 2.36 4.61C2.6 4 2.98 3.44 3.51 2.94C4.15 2.31 4.85 2 5.59 2C5.87 2 6.15 2.06 6.4 2.18C6.66 2.3 6.89 2.48 7.07 2.74L9.39 6.01C9.57 6.26 9.7 6.49 9.79 6.71C9.88 6.92 9.93 7.13 9.93 7.32C9.93 7.56 9.86 7.8 9.72 8.03C9.59 8.26 9.4 8.5 9.16 8.74L8.4 9.53C8.29 9.64 8.24 9.77 8.24 9.93C8.24 10.01 8.25 10.08 8.27 10.16C8.3 10.24 8.33 10.3 8.35 10.36C8.53 10.69 8.84 11.12 9.28 11.64C9.73 12.16 10.21 12.69 10.73 13.22C10.83 13.32 10.94 13.42 11.04 13.52C11.44 13.91 11.45 14.55 11.05 14.95Z" fill="currentColor"/>
            <path d="M21.9696 18.3291C21.9696 18.6091 21.9196 18.8991 21.8196 19.1791C21.7896 19.2591 21.7596 19.3391 21.7196 19.4191C21.5496 19.7791 21.3296 20.1191 21.0396 20.4391C20.5496 20.9791 20.0096 21.3691 19.3996 21.6191C19.3896 21.6191 19.3796 21.6291 19.3696 21.6291C18.7796 21.8691 18.1396 21.9991 17.4496 21.9991C16.4296 21.9991 15.3396 21.7591 14.1896 21.2691C13.0396 20.7791 11.8896 20.1191 10.7496 19.2891C10.3596 18.9991 9.96961 18.7091 9.59961 18.3991L12.8696 15.1291C13.1496 15.3391 13.3996 15.4991 13.6096 15.6091C13.6596 15.6291 13.7196 15.6591 13.7896 15.6891C13.8696 15.7191 13.9496 15.7291 14.0396 15.7291C14.2096 15.7291 14.3396 15.6691 14.4496 15.5591L15.2096 14.8091C15.4596 14.5591 15.6996 14.3691 15.9296 14.2491C16.1596 14.1091 16.3896 14.0391 16.6396 14.0391C16.8296 14.0391 17.0296 14.0791 17.2496 14.1691C17.4696 14.2591 17.6996 14.3891 17.9496 14.5591L21.2596 16.9091C21.5196 17.0891 21.6996 17.2991 21.8096 17.5491C21.9096 17.7991 21.9696 18.0491 21.9696 18.3291Z" fill="currentColor"/>
          </svg>
        </figure>
        <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold">
          <b className="font-bold">Verify Phone number</b>
        </h2>
        <p className="text-sm text-dark mt-1.25 max-w-[250px] mx-auto">
          We&apos;ll need your Phone nubmer to enable payments for your business
        </p>
        <form onSubmit={form.handleSubmit} className="max-w-[450px] mx-auto mt-6 sm:mt-7.5">
          <ErrorLabel error={lookupPhoneReq?.error?.message} perm />
          {isVerified && (
            <div className="text-sm flex items-center justify-center text-dark bg-grey-fields-100 py-3 w-full rounded-10 mb-5">
              {/* prettier-ignore */}
              <svg viewBox="0 0 18 18" fill="none" className="w-4 mr-2 animate-ping-once">
                <rect width="18" height="18" rx="9" fill="#39B588"/>
                <path d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z" fill="white"/>
              </svg>

              <span className="inline-block font-medium">Phone Verified</span>
            </div>
          )}
          <PhoneInput
            label="Your Phone number"
            name="phone"
            type="number"
            {...getFieldvalues("phone", form)}
            disabled={isVerified}
            inputMode="numeric"
          />
          <DatePickerInput
            label="Your Date of birth"
            {...getFieldvalues("dob", form)}
            value={ddMMYYYYToDate(form.values.dob ?? "")}
            onChange={(e) => form.setFieldValue("dob", dateToDDMMYYYY(e.target.value))}
            format="dd-mm-yyyy"
            maxDate={dayjs().subtract(18, "year").toDate()}
            minDate={dayjs().subtract(120, "year").toDate()}
          />

          {lookupPhoneReq.response && !isVerified && (
            <div className="mt-4">
              <div className="w-full bg-grey-fields-100 text-accent-green-500 text-1xs font-medium py-2 px-2.5 text-center mb-6 rounded-5">
                We&apos;ve sent a token to {removeCountryCode(lookupPhoneReq?.response?.data?.phone)}
              </div>
              {/* <CodeInput /> */}
              <TokenForm
                value={phoneObjectToString(form.values.phone)}
                verifyTokenReq={verifyPhoneReq}
                setIsVerified={setIsVerified}
                fieldKey="phone"
                resendTokenReq={resendTokenReq}
              />
            </div>
          )}
          <div className="mt-6 sm:mt-7.5 space-y-3.75">
            {showContactSupport && (
              <AppBtn
                isBlock
                size="lg"
                color="neutral"
                href={`${WHATSAPP_LINK}&text=Hi, I'm unable to verify my phone number. My phone number is ${phoneObjectToString(
                  form.values.phone
                )}`}
              >
                Contact Support
              </AppBtn>
            )}
            <AppBtn
              isBlock
              size="lg"
              className=""
              type="submit"
              disabled={lookupPhoneReq?.isLoading ?? updateDobReq?.isLoading}
            >
              {getButtonLabel()}
            </AppBtn>
          </div>
        </form>
      </div>
    </div>
  );
};

const validationSchema = Yup.object().shape({
  phone: phoneValidation("phone"),
  dob: Yup.string().required("Date of birth is required"),
});

export default KYCPhoneInfo;
