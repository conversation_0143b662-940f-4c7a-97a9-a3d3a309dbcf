import { useFormik } from "formik";
import React from "react";
import { SavekYCBasicInfoParams } from "../../../api/interfaces/store.kyc.interface";
import { SaveKYCBasicInfo } from "../../../api/store.kyc";
import { useRequest } from "../../../api/utils";
import { getFieldvalues } from "../../../assets/js/utils/functions";
import { AppBtn } from "../../ui/buttons";
import { InputField } from "../../ui/form-elements";
import * as Yup from "yup";
import ErrorLabel from "../../ui/error-label";
import { COUNTRIES, KYCInfo } from "../../../assets/interfaces";

interface IProps {
  isActive: boolean;
  next: () => void;
  kycInfo: KYCInfo;
  setKycInfo: (kycInfo: KYCInfo) => void;
  user?: any;
  country: COUNTRIES;
}

const KYCBasicInfo: React.FC<IProps> = ({ isActive, next, kycInfo, setKycInfo, user, country }) => {
  const { response, error, makeRequest, isLoading } = useRequest<SavekYCBasicInfoParams>(SaveKYCBasicInfo);
  const bvnExists = Boolean(kycInfo?.bvn);
  const form = useFormik({
    initialValues: {
      first_name: kycInfo?.first_name ?? user?.name.split(" ")[0] ?? "",
      last_name: kycInfo?.last_name ?? user?.name.split(" ")[1] ?? "",
    },
    onSubmit: async (values) => {
      const [res, err] = await makeRequest(values);

      if (res) {
        setKycInfo(res?.data);
        next();
      }
    },
    validationSchema,
  });

  return (
    <div className="w-full h-full max-w-[550px] overflow-y-auto mx-auto py-12.5 sm:pt-16 lg:pt-20 text-center">
      <div className="flex flex-col items-center">
        <figure className="h-[70px] w-[70px] sm:h-20 sm:w-20 bg-accent-green-500 flex items-center justify-center rounded-full">
          {/* prettier-ignore */}
          <svg className="w-7.5 sm:w-9 text-white" viewBox="0 0 24 24" fill="none">
            <path d="M12 2C9.38 2 7.25 4.13 7.25 6.75C7.25 9.32 9.26 11.4 11.88 11.49C11.96 11.48 12.04 11.48 12.1 11.49C12.12 11.49 12.13 11.49 12.15 11.49C12.16 11.49 12.16 11.49 12.17 11.49C14.73 11.4 16.74 9.32 16.75 6.75C16.75 4.13 14.62 2 12 2Z" fill="currentColor"/>
            <path d="M17.0809 14.1489C14.2909 12.2889 9.74094 12.2889 6.93094 14.1489C5.66094 14.9989 4.96094 16.1489 4.96094 17.3789C4.96094 18.6089 5.66094 19.7489 6.92094 20.5889C8.32094 21.5289 10.1609 21.9989 12.0009 21.9989C13.8409 21.9989 15.6809 21.5289 17.0809 20.5889C18.3409 19.7389 19.0409 18.5989 19.0409 17.3589C19.0309 16.1289 18.3409 14.9889 17.0809 14.1489Z" fill="currentColor"/>
          </svg>
        </figure>
        <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-light mt-2.5">
          {" "}
          Setup Payments <br /> <b className="font-bold">What is your Legal Name?</b>
        </h2>
        <span className="text-1sm text-base text-dark inline-block mt-1.5">Your name on Government IDs</span>
      </div>
      {/* <p className="text-sm text-dark mt-1.25">Your legal name.</p> */}
      <form className="max-w-[450px] mx-auto mt-7.5 sm:mt-9" onSubmit={form.handleSubmit}>
        <ErrorLabel error={error?.message} />
        <div className="bg-grey-fields-100 border-grey-border mb-2.5 rounded-15">
          <div className="flex items-start p-2.5">
            <figure className="h-9 w-9 rounded-full bg-white shadow-pill flex items-center justify-center flex-shrink-0">
              {/* prettier-ignore */}
              <svg width="60%" className="text-accent-yellow-500" viewBox="0 0 24 24" fill="none">
                <path d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM11.25 8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V13C12.75 13.41 12.41 13.75 12 13.75C11.59 13.75 11.25 13.41 11.25 13V8ZM12.92 16.38C12.87 16.51 12.8 16.61 12.71 16.71C12.61 16.8 12.5 16.87 12.38 16.92C12.26 16.97 12.13 17 12 17C11.87 17 11.74 16.97 11.62 16.92C11.5 16.87 11.39 16.8 11.29 16.71C11.2 16.61 11.13 16.51 11.08 16.38C11.03 16.26 11 16.13 11 16C11 15.87 11.03 15.74 11.08 15.62C11.13 15.5 11.2 15.39 11.29 15.29C11.39 15.2 11.5 15.13 11.62 15.08C11.86 14.98 12.14 14.98 12.38 15.08C12.5 15.13 12.61 15.2 12.71 15.29C12.8 15.39 12.87 15.5 12.92 15.62C12.97 15.74 13 15.87 13 16C13 16.13 12.97 16.26 12.92 16.38Z" fill="currentColor"/>
              </svg>
            </figure>
            <div className="ml-2.5 text-dark text-1xs text-left">
              <b className="font-semibold text-black-secondary">Important!</b> <br />
              <span className="">
                - Please enter your real name, not your business name.
                {country === COUNTRIES.NG ? (
                  <>
                    <br />- Ensure that your store name is correct, as it&apos;ll be used to create your account
                  </>
                ) : null}
              </span>
            </div>
          </div>
        </div>
        <InputField label="Enter First Name" {...getFieldvalues("first_name", form)} disabled={bvnExists} />
        <InputField label="Enter Last Name (Surname)" {...getFieldvalues("last_name", form)} disabled={bvnExists} />
        <AppBtn isBlock size="lg" className="mt-6 sm:mt-7.5" type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : "Next"}
        </AppBtn>
      </form>
    </div>
  );
};

const validationSchema = Yup.object().shape({
  first_name: Yup.string().required("First name is required"),
  last_name: Yup.string().required("Last name is required"),
});

export default KYCBasicInfo;
