import classNames from "classnames";
import React from "react";

interface IProps {
  next: () => void;
  previous: () => void;
  steps: number;
  step: number;
  canPrevious: boolean;
  canNext: boolean;
  isManual?: boolean;
}

const KYCFooter: React.FC<IProps> = ({ next, previous, step, steps, canNext, canPrevious, isManual = false }) => {
  const start = isManual ? 0 : 1;

  const percentage = (((step > steps - 2 ? steps - 2 : step) - start) / (steps - 2 - start)) * 100;
  const classes = classNames(
    "absolute w-full bg-white bottom-0 border-t border-grey-border border-opacity-50 transform transition-all ease-in-out duration-300",
    {
      "-bottom-full opacity-0": step < start || step >= steps - 2 - start,
      "bottom-0 opacity-100": step >= start,
    }
  );

  return (
    <div className={classes} style={{ transitionDelay: "250ms" }}>
      {step > 0 && (
        <div className="py-4 sm:py-5 px-5 sm:px-6.25 lg:px-7.5 flex items-center space-x-5 sm:space-x-8 lg:space-x-12.5 w-full">
          <div className="flex-1">
            <span className="text-black text-sm font-medium">
              {step} of {steps - 2 - start} complete
            </span>
            <div className="w-full h-2.5 bg-grey-fields-100 rounded-2xl mt-2">
              <div
                className="bg-accent-green-500 h-full rounded-3xl transition-all ease-out duration-200"
                style={{ width: `${percentage}%` }}
              ></div>
            </div>
          </div>
          <div className="ml-auto flex items-center space-x-3.5 mt-0">
            <button
              className={`h-12.5 w-12.5 flex items-center justify-center rounded-full bg-white text-primary-500 shadow-btn relative overflow-hidden ${
                canPrevious ? "" : "is-disabled"
              }`}
              onClick={previous}
              disabled={!canPrevious}
            >
              {/* prettier-ignore */}
              <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                <path d="M13 7H1" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 1L1 7L7 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <button
              className={`h-12.5 w-12.5 flex items-center justify-center rounded-full bg-primary-500 text-white shadow-btn relative overflow-hidden ${
                canNext ? "" : "is-disabled"
              }`}
              onClick={next}
              disabled={!canNext}
            >
              {/* prettier-ignore */}
              <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                <path d="M1 7H13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 1L13 7L7 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default KYCFooter;
