import { useFormik } from "formik";
import React, { useEffect, useState } from "react";
import { LookupBVNParams, VerifyBVNParms } from "../../../api/interfaces/store.kyc.interface";
import { LookupBVN, ResendBVNToken, VerifyBVN } from "../../../api/store.kyc";
import { useRequest } from "../../../api/utils";
import { dateToDDMMYYYY, ddMMYYYYToDate, getFieldvalues } from "../../../assets/js/utils/functions";
import { AppBtn } from "../../ui/buttons";
import { DatePickerInput, InputField, SelectDropdown } from "../../ui/form-elements";
import { TierBadge } from "./commons";
import * as Yup from "yup";
import ErrorLabel from "../../ui/error-label";
import TokenForm from "./token-form";
import { KYCInfo, Tier } from "../../../assets/interfaces";
import { WHATSAPP_LINK } from "../../../assets/js/utils/constants";
import dayjs from "dayjs";
import Portal from "@/components/portal";
import { BVNExplainerModal } from "./modals/bvn-explainer-modal";
import { useModals } from "@/components/hooks/useModals";

interface IProps {
  isActive: boolean;
  kycInfo: KYCInfo;
  setKycInfo: (kycInfo: KYCInfo) => void;
  next: () => void;
}

const KYCBVNInfo: React.FC<IProps> = ({ kycInfo, setKycInfo, next }) => {
  const [hasLookup, setHasLookup] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [showContactSupport, setShowContactSupport] = useState(false);
  const lookupBvnReq = useRequest<LookupBVNParams>(LookupBVN);
  const verifyBVNReq = useRequest<VerifyBVNParms>(VerifyBVN);
  const resendTokenReq = useRequest(ResendBVNToken);
  const { modals, toggleModal } = useModals(["bvn"]);

  const form = useFormik({
    initialValues: { bvn: kycInfo?.bvn, dob: kycInfo?.dob },
    onSubmit: async (values) => {
      if (!hasLookup && !isVerified) {
        const [res, err] = await lookupBvnReq.makeRequest(values);

        if (res) {
          setHasLookup(true);
        }

        if (err && err.statusCode === 412) {
          setShowContactSupport(true);
        }
      }

      if (verifyBVNReq.response) {
        setKycInfo(verifyBVNReq.response?.data);
        // next();
      }

      if (isVerified) {
        next();
      }
    },
    validationSchema,
  });

  useEffect(() => {
    if (kycInfo?.bvn_verified_at && kycInfo?.bvn) {
      setIsVerified(true);
    }
  }, [kycInfo]);

  const getButtonLabel = () => {
    if (!hasLookup && lookupBvnReq.isLoading) {
      return "Please wait...";
    }

    if (isVerified) {
      return "Next";
    }

    if (!hasLookup) {
      return "Verify BVN";
    }

    if (hasLookup) {
      return "Next";
    }
  };

  return (
    <>
      <div className="h-full overflow-y-auto">
        <div className="w-full max-w-[550px] mx-auto py-12.5 sm:pt-16 lg:pt-20 text-center !pb-48">
          {/* <TierBadge className="text-primary-500 bg-primary-500" tier={Tier.TIER_1} /> */}
          <figure className="h-[70px] w-[70px] sm:h-20 sm:w-20 bg-accent-green-500 flex items-center justify-center rounded-full mb-3.75 mx-auto">
            {/* prettier-ignore */}
            <svg className="w-7.5 sm:w-9 text-white" viewBox="0 0 24 24" fill="none">
              <path d="M19.4491 6.94063V9.45062C19.4491 10.1606 18.7291 10.6206 18.0591 10.3706C17.2191 10.0606 16.2891 9.94062 15.3091 10.0406C12.9291 10.3006 10.4891 12.5906 10.0891 14.9606C9.75906 16.9306 10.3891 18.7706 11.5991 20.0706C12.1491 20.6706 11.7791 21.6406 10.9691 21.7306C10.2791 21.8106 9.59906 21.7906 9.21906 21.5106L3.71906 17.4006C3.06906 16.9106 2.53906 15.8506 2.53906 15.0306V6.94063C2.53906 5.81063 3.39906 4.57063 4.44906 4.17063L9.94906 2.11062C10.5191 1.90063 11.4591 1.90063 12.0291 2.11062L17.5291 4.17063C18.5891 4.57063 19.4491 5.81063 19.4491 6.94063Z" fill="currentColor"/>
              <path d="M16 11.5117C13.52 11.5117 11.5 13.5317 11.5 16.0117C11.5 18.4917 13.52 20.5117 16 20.5117C18.48 20.5117 20.5 18.4917 20.5 16.0117C20.5 13.5217 18.48 11.5117 16 11.5117Z" fill="currentColor"/>
              <path d="M21 22.0009C20.73 22.0009 20.48 21.8909 20.29 21.7109C20.25 21.6609 20.2 21.6109 20.17 21.5509C20.13 21.5009 20.1 21.4409 20.08 21.3809C20.05 21.3209 20.03 21.2609 20.02 21.2009C20.01 21.1309 20 21.0709 20 21.0009C20 20.8709 20.03 20.7409 20.08 20.6209C20.13 20.4909 20.2 20.3909 20.29 20.2909C20.52 20.0609 20.87 19.9509 21.19 20.0209C21.26 20.0309 21.32 20.0509 21.38 20.0809C21.44 20.1009 21.5 20.1309 21.55 20.1709C21.61 20.2009 21.66 20.2509 21.71 20.2909C21.8 20.3909 21.87 20.4909 21.92 20.6209C21.97 20.7409 22 20.8709 22 21.0009C22 21.2609 21.89 21.5209 21.71 21.7109C21.66 21.7509 21.61 21.7909 21.55 21.8309C21.5 21.8709 21.44 21.9009 21.38 21.9209C21.32 21.9509 21.26 21.9709 21.19 21.9809C21.13 21.9909 21.06 22.0009 21 22.0009Z" fill="currentColor"/>
            </svg>
          </figure>
          <div className="flex flex-col items-center  max-w-[250px] mx-auto">
            <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold">Your BVN</h2>
            <button
              className="flex items-center text-primary-500 font-medium text-sm mt-1.5 outline-none"
              onClick={() => toggleModal("bvn")}
            >
              Why is my BVN needed?
              {/* prettier-ignore */}
              <svg className="w-2 sm:w-2.5 ml-1" viewBox="0 0 10 10" fill="none">
                <path d="M1.24264 0.24265V1.73818L7.16643 1.74348L0.71231 8.1976L1.77297 9.25826L8.22709 2.80414L8.23239 8.72793H9.72792V0.24265H1.24264Z" fill="#332089"/>
              </svg>
            </button>
          </div>
          <form onSubmit={form.handleSubmit} className="max-w-[450px] mx-auto mt-6 sm:mt-7.5">
            <ErrorLabel error={lookupBvnReq?.error?.message} perm />
            {isVerified && (
              <div className="text-sm flex items-center justify-center text-dark bg-grey-fields-100 py-3 w-full rounded-10 mb-5">
                {/* prettier-ignore */}
                <svg viewBox="0 0 18 18" fill="none" className="w-4 mr-2 animate-ping-once">
                  <rect width="18" height="18" rx="9" fill="#39B588"/>
                  <path d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z" fill="white"/>
                </svg>

                <span className="inline-block font-medium">BVN Verified</span>
              </div>
            )}
            <InputField
              label="Your BVN number"
              name="bvn"
              type="number"
              {...getFieldvalues("bvn", form)}
              onChange={(e) => form.setFieldValue("bvn", String(e.target.value))}
              disabled={isVerified}
              inputMode="numeric"
            />
            <DatePickerInput
              label="Your Date of birth"
              {...getFieldvalues("dob", form)}
              value={ddMMYYYYToDate(form.values.dob ?? "")}
              onChange={(e) => form.setFieldValue("dob", dateToDDMMYYYY(e.target.value))}
              format="dd-mm-yyyy"
              disabled={isVerified}
              maxDate={dayjs().subtract(18, "year").toDate()}
              minDate={dayjs().subtract(120, "year").toDate()}
            />

            {lookupBvnReq.response && !isVerified && (
              <div className="mt-4">
                <div className="w-full bg-grey-fields-100 text-accent-green-500 text-1xs font-medium py-2 px-2.5 text-center mb-6 rounded-5">
                  We&apos;ve sent a token to{" "}
                  {lookupBvnReq?.response?.data?.email ? lookupBvnReq?.response?.data?.email : ""},{" "}
                  {lookupBvnReq?.response?.data?.email ? "and" : ""}{" "}
                  {lookupBvnReq?.response?.data?.phone ? lookupBvnReq?.response?.data?.phone : ""}
                </div>
                {/* <CodeInput /> */}
                <TokenForm
                  fieldKey="bvn"
                  value={form.values.bvn}
                  verifyTokenReq={verifyBVNReq}
                  setIsVerified={setIsVerified}
                  resendTokenReq={resendTokenReq}
                />
              </div>
            )}
            <div className="mt-6 sm:mt-7.5 space-y-3.75">
              {showContactSupport && (
                <AppBtn
                  isBlock
                  size="lg"
                  color="neutral"
                  href={`${WHATSAPP_LINK}&text=Hi, I'm unable to verify my BVN`}
                >
                  Contact Support
                </AppBtn>
              )}
              <AppBtn isBlock size="lg" className="" type="submit" disabled={lookupBvnReq?.isLoading}>
                {getButtonLabel()}
              </AppBtn>
            </div>
          </form>
        </div>
      </div>
      <Portal>
        <BVNExplainerModal show={modals.bvn.show} toggle={() => toggleModal("bvn")} />
      </Portal>
    </>
  );
};

const validationSchema = Yup.object().shape({
  bvn: Yup.string()
    .required("BVN is required")
    .test("digits", "BVN should contain only digits", (value) => /^\d+$/.test(value))
    .length(11, "BVN should be 11 digits"),
});

export default KYCBVNInfo;
