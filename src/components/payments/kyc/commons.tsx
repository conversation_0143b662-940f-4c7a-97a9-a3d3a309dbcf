import React from "react";
import { Tier } from "../../../assets/interfaces";
import { emit } from "../../hooks/useListener";

interface TierBadgeProps {
  className: string;
  tier: Tier;
}

const TierBadge: React.FC<TierBadgeProps> = ({ className, tier }) => {
  const toggleModal = () => {
    emit("show-tier-modal", tier);
  };

  return (
    <button
      className={`flex items-center mx-auto bg-opacity-10 p-2 rounded-[25px] mb-3.75 ${className}`}
      onClick={toggleModal}
    >
      <figure className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
        {/* prettier-ignore */}
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
          <path d="M6.285 9L8.0925 10.815L11.715 7.185" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.0625 1.83749C8.58 1.39499 9.4275 1.39499 9.9525 1.83749L11.1375 2.85749C11.3625 3.05249 11.7825 3.20999 12.0825 3.20999H13.3575C14.1525 3.20999 14.805 3.86249 14.805 4.65749V5.93249C14.805 6.22499 14.9625 6.65249 15.1575 6.87749L16.1775 8.06249C16.62 8.57999 16.62 9.42749 16.1775 9.95249L15.1575 11.1375C14.9625 11.3625 14.805 11.7825 14.805 12.0825V13.3575C14.805 14.1525 14.1525 14.805 13.3575 14.805H12.0825C11.79 14.805 11.3625 14.9625 11.1375 15.1575L9.9525 16.1775C9.435 16.62 8.5875 16.62 8.0625 16.1775L6.8775 15.1575C6.6525 14.9625 6.2325 14.805 5.9325 14.805H4.635C3.84 14.805 3.1875 14.1525 3.1875 13.3575V12.075C3.1875 11.7825 3.03 11.3625 2.8425 11.1375L1.83 9.94499C1.395 9.42749 1.395 8.58749 1.83 8.06999L2.8425 6.87749C3.03 6.65249 3.1875 6.23249 3.1875 5.93999V4.64999C3.1875 3.85499 3.84 3.20249 4.635 3.20249H5.9325C6.225 3.20249 6.6525 3.04499 6.8775 2.84999L8.0625 1.83749Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
      </figure>
      <span className="inline-block text-black-300 font-medium text-sm mx-1">{tierMap[tier]}</span>
      {/* prettier-ignore */}
      <svg className="w-4" viewBox="0 0 15 16" fill="none">
        <path d="M3.96484 11.5355L11.0359 4.46446" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M11.0352 11.5355L11.0352 4.46446L3.96409 4.46445" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    </button>
  );
};

const tierMap = {
  [Tier.TIER_1]: "Tier 1",
  [Tier.TIER_2]: "Tier 2",
  [Tier.TIER_3]: "Tier 3",
};

export { TierBadge };
