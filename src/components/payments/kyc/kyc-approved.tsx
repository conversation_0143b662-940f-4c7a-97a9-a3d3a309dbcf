import React, { useEffect, useState } from "react";
import ContentWithCopy from "../../ui/content-with-copy";
import SuccessAnimation from "../../ui/success-animation";
import { AccountInformation, KYCInfo } from "../../../assets/interfaces";
import DataAccordion from "../../ui/data-accordion";
import { stepD<PERSON>, <PERSON><PERSON><PERSON> } from "../invoices/dashboard/setup-progress";
import { AppBtn, RoundActionBtn } from "../../ui/buttons";
import router from "next/router";
import LazyImage from "../../lazy-image";
import authContext from "../../../contexts/auth-context";
import { useFetcher } from "../../../api/utils";
import { useModals } from "../../hooks/useModals";
import { GetPaymentsSetupProgress, GetStoreWallet } from "../../../api";
import SuccessIcon from "../../../assets/icons/statuses/success.svg";
import Portal from "../../portal";
import classNames from "classnames";
import useCopyClipboard from "@/components/hooks/useCopyClipboard";
import { subdomainStoreLink } from "@/assets/js/utils/functions";
import MakeTestPaymentModal from "@/components/wallets/test-payment/modal";

interface Props {
  submitResponse: any;
  kycInfo: KYCInfo;
}

const KYCApproved: React.FC<Props> = ({ submitResponse, kycInfo }) => {
  const { store } = authContext.useContainer();
  const { response } = useFetcher(GetPaymentsSetupProgress, []);
  const { modals, toggleModal } = useModals(["make_test_payment"]);
  const getStoreWalletReq = useFetcher(GetStoreWallet);
  const account: AccountInformation =
    submitResponse?.data?.accounts[0] ?? getStoreWalletReq?.response?.data?.accounts[0] ?? null;
  // const account: AccountInformation = submitResponse?.data?.account;

  const setupProgressData = { ...response?.data, has_made_test_payment: store?.onboarding_steps?.test_payment_made };
  const steps = (
    setupProgressData ? stepData.map((s) => ({ ...s, isCompleted: setupProgressData[s.key] })) : stepData
  ).sort((a, b) => (b.isCompleted ? -1 : 0));

  const handleStepClick = (step: StepKey) => {
    switch (step) {
      case "has_made_test_payment":
        toggleModal("make_test_payment");
        break;
      case "has_delivery_areas":
        router.push("/my-store/delivery-areas");
        break;
      case "has_withdrawal_accounts":
        router.push("/my-store/payments?tab=withdrawal_accounts");
        break;
      case "security_pin_added":
        router.push("/my-store/payments?tab=security_pin");
        break;
      case "has_enabled_direct_checkout":
        router.push("/my-store/payments");
        break;
    }
  };

  return (
    <div className={classNames("flex flex-col items-center w-full max-w-[370px] mx-auto mt-20 sm:mt-40 mb-10 sm:mb-0")}>
      <div className="flex flex-col items-center">
        <figure className="mb-5 sm:mb-7.5">
          <SuccessAnimation />
        </figure>
        <h3 className="text-black font-bold text-2xl sm:text-3lg lg:text-3xl text-center">You’ve been activated</h3>
        <p className="text-dark text-1xs sm:text-sm mt-2.5 text-center max-w-[350px]">
          Your KYC submission has been approved. <br />
          You can now create invoices and receive payments
        </p>
      </div>

      {account && (
        <article className="bg-grey-fields-100 rounded-10 sm:rounded-15 flex items-start p-3 sm:p-3.75 mt-6 sm:mt-7 w-full justify-between">
          <div>
            <span className="text-xs sm:text-1xs text-dark">{account?.bank_name}</span>
            <div>
              <ContentWithCopy className="flex items-center" text={account?.account_number}>
                <h3 className="text-black text-lg sm:text-xl font-bold">{account?.account_number}</h3>
              </ContentWithCopy>
            </div>
            <span className="text-xs sm:text-1xs uppercase text-black-secondary inline-block mt-1 font-medium">
              {account?.account_name}
            </span>
          </div>
          <figure className="h-9 w-9 sm:h-10 sm:w-10 rounded-full bg-white shadow-pill flex-shrink-0 overflow-hidden relative">
            <LazyImage
              src={account?.image}
              alt={account?.bank_name}
              className="w-full h-full"
              loaderClasses="rounded-full"
            />
          </figure>
        </article>
      )}

      <div className="mt-5 sm:mt-6.25 w-full">
        <DataAccordion isClosed={false} deactivated title={"Other things you can try"}>
          <ul className="flex flex-col mt-3 py-1.25 px-3.75 rounded-[12px] sm:rounded-15 border border-grey-divider divide-y divide-grey-divider w-full">
            {steps.map((step, index) => (
              <li
                className="flex items-center justify-between py-3.75 w-full group cursor-pointer"
                key={index}
                onClick={() => !step?.isCompleted && handleStepClick(step.key)}
              >
                <div className="flex items-center">
                  <figure
                    className={`h-9 w-9 sm:h-10 sm:w-10 rounded-full shadow-pill flex items-center justify-center bg-opacity-10 ${step.bg} ${step.color}`}
                  >
                    {step.icon}
                  </figure>
                  <div className="ml-2.5">
                    <h4 className="text-black font-bold text-1sm -mb-1">{step.title}</h4>
                    <span className="text-xs sm:text-1xs text-dark">{step.description}</span>
                  </div>
                </div>
                {step?.isCompleted ? (
                  <div className="h-9 w-9 text-accent-green-500 bg-accent-green-500 bg-opacity-10 p-1.5 rounded-full">
                    {/* prettier-ignore */}
                    <svg width="100%" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM16.78 9.7L11.11 15.37C10.97 15.51 10.78 15.59 10.58 15.59C10.38 15.59 10.19 15.51 10.05 15.37L7.22 12.54C6.93 12.25 6.93 11.77 7.22 11.48C7.51 11.19 7.99 11.19 8.28 11.48L10.58 13.78L15.72 8.64C16.01 8.35 16.49 8.35 16.78 8.64C17.07 8.93 17.07 9.4 16.78 9.7Z" fill="currentColor"/>
                    </svg>
                  </div>
                ) : (
                  <RoundActionBtn>
                    {/* prettier-ignore */}
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="text-primary-500 transform ease-out transition-transform duration-200 group-hover:translate-x-0.5">
                    <path d="M3.33331 8H7.99998H12.6666" stroke="currentColor" strokeLinecap="round" strokeWidth={1.5} strokeLinejoin="round"/>
                    <path d="M8 3.33334L12.6667 8L8 12.6667" stroke="currentColor" strokeLinecap="round" strokeWidth={1.5} strokeLinejoin="round"/>
                  </svg>
                  </RoundActionBtn>
                )}
              </li>
            ))}
          </ul>
        </DataAccordion>
      </div>

      <div className="bg-white w-full fixed bottom-0 py-2.5 px-3.5 sm:!p-0 sm:relative border-t border-grey-divider z-999">
        <AppBtn href="/dashboard" size="lg" isBlock>
          Continue to dashboard
        </AppBtn>
      </div>

      <Portal>
        <MakeTestPaymentModal
          show={modals.make_test_payment.show}
          country={kycInfo?.country}
          toggle={() => toggleModal("make_test_payment")}
        />
      </Portal>
    </div>
  );
};

export default KYCApproved;
