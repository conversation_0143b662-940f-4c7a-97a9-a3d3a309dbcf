import { PAYMENT_STATUS, PaymentInterface } from "@/assets/interfaces";
import { toCurrency } from "../../../assets/js/utils/functions";
import Badge from "../../ui/badge";
import { RoundActionBtn } from "../../ui/buttons";
import Dropdown, { DropdownItem } from "../../ui/dropdown-new";
import { toNaira } from "@/assets/js/utils/utils";
import { getPaymentBadgeStatusColor } from "./payment-item";

interface PaymentItemMobileProps {
  index?: number;
  payment: PaymentInterface;
  onAction?: (action: "click" | "edit" | "delete" | "download" | "copy-link") => void;
}
export const PaymentItemMobile: React.FC<PaymentItemMobileProps> = ({ index, payment, onAction }) => {
  const dropdownItems: DropdownItem[] = [
    {
      text: "Download Receipt",
      link: undefined,
      onClick: () => {
        onAction("download");
      },
      icon: <RoundActionBtn icon="download" size="sm" />,
      skip: payment.status !== PAYMENT_STATUS.SUCCESS || !payment?.invoice?.receipt,
    },
  ];

  const discountColors = ["accent-orange-500", "accent-green-500", "accent-red-500", "accent-yellow-500"];
  const colorClass = discountColors[index % discountColors.length];

  return (
    <li className="rounded-10 border border-grey-divider p-3 mb-2.5 cursor-pointer" onClick={() => onAction("click")}>
      <div className="relative flex items-center">
        <figure
          className={`flex-shrink-0 text-${colorClass} bg-${colorClass} bg-opacity-10 w-14 h-14 rounded-md flex items-center justify-center overflow-hidden`}
        >
          {/* prettier-ignore */}
          <svg width="55%" viewBox="0 0 24 24" fill="none">
            <path opacity="0.4" d="M19.3009 7.91998V13.07C19.3009 16.15 17.5409 17.47 14.9009 17.47H6.11093C5.66093 17.47 5.23093 17.43 4.83093 17.34C4.58093 17.3 4.34094 17.23 4.12094 17.15C2.62094 16.59 1.71094 15.29 1.71094 13.07V7.91998C1.71094 4.83998 3.47093 3.52002 6.11093 3.52002H14.9009C17.1409 3.52002 18.7509 4.47001 19.1809 6.64001C19.2509 7.04001 19.3009 7.44998 19.3009 7.91998Z" fill="currentColor"/>
            <path d="M22.3001 10.9201V16.0701C22.3001 19.1501 20.5401 20.4701 17.9001 20.4701H9.11008C8.37008 20.4701 7.70009 20.3701 7.12009 20.1501C5.93009 19.7101 5.12008 18.8001 4.83008 17.3401C5.23008 17.4301 5.66008 17.4701 6.11008 17.4701H14.9001C17.5401 17.4701 19.3001 16.1501 19.3001 13.0701V7.9201C19.3001 7.4501 19.2601 7.03014 19.1801 6.64014C21.0801 7.04014 22.3001 8.38011 22.3001 10.9201Z" fill="currentColor"/>
            <path d="M10.4994 13.1399C11.9574 13.1399 13.1394 11.9579 13.1394 10.4999C13.1394 9.04185 11.9574 7.85986 10.4994 7.85986C9.04136 7.85986 7.85938 9.04185 7.85938 10.4999C7.85938 11.9579 9.04136 13.1399 10.4994 13.1399Z" fill="currentColor"/>
            <path d="M4.7793 8.25C4.3693 8.25 4.0293 8.59 4.0293 9V12C4.0293 12.41 4.3693 12.75 4.7793 12.75C5.1893 12.75 5.5293 12.41 5.5293 12V9C5.5293 8.59 5.1993 8.25 4.7793 8.25Z" fill="currentColor"/>
            <path d="M16.2109 8.25C15.8009 8.25 15.4609 8.59 15.4609 9V12C15.4609 12.41 15.8009 12.75 16.2109 12.75C16.6209 12.75 16.9609 12.41 16.9609 12V9C16.9609 8.59 16.6309 8.25 16.2109 8.25Z" fill="currentColor"/>
          </svg>
        </figure>
        <div className="flex items-center justify-between ml-2.5 w-full">
          <div>
            <h6 className="text-sm text-dark max-w-[215px] sm:max-w-none whitespace-nowrap overflow-ellipsis overflow-hidden font-medium mb-2.5 mt-0.5">
              {payment?.customer?.name}
            </h6>
            <span className="text-1xs text-black  font-semibold">
              {toCurrency(toNaira(payment.amount), payment.currency)}
            </span>
          </div>
          {/* <div className="flex items-center justify-between"> */}
          <Badge
            {...{
              color: getPaymentBadgeStatusColor(payment.status),
              text: payment.status,
            }}
            className="absolute -bottom-0 right-0"
          />
          {/* </div> */}
        </div>
        <div className="absolute -top-1 right-0">
          <Dropdown items={dropdownItems} vPosition="BOTTOM" gap={false}>
            <button className="z-[999] dropdown-toggle text-black-400 p-2 -mt-2 -mr-2">
              {/* prettier-ignore */}
              <svg className="w-5 h-5" viewBox="0 0 20 20" fill="none">
                <g opacity="0.5">
                  <path d="M4.16667 8.73837C3.25 8.73837 2.5 9.48837 2.5 10.405C2.5 11.3217 3.25 12.0717 4.16667 12.0717C5.08333 12.0717 5.83333 11.3217 5.83333 10.405C5.83333 9.48837 5.08333 8.73837 4.16667 8.73837Z" fill="currentColor" />
                  <path d="M15.8337 8.73837C14.917 8.73837 14.167 9.48837 14.167 10.405C14.167 11.3217 14.917 12.0717 15.8337 12.0717C16.7503 12.0717 17.5003 11.3217 17.5003 10.405C17.5003 9.48837 16.7503 8.73837 15.8337 8.73837Z" fill="currentColor" />
                  <path d="M9.99967 8.73837C9.08301 8.73837 8.33301 9.48837 8.33301 10.405C8.33301 11.3217 9.08301 12.0717 9.99967 12.0717C10.9163 12.0717 11.6663 11.3217 11.6663 10.405C11.6663 9.48837 10.9163 8.73837 9.99967 8.73837Z" fill="currentColor" />
                </g>
              </svg>
            </button>
          </Dropdown>
        </div>
      </div>
    </li>
  );
};
