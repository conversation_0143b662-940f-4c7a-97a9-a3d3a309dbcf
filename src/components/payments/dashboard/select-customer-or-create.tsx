import { GetCustomers, GetCustomersBasic } from "@/api";
import { GetCustomersParams } from "@/api/interfaces";
import { useFetcher } from "@/api/utils";
import { removeCountryCode } from "@/assets/js/utils/functions";
import { AppBtn } from "@/components/ui/buttons";
import { DropdownOptionInterface } from "@/components/ui/form-elements/select-dropdown";
import Modal, { ModalBody, ModalFooter } from "@/components/ui/modal";
import StoreLogo from "@/components/ui/store-logo";
import classNames from "classnames";

interface Props {
  onSelect: () => void;
  toggle: VoidFunction;
  show: boolean;
}

export const SelectOrAddCustomerModal: React.FC<Props> = ({ onSelect, toggle, show }) => {
  const { response, error, isLoading, makeRequest } = useFetcher<GetCustomersParams>(GetCustomersBasic, {
    per_page: Number.MAX_SAFE_INTEGER,
  });
  return (
    <Modal title="Select or add a customer" show={show} toggle={toggle}>
      <ModalBody>
        {response?.data?.data?.map((c) => (
          <CustomerOption isOption customerName={c.name} customerPhone={c.phone} key={c.id} />
        ))}
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock>Create Customer</AppBtn>
      </ModalFooter>
    </Modal>
  );
};

export const CustomerOption: React.FC<{ customerName: string; customerPhone: string; isOption?: boolean }> = ({
  customerName,
  customerPhone,
  isOption,
}) => {
  return (
    <div className="flex items-center py-2.5 border-b border-grey-divider hover:bg-grey-fields-100 cursor-pointer">
      <StoreLogo
        className={classNames("mr-2", { "h-6 w-6 text-sm": !isOption, "h-10 w-10 text-lg": isOption })}
        storeName={customerName}
        logo={null}
      />
      <div className={classNames({ "ml-1.5": isOption })}>
        <span className={classNames("inline-block text-sm text-black-secondary", { "font-medium": isOption })}>
          {customerName ?? "Anonymous Customer"}
        </span>
        {isOption && (
          <div className="flex items-center mt-0.5">
            <span className="text-xs text-dark inline-block">{removeCountryCode(customerPhone)}</span>
          </div>
        )}
      </div>
    </div>
  );
};
