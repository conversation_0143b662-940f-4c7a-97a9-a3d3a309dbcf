import router from "next/router";
import { useEffect, useRef, useState } from "react";
import { GetInvoicesParams } from "../../../api/interfaces/invoices.interface";
import { DownloadInvoicePdf, GetInvoices } from "../../../api/invoices";
import { useFetcher } from "../../../api/utils";
import { InvoiceInterface, INVOICE_STATUSES } from "../../../assets/interfaces/invoices";
import { toAppUrl } from "../../../assets/js/utils/functions";
import authContext from "../../../contexts/auth-context";
import ClearSearch from "../../clear-search";
import useCopyClipboard from "../../hooks/useCopyClipboard";
import useFluxState from "../../hooks/useFluxState";
import { useModals } from "../../hooks/useModals";
import usePagination from "../../hooks/usePagination";
import usePDFDownloads from "../../hooks/usePDFDownloads";
import useScreenSize from "../../hooks/useScreenSize";
import useSearchParams from "../../hooks/useSearchParams";
import Portal from "../../portal";
import AppSearchBar from "@/components/ui/app-search-bar";
import { AppBtn } from "@/components/ui/buttons";
import Pagination from "@/components/ui/pagination";
import Table, { TableBody, TableHead, TableHeadItem } from "@/components/ui/table";
import PaymentsContentState from "./content-state";

// import { InvoiceItemMobile } from "./invoice-item-mobile";
import PaymentTableItem from "./payment-item";
import { DownloadReceiptPDF, GetInvoicePayments } from "@/api";
import { GetPaymentHistoryParams } from "@/api/interfaces";
import { PAYMENT_STATUS, PaymentInterface } from "@/assets/interfaces";
import useClickOutside from "@/components/hooks/useClickOutside";
import { PaymentItemMobile } from "./payment-item-mobile";
import CustomerDetailsModal from "@/components/orders/modals/customer";
import PaymentDetailsModal from "./payment-details";

interface Props {}

export const PaymentsTable: React.FC<Props> = ({}) => {
  const { storeId } = authContext.useContainer();
  const { width, isSmall } = useScreenSize();
  // const isSmall = width < 800;
  const searchBar = useRef(null);
  const PER_PAGE = 10;
  const { currentPage, goNext, goPrevious, setPage } = usePagination();
  const { modals, toggleModal } = useModals(["details", "customer"]);
  const [currentPayment, setCurrentPayment] = useState<number>(0);
  const [searchQuery, setSearchQuery] = useState("");
  const { search } = useSearchParams(["search"]);
  const getPaymentsHistoryReq = useFetcher<GetPaymentHistoryParams>(GetInvoicePayments, {
    page: currentPage,
    per_page: PER_PAGE,
    status: PAYMENT_STATUS.SUCCESS,
    search,
  });
  const payments: PaymentInterface[] = getPaymentsHistoryReq?.response?.data ?? [];
  const [fullViewOnMobile, setFullViewOnMobile] = useState(false);
  const selectedPayment = payments?.[currentPayment];
  const { isLoadingPdf, download } = usePDFDownloads({
    request: DownloadReceiptPDF,
    data: { receiptId: selectedPayment?.invoice?.receipt },
    filename: `receipt-${selectedPayment?.invoice?.receipt}`,
  });
  const [error, setError] = useState(undefined);
  const pageNotReady = getPaymentsHistoryReq.isLoading || getPaymentsHistoryReq.error || payments.length < 1;

  useClickOutside(searchBar, () => {
    setFullViewOnMobile(false);
  });

  const onAction = (action: string, index: number) => {
    if (!payments[index]) return;

    setCurrentPayment(index);
    switch (action) {
      case "click":
        toggleModal("details");
        break;
      case "download":
        download();
        break;
      default:
      //do nothing
    }
  };

  const updatePayments = () => {
    getPaymentsHistoryReq.makeRequest();
  };

  return (
    <div className="w-full pb-10">
      <div className="flex justify-between items-center pb-5 mt-3.75">
        <h4
          className={`text-black text-[17px] sm:text-lg lg:text-xl font-bold font-display ${
            fullViewOnMobile && "md:block hidden"
          }`}
        >
          All Payments
        </h4>
        <div ref={searchBar} className={`flex items-stretch ${fullViewOnMobile ? " flex-1 md:flex-none" : ""}`}>
          <AppSearchBar
            {...{
              placeholder: `Search by customer or ID`,
              searchQuery,
              setSearchQuery,
              fullViewOnMobile,
              setFullViewOnMobile,
            }}
          />
          <div className={`ml-3 md:ml-3.5 ${fullViewOnMobile ? "hidden sm:block" : "block"}`}>
            {!isSmall && (
              <AppBtn size="md" onClick={() => router.push("/payments/request-payment")}>
                Request a Payment
              </AppBtn>
            )}
            {isSmall && (
              <AppBtn
                size="md"
                onClick={() => router.push("/payments/request-payment")}
                className="!rounded-full !p-0 !h-9 !w-9"
              >
                {/* prettier-ignore */}
                <svg width={17} height={17} viewBox="0 0 17 17" fill="none" >
                <path d="M4.25 8.5h8.5M8.5 12.75v-8.5" stroke="#fff" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              </AppBtn>
            )}
          </div>
        </div>
      </div>
      <ClearSearch search={search} />
      {pageNotReady && <PaymentsContentState request={getPaymentsHistoryReq} isEmpty={payments.length < 1} />}

      {!pageNotReady && (
        <>
          {!isSmall && (
            <Table className="hidden sm:table">
              <TableHead>
                {/* <TableHeadItem>INVOICE ID</TableHeadItem> */}
                <TableHeadItem>Reference</TableHeadItem>
                <TableHeadItem>AMOUNT</TableHeadItem>
                <TableHeadItem>Customer</TableHeadItem>
                {/* <TableHeadItem>Total Paid</TableHeadItem> */}
                <TableHeadItem>STATUS</TableHeadItem>
                <TableHeadItem>DATE</TableHeadItem>
                <TableHeadItem>Actions</TableHeadItem>
              </TableHead>
              <TableBody>
                {payments.map((payment, index) => {
                  return (
                    <PaymentTableItem
                      payment={payment}
                      isLoadingPdf={false}
                      key={index}
                      onAction={(action: string) => onAction(action, index)}
                    />
                  );
                })}
              </TableBody>
            </Table>
          )}
          {isSmall && (
            <ul className="block md:hidden">
              {payments.map((payment, index) => {
                return (
                  <PaymentItemMobile
                    key={index}
                    index={index}
                    payment={payment}
                    onAction={(action: string) => onAction(action, index)}
                  />
                );
              })}
            </ul>
          )}
          <Pagination
            {...{
              goNext,
              goPrevious,
              per_page: PER_PAGE,
              label: "Payments",
              data: getPaymentsHistoryReq?.response,
              currentPage,
              length: payments?.length,
              setPage,
            }}
          />
        </>
      )}
      {selectedPayment && (
        <Portal>
          <PaymentDetailsModal
            show={modals.details.show}
            toggle={() => toggleModal("details")}
            payment={selectedPayment}
            toggleCustomerModal={() => toggleModal("customer")}
            updatePayments={updatePayments}
          />
          {selectedPayment && selectedPayment?.customer?.id && (
            <CustomerDetailsModal
              show={modals.customer.show}
              toggle={() => toggleModal("customer")}
              customer={selectedPayment?.customer}
            />
          )}
        </Portal>
      )}
    </div>
  );
};
