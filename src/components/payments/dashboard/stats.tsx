import classNames from "classnames";
import React from "react";
import { GetStoreOrderStatistics } from "@/api/orders-customers";
import { RequestInterface, useFetcher } from "@/api/utils";
import { getUserCountry, millify } from "@/assets/js/utils/functions";
import PageStatistics from "@/components/ui/page-stats";
import { CURRENCIES } from "@/assets/interfaces";
import Switcher from "@/components/ui/currency-switcher";
import { DropdownItem } from "@/components/ui/dropdown-new";
import { toKobo, toNaira } from "@/assets/js/utils/utils";
// import { DropdownItem } from "../ui/dropdown-new";

type StatsData = {
  total_payments: number;
  total_volume: number;
  average_daily_volume: number;
  average_volume: number;
};

interface Props {
  data: StatsData;
  statsReq: RequestInterface<{}>;
  currencies: CURRENCIES[];
  setCurrency: (currency: CURRENCIES) => void;
  currency: CURRENCIES;
}

export const PaymentStatistics: React.FC<Props> = ({ data, statsReq, currencies, setCurrency, currency }) => {
  const { isLoading, makeRequest, error } = statsReq;
  const stats = getStats(
    data ?? { total_payments: 0, total_volume: 0, average_daily_volume: 0, average_volume: 0 },
    currency
  );

  const switcherDropdownItems: DropdownItem[] = currencies.map((curr) => ({
    text: curr,
    link: undefined,
    onClick: () => setCurrency(curr),
    icon: null,
  }));

  return (
    <div className="relative pt-5">
      {currencies?.length > 1 && (
        <div className="mb-2.5 xl:-mb-10 flex items-center">
          <div className="z-[900] ml-auto">
            <Switcher dropdownItems={switcherDropdownItems} selected={currency} />
          </div>
        </div>
      )}
      <PageStatistics error={!!error} isLoading={isLoading} data={stats} retryFun={() => makeRequest({})} />
    </div>
  );

  // return <PageStatistics error={!!error} isLoading={isLoading} data={stats} retryFun={() => makeRequest({})} />;
};

const getStats = (data: StatsData, currency) => {
  return [
    {
      icon:
        // prettier-ignore
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-5 sm:w-6.25">
          <path d="M19.1709 6.63953C18.7409 4.46953 17.1309 3.51953 14.8909 3.51953H6.11094C3.47094 3.51953 1.71094 4.83953 1.71094 7.91953V13.0695C1.71094 15.2895 2.62094 16.5895 4.12094 17.1495C4.34094 17.2295 4.58094 17.2995 4.83094 17.3395C5.23094 17.4295 5.66094 17.4695 6.11094 17.4695H14.9009C17.5409 17.4695 19.3009 16.1495 19.3009 13.0695V7.91953C19.3009 7.44953 19.2609 7.02953 19.1709 6.63953ZM5.53094 11.9995C5.53094 12.4095 5.19094 12.7495 4.78094 12.7495C4.37094 12.7495 4.03094 12.4095 4.03094 11.9995V8.99953C4.03094 8.58953 4.37094 8.24953 4.78094 8.24953C5.19094 8.24953 5.53094 8.58953 5.53094 8.99953V11.9995ZM10.5009 13.1395C9.04094 13.1395 7.86094 11.9595 7.86094 10.4995C7.86094 9.03953 9.04094 7.85953 10.5009 7.85953C11.9609 7.85953 13.1409 9.03953 13.1409 10.4995C13.1409 11.9595 11.9609 13.1395 10.5009 13.1395ZM16.9609 11.9995C16.9609 12.4095 16.6209 12.7495 16.2109 12.7495C15.8009 12.7495 15.4609 12.4095 15.4609 11.9995V8.99953C15.4609 8.58953 15.8009 8.24953 16.2109 8.24953C16.6209 8.24953 16.9609 8.58953 16.9609 8.99953V11.9995Z" fill="currentColor"/>
          <path d="M22.3017 10.9183V16.0683C22.3017 19.1483 20.5417 20.4783 17.8917 20.4783H9.11172C8.36172 20.4783 7.69172 20.3683 7.11172 20.1483C6.64172 19.9783 6.23172 19.7283 5.90172 19.4083C5.72172 19.2383 5.86172 18.9683 6.11172 18.9683H14.8917C18.5917 18.9683 20.7917 16.7683 20.7917 13.0783V7.91832C20.7917 7.67832 21.0617 7.52832 21.2317 7.70832C21.9117 8.42832 22.3017 9.47832 22.3017 10.9183Z" fill="currentColor"/>
        </svg>,
      label: "Total Volume",
      value: toNaira(data.total_volume),
      formatted_value: `${currency} ${millify(toNaira(data.total_volume), 2)}`,
      color: "bg-accent-green-500",
    },
    {
      icon:
        // prettier-ignore
        <svg className="w-4.5 sm:w-6" viewBox="0 0 24 24" fill="none">
          <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM15.75 15.75H8.25C7.84 15.75 7.5 15.41 7.5 15C7.5 14.59 7.84 14.25 8.25 14.25H15.75C16.16 14.25 16.5 14.59 16.5 15C16.5 15.41 16.16 15.75 15.75 15.75ZM15.75 9.75H8.25C7.84 9.75 7.5 9.41 7.5 9C7.5 8.59 7.84 8.25 8.25 8.25H15.75C16.16 8.25 16.5 8.59 16.5 9C16.5 9.41 16.16 9.75 15.75 9.75Z" fill="currentColor"/>
        </svg>,
      label: "No. Payments",
      formatted_value: data.total_payments,
      value: data.total_payments,
      color: "bg-accent-red-500",
    },
    {
      icon:
        // prettier-ignore
        <svg className="w-4.5 sm:w-6" viewBox="0 0 20 20" fill="none">
          <path d="M18.3334 18.333H1.66675C1.32508 18.333 1.04175 18.0497 1.04175 17.708C1.04175 17.3663 1.32508 17.083 1.66675 17.083H18.3334C18.6751 17.083 18.9584 17.3663 18.9584 17.708C18.9584 18.0497 18.6751 18.333 18.3334 18.333Z" fill="currentColor"/>
          <path d="M8.125 3.33366V18.3337H11.875V3.33366C11.875 2.41699 11.5 1.66699 10.375 1.66699H9.625C8.5 1.66699 8.125 2.41699 8.125 3.33366Z" fill="currentColor"/>
          <path d="M2.5 8.33366V18.3337H5.83333V8.33366C5.83333 7.41699 5.5 6.66699 4.5 6.66699H3.83333C2.83333 6.66699 2.5 7.41699 2.5 8.33366Z" fill="currentColor"/>
          <path d="M14.1667 12.4997V18.333H17.5001V12.4997C17.5001 11.583 17.1667 10.833 16.1667 10.833H15.5001C14.5001 10.833 14.1667 11.583 14.1667 12.4997Z" fill="currentColor"/>
        </svg>,
      label: "Average Daily",
      value: toNaira(data.average_daily_volume),
      formatted_value: `${currency} ${millify(toNaira(data.average_daily_volume), 2)}`,
      color: "bg-accent-yellow-500",
    },
    {
      icon:
        // prettier-ignore
        <svg className="w-4.5 sm:w-6"  viewBox="0 0 25 25" fill="none">
        <path d="M16.8646 2.08331H8.13541C4.34374 2.08331 2.08333 4.34373 2.08333 8.1354V16.8541C2.08333 20.6562 4.34374 22.9166 8.13541 22.9166H16.8542C20.6458 22.9166 22.9062 20.6562 22.9062 16.8646V8.1354C22.9167 4.34373 20.6562 2.08331 16.8646 2.08331ZM17.9792 10.375L15.5729 13.4791C15.2708 13.8646 14.8437 14.1146 14.3542 14.1666C13.8646 14.2291 13.3854 14.0937 13 13.7916L11.0937 12.2916C11.0208 12.2291 10.9375 12.2291 10.8958 12.2396C10.8542 12.2396 10.7812 12.2604 10.7187 12.3437L8.23958 15.5625C8.08333 15.7604 7.85416 15.8646 7.62499 15.8646C7.45833 15.8646 7.29166 15.8125 7.14583 15.6979C6.80208 15.4375 6.73958 14.9479 6.99999 14.6041L9.47916 11.3854C9.78125 11 10.2083 10.75 10.6979 10.6875C11.1771 10.625 11.6667 10.7604 12.0521 11.0625L13.9583 12.5625C14.0312 12.625 14.1042 12.625 14.1562 12.6146C14.1979 12.6146 14.2708 12.5937 14.3333 12.5104L16.7396 9.40623C17 9.06248 17.5 8.99998 17.8333 9.27081C18.1771 9.55206 18.2396 10.0416 17.9792 10.375Z" fill="white" />
      </svg>,
      label: "Av. Transaction",
      value: toNaira(data.total_volume / data.total_payments),
      formatted_value: `${currency} ${millify(toNaira(data.total_volume / data.total_payments), 2)}`,
      color: "bg-accent-orange-500",
    },
  ];
};
