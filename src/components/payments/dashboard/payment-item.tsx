import { toCurrency } from "../../../assets/js/utils/functions";
import { TableCell, TableRow } from "../../ui/table";
import Badge from "../../ui/badge";
import { formatDateString, toNaira } from "../../../assets/js/utils/utils";
import StoreLogo from "../../ui/store-logo";
import RoundActionButton from "../../ui/buttons/round-action-btn";
import { PAYMENT_STATUS, PaymentInterface } from "@/assets/interfaces";
import ContentWithCopy from "@/components/ui/content-with-copy";

interface Props {
  payment: PaymentInterface;
  onAction?: (action: "click" | "download") => void;
  isLoadingPdf: boolean;
}

const PaymentTableItem: React.FC<Props> = ({ payment, onAction, isLoadingPdf }) => {
  return (
    <TableRow onClick={() => onAction("click")}>
      <TableCell>
        <ContentWithCopy text={payment.reference}>{payment.reference}</ContentWithCopy>
      </TableCell>
      <TableCell className="font-medium">
        <span className="text-black">{toCurrency(toNaira(payment.amount), payment.currency)}</span>
      </TableCell>
      <TableCell>
        <div className="flex items-center">
          <StoreLogo className="h-6.25 w-6.25 text-xs" logo="" storeName={payment?.customer?.name ?? "-"} />
          <span className="text-dark text-sm inline-block ml-1.5">
            {(payment?.customer?.name ?? "-").split(" ")[0]}
          </span>
        </div>
      </TableCell>
      <TableCell className="text-black">
        <Badge {...{ color: getPaymentBadgeStatusColor(payment.status), text: payment.status }} />
      </TableCell>
      <TableCell> {formatDateString(new Date(payment.created_at))} </TableCell>
      <TableCell className="space-x-2.5 flex items-center" stopBubble>
        {isLoadingPdf ? (
          <div className="spinner text-primary-500"></div>
        ) : (
          <RoundActionButton
            onClick={() => onAction("download")}
            icon="download"
            disabled={payment.status !== PAYMENT_STATUS.SUCCESS || !payment?.invoice?.receipt}
          />
        )}
      </TableCell>
    </TableRow>
  );
};

export const getPaymentBadgeStatusColor = (status: PAYMENT_STATUS) => {
  if (status === PAYMENT_STATUS.PENDING) return "yellow";
  if (status === PAYMENT_STATUS.SUCCESS) return "green";
  if (status === PAYMENT_STATUS.FAILED) return "red";
};

export default PaymentTableItem;
