import React, { useEffect, useState } from "react";
import Modal, { ModalBody } from "../../ui/modal";
import { CustomerInterface, PaymentInterface } from "@/assets/interfaces";
import StoreLogo from "../../ui/store-logo";
import { humanFriendlyDate, removeCountryCode, toCurrency } from "@/assets/js/utils/functions";
import { ProductDetailIcons } from "../../products/products-page/product-details";
import Badge from "../../ui/badge";
import { getPaymentBadgeStatusColor } from "./payment-item";
import { toNaira } from "@/assets/js/utils/utils";
import ContentWithCopy from "@/components/ui/content-with-copy";
import router from "next/router";
import Portal from "@/components/portal";
import ViewReceiptModal from "@/components/receipts/view-receipt-modal";
import { useModals } from "@/components/hooks/useModals";
import { SelectWithModal } from "@/components/ui/form-elements";
import { CustomerOptionRender } from "../invoices/create/basic-info";
import { useFetcher, useRequest } from "@/api/utils";
import { GetCustomersParams, LinkCustomerToPaymentParams } from "@/api/interfaces";
import { GetCustomers, GetCustomersBasic, LinkCustomerToPayments } from "@/api";
import { DropdownOptionInterface } from "@/components/ui/form-elements/select-dropdown";
import AddCustomerModal from "@/components/orders/modals/add-customer";
import { toast } from "@/components/ui/toast";

interface Props {
  show: boolean;
  toggle: VoidFunction;
  payment: PaymentInterface;
  toggleCustomerModal: () => void;
  updatePayments: VoidFunction;
}

const PaymentDetailsModal: React.FC<Props> = ({ show, toggle, payment, toggleCustomerModal, updatePayments }) => {
  const linkCustomerRequest = useRequest<LinkCustomerToPaymentParams>(LinkCustomerToPayments);
  const { response, error, isLoading, makeRequest } = useFetcher<GetCustomersParams>(GetCustomersBasic, {
    per_page: Number.MAX_SAFE_INTEGER,
  });
  const [customers, setCustomers] = useState<DropdownOptionInterface[]>([]);
  const { modals, toggleModal } = useModals(["receipt", "add_customer"]);

  useEffect(() => {
    if (response?.data?.data) {
      setCustomers(
        response.data.data.map((c) => ({
          text: c.name ?? "-",
          value: c.id,
          meta: { phone: c.phone, email: c?.email, name: c.name },
        }))
      );
    }
  }, [response]);

  const addNewCustomer = (c: CustomerInterface) => {
    const optionsCopy = [...customers];
    optionsCopy.unshift({
      text: c.name,
      value: c.id,
      meta: { phone: c.phone, email: c?.email, name: c?.name },
    });

    setCustomers(optionsCopy);
  };

  const handleLinkCustomer = async (customer: string) => {
    const [response, error] = await linkCustomerRequest.makeRequest({
      customer_id: customer.toString(),
      reference: payment.reference,
    });
    if (response) {
      toast.success({
        message: "Customer linked successfully",
        title: "Success",
      });
      updatePayments();
    } else {
      toast.error({
        message: error?.message,
        title: "Error",
      });
    }
  };

  return (
    <>
      <Modal size="md" show={show} toggle={toggle} title="Payment Details">
        <ModalBody>
          <div className="flex items-center justify-between border-b border-grey-border border-opacity-50 pb-4 mb-3.75">
            <div className="">
              <span className="text-1xs sm:text-sm text-gray-600">Amount</span>
              <h4 className="text-xl sm:text-2xl font-semibold -mt-1">
                {toCurrency(toNaira(payment.amount), payment.currency)}
              </h4>
            </div>
            <div className="bg-accent-green-500 rounded-full h-11.5 w-11.5 flex text-white items-center justify-center">
              {/* prettier-ignore */}
              <svg width="50%" viewBox="0 0 24 24" fill="none">
                <path d="M19.1709 6.63953C18.7409 4.46953 17.1309 3.51953 14.8909 3.51953H6.11094C3.47094 3.51953 1.71094 4.83953 1.71094 7.91953V13.0695C1.71094 15.2895 2.62094 16.5895 4.12094 17.1495C4.34094 17.2295 4.58094 17.2995 4.83094 17.3395C5.23094 17.4295 5.66094 17.4695 6.11094 17.4695H14.9009C17.5409 17.4695 19.3009 16.1495 19.3009 13.0695V7.91953C19.3009 7.44953 19.2609 7.02953 19.1709 6.63953ZM5.53094 11.9995C5.53094 12.4095 5.19094 12.7495 4.78094 12.7495C4.37094 12.7495 4.03094 12.4095 4.03094 11.9995V8.99953C4.03094 8.58953 4.37094 8.24953 4.78094 8.24953C5.19094 8.24953 5.53094 8.58953 5.53094 8.99953V11.9995ZM10.5009 13.1395C9.04094 13.1395 7.86094 11.9595 7.86094 10.4995C7.86094 9.03953 9.04094 7.85953 10.5009 7.85953C11.9609 7.85953 13.1409 9.03953 13.1409 10.4995C13.1409 11.9595 11.9609 13.1395 10.5009 13.1395ZM16.9609 11.9995C16.9609 12.4095 16.6209 12.7495 16.2109 12.7495C15.8009 12.7495 15.4609 12.4095 15.4609 11.9995V8.99953C15.4609 8.58953 15.8009 8.24953 16.2109 8.24953C16.6209 8.24953 16.9609 8.58953 16.9609 8.99953V11.9995Z" fill="#FFFFFF"/>
                <path d="M22.3017 10.9183V16.0683C22.3017 19.1483 20.5417 20.4783 17.8917 20.4783H9.11172C8.36172 20.4783 7.69172 20.3683 7.11172 20.1483C6.64172 19.9783 6.23172 19.7283 5.90172 19.4083C5.72172 19.2383 5.86172 18.9683 6.11172 18.9683H14.8917C18.5917 18.9683 20.7917 16.7683 20.7917 13.0783V7.91832C20.7917 7.67832 21.0617 7.52832 21.2317 7.70832C21.9117 8.42832 22.3017 9.47832 22.3017 10.9183Z" fill="#FFFFFF"/>
              </svg>
            </div>
          </div>
          <div className="space-y-2.5">
            <div className="border-b border-grey-divider pb-3.75">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-1sm sm:text-base text-black-secondary mb-1.5 font-display">
                    Customer Info
                  </h4>
                  {payment?.customer && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center flex-1 overflow-hidden">
                        <StoreLogo
                          storeName={payment?.customer.name}
                          logo={null}
                          className="h-9 w-9 text-1sm sm:text-base font-bold mr-3"
                        />
                        <div className="overflow-hidden mr-2.5">
                          <h4 className="text-1sm sm:text-base -mb-1 font-bold whitespace-nowrap overflow-ellipsis overflow-hidden leading-snug mt-0.5">
                            {payment?.customer.name}{" "}
                          </h4>
                          <span className="text-dark text-xs sm:text-1xs inline-block leading-none">
                            {payment?.customer.phone ? removeCountryCode(payment?.customer.phone) : "No Customer Data"}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {payment?.customer?.id ? (
                  <button
                    onClick={() => toggleCustomerModal()}
                    className="rounded-full flex items-center py-1.75 px-2.5 sm:px-3 sm:py-[9px] bg-grey-fields-200 font-medium text-primary-500 text-1xs lg:text-sm flex-shrink-0"
                  >
                    <span className="mr-0.5">View Profile</span>
                    {/* prettier-ignore */}
                    <svg width={14} viewBox="0 0 15 16" fill="none">
                      <path d="M4.39355 11.5359L11.4646 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M11.4639 11.5359L11.4639 4.46482L4.3928 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                ) : (
                  <>
                    <SelectWithModal
                      hasSearch
                      searchLabel="Search customers"
                      OptionRender={CustomerOptionRender}
                      label="Select or Create Customer"
                      options={customers}
                      value={null}
                      onChange={(e) => handleLinkCustomer(e.target.value)}
                      action={{
                        label: "Can't find customer? Create",
                        onClick: () => toggleModal("add_customer"),
                      }}
                      isLoadingData={isLoading}
                      className="!mt-0"
                    >
                      <button className="rounded-full flex items-center py-1.5 px-2 sm:px-2.5 sm:py-1.75 bg-grey-fields-200 font-medium text-primary-500 text-1xs flex-shrink-0">
                        <span className="mr-0.5">Link Customer</span>
                        {/* prettier-ignore */}
                        <svg width={14} viewBox="0 0 15 16" fill="none">
                          <path d="M4.39355 11.5359L11.4646 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                          <path d="M11.4639 11.5359L11.4639 4.46482L4.3928 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </button>
                    </SelectWithModal>
                  </>
                )}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between  py-1.75">
                <div className="flex items-center">
                  <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-dark">
                    {ProductDetailIcons.availability}
                  </div>
                  <span className="text-1xs sm:text-sm text-dark">Status</span>
                </div>
                <Badge
                  {...{ color: getPaymentBadgeStatusColor(payment.status), text: payment.status.replace("_", " ") }}
                />
              </div>
              <div className="flex items-center justify-between  py-1.75">
                <div className="flex items-center">
                  <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-grey-subtext">
                    {/* prettier-ignore */}
                    <svg width="15" viewBox="0 0 24 24" fill="none">
                    <path d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M15.5 9.75C16.3284 9.75 17 9.07843 17 8.25C17 7.42157 16.3284 6.75 15.5 6.75C14.6716 6.75 14 7.42157 14 8.25C14 9.07843 14.6716 9.75 15.5 9.75Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8.5 9.75C9.32843 9.75 10 9.07843 10 8.25C10 7.42157 9.32843 6.75 8.5 6.75C7.67157 6.75 7 7.42157 7 8.25C7 9.07843 7.67157 9.75 8.5 9.75Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8.4 13.3H15.6C16.1 13.3 16.5 13.7 16.5 14.2C16.5 16.69 14.49 18.7 12 18.7C9.51 18.7 7.5 16.69 7.5 14.2C7.5 13.7 7.9 13.3 8.4 13.3Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  </div>
                  <span className=" text-sm text-dark">Payment Reference</span>
                </div>
                <span className="font-medium pt-1 ml-2 text-black-secondary text-sm">
                  <ContentWithCopy text={payment.reference}>{payment.reference}</ContentWithCopy>
                </span>
              </div>
              <div className="flex items-center justify-between  py-1.75">
                <div className="flex items-center">
                  <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-grey-subtext">
                    {ProductDetailIcons.date}
                  </div>
                  <span className="text-1xs sm:text-sm text-dark">Date</span>
                </div>
                <span className="ml-2 text-black-secondary font-medium text-1xs">
                  {humanFriendlyDate(payment?.updated_at)}
                </span>
              </div>

              {/* <div className="flex items-center justify-between  py-1.75">
              <div className="flex items-center">
                <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-grey-subtext">
                  {ProductDetailIcons.date}
                </div>
                <span className="text-1xs sm:text-sm text-dark">End Time</span>
              </div>
              <span className="ml-2 text-black-secondary font-medium text-1xs">
                {session?.ended_at ? humanFriendlyDate(session?.ended_at) : "-"}
              </span>
            </div> */}

              <div className="flex items-center justify-between py-1.75">
                <div className="flex items-center">
                  <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-grey-subtext">
                    {ProductDetailIcons.payment}
                  </div>
                  <span className="text-1xs sm:text-sm text-dark">Customer Fees</span>
                </div>
                <span className="ml-2 text-black-secondary font-medium text-1xs">
                  {toCurrency(toNaira(payment.amount_with_charge - payment.amount), payment.currency)}
                </span>
              </div>
              {payment?.invoice?.receipt && (
                <div className="flex items-center justify-between py-1.75">
                  <div className="flex items-center">
                    <div className="p-2 bg-grey-fields-200 rounded-full h-[fit-content] mr-2 text-dark">
                      {/* prettier-ignore */}
                      <svg width="15" viewBox="0 0 24 24" fill="none">
                        <path d="M20 7.04V16.96C20 18.48 19.86 19.56 19.5 20.33C19.5 20.34 19.49 20.36 19.48 20.37C19.26 20.65 18.97 20.79 18.63 20.79C18.1 20.79 17.46 20.44 16.77 19.7C15.95 18.82 14.69 18.89 13.97 19.85L12.96 21.19C12.56 21.73 12.03 22 11.5 22C10.97 22 10.44 21.73 10.04 21.19L9.02002 19.84C8.31002 18.89 7.05999 18.82 6.23999 19.69L6.22998 19.7C5.09998 20.91 4.10002 21.09 3.52002 20.37C3.51002 20.36 3.5 20.34 3.5 20.33C3.14 19.56 3 18.48 3 16.96V7.04C3 5.52 3.14 4.44 3.5 3.67C3.5 3.66 3.50002 3.65 3.52002 3.64C4.09002 2.91 5.09998 3.09 6.22998 4.3L6.23999 4.31C7.05999 5.18 8.31002 5.11 9.02002 4.16L10.04 2.81C10.44 2.27 10.97 2 11.5 2C12.03 2 12.56 2.27 12.96 2.81L13.97 4.15C14.69 5.11 15.95 5.18 16.77 4.3C17.46 3.56 18.1 3.21 18.63 3.21C18.97 3.21 19.26 3.36 19.48 3.64C19.5 3.65 19.5 3.66 19.5 3.67C19.86 4.44 20 5.52 20 7.04Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M8 10.25H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M8 13.75H14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    <span className=" text-sm text-dark">View & Share Receipt</span>
                  </div>
                  <button
                    onClick={() => toggleModal("receipt")}
                    className="rounded-full flex items-center py-1.5 px-2 sm:px-2.5 sm:py-1.75 bg-grey-fields-200 font-medium text-primary-500 text-1xs flex-shrink-0"
                  >
                    <span className="mr-0.5">Open Receipt</span>
                    {/* prettier-ignore */}
                    <svg width={14} viewBox="0 0 15 16" fill="none">
                      <path d="M4.39355 11.5359L11.4646 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M11.4639 11.5359L11.4639 4.46482L4.3928 4.46482" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                </div>
              )}
            </div>
          </div>
        </ModalBody>
      </Modal>
      {payment?.invoice?.receipt && (
        <Portal>
          <ViewReceiptModal
            show={modals.receipt.show}
            toggle={() => toggleModal("receipt")}
            receipt_id={payment.invoice.receipt}
          />
          <AddCustomerModal
            addCustomer={addNewCustomer}
            show={modals.add_customer.show}
            toggle={() => toggleModal("add_customer")}
          />
        </Portal>
      )}
    </>
  );
};

export default PaymentDetailsModal;
