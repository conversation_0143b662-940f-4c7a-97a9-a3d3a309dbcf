import React from "react";
import { GetInvoicesParams } from "@/api/interfaces/invoices.interface";
import { RequestInterface } from "@/api/utils";
import { reloadPage } from "@/assets/js/utils/functions";
import { AppBtn } from "@/components/ui/buttons";
import ContentState from "@/components/ui/content-state";

interface IProps {
  request: RequestInterface<GetInvoicesParams>;
  isEmpty: boolean;
}

const PaymentsContentState: React.FC<IProps> = ({ request, isEmpty }) => {
  return (
    <ContentState
      title="No payments to show"
      description="Create an invoice to collect payments from a customer"
      loadingText="Loading payments..."
      isEmpty={isEmpty}
      isLoading={request.isLoading}
      error={request.error}
      errorTitle="Something went wrong"
      errorMessage="Couldn't load payments, please retry"
      errorAction={
        <AppBtn size="md" onClick={reloadPage}>
          Reload page
        </AppBtn>
      }
      emptyIcon={
        // prettier-ignore
        <svg className="w-7.5 sm:w-9 lg:w-10" viewBox="0 0 24 24" fill="none">
          <path opacity="0.4" d="M20.5 10.19H17.61C15.24 10.19 13.31 8.26 13.31 5.89V3C13.31 2.45 12.86 2 12.31 2H8.07C4.99 2 2.5 4 2.5 7.57V16.43C2.5 20 4.99 22 8.07 22H15.93C19.01 22 21.5 20 21.5 16.43V11.19C21.5 10.64 21.05 10.19 20.5 10.19Z" fill="currentColor"/>
          <path d="M15.7997 2.20999C15.3897 1.79999 14.6797 2.07999 14.6797 2.64999V6.13999C14.6797 7.59999 15.9197 8.80999 17.4297 8.80999C18.3797 8.81999 19.6997 8.81999 20.8297 8.81999C21.3997 8.81999 21.6997 8.14999 21.2997 7.74999C19.8597 6.29999 17.2797 3.68999 15.7997 2.20999Z" fill="currentColor"/>
        </svg>
      }
    >
      <AppBtn size="md" href="/payments/invoices/create">
        Create Invoice
      </AppBtn>
    </ContentState>
  );
};

export default PaymentsContentState;
