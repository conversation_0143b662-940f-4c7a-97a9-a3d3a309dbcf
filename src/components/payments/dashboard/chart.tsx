import React from "react";
import { GetStoreAnalyticsParams } from "@/api/interfaces/analytics.interface";
import { CustomErrorType, RequestInterface } from "@/api/utils";
import { getFilter } from "@/pages/dashboard";
import StatisticsGraph, { ChartState, TimeRange } from "@/components/ui/statistics-graph";
import { CURRENCIES } from "@/assets/interfaces";

interface IProps {
  // getDataReq: RequestInterface<GetStoreAnalyticsParams>;
  range: TimeRange;
  setRange: (range: TimeRange) => void;
  statsReq: RequestInterface<{}>;
  data: { count: []; volumes: [] };
  currency: CURRENCIES;
}

export const PaymentChart: React.FC<IProps> = ({ statsReq, range, setRange, data, currency }) => {
  const { response, error, makeRequest, isLoading } = statsReq;
  // const data = response?.data;

  if (isLoading || error) {
    return <ChartState isLoading={isLoading} error={!!error} retryFun={() => makeRequest({})} label="Total Payments" />;
  }

  return (
    <div className="sm:mt-6.25 lg:mt-7.5">
      <StatisticsGraph
        range={range}
        setRange={setRange}
        labels={[
          { name: "Total Payments", type: "number" },
          { name: `Volume`, type: "currency" },
        ]}
        seriesData={data ? [data.count, data.volumes] : [[], []]}
        currency={currency}
      />
    </div>
  );
};
