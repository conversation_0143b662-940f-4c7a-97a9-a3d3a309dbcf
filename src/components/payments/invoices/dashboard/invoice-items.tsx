import { toCurrency } from "../../../../assets/js/utils/functions";
import { TableCell, TableRow } from "../../../ui/table";
import Badge from "../../../ui/badge";
import { InvoiceInterface, INVOICE_STATUSES, INVOICE_TYPES } from "../../../../assets/interfaces/invoices";
import { formatDateString } from "../../../../assets/js/utils/utils";
import StoreLogo from "../../../ui/store-logo";
import RoundActionButton from "../../../ui/buttons/round-action-btn";

export type ClickActions = "click" | "edit" | "delete" | "download" | "copy-link";

interface Props {
  item: InvoiceInterface; //add type later
  onAction?: (action: ClickActions) => void;
  isLoadingPdf: boolean;
}

const InvoiceItems: React.FC<Props> = ({ item, onAction, isLoadingPdf }) => {
  return (
    <TableRow onClick={() => onAction("click")}>
      <TableCell className="font-medium capitalize">{item.title}</TableCell>
      <TableCell>
        <div className="flex items-center">
          <StoreLogo className="h-6.25 w-6.25 text-xs" logo="" storeName={item?.receiver?.name ?? "-"} />
          <span className="text-dark text-sm inline-block ml-1.5">
            {item?.receiver?.name.split(" ")[0] ?? "Unknown"}
          </span>
        </div>
      </TableCell>
      <TableCell className="">
        <span className="text-black font-medium">{toCurrency(item.total_amount, item?.currency)}</span>
      </TableCell>
      {item.type === INVOICE_TYPES.REGULAR && <TableCell> {formatDateString(new Date(item.date_due))} </TableCell>}
      <TableCell className="text-black">
        <Badge
          {...{ color: getBadgeStatusColor(item.status, item?.is_draft), text: item?.is_draft ? "DRAFT" : item.status }}
        />
      </TableCell>
      <TableCell className="space-x-2.5 flex items-center" stopBubble>
        {item.status === INVOICE_STATUSES.PENDING && <RoundActionButton onClick={() => onAction("edit")} icon="edit" />}

        {(!item.is_draft || item.type === INVOICE_TYPES.PAYMENT_LINK) && (
          <RoundActionButton onClick={() => onAction("copy-link")} icon="link" />
        )}

        <DownloadControls invoice={item} isLoadingPdf={isLoadingPdf} onAction={onAction} />
        <RoundActionButton
          onClick={() => onAction("delete")}
          icon="delete"
          disabled={item.status !== INVOICE_STATUSES.PENDING}
        />
      </TableCell>
    </TableRow>
  );
};

const DownloadControls = ({
  invoice,
  isLoadingPdf,
  onAction,
}: {
  invoice: InvoiceInterface;
  isLoadingPdf: boolean;
  onAction: (action: ClickActions) => void;
}) => {
  if (invoice.is_draft || (invoice.type === INVOICE_TYPES.PAYMENT_LINK && invoice.status !== INVOICE_STATUSES.PAID))
    return null;

  return (
    <>
      {isLoadingPdf ? (
        <div className="spinner text-primary-500"></div>
      ) : (
        <RoundActionButton onClick={() => onAction("download")} icon="download" disabled={invoice.is_draft} />
      )}
    </>
  );
};

export const getBadgeStatusColor = (status: INVOICE_STATUSES, is_draft?: boolean) => {
  if (is_draft) return "dark";
  if (status === INVOICE_STATUSES.PENDING) return "yellow";
  if (status === INVOICE_STATUSES.PAID) return "green";
  if (status === INVOICE_STATUSES.EXPIRED) return "red";
};

export default InvoiceItems;
