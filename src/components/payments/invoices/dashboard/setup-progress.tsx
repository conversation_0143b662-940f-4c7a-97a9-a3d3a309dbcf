import classNames from "classnames";
import router from "next/router";
import { useState } from "react";
import { GetPaymentsSetupProgress } from "../../../../api";
import { useFetcher } from "../../../../api/utils";
import authContext from "../../../../contexts/auth-context";
import useFluxState from "../../../hooks/useFluxState";
import { useModals } from "../../../hooks/useModals";
import Portal from "../../../portal";
import MakeTestPaymentModal from "@/components/wallets/test-payment/modal";

interface Props {
  canManagePayments: boolean;
}

interface ProgressData {
  has_made_test_payment: boolean;
  has_withdrawal_accounts: boolean;
  has_enabled_direct_checkout: boolean;
  has_delivery_areas: boolean;
}

export type StepKey =
  | "has_made_test_payment"
  | "has_withdrawal_accounts"
  | "has_enabled_direct_checkout"
  | "has_delivery_areas"
  | "security_pin_added";

//onboarding-progress
const PaymentsOnboardingProgress: React.FC<Props> = ({ canManagePayments }) => {
  const { store } = authContext.useContainer();
  const { isLoading, makeRequest, error, response } = useFetcher(GetPaymentsSetupProgress, []);
  const { modals, toggleModal } = useModals(["make_test_payment"]);
  const setupProgressData = { ...response?.data, has_made_test_payment: !!store?.onboarding_steps?.test_payment_made };
  const steps = (
    setupProgressData ? stepData.map((s) => ({ ...s, isCompleted: setupProgressData[s.key] })) : stepData
  ).sort((a, b) => (b.isCompleted ? -1 : 0));
  const completedCount = steps.filter((s) => s.isCompleted).length;
  const [showProgress, setShowProgress] = useState(true);

  const handleStepClick = (step: StepKey) => {
    switch (step) {
      case "has_made_test_payment":
        toggleModal("make_test_payment");
        break;
      case "has_delivery_areas":
        router.push("/my-store/delivery-areas");
        break;
      case "has_withdrawal_accounts":
        router.push("/my-store/payments?tab=withdrawal_accounts");
        break;
      case "security_pin_added":
        router.push("/my-store/payments?tab=security_pin");
        break;
      case "has_enabled_direct_checkout":
        router.push("/my-store/payments");
        break;
    }
  };

  const accordionClasses = classNames(
    "flex overflow-x-auto space-x-3.75 transition-all duration-300 ease-in-out px-5 sm:px-6.25 lg:px-7.5 -mx-5 sm:-mx-6.25 lg:-mx-7.5",
    {
      "max-h-[1000px] mt-5 sm:mt-6 lg:mt-[27.5px]": showProgress,
      "max-h-0 overflow-hidden": !showProgress,
    }
  );

  if (!setupProgressData || Object.values(response?.data ?? {}).every((v) => v === true)) {
    return null;
  }

  return (
    <div className="py-5 sm:py-6 lg:py-7.5 border-b border-grey-border border-opacity-50">
      <div className="flex justify-between">
        <h1 className="font-bold text-lg md:text-xl">
          Finish setting up
          <span className="ml-1.5 text-placeholder">
            {completedCount}/{steps.length} complete
          </span>
        </h1>
        <button
          onClick={() => setShowProgress(!showProgress)}
          className="flex items-center justify-center bg-grey-fields-100 w-7.5 h-7.5 rounded-full"
        >
          {/* prettier-ignore */}
          <svg width="12" height="7" viewBox="0 0 12 7" fill="none" className={`text-dark transition-all duration-100 ease-out transform ${showProgress ? "rotate-180" : ""}`}>
            <path d="M1 1L6 6L11 0.999999" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>
      </div>
      <ul className={accordionClasses}>
        {steps.map((step, index) => {
          return (
            <li
              key={index}
              className="bg-grey-fields-100 flex items-center rounded-10 py-5 px-4 md:py-6.25 md:px-5 group cursor-pointer"
            >
              <button
                className="flex items-center w-[245px] md:w-[265px]"
                onClick={() => !step?.isCompleted && handleStepClick(step.key)}
              >
                {!step.isCompleted && (
                  <figure
                    className={`w-10 h-10 sm:h-11.25 sm:w-11.25 md:w-12.5 md:h-12.5 flex items-center justify-center rounded-full bg-white ${step.color}`}
                  >
                    {step.icon}
                  </figure>
                )}
                {step.isCompleted && (
                  <figure
                    className={`w-10 h-10 sm:h-11.25 sm:w-11.25 md:w-12.5 md:h-12.5 flex items-center justify-center rounded-full bg-white text-accent-green-500`}
                  >
                    {/* prettier-ignore */}
                    <svg width="55%" viewBox="0 0 18 18" fill="none">
                      <rect width="18" height="18" rx="9" fill="currentColor"/>
                      <path d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z" fill="white"/>
                    </svg>
                  </figure>
                )}
                <div className="flex flex-col items-start ml-2.5">
                  <span className="text-black text-sm sm:text-base font-semibold">{step.title}</span>
                  <span className="text-dark text-1xs text-left">{step.description}</span>
                </div>
              </button>
              {!step.isCompleted && (
                <div className="group-hover:text-primary-500 text-dark transition-color ease-out duration-200">
                  {/* prettier-ignore */}
                  <svg width="6" height="12" viewBox="0 0 6 12" fill="none">
                  <path d="M0.155061 11.8299C-0.0328918 11.6238 -0.0499784 11.3012 0.103801 11.0739L0.155061 11.0088L4.72165 6L0.155061 0.991223C-0.0328918 0.785082 -0.0499784 0.462503 0.103801 0.235191L0.155061 0.170068C0.343014 -0.0360737 0.63713 -0.0548143 0.844385 0.113847L0.903762 0.170068L5.84494 5.58942C6.03289 5.79556 6.04998 6.11814 5.8962 6.34545L5.84494 6.41058L0.903762 11.8299C0.697014 12.0567 0.361809 12.0567 0.155061 11.8299Z" fill="currentColor" />
                </svg>
                </div>
              )}
            </li>
          );
        })}
      </ul>
      <Portal>
        <MakeTestPaymentModal
          show={modals.make_test_payment.show}
          toggle={() => toggleModal("make_test_payment")}
          country={store?.country?.code}
        />
      </Portal>
    </div>
  );
};

interface IStepData {
  title: string;
  description: string;
  icon: JSX.Element;
  isCompleted: boolean;
  color: string;
  key: StepKey;
  bg: string;
}

export const stepData: IStepData[] = [
  {
    title: "Test Payments",
    description: "Make a test payment",
    icon:
      // prettier-ignore
      <svg className="w-[55%]" viewBox="0 0 20 20" fill="none">
        <path d="M18.3334 6.29134C18.3334 6.84134 17.8834 7.29134 17.3334 7.29134H2.66675C2.11675 7.29134 1.66675 6.84134 1.66675 6.29134V6.28301C1.66675 4.37467 3.20841 2.83301 5.11675 2.83301H14.8751C16.7834 2.83301 18.3334 4.38301 18.3334 6.29134Z" fill="currentColor"/>
        <path d="M1.66675 9.54199V13.717C1.66675 15.6253 3.20841 17.167 5.11675 17.167H14.8751C16.7834 17.167 18.3334 15.617 18.3334 13.7087V9.54199C18.3334 8.99199 17.8834 8.54199 17.3334 8.54199H2.66675C2.11675 8.54199 1.66675 8.99199 1.66675 9.54199ZM6.66675 14.3753H5.00008C4.65841 14.3753 4.37508 14.092 4.37508 13.7503C4.37508 13.4087 4.65841 13.1253 5.00008 13.1253H6.66675C7.00841 13.1253 7.29175 13.4087 7.29175 13.7503C7.29175 14.092 7.00841 14.3753 6.66675 14.3753ZM12.0834 14.3753H8.75008C8.40841 14.3753 8.12508 14.092 8.12508 13.7503C8.12508 13.4087 8.40841 13.1253 8.75008 13.1253H12.0834C12.4251 13.1253 12.7084 13.4087 12.7084 13.7503C12.7084 14.092 12.4251 14.3753 12.0834 14.3753Z" fill="currentColor"/>
      </svg>,
    isCompleted: false,
    color: "text-accent-green-500",
    bg: "bg-accent-green-500",
    key: "has_made_test_payment",
  },
  {
    title: "Direct Checkout",
    description: "Allow customers pay on store",
    icon:
      // prettier-ignore
      <svg className="w-[55%]" viewBox="0 0 18 18" fill="none">
        <path d="M14.43 4.18492H14.13L11.595 1.64992C11.3925 1.44742 11.0625 1.44742 10.8525 1.64992C10.65 1.85242 10.65 2.18242 10.8525 2.39242L12.645 4.18492H5.355L7.1475 2.39242C7.35 2.18992 7.35 1.85992 7.1475 1.64992C6.945 1.44742 6.615 1.44742 6.405 1.64992L3.8775 4.18492H3.5775C2.9025 4.18492 1.5 4.18492 1.5 6.10492C1.5 6.83242 1.65 7.31242 1.965 7.62742C2.145 7.81492 2.3625 7.91242 2.595 7.96492C2.8125 8.01742 3.045 8.02492 3.27 8.02492H14.73C14.9625 8.02492 15.18 8.00992 15.39 7.96492C16.02 7.81492 16.5 7.36492 16.5 6.10492C16.5 4.18492 15.0975 4.18492 14.43 4.18492Z" fill="currentColor" />
        <path d="M14.3175 9H3.68252C3.21752 9 2.86502 9.4125 2.94002 9.87L3.57002 13.725C3.78002 15.015 4.34252 16.5 6.84002 16.5H11.0475C13.575 16.5 14.025 15.2325 14.295 13.815L15.0525 9.8925C15.1425 9.4275 14.79 9 14.3175 9ZM11.16 12.0375L8.72252 14.2875C8.61752 14.385 8.48252 14.4375 8.34002 14.4375C8.19752 14.4375 8.05502 14.385 7.94252 14.2725L6.81752 13.1475C6.60002 12.93 6.60002 12.57 6.81752 12.3525C7.04252 12.135 7.39502 12.135 7.62002 12.3525L8.36252 13.095L10.4025 11.2125C10.6275 11.0025 10.9875 11.0175 11.1975 11.2425C11.4075 11.475 11.3925 11.8275 11.16 12.0375Z" fill="currentColor" />
      </svg>,
    isCompleted: false,
    color: "text-accent-red-500",
    bg: "bg-accent-red-500",
    key: "has_enabled_direct_checkout",
  },
  {
    title: "Delivery Areas",
    description: "Add delivery areas & Fees",
    icon:
      // prettier-ignore
      <svg className="w-[55%]" viewBox="0 0 20 20" fill="none">
        <path d="M6.35845 2.97506C6.50793 2.89353 6.66678 3.01897 6.66678 3.18924V14.4667C6.66678 14.664 6.53169 14.8306 6.35845 14.9251V14.9251L4.40011 16.0417C3.03345 16.8251 1.90845 16.1751 1.90845 14.5917V6.4834C1.90845 5.9584 2.28345 5.3084 2.75011 5.04173L6.35845 2.97506V2.97506Z" fill="#F35508"/>
        <path d="M12.2219 5.06214C12.3922 5.1465 12.5 5.32013 12.5 5.51021V16.2868C12.5 16.6553 12.115 16.8972 11.783 16.7372L8.40798 15.1112C8.23498 15.0279 8.125 14.8528 8.125 14.6608V3.83878C8.125 3.46792 8.51456 3.22613 8.8469 3.39071L12.2219 5.06214Z" fill="#F35508"/>
        <path d="M18.3333 5.40806V13.5164C18.3333 14.0414 17.9583 14.6914 17.4916 14.9581L14.7069 16.554C14.3735 16.7451 13.9583 16.5044 13.9583 16.1202V5.32338C13.9583 5.14387 14.0545 4.97813 14.2104 4.88914L15.8416 3.95806C17.2083 3.17473 18.3333 3.82473 18.3333 5.40806Z" fill="#F35508"/>
      </svg>,
    isCompleted: false,
    color: "text-accent-orange-500",
    bg: "bg-accent-orange-500",
    key: "has_delivery_areas",
  },
  {
    title: "Setup Withdrawals",
    description: "Add withdrawal accounts",
    icon:
      // prettier-ignore
      <svg className="w-[55%]" viewBox="0 0 18 18" fill="none">
        <path d="M15.7275 12.06C15.5475 14.0625 14.1 15.375 12 15.375H5.25C3.18 15.375 1.5 13.695 1.5 11.625V6.375C1.5 4.335 2.73 2.91 4.6425 2.67C4.8375 2.64 5.04 2.625 5.25 2.625H12C12.195 2.625 12.3825 2.6325 12.5625 2.6625C14.355 2.8725 15.57 4.125 15.7275 5.94C15.75 6.1575 15.57 6.3375 15.3525 6.3375H14.19C13.47 6.3375 12.8025 6.615 12.3225 7.11C11.7525 7.665 11.4675 8.445 11.535 9.225C11.655 10.59 12.855 11.6625 14.28 11.6625H15.3525C15.57 11.6625 15.75 11.8425 15.7275 12.06Z" fill="currentColor" />
        <path d="M16.4999 8.22789V9.77289C16.4999 10.1854 16.1699 10.5229 15.7499 10.5379H14.2799C13.4699 10.5379 12.7274 9.94539 12.6599 9.13539C12.6149 8.66289 12.7949 8.22039 13.1099 7.91289C13.3874 7.62789 13.7699 7.46289 14.1899 7.46289H15.7499C16.1699 7.47789 16.4999 7.81539 16.4999 8.22789Z" fill="currentColor" />
      </svg>,
    isCompleted: false,
    color: "text-accent-yellow-500",
    bg: "bg-accent-yellow-500",
    key: "has_withdrawal_accounts",
  },
  {
    title: "Security Pin",
    description: "Set a security pin for withdrawals",
    icon:
      // prettier-ignore
      <svg className="w-[55%]" viewBox="0 0 24 24" fill="none">
    <path d="M18.75 8V10.1C18.31 10.04 17.81 10.01 17.25 10V8C17.25 4.85 16.36 2.75 12 2.75C7.64 2.75 6.75 4.85 6.75 8V10C6.19 10.01 5.69 10.04 5.25 10.1V8C5.25 5.1 5.95 1.25 12 1.25C18.05 1.25 18.75 5.1 18.75 8Z" fill="currentColor"/>
    <path d="M18.75 10.1C18.31 10.04 17.81 10.01 17.25 10H6.75C6.19 10.01 5.69 10.04 5.25 10.1C2.7 10.41 2 11.66 2 15V17C2 21 3 22 7 22H17C21 22 22 21 22 17V15C22 11.66 21.3 10.41 18.75 10.1ZM8.71 16.71C8.52 16.89 8.26 17 8 17C7.87 17 7.74 16.97 7.62 16.92C7.49 16.87 7.39 16.8 7.29 16.71C7.11 16.52 7 16.26 7 16C7 15.87 7.03 15.74 7.08 15.62C7.13 15.5 7.2 15.39 7.29 15.29C7.39 15.2 7.49 15.13 7.62 15.08C7.99 14.92 8.43 15.01 8.71 15.29C8.8 15.39 8.87 15.5 8.92 15.62C8.97 15.74 9 15.87 9 16C9 16.26 8.89 16.52 8.71 16.71ZM12.92 16.38C12.87 16.5 12.8 16.61 12.71 16.71C12.52 16.89 12.26 17 12 17C11.73 17 11.48 16.89 11.29 16.71C11.2 16.61 11.13 16.5 11.08 16.38C11.03 16.26 11 16.13 11 16C11 15.73 11.11 15.48 11.29 15.29C11.66 14.92 12.33 14.92 12.71 15.29C12.89 15.48 13 15.73 13 16C13 16.13 12.97 16.26 12.92 16.38ZM16.71 16.71C16.52 16.89 16.26 17 16 17C15.74 17 15.48 16.89 15.29 16.71C15.11 16.52 15 16.27 15 16C15 15.73 15.11 15.48 15.29 15.29C15.67 14.92 16.34 14.92 16.71 15.29C16.75 15.34 16.79 15.39 16.83 15.45C16.87 15.5 16.9 15.56 16.92 15.62C16.95 15.68 16.97 15.74 16.98 15.8C16.99 15.87 17 15.94 17 16C17 16.26 16.89 16.52 16.71 16.71Z" fill="currentColor"/>
    </svg>,
    isCompleted: false,
    color: "text-accent-red-500",
    bg: "bg-accent-red-500",
    key: "security_pin_added",
  },
  // {
  //   title: "Upgrade Account",
  //   description: "Update your KYC details",
  //   icon:
  //     // prettier-ignore
  //     <svg className="w-[55%]" viewBox="0 0 20 20" fill="none">
  //       <path d="M10.0001 1.66699C5.40008 1.66699 1.66675 5.40033 1.66675 10.0003C1.66675 14.6003 5.40008 18.3337 10.0001 18.3337C14.6001 18.3337 18.3334 14.6003 18.3334 10.0003C18.3334 5.40033 14.6001 1.66699 10.0001 1.66699ZM12.9417 10.0253C12.8167 10.1503 12.6584 10.2087 12.5001 10.2087C12.3417 10.2087 12.1834 10.1503 12.0584 10.0253L10.6251 8.59199V12.917C10.6251 13.2587 10.3417 13.542 10.0001 13.542C9.65841 13.542 9.37508 13.2587 9.37508 12.917V8.59199L7.94175 10.0253C7.70008 10.267 7.30008 10.267 7.05841 10.0253C6.81675 9.78366 6.81675 9.38366 7.05841 9.14199L9.55841 6.64199C9.80008 6.40033 10.2001 6.40033 10.4417 6.64199L12.9417 9.14199C13.1834 9.38366 13.1834 9.78366 12.9417 10.0253Z" fill="currentColor"/>
  //     </svg>,
  //   isCompleted: false,
  //   color: "text-accent-green-500",
  // },
];

export default PaymentsOnboardingProgress;
