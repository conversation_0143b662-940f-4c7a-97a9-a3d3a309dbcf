import { useEffect, useState } from "react";
import { GetInvoiceStats } from "../../../../api/invoices";
import { useFetcher } from "../../../../api/utils";
import { CURRENCIES } from "../../../../assets/interfaces";
import { getProductsCurrency, getUserCountry, millify } from "../../../../assets/js/utils/functions";
import Switcher from "../../../ui/currency-switcher";
import PageStatistics from "../../../ui/page-stats";
import { DropdownItem } from "@/components/ui/dropdown-new";

interface Props {}

interface InvoiceStats {
  total_invoices: number;
  total_amount_received: number;
  total_paid_invoices: number;
  invoices_unpaid: number;
}

const InvoiceStatistics: React.FC<Props> = ({}) => {
  const { isLoading, error, response, makeRequest } = useFetcher(GetInvoiceStats);
  const [currency, setCurrency] = useState(getProductsCurrency());

  const responseData = response?.data;
  const stats = getStats(responseData?.grouped_data[currency] ?? null, currency);

  useEffect(() => {
    if (responseData?.currencies?.length > 0) {
      setCurrency(
        responseData?.currencies.includes(getProductsCurrency()) ? getProductsCurrency() : responseData?.currencies[0]
      );
    }
  }, [responseData]);

  const switcherDropdownItems: DropdownItem[] = responseData?.currencies.map((curr) => ({
    text: curr,
    link: undefined,
    onClick: () => setCurrency(curr),
    icon: null,
  }));

  return (
    <div className="relative pt-5">
      {responseData?.currencies?.length > 1 && (
        <div className="mb-2.5 xl:-mb-10 flex items-center">
          <div className="z-[900] ml-auto">
            <Switcher dropdownItems={switcherDropdownItems} selected={currency} />
          </div>
        </div>
      )}
      <PageStatistics error={!!error} isLoading={isLoading} data={stats} retryFun={() => makeRequest()} />
    </div>
  );
};

const getStats = (data: InvoiceStats, currency: CURRENCIES) => {
  return [
    {
      label: "Total Amount",
      formatted_value: `${currency} ${millify(data?.total_amount_received ?? 0)}`,
      value: data?.total_amount_received ?? 0,
      color: "bg-accent-red-500",
      icon:
        // prettier-ignore
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-5 sm:w-6.25">
          <path d="M19.1709 6.63953C18.7409 4.46953 17.1309 3.51953 14.8909 3.51953H6.11094C3.47094 3.51953 1.71094 4.83953 1.71094 7.91953V13.0695C1.71094 15.2895 2.62094 16.5895 4.12094 17.1495C4.34094 17.2295 4.58094 17.2995 4.83094 17.3395C5.23094 17.4295 5.66094 17.4695 6.11094 17.4695H14.9009C17.5409 17.4695 19.3009 16.1495 19.3009 13.0695V7.91953C19.3009 7.44953 19.2609 7.02953 19.1709 6.63953ZM5.53094 11.9995C5.53094 12.4095 5.19094 12.7495 4.78094 12.7495C4.37094 12.7495 4.03094 12.4095 4.03094 11.9995V8.99953C4.03094 8.58953 4.37094 8.24953 4.78094 8.24953C5.19094 8.24953 5.53094 8.58953 5.53094 8.99953V11.9995ZM10.5009 13.1395C9.04094 13.1395 7.86094 11.9595 7.86094 10.4995C7.86094 9.03953 9.04094 7.85953 10.5009 7.85953C11.9609 7.85953 13.1409 9.03953 13.1409 10.4995C13.1409 11.9595 11.9609 13.1395 10.5009 13.1395ZM16.9609 11.9995C16.9609 12.4095 16.6209 12.7495 16.2109 12.7495C15.8009 12.7495 15.4609 12.4095 15.4609 11.9995V8.99953C15.4609 8.58953 15.8009 8.24953 16.2109 8.24953C16.6209 8.24953 16.9609 8.58953 16.9609 8.99953V11.9995Z" fill="currentColor"/>
          <path d="M22.3017 10.9183V16.0683C22.3017 19.1483 20.5417 20.4783 17.8917 20.4783H9.11172C8.36172 20.4783 7.69172 20.3683 7.11172 20.1483C6.64172 19.9783 6.23172 19.7283 5.90172 19.4083C5.72172 19.2383 5.86172 18.9683 6.11172 18.9683H14.8917C18.5917 18.9683 20.7917 16.7683 20.7917 13.0783V7.91832C20.7917 7.67832 21.0617 7.52832 21.2317 7.70832C21.9117 8.42832 22.3017 9.47832 22.3017 10.9183Z" fill="currentColor"/>
        </svg>,
    },
    {
      label: "Total Invoices",
      formatted_value: data?.total_invoices ?? 0,
      value: data?.total_invoices ?? 0,
      color: "bg-accent-yellow-500",
      icon:
        // prettier-ignore
        <svg viewBox="0 0 25 26" fill="none" className="w-5 sm:w-6.25">
        <path d="M16.4374 2.58301H8.56242C4.62492 2.58301 3.64575 3.63509 3.64575 7.83301V19.5622C3.64575 22.333 5.16659 22.9893 7.01034 21.0101L7.02075 20.9997C7.87492 20.0934 9.177 20.1663 9.91659 21.1559L10.9687 22.5622C11.8124 23.6768 13.177 23.6768 14.0208 22.5622L15.0728 21.1559C15.8228 20.1559 17.1249 20.083 17.9791 20.9997C19.8333 22.9788 21.3437 22.3226 21.3437 19.5518V7.83301C21.3541 3.63509 20.3749 2.58301 16.4374 2.58301ZM15.6249 12.7393H9.37492C8.94783 12.7393 8.59367 12.3851 8.59367 11.958C8.59367 11.5309 8.94783 11.1768 9.37492 11.1768H15.6249C16.052 11.1768 16.4062 11.5309 16.4062 11.958C16.4062 12.3851 16.052 12.7393 15.6249 12.7393ZM16.6666 8.57259H8.33325C7.90617 8.57259 7.552 8.21842 7.552 7.79134C7.552 7.36426 7.90617 7.01009 8.33325 7.01009H16.6666C17.0937 7.01009 17.4478 7.36426 17.4478 7.79134C17.4478 8.21842 17.0937 8.57259 16.6666 8.57259Z" fill="currentColor" />
      </svg>,
    },
    {
      label: "Paid",
      value: data?.total_paid_invoices ?? 0,
      formatted_value: data?.total_paid_invoices ?? 0,
      color: "bg-accent-green-500",
      icon:
        // prettier-ignore
        <svg viewBox="0 0 25 25" fill="none" className="w-5 sm:w-6.25">
        <path d="M17.7084 4.16699H7.29171C4.16671 4.16699 2.08337 5.72949 2.08337 9.37533V13.0837C2.08337 13.4691 2.47921 13.7087 2.82296 13.5524C3.84379 13.0837 5.02087 12.9066 6.26046 13.1253C9.00004 13.6149 11.0105 16.1566 10.9375 18.9378C10.9271 19.3753 10.8646 19.8024 10.75 20.2191C10.6667 20.542 10.9271 20.8441 11.2605 20.8441H17.7084C20.8334 20.8441 22.9167 19.2816 22.9167 15.6357V9.37533C22.9167 5.72949 20.8334 4.16699 17.7084 4.16699ZM12.5 15.1045C11.0625 15.1045 9.89587 13.9378 9.89587 12.5003C9.89587 11.0628 11.0625 9.89616 12.5 9.89616C13.9375 9.89616 15.1042 11.0628 15.1042 12.5003C15.1042 13.9378 13.9375 15.1045 12.5 15.1045ZM20.0521 14.5837C20.0521 15.0107 19.698 15.3649 19.2709 15.3649C18.8438 15.3649 18.4896 15.0107 18.4896 14.5837V10.417C18.4896 9.98991 18.8438 9.63574 19.2709 9.63574C19.698 9.63574 20.0521 9.98991 20.0521 10.417V14.5837Z" fill="currentColor" />
        <path d="M5.20829 14.583C2.90621 14.583 1.04163 16.4476 1.04163 18.7497C1.04163 19.5309 1.26038 20.2705 1.64579 20.8955C2.36454 22.1038 3.68746 22.9163 5.20829 22.9163C6.72913 22.9163 8.05204 22.1038 8.77079 20.8955C9.15621 20.2705 9.37496 19.5309 9.37496 18.7497C9.37496 16.4476 7.51038 14.583 5.20829 14.583ZM7.26038 18.4059L5.04163 20.458C4.89579 20.5934 4.69788 20.6663 4.51038 20.6663C4.31246 20.6663 4.11454 20.5934 3.95829 20.4372L2.92704 19.4059C2.62496 19.1038 2.62496 18.6038 2.92704 18.3018C3.22913 17.9997 3.72913 17.9997 4.03121 18.3018L4.53121 18.8018L6.19788 17.2601C6.51038 16.9684 7.01038 16.9893 7.30204 17.3018C7.59371 17.6143 7.57288 18.1143 7.26038 18.4059Z" fill="currentColor" />
      </svg>,
    },
    {
      label: "Unpaid",
      value: data?.invoices_unpaid ?? 0,
      formatted_value: data?.invoices_unpaid ?? 0,
      color: "bg-accent-orange-500",
      icon:
        // prettier-ignore
        <svg viewBox="0 0 25 25" fill="none" className="w-5 sm:w-6.25">
        <path d="M17.7084 4.16699H7.29171C4.16671 4.16699 2.08337 5.72949 2.08337 9.37533V13.0837C2.08337 13.4691 2.47921 13.7087 2.82296 13.5524C3.84379 13.0837 5.02087 12.9066 6.26046 13.1253C9.00004 13.6149 11.0105 16.1566 10.9375 18.9378C10.9271 19.3753 10.8646 19.8024 10.75 20.2191C10.6667 20.542 10.9271 20.8441 11.2605 20.8441H17.7084C20.8334 20.8441 22.9167 19.2816 22.9167 15.6357V9.37533C22.9167 5.72949 20.8334 4.16699 17.7084 4.16699ZM12.5 15.1045C11.0625 15.1045 9.89587 13.9378 9.89587 12.5003C9.89587 11.0628 11.0625 9.89616 12.5 9.89616C13.9375 9.89616 15.1042 11.0628 15.1042 12.5003C15.1042 13.9378 13.9375 15.1045 12.5 15.1045ZM20.0521 14.5837C20.0521 15.0107 19.698 15.3649 19.2709 15.3649C18.8438 15.3649 18.4896 15.0107 18.4896 14.5837V10.417C18.4896 9.98991 18.8438 9.63574 19.2709 9.63574C19.698 9.63574 20.0521 9.98991 20.0521 10.417V14.5837Z" fill="currentColor" />
        <path d="M5.20829 14.583C3.95829 14.583 2.84371 15.1351 2.08329 15.9997C1.43746 16.7393 1.04163 17.6976 1.04163 18.7497C1.04163 19.5309 1.26038 20.2705 1.64579 20.8955C2.36454 22.1038 3.68746 22.9163 5.20829 22.9163C6.26038 22.9163 7.21871 22.5309 7.94788 21.8747C8.27079 21.6038 8.55204 21.2705 8.77079 20.8955C9.15621 20.2705 9.37496 19.5309 9.37496 18.7497C9.37496 16.4476 7.51038 14.583 5.20829 14.583ZM6.87496 20.3955C6.71871 20.5518 6.52079 20.6247 6.32288 20.6247C6.12496 20.6247 5.92704 20.5518 5.77079 20.3955L5.21871 19.8434L4.64579 20.4163C4.48954 20.5726 4.29163 20.6455 4.09371 20.6455C3.89579 20.6455 3.69788 20.5726 3.54163 20.4163C3.23954 20.1143 3.23954 19.6143 3.54163 19.3122L4.11454 18.7393L3.56246 18.1872C3.26038 17.8851 3.26038 17.3851 3.56246 17.083C3.86454 16.7809 4.36454 16.7809 4.66663 17.083L5.21871 17.6351L5.73954 17.1143C6.04163 16.8122 6.54163 16.8122 6.84371 17.1143C7.14579 17.4163 7.14579 17.9163 6.84371 18.2184L6.32288 18.7393L6.87496 19.2913C7.17704 19.5934 7.17704 20.083 6.87496 20.3955Z" fill="currentColor" />
      </svg>,
    },
  ];
};
export default InvoiceStatistics;
