import router from "next/router";
import { useEffect, useState } from "react";
import { GetInvoicesParams } from "../../../../api/interfaces/invoices.interface";
import { DownloadInvoicePdf, GetInvoices } from "../../../../api/invoices";
import { useFetcher } from "../../../../api/utils";
import { InvoiceInterface, INVOICE_STATUSES, INVOICE_TYPES } from "../../../../assets/interfaces/invoices";
import { toAppUrl } from "../../../../assets/js/utils/functions";
import authContext from "../../../../contexts/auth-context";
import ClearSearch from "../../../clear-search";
import useCopyClipboard from "../../../hooks/useCopyClipboard";
import useFluxState from "../../../hooks/useFluxState";
import { useModals } from "../../../hooks/useModals";
import usePagination from "../../../hooks/usePagination";
import usePDFDownloads from "../../../hooks/usePDFDownloads";
import useScreenSize from "../../../hooks/useScreenSize";
import useSearchParams from "../../../hooks/useSearchParams";
import Portal from "../../../portal";
import AppSearchBar from "../../../ui/app-search-bar";
import { AppBtn } from "../../../ui/buttons";
import Pagination from "../../../ui/pagination";
import Table, { TableBody, TableHead, TableHeadItem } from "../../../ui/table";
import DeleteInvoiceItemModal from "../modals/delete";
import EditInvoiceModal from "../modals/edit";
import UpdateStatusModal from "../modals/update-status";
import InvoiceContentState from "./invoice-content-state";
import InvoiceInfo from "./invoice-info";
import { InvoiceItemMobile } from "./invoice-item-mobile";
import InvoiceItems from "./invoice-items";

interface Props {}

const InvoiceTable: React.FC<Props> = ({}) => {
  const { storeId } = authContext.useContainer();
  const { width, isSmall } = useScreenSize();
  // const isSmall = width < 800;
  const PER_PAGE = 10;
  const { currentPage, goNext, goPrevious, setPage } = usePagination();
  const { modals, toggleModal } = useModals(["info", "delete", "update", "edit"]);
  const [currentInvoiceIndex, setCurrentInvoiceIndex] = useState<number>(0);
  const [searchQuery, setSearchQuery] = useState("");
  const { search, showInvoice } = useSearchParams(["search", "showInvoice"]);
  const invoiceRequest = useFetcher<GetInvoicesParams>(GetInvoices, {
    filter: { search: search ?? "", type: INVOICE_TYPES.REGULAR },
    page: currentPage,
    per_page: PER_PAGE,
  });
  const [invoiceItems, setInvoiceItems] = useFluxState<InvoiceInterface[]>(
    invoiceRequest?.response?.data?.invoices ?? []
  );
  const [fullViewOnMobile, setFullViewOnMobile] = useState(false);
  const currentInvoice = invoiceItems?.[currentInvoiceIndex];
  const [copied, copy] = useCopyClipboard(toAppUrl(null, true, true), { successDuration: 500 });
  const { isLoadingPdf, download } = usePDFDownloads({
    request: DownloadInvoicePdf,
    data: { invoiceId: currentInvoice?.invoice_id },
    filename: `invoice-${currentInvoice?.invoice_id}`,
  });
  // const [isLoadingPdf, setIsLoadingPdf] = useState(false);
  const [error, setError] = useState(undefined);
  const pageNotReady = invoiceRequest.isLoading || invoiceRequest.error || invoiceItems.length < 1;

  useEffect(() => {
    if (showInvoice) {
      openSpecificInvoice();
    }
  }, [showInvoice]);

  const openSpecificInvoice = async () => {
    const [res, err] = await invoiceRequest.makeRequest();

    if (res) {
      // setCurrentInvoiceIndex(0);
      setTimeout(() => {
        toggleModal("info");
      }, 200);
    }
  };

  const onAction = (action: string, index: number) => {
    if (!invoiceItems[index]) return;

    setCurrentInvoiceIndex(index);
    switch (action) {
      case "click":
        toggleModal("info");
        break;
      case "copy-link":
        copy(toAppUrl(`invoices/${invoiceItems[index].invoice_id}`, true, true));
        break;
      case "delete":
        toggleModal("delete");
        break;
      case "edit":
        toggleModal("edit");
        break;
      case "download":
        download();
        break;
      default:
      //do nothing
    }
  };

  const updateInvoiceList = (invoice: InvoiceInterface) => {
    const invoicesCopy = [...invoiceItems];
    invoicesCopy[currentInvoiceIndex] = invoice;
    setInvoiceItems(invoicesCopy);
  };

  const deleteCurrentInvoice = () => {
    const invoicesCopy = [...invoiceItems];

    invoicesCopy.splice(currentInvoiceIndex, 1);

    if (modals.info.show) {
      toggleModal("info");
    }

    setInvoiceItems(invoicesCopy);
  };

  return (
    <div className="w-full pb-10">
      <div className="flex items-center justify-between py-5">
        <h1 className="text-base md:text-xl font-bold">All invoices</h1>

        <div className="flex items-center gap-2">
          <AppSearchBar
            placeholder="Search Invoices by id"
            {...{ searchQuery, setSearchQuery, fullViewOnMobile, setFullViewOnMobile }}
          />
          {!isSmall && (
            <AppBtn size="md" onClick={() => router.push("/payments/invoices/create")}>
              Create New Invoice
            </AppBtn>
          )}
          {isSmall && (
            <AppBtn
              size="md"
              onClick={() => router.push("/payments/invoices/create")}
              className="!rounded-full !p-0 !h-9 !w-9"
            >
              {/* prettier-ignore */}
              <svg width={17} height={17} viewBox="0 0 17 17" fill="none" >
                    <path d="M4.25 8.5h8.5M8.5 12.75v-8.5" stroke="#fff" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </AppBtn>
          )}
        </div>
      </div>
      <ClearSearch search={search} />
      {pageNotReady && <InvoiceContentState request={invoiceRequest} isEmpty={invoiceItems.length < 1} />}

      {!pageNotReady && (
        <>
          {!isSmall && (
            <Table className="hidden sm:table">
              <TableHead>
                {/* <TableHeadItem>INVOICE ID</TableHeadItem> */}
                <TableHeadItem>Title</TableHeadItem>
                <TableHeadItem>Customer</TableHeadItem>
                <TableHeadItem>AMOUNT</TableHeadItem>
                <TableHeadItem>DATE DUE</TableHeadItem>
                <TableHeadItem>STATUS</TableHeadItem>
                <TableHeadItem></TableHeadItem>
              </TableHead>
              <TableBody>
                {invoiceItems.map((item, index) => {
                  return (
                    <InvoiceItems
                      item={item}
                      isLoadingPdf={currentInvoiceIndex === index && isLoadingPdf}
                      key={index}
                      onAction={(action: string) => onAction(action, index)}
                    />
                  );
                })}
              </TableBody>
            </Table>
          )}
          {isSmall && (
            <ul className="block md:hidden">
              {invoiceItems.map((item, index) => {
                return (
                  <InvoiceItemMobile
                    key={index}
                    index={index}
                    item={item}
                    onAction={(action: string) => onAction(action, index)}
                  />
                );
              })}
            </ul>
          )}
          <Pagination
            {...{
              goNext,
              goPrevious,
              per_page: PER_PAGE,
              label: "invoices",
              data: invoiceRequest?.response,
              currentPage,
              length: invoiceItems?.length,
              setPage,
            }}
          />
        </>
      )}
      <Portal>
        {currentInvoice && (
          <DeleteInvoiceItemModal
            deleteInvoice={deleteCurrentInvoice}
            invoice={currentInvoice}
            toggle={() => toggleModal("delete")}
            show={modals.delete.show}
          />
        )}
        {currentInvoice && (
          <UpdateStatusModal
            updateInvoice={(data: InvoiceInterface) => updateInvoiceList({ ...currentInvoice, ...data })}
            invoice={currentInvoice}
            toggle={() => toggleModal("update")}
            show={modals.update.show}
          />
        )}
        {currentInvoice && (
          <EditInvoiceModal
            invoice={currentInvoice}
            toggle={() => toggleModal("edit")}
            show={modals.edit.show}
            updateInvoices={updateInvoiceList}
          />
        )}
      </Portal>

      {currentInvoice && (
        <InvoiceInfo
          updateInvoice={() => toggleModal("update")}
          deleteInvoice={() => toggleModal("delete")}
          editInvoice={() => {
            toggleModal("edit");
          }}
          show={modals.info.show}
          toggle={() => toggleModal("info")}
          invoice={currentInvoice}
          downloadPdf={() => download()}
          isLoadingPdf={isLoadingPdf}
        />
      )}
    </div>
  );
};

export default InvoiceTable;
