import React from "react";
import { GetInvoicesParams } from "../../../../api/interfaces/invoices.interface";
import { RequestInterface } from "../../../../api/utils";
import { reloadPage } from "../../../../assets/js/utils/functions";
import { AppBtn } from "../../../ui/buttons";
import ContentState from "../../../ui/content-state";

interface IProps {
  request: RequestInterface<GetInvoicesParams>;
  isEmpty: boolean;
  type?: string;
  path?: string;
}

const InvoiceContentState: React.FC<IProps> = ({
  request,
  isEmpty,
  type = "Invoice",
  path = "/payments/invoices/create",
}) => {
  const lowercaseType = type.toLocaleLowerCase();
  return (
    <ContentState
      title={`No ${lowercaseType}s to show`}
      description={`Create your first ${lowercaseType}`}
      loadingText={`Loading ${type}s...`}
      isEmpty={isEmpty}
      isLoading={request.isLoading}
      error={request.error}
      errorTitle="Something went wrong"
      errorMessage={`Couldn't load ${lowercaseType}s, please retry`}
      errorAction={
        <AppBtn size="md" onClick={reloadPage}>
          Reload page
        </AppBtn>
      }
      emptyIcon={
        // prettier-ignore
        <svg className="w-7.5 sm:w-9 lg:w-10" viewBox="0 0 35 35" fill="none">
        <path d="M23.0125 2.91699H11.9875C6.47499 2.91699 5.10416 4.38991 5.10416 10.267V26.6878C5.10416 30.567 7.23332 31.4857 9.81457 28.7149L9.82916 28.7003C11.025 27.4316 12.8479 27.5337 13.8833 28.9191L15.3562 30.8878C16.5375 32.4482 18.4479 32.4482 19.6292 30.8878L21.1021 28.9191C22.1521 27.5191 23.975 27.417 25.1708 28.7003C27.7667 31.4712 29.8812 30.5524 29.8812 26.6732V10.267C29.8958 4.38991 28.525 2.91699 23.0125 2.91699ZM21.875 17.1357H13.125C12.5271 17.1357 12.0312 16.6399 12.0312 16.042C12.0312 15.4441 12.5271 14.9482 13.125 14.9482H21.875C22.4729 14.9482 22.9687 15.4441 22.9687 16.042C22.9687 16.6399 22.4729 17.1357 21.875 17.1357ZM23.3333 11.3024H11.6667C11.0687 11.3024 10.5729 10.8066 10.5729 10.2087C10.5729 9.61074 11.0687 9.11491 11.6667 9.11491H23.3333C23.9312 9.11491 24.4271 9.61074 24.4271 10.2087C24.4271 10.8066 23.9312 11.3024 23.3333 11.3024Z" fill="#AAAAAA"/>
      </svg>
      }
    >
      <AppBtn size="md" href={path}>
        Create {type}
      </AppBtn>
    </ContentState>
  );
};

export default InvoiceContentState;
