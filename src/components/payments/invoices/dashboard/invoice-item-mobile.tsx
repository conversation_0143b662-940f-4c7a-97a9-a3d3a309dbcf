import { InvoiceInterface, INVOICE_STATUSES, INVOICE_TYPES } from "../../../../assets/interfaces/invoices";
import { toCurrency } from "../../../../assets/js/utils/functions";
import Badge from "../../../ui/badge";
import { RoundActionBtn } from "../../../ui/buttons";
import Dropdown, { DropdownItem } from "../../../ui/dropdown-new";
import Toggle from "../../../ui/toggle";
import { getBadgeStatusColor } from "./invoice-items";

interface DiscountMobileProps {
  index?: number;
  item: InvoiceInterface;
  onAction?: (action: "click" | "edit" | "delete" | "download" | "copy-link") => void;
}
export const InvoiceItemMobile: React.FC<DiscountMobileProps> = ({ index, item, onAction }) => {
  const dropdownItems: DropdownItem[] = [
    {
      text: "Copy Link",
      link: undefined,
      onClick: () => {
        onAction("copy-link");
      },
      icon: <RoundActionBtn icon="link" size="sm" />,
      skip: item.is_draft,
    },
    {
      text: "Edit Invoice",
      link: undefined,
      onClick: () => {
        onAction("edit");
      },
      icon: <RoundActionBtn icon="edit" size="sm" />,
      skip: item.status !== INVOICE_STATUSES.PENDING,
    },
    {
      text: item.type === INVOICE_TYPES.PAYMENT_LINK ? "Download Receipt" : "Download Invoice",
      link: undefined,
      onClick: () => {
        onAction("download");
      },
      icon: <RoundActionBtn icon="download" size="sm" />,
      skip: item.is_draft || (item.type === INVOICE_TYPES.PAYMENT_LINK && item.status !== INVOICE_STATUSES.PAID),
    },
    {
      text: "Delete Invoice",
      link: undefined,
      onClick: () => {
        onAction("delete");
      },
      icon: <RoundActionBtn icon="delete" size="sm" />,
      skip: item.status !== INVOICE_STATUSES.PENDING,
    },
  ];

  const discountColors = ["accent-orange-500", "accent-green-500", "accent-red-500", "accent-yellow-500"];
  const colorClass = discountColors[index % discountColors.length];

  return (
    <li className="rounded-10 border border-grey-divider p-3 mb-2.5 cursor-pointer" onClick={() => onAction("click")}>
      <div className="relative flex items-center">
        <figure
          className={`flex-shrink-0 text-${colorClass} bg-${colorClass} bg-opacity-10 w-14 h-14  rounded-md flex items-center justify-center overflow-hidden`}
        >
          {/* prettier-ignore */}
          <>
            {
              item.type === INVOICE_TYPES.REGULAR ? (
                <svg width="24" viewBox="0 0 20 20" fill="none">
                  <path d="M4.66155 17.1582C5.33724 16.4543 6.36726 16.5103 6.96054 17.2782L7.79279 18.3581C8.46024 19.214 9.53969 19.214 10.2071 18.3581L11.0394 17.2782C11.6327 16.5103 12.6627 16.4543 13.3384 17.1582C14.8051 18.678 15.9999 18.1741 15.9999 16.0464V7.0315C16.0082 3.8079 15.2336 3 12.1188 3H5.88933C2.77457 3 2 3.8079 2 7.0315V16.0384C2 18.1741 3.20306 18.67 4.66155 17.1582Z" fill="currentColor"/>
                  <path d="M7.66155 15.1582C8.33724 14.4543 9.36726 14.5103 9.96054 15.2782L10.7928 16.3581C11.4602 17.214 12.5397 17.214 13.2071 16.3581L14.0394 15.2782C14.6327 14.5103 15.6627 14.4543 16.3384 15.1582C17.8051 16.678 18.9999 16.1741 18.9999 14.0464V5.0315C19.0082 1.8079 18.2336 1 15.1188 1H8.88933C5.77457 1 5 1.8079 5 5.0315V14.0384C5 16.1741 6.20306 16.67 7.66155 15.1582Z" fill="currentColor"/>
                  <path d="M7.66155 15.1582C8.33724 14.4543 9.36726 14.5103 9.96054 15.2782L10.7928 16.3581C11.4602 17.214 12.5397 17.214 13.2071 16.3581L14.0394 15.2782C14.6327 14.5103 15.6627 14.4543 16.3384 15.1582C17.8051 16.678 18.9999 16.1741 18.9999 14.0464V5.0315C19.0082 1.8079 18.2336 1 15.1188 1H8.88933C5.77457 1 5 1.8079 5 5.0315V14.0384C5 16.1741 6.20306 16.67 7.66155 15.1582Z" fill="white" fillOpacity="0.5"/>
                  <path d="M15.3684 7H8.63158C8.28632 7 8 6.77333 8 6.5C8 6.22667 8.28632 6 8.63158 6H15.3684C15.7137 6 16 6.22667 16 6.5C16 6.77333 15.7137 7 15.3684 7Z" fill="white"/>
                  <path d="M13.4 10H8.6C8.272 10 8 9.77333 8 9.5C8 9.22667 8.272 9 8.6 9H13.4C13.728 9 14 9.22667 14 9.5C14 9.77333 13.728 10 13.4 10Z" fill="white"/>
                </svg>
              ) : (
                <svg width="24" viewBox="0 0 24 24" fill="none">
                  <path d="M19.0693 14.2401C18.7793 14.5301 18.3193 14.5301 18.0393 14.2401C17.7493 13.9501 17.7493 13.4901 18.0393 13.2101C20.0393 11.2101 20.0393 7.9601 18.0393 5.9701C16.0393 3.9801 12.7893 3.9701 10.7993 5.9701C8.8093 7.9701 8.7993 11.2201 10.7993 13.2101C11.0893 13.5001 11.0893 13.9601 10.7993 14.2401C10.5093 14.5301 10.0493 14.5301 9.7693 14.2401C7.1993 11.6701 7.1993 7.4901 9.7693 4.9301C12.3393 2.3701 16.5193 2.3601 19.0793 4.9301C21.6393 7.5001 21.6393 11.6701 19.0693 14.2401Z" fill="currentColor"/>
                  <path opacity="0.4" d="M19.0695 4.92994C21.6395 7.49994 21.6395 11.6699 19.0695 14.2399C18.1895 15.1199 17.1095 15.6999 15.9795 15.9799C16.5095 13.8199 15.9295 11.4499 14.2395 9.75994C12.5495 8.06994 10.1795 7.48994 8.01953 8.01994C8.29953 6.88994 8.86953 5.80994 9.75953 4.92994C12.3295 2.35994 16.4995 2.35994 19.0695 4.92994Z" fill="currentColor"/>
                  <path d="M4.9307 9.75998C5.2207 9.46998 5.68071 9.46998 5.96071 9.75998C6.25071 10.05 6.25071 10.51 5.96071 10.79C3.96071 12.79 3.96071 16.04 5.96071 18.03C7.9607 20.02 11.2107 20.03 13.2007 18.03C15.1907 16.03 15.2007 12.78 13.2007 10.79C12.9107 10.5 12.9107 10.04 13.2007 9.75998C13.4907 9.46998 13.9507 9.46998 14.2307 9.75998C16.8007 12.33 16.8007 16.51 14.2307 19.07C11.6607 21.63 7.48071 21.64 4.92071 19.07C2.36071 16.5 2.3607 12.33 4.9307 9.75998Z" fill="currentColor"/>
                  <path opacity="0.5" d="M14.2395 9.75988C15.9295 11.4499 16.5095 13.8199 15.9795 15.9799C15.6995 17.1099 15.1195 18.1899 14.2395 19.0699C11.6695 21.6399 7.49945 21.6399 4.92945 19.0699C2.35945 16.4999 2.35945 12.3299 4.92945 9.75988C5.80945 8.86988 6.88945 8.29988 8.01945 8.01988C10.1795 7.48988 12.5495 8.06988 14.2395 9.75988Z" fill="currentColor"/>
                </svg>
              )
            }
          </>
        </figure>
        <div className="flex items-center justify-between ml-2.5 w-full">
          <div>
            <h6 className="text-sm text-dark max-w-[215px] sm:max-w-none whitespace-nowrap overflow-ellipsis overflow-hidden font-medium mb-2.5 mt-0.5 capitalize">
              {item?.title}
            </h6>
            <span className="text-1xs text-black  font-semibold">{toCurrency(item.total_amount, item?.currency)}</span>
          </div>
          {/* <div className="flex items-center justify-between"> */}
          <Badge
            {...{
              color: getBadgeStatusColor(item.status, item?.is_draft),
              text: item?.is_draft ? "DRAFT" : item.status,
            }}
            className="absolute -bottom-0 right-0"
          />
          {/* </div> */}
        </div>
        <div className="absolute -top-1 right-0">
          <Dropdown items={dropdownItems} vPosition="BOTTOM" gap={false}>
            <button className="z-[999] dropdown-toggle text-black-400 p-2 -mt-2 -mr-2">
              {/* prettier-ignore */}
              <svg className="w-5 h-5" viewBox="0 0 20 20" fill="none">
                <g opacity="0.5">
                  <path d="M4.16667 8.73837C3.25 8.73837 2.5 9.48837 2.5 10.405C2.5 11.3217 3.25 12.0717 4.16667 12.0717C5.08333 12.0717 5.83333 11.3217 5.83333 10.405C5.83333 9.48837 5.08333 8.73837 4.16667 8.73837Z" fill="currentColor" />
                  <path d="M15.8337 8.73837C14.917 8.73837 14.167 9.48837 14.167 10.405C14.167 11.3217 14.917 12.0717 15.8337 12.0717C16.7503 12.0717 17.5003 11.3217 17.5003 10.405C17.5003 9.48837 16.7503 8.73837 15.8337 8.73837Z" fill="currentColor" />
                  <path d="M9.99967 8.73837C9.08301 8.73837 8.33301 9.48837 8.33301 10.405C8.33301 11.3217 9.08301 12.0717 9.99967 12.0717C10.9163 12.0717 11.6663 11.3217 11.6663 10.405C11.6663 9.48837 10.9163 8.73837 9.99967 8.73837Z" fill="currentColor" />
                </g>
              </svg>
            </button>
          </Dropdown>
        </div>
      </div>
    </li>
  );
};

const icons = {
  edit:
    // prettier-ignore
    <svg width="14" viewBox="0 0 16 16" fill="none">
      <path d="M3.69332 13.0137C3.28666 13.0137 2.90666 12.8737 2.63332 12.6137C2.28666 12.287 2.11999 11.7937 2.17999 11.2604L2.42666 9.10036C2.47332 8.69369 2.71999 8.15369 3.00666 7.86036L8.47999 2.06702C9.84666 0.620358 11.2733 0.580358 12.72 1.94703C14.1667 3.31369 14.2067 4.74036 12.84 6.18703L7.36666 11.9804C7.08666 12.2804 6.56666 12.5604 6.15999 12.627L4.01332 12.9937C3.89999 13.0004 3.79999 13.0137 3.69332 13.0137ZM10.62 1.94036C10.1067 1.94036 9.65999 2.26036 9.20666 2.74036L3.73332 8.54036C3.59999 8.68036 3.44666 9.01369 3.41999 9.20703L3.17332 11.367C3.14666 11.587 3.19999 11.767 3.31999 11.8804C3.43999 11.9937 3.61999 12.0337 3.83999 12.0004L5.98666 11.6337C6.17999 11.6004 6.49999 11.427 6.63332 11.287L12.1067 5.49369C12.9333 4.61369 13.2333 3.80036 12.0267 2.66703C11.4933 2.15369 11.0333 1.94036 10.62 1.94036Z" fill="currentColor" />
      <path d="M11.56 7.30023C11.5466 7.30023 11.5266 7.30023 11.5133 7.30023C9.4333 7.09356 7.75996 5.51356 7.43996 3.44689C7.39996 3.17356 7.58663 2.92023 7.85996 2.87356C8.1333 2.83356 8.38663 3.02023 8.4333 3.29356C8.68663 4.90689 9.9933 6.14689 11.62 6.30689C11.8933 6.33356 12.0933 6.58022 12.0666 6.85356C12.0333 7.10689 11.8133 7.30023 11.56 7.30023Z" fill="currentColor" />
      <path d="M14 15.167H2C1.72667 15.167 1.5 14.9403 1.5 14.667C1.5 14.3937 1.72667 14.167 2 14.167H14C14.2733 14.167 14.5 14.3937 14.5 14.667C14.5 14.9403 14.2733 15.167 14 15.167Z" fill="currentColor" />
    </svg>,
  delete:
    // prettier-ignore
    <svg width="14" viewBox="0 0 20 20" fill="none">
      <path d="M17.0142 5.45236C16.998 5.45236 16.9737 5.45236 16.9494 5.45236C12.6635 5.02296 8.38571 4.86093 4.14844 5.29032L2.49566 5.45236C2.15539 5.48477 1.85562 5.24171 1.82321 4.90144C1.7908 4.56116 2.03386 4.26949 2.36604 4.23708L4.01881 4.07505C8.329 3.63755 12.6959 3.80769 17.0709 4.23708C17.4031 4.26949 17.6461 4.56926 17.6137 4.90144C17.5894 5.21741 17.3221 5.45236 17.0142 5.45236Z" fill="currentColor" strokeWidth="1"/>
      <path d="M6.88754 4.63422C6.85513 4.63422 6.82272 4.63422 6.78221 4.62612C6.45814 4.56941 6.23129 4.25344 6.288 3.92936L6.46624 2.86802C6.59587 2.09024 6.77411 1.0127 8.66184 1.0127H10.7845C12.6804 1.0127 12.8586 2.13075 12.9801 2.87612L13.1584 3.92936C13.2151 4.26154 12.9882 4.57751 12.6642 4.62612C12.332 4.68283 12.016 4.45598 11.9674 4.13191L11.7892 3.07867C11.6757 2.37381 11.6514 2.23608 10.7926 2.23608H8.66994C7.81115 2.23608 7.79494 2.3495 7.67342 3.07057L7.48707 4.12381C7.43846 4.42358 7.1792 4.63422 6.88754 4.63422Z" fill="currentColor" strokeWidth="1"/>
      <path d="M12.3222 18.4319H7.12079C4.29325 18.4319 4.17982 16.8682 4.0907 15.6043L3.56408 7.44576C3.53977 7.11359 3.79903 6.82192 4.13121 6.79762C4.47149 6.78141 4.75505 7.03257 4.77936 7.36475L5.30598 15.5233C5.3951 16.7548 5.42751 17.2166 7.12079 17.2166H12.3222C14.0236 17.2166 14.056 16.7548 14.137 15.5233L14.6636 7.36475C14.6879 7.03257 14.9796 6.78141 15.3118 6.79762C15.6439 6.82192 15.9032 7.10549 15.8789 7.44576L15.3523 15.6043C15.2632 16.8682 15.1497 18.4319 12.3222 18.4319Z" fill="currentColor" strokeWidth="1"/>
      <path d="M11.0673 13.976H8.36936C8.03718 13.976 7.76172 13.7006 7.76172 13.3684C7.76172 13.0362 8.03718 12.7607 8.36936 12.7607H11.0673C11.3994 12.7607 11.6749 13.0362 11.6749 13.3684C11.6749 13.7006 11.3994 13.976 11.0673 13.976Z" fill="currentColor" strokeWidth="1"/>
      <path d="M11.7484 10.7348H7.69748C7.36531 10.7348 7.08984 10.4593 7.08984 10.1272C7.08984 9.79499 7.36531 9.51953 7.69748 9.51953H11.7484C12.0806 9.51953 12.356 9.79499 12.356 10.1272C12.356 10.4593 12.0806 10.7348 11.7484 10.7348Z" fill="currentColor" strokeWidth="1"/>
    </svg>,
  download:
    // prettier-ignore
    <svg width="14" viewBox="0 0 14 14" fill="none">
      <path d="M7.08146 9.291L7.08146 1.26367" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M9.02539 7.33887L7.08139 9.29087L5.13739 7.33887" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M10.1699 4.41895H10.7919C12.1486 4.41895 13.2479 5.51828 13.2479 6.87561L13.2479 10.1316C13.2479 11.4849 12.1513 12.5816 10.7979 12.5816L3.37126 12.5816C2.01459 12.5816 0.914592 11.4816 0.914592 10.1249L0.914592 6.86828C0.914592 5.51561 2.01193 4.41895 3.36459 4.41895H3.99259" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
    </svg>,
};
