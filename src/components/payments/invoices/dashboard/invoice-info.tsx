import React, { useEffect, useState } from "react";
import { InvoiceParams } from "../../../../api/interfaces/invoices.interface";
import { DownloadInvoicePdf, UpdateInvoiceToPaid } from "../../../../api/invoices";
import { useRequest } from "../../../../api/utils";
import { InvoiceInterface, INVOICE_STATUSES } from "../../../../assets/interfaces/invoices";
import { resolvePhone, toAppUrl, toCurrency } from "../../../../assets/js/utils/functions";
import { formatDateString } from "../../../../assets/js/utils/utils";
import useCopyClipboard from "../../../hooks/useCopyClipboard";
import { useModals } from "../../../hooks/useModals";
import usePreventFirstRun from "../../../hooks/usePreventFirstRun";
import Portal from "../../../portal";
import ViewReceiptModal from "../../../receipts/view-receipt-modal";
import AsideToBottomSheet from "../../../ui/aside-to-bottom-sheet";
import Badge from "../../../ui/badge";
import { AppBtn } from "../../../ui/buttons";
import Checkbox from "../../../ui/form-elements/checkbox";
import FullScreenModal from "../../../ui/full-screen-modal";
import StoreLogo from "../../../ui/store-logo";
import EditInvoiceModal from "../modals/edit";
import SendModal from "../modals/send";
import { icons, InvoiceActionItem, InvoiceInfoItem } from "./invoice-info-utils";
import { getBadgeStatusColor } from "./invoice-items";
`import UpdateStatusModal from "../modals/update-status";
`;
interface IProps {
  show: boolean;
  toggle: () => void;
  invoice: InvoiceInterface;
  isLoadingPdf: boolean;
  downloadPdf: VoidFunction;
  updateInvoice: VoidFunction;
  deleteInvoice: VoidFunction;
  editInvoice: VoidFunction;
}

const InvoiceInfo: React.FC<IProps> = ({
  isLoadingPdf,
  show,
  toggle,
  invoice,
  updateInvoice,
  deleteInvoice,
  downloadPdf,
  editInvoice,
}) => {
  const invoiceLink = toAppUrl(`invoices/${invoice.invoice_id}`, true, true);
  const [_, copy] = useCopyClipboard(invoiceLink, { successDuration: 200 });
  const { modals, toggleModal } = useModals(["send", "delete", "update", "receipt"]);
  const isPaid = invoice.status === INVOICE_STATUSES.PAID;
  const [remount, setRemount] = useState(false); //used to remount invoice iframe

  usePreventFirstRun(() => {
    setRemount(true);

    setTimeout(() => {
      setRemount(false);
    }, 500);
  }, [invoice]);

  return (
    <FullScreenModal
      {...{ show, toggle }}
      title={invoice.title}
      // titleAddon={
      //   <Badge
      //     {...{
      //       color: getBadgeStatusColor(invoice.status, invoice?.is_draft),
      //       text: invoice?.is_draft ? "DRAFT" : invoice.status,
      //     }}
      //     size="sm"
      //     className="ml-1.5 mt-1"
      //   />
      // }
    >
      <div className="min-h-full w-full bg-grey-fields-200">
        <div className="grid max-w-[1200px] w-full mx-auto pb-25 lg:gap-5 lg:grid-cols-[minmax(400px,1fr),minmax(350px,38%)] items-start">
          <div className="w-full sm:px-8 sm:py-8 md:py-10 md:px-10 lg:py-12.5 lg:px-0 mx-auto">
            {invoice?.is_draft && (
              <div className="w-full bg-white min-h-screen flex items-center justify-center">
                <div className="flex items-center flex-col mt-[-25%]">
                  <figure className="bg-grey-fields-100 rounded-full h-20 w-20 sm:h-24 sm:w-24 flex items-center justify-center text-grey-muted">
                    {/* prettier-ignore */}
                    <svg width="50%" viewBox="0 0 24 24" fill="none">
                      <path opacity="0.4" d="M20.5 10.19H17.61C15.24 10.19 13.31 8.26 13.31 5.89V3C13.31 2.45 12.86 2 12.31 2H8.07C4.99 2 2.5 4 2.5 7.57V16.43C2.5 20 4.99 22 8.07 22H15.93C19.01 22 21.5 20 21.5 16.43V11.19C21.5 10.64 21.05 10.19 20.5 10.19Z" fill="currentColor"/>
                      <path d="M15.7997 2.20999C15.3897 1.79999 14.6797 2.07999 14.6797 2.64999V6.13999C14.6797 7.59999 15.9197 8.80999 17.4297 8.80999C18.3797 8.81999 19.6997 8.81999 20.8297 8.81999C21.3997 8.81999 21.6997 8.14999 21.2997 7.74999C19.8597 6.29999 17.2797 3.68999 15.7997 2.20999Z" fill="currentColor"/>
                    </svg>
                  </figure>
                  <h4 className="text-black-secondary font-bold text-base sm:text-lg mt-2.5 mb-0.5">
                    Cannot preview draft invoice
                  </h4>
                  <span className="text-1xs sm:text-sm text-dark">Edit and publish the invoice to see a preview</span>
                </div>
              </div>
            )}
            {!invoice?.is_draft && !remount && (
              <iframe
                className="w-full min-h-[120vh]"
                id="invoice-iframe"
                src={toAppUrl(`invoices/pdf/${invoice.invoice_id}`, true, true)}
              ></iframe>
            )}
          </div>
          <AsideToBottomSheet
            title={
              <div className="flex items-center">
                <h1 className="text-base sm:text-lg font-bold">Manage Invoice</h1>
              </div>
            }
          >
            <div className="flex-col">
              <div className="flex flex-col items-center  pb-4 sm:pb-5">
                <StoreLogo
                  className="h-12.5 w-12.5 md:h-15 md:w-15 text-xl sm:text-2lg lg:text-2xl"
                  logo=""
                  storeName={invoice?.receiver.name}
                />
                <h5 className="font-bold text-center text-lg sm:text-xl mb-1.25 font-display !leading-none mt-3.75">
                  {invoice?.receiver.name}
                </h5>
                <span className="text-1xs text-black text-center block">{resolvePhone(invoice?.receiver.phone)} </span>
                <Badge
                  {...{
                    color: getBadgeStatusColor(invoice.status, invoice?.is_draft),
                    text: invoice?.is_draft ? "DRAFT" : invoice.status,
                  }}
                  size="md"
                  className="ml-1.5 mt-2"
                />
              </div>
              <ul className="border-t border-grey-divider py-4 sm:py-5 space-y-2.5 sm:space-y-4">
                <InvoiceInfoItem title="Invoice ID" value={invoice?.invoice_id} icon={icons.id} />
                {invoice?.receiver.email && (
                  <InvoiceInfoItem title="Email" value={invoice?.receiver?.email} icon={icons.email} />
                )}
                {!isPaid && (
                  <InvoiceInfoItem
                    title="Date Due"
                    value={formatDateString(new Date(invoice.date_due))}
                    icon={icons.calendar}
                  />
                )}

                {isPaid && (
                  <InvoiceInfoItem
                    title="Date Paid"
                    value={formatDateString(new Date(invoice.paid_at))}
                    icon={icons.calendar}
                  />
                )}
                <InvoiceInfoItem
                  title="Total Amount"
                  value={toCurrency(invoice?.total_amount, invoice.currency)}
                  icon={icons.money}
                />
              </ul>
              {!invoice.is_draft && (
                <ul className="border-t border-grey-divider py-4 sm:py-5 space-y-3 sm:space-y-4">
                  <InvoiceActionItem
                    onClick={() => copy()}
                    label="Copy invoice link"
                    icon={icons.copy}
                    className="bg-primary-pastel !bg-opacity-100 text-primary-500"
                  />
                  <InvoiceActionItem
                    onClick={() => toggleModal("send")}
                    label="Send Invoice"
                    icon={icons.send}
                    className="bg-accent-red-500 text-accent-red-500"
                  />
                  <div className="flex items-center">
                    <InvoiceActionItem
                      onClick={() => downloadPdf()}
                      label="Download as PDF"
                      icon={icons.download}
                      className="bg-accent-orange-500 text-accent-orange-500"
                    />
                    {isLoadingPdf && <div className="spinner text-primary-500 !h-6 !w-6"></div>}
                  </div>
                </ul>
              )}
              {!isPaid && !invoice.is_draft && (
                <div className="border-t border-grey-divider py-2.5 sm:py-3.75 space-y-3 sm:space-y-4">
                  <div className="inline-flex items-center cursor-pointer" onClick={() => updateInvoice()}>
                    <div className="h-7.5 w-7.5 rounded-full bg-grey-fields-100 flex items-center justify-center">
                      <Checkbox onClick={(e) => e.stopPropagation()} id="invoice_status" checked={isPaid} name="test" />
                    </div>
                    <span className="text-dark text-sm inline-block ml-2">Mark as paid</span>
                  </div>
                </div>
              )}

              {isPaid && invoice?.receipt && (
                <ul className="border-t border-grey-divider py-2.5 sm:py-3.75 space-y-3 sm:space-y-4">
                  <InvoiceActionItem
                    onClick={() => toggleModal("receipt")}
                    label="Invoice Receipt"
                    icon={icons.receipt}
                    className="bg-accent-green-500 text-accent-green-500"
                  />
                </ul>
              )}
            </div>
            {!isPaid && (
              <div className="grid grid-cols-2 border-t border-grey-divider gap-2.5 pt-3.75 -mb-3.75 px-8 -mx-8 stick bottom-0">
                <AppBtn
                  onClick={() => deleteInvoice()}
                  className="!text-sm !flex-grow text-accent-red-500 bg"
                  color="neutral"
                >
                  Delete Invoice
                </AppBtn>
                <AppBtn className="!flex-grow !text-sm" onClick={editInvoice}>
                  Edit Invoice
                </AppBtn>
              </div>
            )}
            <Portal>
              <SendModal invoice={invoice} toggle={() => toggleModal("send")} show={modals.send.show}></SendModal>
              {invoice?.receipt && (
                <ViewReceiptModal
                  receipt_id={invoice?.receipt}
                  toggle={() => toggleModal("receipt")}
                  show={modals.receipt.show}
                />
              )}
            </Portal>
          </AsideToBottomSheet>
        </div>
      </div>
    </FullScreenModal>
  );
};

export default InvoiceInfo;
