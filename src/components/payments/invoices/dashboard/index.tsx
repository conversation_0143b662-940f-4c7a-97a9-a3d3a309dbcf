import React, { useEffect } from "react";
import { actionIsAllowed, SCOPES } from "../../../../assets/js/utils/permissions";
import authContext from "../../../../contexts/auth-context";
import DashboardLayout from "../../../ui/layouts/dashboard";
import InvoiceStatistics from "./invoice-statistics";
import InvoiceTable from "./invoice-table";
import SetupProgress from "./setup-progress";

interface Props {}

const InvoicesDashboard = ({}) => {
  return (
    <DashboardLayout title="Invoices" padding={false}>
      <div className="h-full overflow-y-auto pb-12.5 px-5 sm:px-6.25 lg:px-7.5">
        <InvoicesDashboardMain />
      </div>
    </DashboardLayout>
  );
};

const InvoicesDashboardMain = () => {
  const { userRole } = authContext.useContainer();
  const canManagePayments = actionIsAllowed({
    permission: SCOPES.WALLETS.CAN_MANAGE_WALLET,
    userRole,
  });

  return (
    <>
      {canManagePayments && <InvoiceStatistics />}
      <SetupProgress canManagePayments={canManagePayments} />
      <InvoiceTable />
    </>
  );
};

export default InvoicesDashboard;
