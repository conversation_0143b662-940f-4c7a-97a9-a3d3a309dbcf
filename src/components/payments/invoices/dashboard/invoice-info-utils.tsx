interface InvoiceInfoItemProps {
  title: string;
  value: string;
  icon: React.ReactElement;
}

export const InvoiceInfoItem: React.FC<InvoiceInfoItemProps> = ({ title, icon, value }) => {
  return (
    <li className="flex items-center justify-between">
      <div className="flex items-center ">
        <div className="rounded-full h-7 w-7 text-dark bg-grey-fields-100 flex items-center justify-center">{icon}</div>
        <span className="text-1xs md:text-sm ml-3.75 text-dark">{title}</span>
      </div>
      <span className="text-1xs md:text-sm ml-2 font-medium text-black-secondary">{value}</span>
    </li>
  );
};

interface InvoiceActionItemProps {
  label: string;
  icon: React.ReactElement;
  onClick?: VoidFunction;
  className?: string;
}

export const InvoiceActionItem: React.FC<InvoiceActionItemProps> = ({ label, icon, onClick, className }) => {
  return (
    <li className="w-full">
      <button
        className="flex items-center text-dark hover:text-black transition-all ease-out duration-200"
        onClick={onClick}
      >
        <figure className={`h-7 w-7 bg-opacity-10 rounded-full flex items-center justify-center mr-2.5 ${className}`}>
          {icon}
        </figure>
        <span className="text-sm font-medium">{label}</span>
      </button>
    </li>
  );
};

export const icons = {
  id: (
    <>
      {/* prettier-ignore */}
      <svg width="15" viewBox="0 0 24 24" fill="none">
        <path d="M10 3L8 21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M16 3L14 21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M3.5 9H21.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M2.5 15H20.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    </>
  ),
  email:
    // prettier-ignore
    <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
      <path d="M10.625 12.8125H4.375C2.5 12.8125 1.25 11.875 1.25 9.6875V5.3125C1.25 3.125 2.5 2.1875 4.375 2.1875H10.625C12.5 2.1875 13.75 3.125 13.75 5.3125V9.6875C13.75 11.875 12.5 12.8125 10.625 12.8125Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.625 5.625L8.66875 7.1875C8.025 7.7 6.96875 7.7 6.325 7.1875L4.375 5.625" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  calendar:
    // prettier-ignore
    <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
      <path d="M5 1.25V3.125" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10 1.25V3.125" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M2.1875 5.68164H12.8125" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M13.75 11.875C13.75 12.3438 13.6187 12.7875 13.3875 13.1625C12.9562 13.8875 12.1625 14.375 11.25 14.375C10.6188 14.375 10.0438 14.1437 9.60625 13.75C9.4125 13.5875 9.24375 13.3875 9.1125 13.1625C8.88125 12.7875 8.75 12.3438 8.75 11.875C8.75 10.4937 9.86875 9.375 11.25 9.375C12 9.375 12.6687 9.70624 13.125 10.225C13.5125 10.6687 13.75 11.2438 13.75 11.875Z" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.2754 11.8742L10.8941 12.493L12.2254 11.2617" stroke="currentColor" strokeWidth="0.9375" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M13.125 5.3125V10.225C12.6687 9.70624 12 9.375 11.25 9.375C9.86875 9.375 8.75 10.4937 8.75 11.875C8.75 12.3438 8.88125 12.7875 9.1125 13.1625C9.24375 13.3875 9.4125 13.5875 9.60625 13.75H5C2.8125 13.75 1.875 12.5 1.875 10.625V5.3125C1.875 3.4375 2.8125 2.1875 5 2.1875H10C12.1875 2.1875 13.125 3.4375 13.125 5.3125Z" stroke="currentColor" strokeWidth="0.9375" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7.49706 8.5625H7.50267" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5.18407 8.5625H5.18968" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5.18407 10.4375H5.18968" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  money:
    // prettier-ignore
    <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
      <path d="M10.625 12.8125H4.375C2.5 12.8125 1.25 11.875 1.25 9.6875V5.3125C1.25 3.125 2.5 2.1875 4.375 2.1875H10.625C12.5 2.1875 13.75 3.125 13.75 5.3125V9.6875C13.75 11.875 12.5 12.8125 10.625 12.8125Z" stroke="currentColor" strokeWidth="1" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7.5 9.375C8.53553 9.375 9.375 8.53553 9.375 7.5C9.375 6.46447 8.53553 5.625 7.5 5.625C6.46447 5.625 5.625 6.46447 5.625 7.5C5.625 8.53553 6.46447 9.375 7.5 9.375Z" stroke="currentColor" strokeWidth="1" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M3.4375 5.9375V9.0625" stroke="currentColor" strokeWidth="1" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M11.5625 5.9375V9.0625" stroke="currentColor" strokeWidth="1" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  copy:
    // prettier-ignore
    <svg className="w-3.75" viewBox="0 0 18 18" fill="none">
      <path d="M12 9.675V12.825C12 15.45 10.95 16.5 8.325 16.5H5.175C2.55 16.5 1.5 15.45 1.5 12.825V9.675C1.5 7.05 2.55 6 5.175 6H8.325C10.95 6 12 7.05 12 9.675Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M16.5 5.175V8.325C16.5 10.95 15.45 12 12.825 12H12V9.675C12 7.05 10.95 6 8.325 6H6V5.175C6 2.55 7.05 1.5 9.675 1.5H12.825C15.45 1.5 16.5 2.55 16.5 5.175Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  send:
    // prettier-ignore
    <svg className="w-3.75" viewBox="0 0 18 18" fill="none">
      <path d="M5.54977 4.74012L11.9173 2.61762C14.7748 1.66512 16.3273 3.22512 15.3823 6.08262L13.2598 12.4501C11.8348 16.7326 9.49477 16.7326 8.06977 12.4501L7.43977 10.5601L5.54977 9.93012C1.26727 8.50512 1.26727 6.17262 5.54977 4.74012Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7.58203 10.2374L10.267 7.54492" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  download:
    // prettier-ignore
    <svg className="w-3.75" viewBox="0 0 18 18" fill="none">
      <path d="M6.75 16.5H11.25C15 16.5 16.5 15 16.5 11.25V6.75C16.5 3 15 1.5 11.25 1.5H6.75C3 1.5 1.5 3 1.5 6.75V11.25C1.5 15 3 16.5 6.75 16.5Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M6.75 8.63281L9 10.8828L11.25 8.63281" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M9 10.8828V4.88281" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M4.5 12.3828C7.4175 13.3578 10.5825 13.3578 13.5 12.3828" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  receipt: (
    <>
      {/* prettier-ignore */}
      <svg width="15" viewBox="0 0 24 24" fill="none">
        <path d="M20 7.04V16.96C20 18.48 19.86 19.56 19.5 20.33C19.5 20.34 19.49 20.36 19.48 20.37C19.26 20.65 18.97 20.79 18.63 20.79C18.1 20.79 17.46 20.44 16.77 19.7C15.95 18.82 14.69 18.89 13.97 19.85L12.96 21.19C12.56 21.73 12.03 22 11.5 22C10.97 22 10.44 21.73 10.04 21.19L9.02002 19.84C8.31002 18.89 7.05999 18.82 6.23999 19.69L6.22998 19.7C5.09998 20.91 4.10002 21.09 3.52002 20.37C3.51002 20.36 3.5 20.34 3.5 20.33C3.14 19.56 3 18.48 3 16.96V7.04C3 5.52 3.14 4.44 3.5 3.67C3.5 3.66 3.50002 3.65 3.52002 3.64C4.09002 2.91 5.09998 3.09 6.22998 4.3L6.23999 4.31C7.05999 5.18 8.31002 5.11 9.02002 4.16L10.04 2.81C10.44 2.27 10.97 2 11.5 2C12.03 2 12.56 2.27 12.96 2.81L13.97 4.15C14.69 5.11 15.95 5.18 16.77 4.3C17.46 3.56 18.1 3.21 18.63 3.21C18.97 3.21 19.26 3.36 19.48 3.64C19.5 3.65 19.5 3.66 19.5 3.67C19.86 4.44 20 5.52 20 7.04Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8 10.25H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8 13.75H14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    </>
  ),
};
