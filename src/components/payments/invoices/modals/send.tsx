import { useFormik } from "formik";
import { useEffect, useState } from "react";
import * as Yup from "yup";
import { SendInvoiceViaMail } from "../../../../api";
import { SendInvoiceViaMailParams } from "../../../../api/interfaces";
import { useRequest } from "../../../../api/utils";
import { InvoiceInterface } from "../../../../assets/interfaces/invoices";
import { phoneValidation } from "../../../../assets/js/utils/common-validations";
import {
  getFieldvalues,
  phoneObjectFromString,
  phoneObjectToString,
  toAppUrl,
  toCurrency,
} from "../../../../assets/js/utils/functions";
import { getWhatsappLink } from "../../../../assets/js/utils/utils";
import { AppBtn } from "../../../ui/buttons";
import ErrorLabel from "../../../ui/error-label";
import { InputField, PhoneInput, TextArea } from "../../../ui/form-elements";
import Radio from "../../../ui/form-elements/radio";
import Modal, { ModalBody, ModalFooter } from "../../../ui/modal";
import SuccessAnimation from "../../../ui/success-animation";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  invoice: InvoiceInterface;
  paymentLink?: string;
  title?: string;
}

const SendModal: React.FC<Props> = ({ show, toggle, invoice, paymentLink, title = "Invoice" }) => {
  const { error, response, makeRequest, isLoading } = useRequest<SendInvoiceViaMailParams>(SendInvoiceViaMail);
  const [sendState, setSendState] = useState<"sending" | "sent">("sending");

  paymentLink = paymentLink ?? toAppUrl(`invoices/${invoice.invoice_id}`);

  useEffect(() => {
    if (response !== null) {
      setSendState("sent");
    }
  }, [response]);

  useEffect(() => {
    if (!show) {
      setSendState("sending");
    }
  }, [show]);

  const form = useFormik({
    initialValues: {
      body: `Here's your invoice for the payment of ${toCurrency(
        invoice.total_amount,
        invoice.currency
      )}, you can use this link to view it and make payment: ${paymentLink}`,
      phone: phoneObjectFromString(invoice?.receiver?.phone),
      email: invoice?.receiver?.email,
      method: "email",
      // attach: true,
    },
    validationSchema,
    onSubmit: (payload) => {
      const { email, phone, method, body } = payload;
      const isEmail = method === "email";

      if (isEmail) {
        makeRequest({
          id: invoice.id,
          data: { email, message: body },
        });
        return;
      }

      window.open(getWhatsappLink(phoneObjectToString(phone), body), "_blank");
      setSendState("sent");
    },
  });

  const { method, body, phone, email } = form.values;

  return (
    <Modal {...{ show, toggle }} title={`Send ${title}`} size="midi">
      <form className="flex flex-col flex-auto overflow-hidden" onSubmit={form.handleSubmit}>
        <ModalBody className="">
          {sendState === "sending" && (
            <>
              <ErrorLabel error={error?.message} />
              <div className="flex items-center gap-7.5">
                <div className="flex gap-2.5">
                  <Radio
                    name="email"
                    value={"email"}
                    chosen={method}
                    onChange={() => form.setFieldValue("method", "email")}
                  />
                  <span className="text-black-secondary text-sm font-medium">Email Address</span>
                </div>
                <div className="flex gap-2.5">
                  <Radio
                    name="whatsapp"
                    value={"whatsapp"}
                    chosen={method}
                    onChange={() => form.setFieldValue("method", "whatsapp")}
                  />
                  <span className="text-black-secondary text-sm font-medium">Whatsapp Number</span>
                </div>
              </div>
              <div className="mt-7.5">
                {method === "email" && (
                  <InputField label="Receiver Email" {...getFieldvalues("email", form)}></InputField>
                )}
                {method === "whatsapp" && <PhoneInput label="Phone Number" {...getFieldvalues("phone", form)} />}
                <TextArea label="Message Body" {...getFieldvalues("body", form)} />
                {method === "email" && (
                  <div className="flex items-center mt-2 py-2 px-2.5 bg-grey-fields-100 rounded-8">
                    {/* prettier-ignore */}
                    <svg width="22" viewBox="0 0 24 24" fill="none" className="text-accent-yellow-500">
                      <path opacity="0.93" d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM16.37 14.35L14.15 16.57C13.61 17.11 12.91 17.37 12.21 17.37C11.51 17.37 10.8 17.1 10.27 16.57C9.75 16.05 9.46 15.36 9.46 14.63C9.46 13.9 9.75 13.2 10.27 12.69L11.68 11.28C11.97 10.99 12.45 10.99 12.74 11.28C13.03 11.57 13.03 12.05 12.74 12.34L11.33 13.75C11.09 13.99 10.96 14.3 10.96 14.63C10.96 14.96 11.09 15.28 11.33 15.51C11.82 16 12.61 16 13.1 15.51L15.32 13.29C16.59 12.02 16.59 9.96 15.32 8.69C14.05 7.42 11.99 7.42 10.72 8.69L8.3 11.11C7.79 11.62 7.51 12.29 7.51 13C7.51 13.71 7.79 14.39 8.3 14.89C8.59 15.18 8.59 15.66 8.3 15.95C8.01 16.24 7.53 16.24 7.24 15.95C6.44 15.18 6 14.13 6 13.01C6 11.89 6.43 10.84 7.22 10.05L9.64 7.63C11.49 5.78 14.51 5.78 16.36 7.63C18.22 9.48 18.22 12.5 16.37 14.35Z" fill="currentColor"/>
                    </svg>
                    <span className="text-1xs text-dark inline-block ml-1.5 font-medium">
                      Invoice PDF will also be attached
                    </span>
                    {/* <Checkbox
                      id="attach_checkbox"
                      neutral
                      name="attach"
                      label="Attach a PDF of Invoice"
                      checked={attach}
                      onChange={() => form.setFieldValue("attach", !attach)}
                    /> */}
                  </div>
                )}
              </div>
            </>
          )}

          {sendState === "sent" && (
            <div className="flex flex-col items-center py-4">
              <SuccessAnimation />
              {/* <ErrorAnimation/> */}
              <h2 className="text-2xl sm:text-3xl font-bold mt-5">{title} Sent</h2>
              <span className="max-w-[300px] text-center text-sm sm:text-1sm text-black-secondary mt-1.25">
                {title}: {invoice.title} has been sent to {method === "email" ? email : phoneObjectToString(phone)}{" "}
                successfully
              </span>
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          {sendState === "sending" && (
            <AppBtn type="submit" isBlock size="lg" disabled={isLoading}>
              {isLoading ? "Sending..." : "Send Invoice"}
            </AppBtn>
          )}

          {sendState === "sent" && (
            <AppBtn isBlock size="lg" onClick={() => toggle(false)}>
              Close
            </AppBtn>
          )}
        </ModalFooter>
      </form>
    </Modal>
  );
};
export default SendModal;

const validationSchema = Yup.object({
  method: Yup.string(),
  email: Yup.string().when("method", {
    is: "email",
    then: Yup.string().email("Please enter a valid email").required("Email is required"),
    otherwise: null,
  }),
  phone: Yup.object().when("method", {
    is: "whatsapp",
    then: phoneValidation(),
    otherwise: null,
  }),
  body: Yup.string().required("Body is required"),
  // attach: Yup.boolean(),
});

// const stateIcons = {
//   successfull:
//     // prettier-ignore
//     <svg width="50" height="50" viewBox="0 0 50 50" fill="none">
//         <path d="M24.9994 4.16699C13.5202 4.16699 4.16602 13.5212 4.16602 25.0003C4.16602 36.4795 13.5202 45.8337 24.9994 45.8337C36.4785 45.8337 45.8327 36.4795 45.8327 25.0003C45.8327 13.5212 36.4785 4.16699 24.9994 4.16699ZM34.9577 20.2087L23.1452 32.0212C22.8535 32.3128 22.4577 32.4795 22.041 32.4795C21.6244 32.4795 21.2285 32.3128 20.9369 32.0212L15.041 26.1253C14.4368 25.5212 14.4368 24.5212 15.041 23.917C15.6452 23.3128 16.6452 23.3128 17.2493 23.917L22.041 28.7087L32.7494 18.0003C33.3535 17.3962 34.3535 17.3962 34.9577 18.0003C35.5619 18.6045 35.5619 19.5837 34.9577 20.2087Z" fill="white"/>
//     </svg>,
//   failed:
//     // prettier-ignore
//     <svg width="50" height="50" viewBox="0 0 50 50" fill="none">
//         <path d="M24.9994 4.16699C13.5202 4.16699 4.16602 13.5212 4.16602 25.0003C4.16602 36.4795 13.5202 45.8337 24.9994 45.8337C36.4785 45.8337 45.8327 36.4795 45.8327 25.0003C45.8327 13.5212 36.4785 4.16699 24.9994 4.16699ZM31.9993 29.792C32.6035 30.3962 32.6035 31.3962 31.9993 32.0003C31.6868 32.3128 31.291 32.4587 30.8952 32.4587C30.4994 32.4587 30.1035 32.3128 29.791 32.0003L24.9994 27.2087L20.2077 32.0003C19.8952 32.3128 19.4994 32.4587 19.1035 32.4587C18.7077 32.4587 18.3119 32.3128 17.9994 32.0003C17.3952 31.3962 17.3952 30.3962 17.9994 29.792L22.791 25.0003L17.9994 20.2087C17.3952 19.6045 17.3952 18.6045 17.9994 18.0003C18.6035 17.3962 19.6035 17.3962 20.2077 18.0003L24.9994 22.792L29.791 18.0003C30.3952 17.3962 31.3952 17.3962 31.9993 18.0003C32.6035 18.6045 32.6035 19.6045 31.9993 20.2087L27.2077 25.0003L31.9993 29.792Z" fill="white"/>
//     </svg>,
// };
