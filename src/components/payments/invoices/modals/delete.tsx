import { InvoiceParams } from "../../../../api/interfaces/invoices.interface";
import { DeleteInvoice } from "../../../../api/invoices";
import { useRequest } from "../../../../api/utils";
import { INVOICE_TYPES, InvoiceInterface } from "../../../../assets/interfaces/invoices";
import { AppBtn } from "../../../ui/buttons";
import ErrorLabel from "../../../ui/error-label";
import Modal, { ModalBody, ModalFooter } from "../../../ui/modal";
import SuccessLabel from "../../../ui/success-label";

interface Props {
  toggle: (state: boolean) => void;
  show: boolean;
  invoice: InvoiceInterface;
  deleteInvoice: () => void;
}

const DeleteInvoiceItemModal: React.FC<Props> = ({ toggle, show, invoice, deleteInvoice }) => {
  const label = invoice.type === INVOICE_TYPES.PAYMENT_LINK ? "Invoice" : "Payment Link";
  const { makeRequest, isLoading, error, response } = useRequest<InvoiceParams>(DeleteInvoice);
  const handleDelete = async () => {
    const [res, err] = await makeRequest({ invoiceId: invoice.id });
    if (res) {
      deleteInvoice();

      setTimeout(() => {
        toggle(false);
      }, 500);
    }
  };

  return (
    <Modal {...{ show, toggle }} title="Delete Coupon" size="sm">
      <ModalBody>
        <ErrorLabel error={error?.message} />
        <SuccessLabel message={response ? `${label} deleted successfully!` : ""} />
        <div className="text-center">
          <h5 className="text-black text-base font-semibold">Do you want to delete this {label}?</h5>
          <p className="text-sm text-grey-subtext mt-1 max-w-xs mx-auto">
            This {label} will be deleted permanently, if this {label.toLocaleLowerCase()} has been paid - Mark it as
            paid.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="danger" onClick={handleDelete} disabled={isLoading}>
          {isLoading ? `Deleting ${label}...` : `Delete ${label}`}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};
export default DeleteInvoiceItemModal;
