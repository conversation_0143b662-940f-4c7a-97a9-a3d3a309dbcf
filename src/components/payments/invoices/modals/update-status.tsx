import { useEffect } from "react";
import { InvoiceParams } from "../../../../api/interfaces/invoices.interface";
import { DeleteInvoice, UpdateInvoiceToPaid } from "../../../../api/invoices";
import { useRequest } from "../../../../api/utils";
import { InvoiceInterface } from "../../../../assets/interfaces/invoices";
import { AppBtn } from "../../../ui/buttons";
import ErrorLabel from "../../../ui/error-label";
import Modal, { ModalBody, ModalFooter } from "../../../ui/modal";
import SuccessLabel from "../../../ui/success-label";
import { toast } from "../../../ui/toast";

interface Props {
  toggle: (state: boolean) => void;
  show: boolean;
  invoice: InvoiceInterface;
  updateInvoice: (data: InvoiceInterface) => void;
}

const UpdateStatusModal: React.FC<Props> = ({ toggle, show, invoice, updateInvoice }) => {
  const { makeRequest, isLoading, error, response, clearResponse } = useRequest<InvoiceParams>(UpdateInvoiceToPaid);

  const toastOpts = {
    loading: {
      title: "Updating invoice status",
      message: "Please wait...",
    },
    success: {
      title: "Successful",
      message: "Invoice status updated successfully!",
    },
    error: {
      title: "Failed",
      message: "We couldn't update the status for this invoice!",
      actionText: "Retry",
      actionFunc: () => {},
    },
  };

  useEffect(() => {
    if (!show) {
      clearResponse();
    }
  }, [show]);

  const markAsPaid = () => {
    toast.promise(() => updateInvoiceToPaid(), toastOpts);
  };

  const updateInvoiceToPaid = async () => {
    const [res, err] = await makeRequest({ invoiceId: invoice.id });

    if (err) {
      return Promise.reject(err);
    } else {
      updateInvoice(res?.data);

      setTimeout(() => {
        toggle(false);
      }, 1000);
      return Promise.resolve(res);
    }
  };

  return (
    <Modal title="Mark as Paid" {...{ show, toggle }} size="sm">
      <ModalBody>
        <ErrorLabel error={error?.message} />
        <SuccessLabel message={response ? "Invoice updated successfully!" : ""} />
        <div className="text-center py-2.5 flex flex-col items-center">
          <div className="h-15 w-15 rounded-full bg-accent-green-500 mb-5 flex items-center justify-center">
            {/* prettier-ignore */}
            <svg width="50%" viewBox="0 0 30 30" fill="none">
              <path d="M23.75 18.75C20.9875 18.75 18.75 20.9875 18.75 23.75C18.75 24.6875 19.0125 25.575 19.475 26.325C20.3375 27.775 21.925 28.75 23.75 28.75C25.575 28.75 27.1625 27.775 28.025 26.325C28.4875 25.575 28.75 24.6875 28.75 23.75C28.75 20.9875 26.5125 18.75 23.75 18.75ZM26.3375 23.2125L23.675 25.675C23.5 25.8375 23.2625 25.925 23.0375 25.925C22.8 25.925 22.5625 25.8375 22.375 25.65L21.1375 24.4125C20.775 24.05 20.775 23.45 21.1375 23.0875C21.5 22.725 22.1 22.725 22.4625 23.0875L23.0625 23.6875L25.0625 21.8375C25.4375 21.4875 26.0375 21.5125 26.3875 21.8875C26.7375 22.2625 26.7125 22.85 26.3375 23.2125Z" fill="white"/>
              <path d="M27.5 9.4375V10C27.5 10.6875 26.9375 11.25 26.25 11.25H3.75C3.0625 11.25 2.5 10.6875 2.5 10V9.425C2.5 6.5625 4.8125 4.25 7.675 4.25H22.3125C25.175 4.25 27.5 6.575 27.5 9.4375Z" fill="white"/>
              <path d="M2.5 14.3748V20.5748C2.5 23.4373 4.8125 25.7498 7.675 25.7498H15.5C16.225 25.7498 16.85 25.1373 16.7875 24.4123C16.6125 22.4998 17.225 20.4248 18.925 18.7748C19.625 18.0873 20.4875 17.5623 21.425 17.2623C22.9875 16.7623 24.5 16.8248 25.8375 17.2748C26.65 17.5498 27.5 16.9623 27.5 16.0998V14.3623C27.5 13.6748 26.9375 13.1123 26.25 13.1123H3.75C3.0625 13.1248 2.5 13.6873 2.5 14.3748ZM10 21.5623H7.5C6.9875 21.5623 6.5625 21.1373 6.5625 20.6248C6.5625 20.1123 6.9875 19.6873 7.5 19.6873H10C10.5125 19.6873 10.9375 20.1123 10.9375 20.6248C10.9375 21.1373 10.5125 21.5623 10 21.5623Z" fill="white"/>
            </svg>
          </div>
          <h5 className="text-black text-base font-semibold">Do you want to mark as paid?</h5>
          <p className="text-sm text-grey-subtext mt-1 max-w-xs mx-auto">
            A receipt will be generated and this action is irreversible.
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <AppBtn isBlock color="primary" onClick={markAsPaid} disabled={isLoading}>
          {isLoading ? "Update Invoice..." : "Yes, mark as paid"}
        </AppBtn>
      </ModalFooter>
    </Modal>
  );
};
export default UpdateStatusModal;
