import React, { useEffect, useState } from "react";
import {
  InvoiceForm as InvoiceFormType,
  InvoiceInterface,
  INVOICE_FEE_TYPES,
} from "../../../../assets/interfaces/invoices";
import { AppBtn } from "../../../ui/buttons";
import <PERSON><PERSON>, { ModalBody, ModalFooter } from "../../../ui/modal";
import InvoiceForm from "../create";
import { CURRENCIES } from "../../../../assets/interfaces";

interface Props {
  show: boolean;
  toggle: (state: boolean) => void;
  invoice: InvoiceInterface;
  updateInvoices: (invoice: InvoiceInterface) => void;
}

const EditInvoiceModal: React.FC<Props> = ({ show, toggle, invoice, updateInvoices }) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const invoiceForm: InvoiceFormType = {
    title: invoice.title,
    customer: invoice.receiver?.id,
    date_due: new Date(invoice.date_due),
    date_created: new Date(invoice.date_created),
    fees: invoice.fees.map((f) => ({
      type: f.type,
      amount: f.type === INVOICE_FEE_TYPES.DISCOUNT ? f.amount * -1 : f.amount,
    })),
    items: invoice.items,
    store_logo: invoice.sender.image,
    store_address: invoice.sender.address,
    currency: invoice?.currency ?? CURRENCIES.NGN,
  };

  useEffect(() => {
    if (!show) {
      setIsSubmitted(false);
      setIsSubmitting(false);
    }
  }, [show]);

  const submitForm = () => {
    const btn = document.getElementById("invoice-form-trigger");

    if (btn) {
      btn.click();
      setIsSubmitting(true);
    }
  };

  const handleSubmitted = (invoice: InvoiceInterface) => {
    setIsSubmitting(false);
    setIsSubmitted(true);
    updateInvoices(invoice);
  };

  return (
    <Modal {...{ show, toggle }} title="Edit Invoice" size="midi">
      <ModalBody>
        <InvoiceForm initialData={invoiceForm} invoiceId={invoice.id} handleSuccess={handleSubmitted} />
      </ModalBody>
      <ModalFooter>
        <div className="w-full">
          {invoice.is_draft && !isSubmitted && (
            <div className="bg-grey-fields-100 rounded-8 text-1xs text-dark px-3 py-2 flex items-center mb-2">
              {/* prettier-ignore */}
              <svg width="16" viewBox="0 0 18 19" fill="none" className="mr-1.5 text-accent-green-500">
                <path d="M9 17.5625C4.5525 17.5625 0.9375 13.9475 0.9375 9.5C0.9375 5.0525 4.5525 1.4375 9 1.4375C13.4475 1.4375 17.0625 5.0525 17.0625 9.5C17.0625 13.9475 13.4475 17.5625 9 17.5625ZM9 2.5625C5.175 2.5625 2.0625 5.675 2.0625 9.5C2.0625 13.325 5.175 16.4375 9 16.4375C12.825 16.4375 15.9375 13.325 15.9375 9.5C15.9375 5.675 12.825 2.5625 9 2.5625Z" fill="currentColor"/>
                <path d="M9 10.8125C8.6925 10.8125 8.4375 10.5575 8.4375 10.25V6.5C8.4375 6.1925 8.6925 5.9375 9 5.9375C9.3075 5.9375 9.5625 6.1925 9.5625 6.5V10.25C9.5625 10.5575 9.3075 10.8125 9 10.8125Z" fill="currentColor"/>
                <path d="M9 13.2499C8.9025 13.2499 8.805 13.2274 8.715 13.1899C8.625 13.1524 8.5425 13.0999 8.4675 13.0324C8.4 12.9574 8.3475 12.8824 8.31 12.7849C8.2725 12.6949 8.25 12.5974 8.25 12.4999C8.25 12.4024 8.2725 12.3049 8.31 12.2149C8.3475 12.1249 8.4 12.0424 8.4675 11.9674C8.5425 11.8999 8.625 11.8474 8.715 11.8099C8.895 11.7349 9.105 11.7349 9.285 11.8099C9.375 11.8474 9.4575 11.8999 9.5325 11.9674C9.6 12.0424 9.6525 12.1249 9.69 12.2149C9.7275 12.3049 9.75 12.4024 9.75 12.4999C9.75 12.5974 9.7275 12.6949 9.69 12.7849C9.6525 12.8824 9.6 12.9574 9.5325 13.0324C9.4575 13.0999 9.375 13.1524 9.285 13.1899C9.195 13.2274 9.0975 13.2499 9 13.2499Z" fill="currentColor"/>
              </svg>
              Editing this draft invoice will publish it
            </div>
          )}
          <AppBtn size="lg" isBlock disabled={isSubmitting} onClick={isSubmitted ? () => toggle(false) : submitForm}>
            {isSubmitting ? "Saving Updates..." : isSubmitted ? "Close" : "Save Updates"}
          </AppBtn>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default EditInvoiceModal;
