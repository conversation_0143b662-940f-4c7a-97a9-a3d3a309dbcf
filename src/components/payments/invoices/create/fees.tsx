import classNames from "classnames";
import { FormikProps } from "formik";
import React, { useEffect, useState } from "react";
import { array } from "yup";
import { InvoiceFee, InvoiceForm, INVOICE_FEE_TYPES } from "../../../../assets/interfaces/invoices";
import { convertAmount, getRandString } from "../../../../assets/js/utils/functions";
import { AppBtn } from "../../../ui/buttons";
import { DropdownOptionInterface } from "../../../ui/form-elements/select-dropdown";
import FeeItem from "./fee-item";
import StepAccordion from "../../../ui/step-accordion";
import { COUNTRIES } from "@/assets/interfaces";
import authContext from "@/contexts/auth-context";
import { COUNTRY_VATS } from "@/assets/js/utils/constants";

interface IProps {
  isActive: boolean;
  form: FormikProps<InvoiceForm>;
  toggleStep: () => void;
  stepComplete: boolean;
  isEditing?: boolean;
  country: COUNTRIES;
}

const InvoiceFees: React.FC<IProps> = (props) => {
  const { isActive, form, toggleStep, stepComplete, isEditing, country } = props;
  const [fees, setFees] = useState<Map<string, InvoiceFee>>(new Map());
  const nonEmptyFees = Array.from(fees)
    .map((f) => f[1])
    .filter((f) => f.type || f.amount !== 0);

  const submitIsEnabled = nonEmptyFees.every((f) => f.type && f.amount !== 0);
  const hasError = Boolean(form?.errors?.fees) || form?.errors?.fees?.length > 0;

  useEffect(() => {
    const formFees = form?.values.fees;

    if (formFees.length > 0) {
      const feesCopy = new Map();

      formFees.forEach((f) => {
        const randId = getRandString(12);
        feesCopy.set(randId, f);
      });

      setFees(feesCopy);
      return;
    }

    if (fees.size < 1) {
      addNewFee();
    }
  }, []);

  useEffect(() => {
    const nonEmptyFees = Array.from(fees)
      .map((f) => f[1])
      .filter((f) => f.type || f.amount !== 0);

    form.setFieldValue("fees", nonEmptyFees);
  }, [fees]);

  const addNewFee = () => {
    const randId = getRandString(12);

    const feesCopy = new Map(fees);
    feesCopy.set(randId, {
      amount: 0,
      type: null,
    });

    setFees(feesCopy);
  };

  const updateFee = (fee: InvoiceFee, key: string) => {
    const feesCopy = new Map(fees);
    feesCopy.set(key, fee);

    setFees(feesCopy);
  };

  const deleteFee = (key: string) => {
    const feesCopy = new Map(fees);

    feesCopy.delete(key);

    if (fees.size === 1) {
      feesCopy.set(getRandString(12), {
        amount: 0,
        type: null,
      });
    }

    setFees(feesCopy);
  };

  const subTotal = form.values.items.reduce(
    (total, item) => total + convertAmount(item.price * item.quantity, form.values.currency),
    0
  );
  const selectedFees = Array.from(fees.values()).map((f) => f.type);

  return (
    <StepAccordion
      {...{ isActive, isFirstStepComplete: true, toggleStep, stepComplete, isOptional: true, hasError }}
      title="Fees"
    >
      <div>
        <div className="space-y-4">
          {Array.from(fees).map((fee, index) => (
            <FeeItem
              key={fee[0]}
              fee={fee[1]}
              updateFee={(f) => updateFee(f, fee[0])}
              deleteFee={() => deleteFee(fee[0])}
              subTotal={subTotal}
              errors={Array.isArray(form.errors?.fees) ? form.errors.fees[index] : null}
              fees={feeOptions(country).filter((f) => f.value === fee[1].type || selectedFees.indexOf(f.value) === -1)}
              currency={form.values.currency}
              country={country}
            />
          ))}
        </div>
        <button
          className="mt-5 flex items-center text-primary-500 text-sm font-medium"
          onClick={addNewFee}
          type="button"
        >
          {/* prettier-ignore */}
          <svg viewBox="0 0 14 14" fill="none" className="w-3.75 mr-1">
            <path d="M7 2.91675V11.0834" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M2.91675 7H11.0834" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          Add Fee
        </button>
      </div>

      {/* <AppBtn isBlock className="mt-5" disabled={!submitIsEnabled} type="submit" size="lg">
        {nonEmptyFees.length > 0 ? "Next" : "Skip"}
      </AppBtn> */}

      <div className={`flex items-center w-full ${isEditing ? "mt-3.5" : "mt-5"}`}>
        <AppBtn
          isBlock
          disabled={!submitIsEnabled}
          type="submit"
          size={isEditing ? "md" : "lg"}
          color={isEditing ? "neutral" : "primary"}
        >
          {nonEmptyFees.length > 0 ? "Next" : "Skip"}
        </AppBtn>
      </div>
    </StepAccordion>
  );
};

// const feeOptions: DropdownOptionInterface[] = [
//   {
//     value: "DISCOUNT",
//     text: "Discount",
//   },
//   {
//     value: "DELIVERY",
//     text: "Delivery",
//   },
//   {
//     value: "VAT",
//     text: "VAT (7.5%)",
//   },
// ];

export const feeOptions = (country: COUNTRIES): DropdownOptionInterface[] => [
  {
    value: "DISCOUNT",
    text: "Discount",
  },
  {
    value: "DELIVERY",
    text: "Delivery",
  },
  {
    value: "VAT",
    text: `VAT (${COUNTRY_VATS[country].label})`,
  },
];

export default InvoiceFees;
