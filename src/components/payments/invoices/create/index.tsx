import React, { useEffect, useState } from "react";
import { FormikErrors, FormikProps, useFormik } from "formik";
import useSteps from "../../../hooks/useSteps";
import InvoiceFormBasicInfo from "./basic-info";
import InvoiceExtraInfoForm from "./extra-info";
import InvoiceFeesForm from "./fees";
import InvoiceItemsForm from "./items";
import { useRequest } from "../../../../api/utils";
import { CreateInvoice as CreateInvoiceReq, DraftInvoice, UpdateInvoice } from "../../../../api/invoices";
import { CreateInvoiceParams } from "../../../../api/interfaces/invoices.interface";
import authContext from "../../../../contexts/auth-context";
import CreateInvoiceSucess from "./success";
import {
  InvoiceForm as InvoiceFormType,
  InvoiceInterface,
  INVOICE_FEE_TYPES,
} from "../../../../assets/interfaces/invoices";
import { transformYupErrorsIntoObject } from "../../../../assets/js/utils/utils";
import * as Yup from "yup";
import InvoiceFormFooter from "./footer";
import classNames from "classnames";
import ErrorLabel from "../../../ui/error-label";
import { getProductsCurrency } from "../../../../assets/js/utils/functions";
import { CURRENCIES } from "@/assets/interfaces";

interface Props {
  initialData?: InvoiceFormType;
  invoiceId?: string;
  handleSuccess?: (invoice: InvoiceInterface) => void;
}

const InvoiceForm: React.FC<Props> = ({ initialData, invoiceId, handleSuccess }) => {
  const isEditing = Boolean(initialData);
  const { storeId, store } = authContext.useContainer();
  const formSteps = ["BASIC_INFO", "ITEMS", "FEES", "EXTRA_INFO"];
  const [draftingInvoice, setDraftingInvoice] = useState(false);
  const createInvoiceReq = useRequest(CreateInvoiceReq);
  const draftInvoiceReq = useRequest(DraftInvoice);
  const updateInvoiceReq = useRequest(UpdateInvoice);

  const { steps, step, stepIndex, next, previous, changeStep, isActive } = useSteps(
    ["", ...formSteps, "SUCCESS"], //added empty step for toggling
    1
  );

  const [stepsStatus, setStepsStatus] = useState({
    BASIC_INFO: false,
    ITEMS: false,
    FEES: false,
    EXTRA_INFO: false,
  });
  const [hasError, setHasError] = useState(false);
  const [firstStepIsComplete, setFirstStepIsComplete] = useState(false);

  const form = useFormik<InvoiceFormType>({
    initialValues: initialData ?? {
      title: "",
      customer: "",
      date_created: new Date(),
      date_due: null,
      items: [],
      fees: [],
      store_logo: store?.logo,
      store_address: store?.address,
      currency: store?.currencies?.products,
    },
    onSubmit: () => {
      switch (stepIndex) {
        case 1:
          setFirstStepIsComplete(true);
          next();
          break;
        case 2:
          next();
          break;
        case 3:
          next();
          break;
        case 4:
          changeStep("");
          submitInvoice();
          break;
      }
    },
    validationSchema: validationSchema(stepIndex),
  });

  useEffect(() => {
    setHasError(false);
    computeStepStatuses(form);
  }, [form.values]);

  useEffect(() => {
    if (isEditing) changeStep("");
  }, []);

  useEffect(() => {
    if (store) {
      setTimeout(() => {
        form.setFieldValue("currency", getProductsCurrency());
      }, 1000);
    }
  }, [store]);

  const getRequest = (isDraft: boolean) => {
    if (isEditing) return updateInvoiceReq;
    return isDraft ? draftInvoiceReq : createInvoiceReq;
  };

  const computeStepStatuses = async (form: FormikProps<InvoiceFormType>) => {
    //SET THE STATUS OF EACH SECTION
    const statusCopy = { ...stepsStatus };
    const errorStatuses = await checkErrors(form);
    formSteps.forEach((value) => (statusCopy[value] = !errorStatuses[value]));

    setStepsStatus(statusCopy);
  };

  const toggleStep = (s: string) => (step === s ? changeStep("") : changeStep(s));

  const submitInvoice = async (isDraft: boolean = false) => {
    const values = form.values;
    const payload: CreateInvoiceParams = { ...values, store: storeId };

    //validate draft requirements

    setDraftingInvoice(isDraft);

    if (!isDraft) {
      try {
        await validationSchema(4).validate(form.values, { abortEarly: false });
      } catch (err) {
        const errors = transformYupErrorsIntoObject(err);
        form.setErrors(errors);
        Object.keys(errors).forEach((k) => form.setFieldTouched(k, true));
        setHasError(true);
        return;
      }
    }

    //Ensure discount fees are sent as negative numbers
    const payloadCopy = JSON.parse(JSON.stringify(payload)); //DEEP COPY WITHOUT REFERENCE TO ORIGINAL OBJECT
    const discountFeeIndex = payloadCopy.fees.findIndex((f) => f.type === INVOICE_FEE_TYPES.DISCOUNT);

    if (discountFeeIndex > -1) {
      payloadCopy.fees[discountFeeIndex].amount = -1 * payload.fees[discountFeeIndex].amount;
    }

    const [res, err] = await getRequest(isDraft).makeRequest(
      isEditing ? { invoiceId, data: payloadCopy } : payloadCopy
    );

    if (res) {
      changeStep("SUCCESS");

      if (handleSuccess) {
        handleSuccess(res?.data);
      }
    }
  };

  return (
    <>
      {step !== "SUCCESS" && (
        <>
          <div className={classNames("h-full overflow-y-auto", { "py-12.5 sm:py-20 px-6.25 lg:px-7.5": !isEditing })}>
            <form
              className="w-full max-w-[450px] mx-auto flex flex-col items-center pb-30"
              onSubmit={form.handleSubmit}
            >
              {!isEditing && (
                <div className="flex items-center flex-col">
                  <figure className="h-[70px] w-[70px] sm:h-20 sm:w-20 bg-accent-green-500 flex items-center justify-center rounded-full">
                    {/* prettier-ignore */}
                    <svg className="w-8 sm:w-10" viewBox="0 0 40 40" fill="none">
                      <path d="M26.3007 3.33301H13.7007C7.40065 3.33301 5.83398 5.01634 5.83398 11.733V30.4997C5.83398 34.933 8.26732 35.983 11.2173 32.8163L11.234 32.7997C12.6007 31.3497 14.684 31.4663 15.8673 33.0497L17.5507 35.2997C18.9007 37.083 21.084 37.083 22.434 35.2997L24.1173 33.0497C25.3173 31.4497 27.4007 31.333 28.7673 32.7997C31.734 35.9663 34.1507 34.9163 34.1507 30.483V11.733C34.1673 5.01634 32.6007 3.33301 26.3007 3.33301ZM24.734 16.6497L23.9007 17.4997H23.884L18.834 22.5497C18.6173 22.7663 18.1673 22.9997 17.8507 23.033L15.6007 23.3663C14.784 23.483 14.2173 22.8997 14.334 22.0997L14.6507 19.833C14.7007 19.5163 14.9173 19.083 15.134 18.8497L20.2007 13.7997L21.034 12.9497C21.584 12.3997 22.2007 11.9997 22.8673 11.9997C23.434 11.9997 24.0507 12.2663 24.734 12.9497C26.234 14.4497 25.7507 15.633 24.734 16.6497Z" fill="white"/>
                    </svg>
                  </figure>
                  <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-light mt-2.5 sm:mt-4">
                    Create <b className="font-bold">an Invoice</b>
                  </h2>
                </div>
              )}
              <div className={classNames("flex flex-col w-full items-stretch", { "mt-10 sm:mt-12.5": !isEditing })}>
                <div className={"-mb-6"}>
                  <ErrorLabel
                    error={
                      getRequest(draftingInvoice)?.error
                        ? getRequest(draftingInvoice)?.error?.message
                        : hasError
                        ? "You have some errors"
                        : ""
                    }
                    perm={true}
                  />
                </div>
                <InvoiceFormBasicInfo
                  isActive={isActive("BASIC_INFO")}
                  isFirstStepComplete={firstStepIsComplete || isEditing}
                  form={form}
                  toggleStep={() => toggleStep("BASIC_INFO")}
                  stepComplete={stepsStatus.BASIC_INFO}
                  isEditing={isEditing}
                  currencyOptions={store?.currencies?.storefront}
                />

                {(firstStepIsComplete || isEditing) && (
                  <>
                    <InvoiceItemsForm
                      isActive={isActive("ITEMS")}
                      form={form}
                      toggleStep={() => toggleStep("ITEMS")}
                      stepComplete={stepsStatus.ITEMS}
                      isEditing={isEditing}
                      country={store?.country?.code}
                    />
                    <InvoiceFeesForm
                      isActive={isActive("FEES")}
                      form={form}
                      toggleStep={() => toggleStep("FEES")}
                      stepComplete={stepsStatus.FEES}
                      isEditing={isEditing}
                      country={store?.country?.code}
                    />
                    <InvoiceExtraInfoForm
                      isActive={isActive("EXTRA_INFO")}
                      form={form}
                      toggleStep={() => toggleStep("EXTRA_INFO")}
                      stepComplete={stepsStatus.EXTRA_INFO}
                      isEditing={isEditing}
                    />
                  </>
                )}
              </div>
              <button id="invoice-form-trigger" className="hidden" type="button" onClick={() => submitInvoice(false)}>
                Submit
              </button>
            </form>
          </div>
          <InvoiceFormFooter
            showFooter={firstStepIsComplete && !isEditing}
            form={form}
            creatingInvoice={createInvoiceReq.isLoading}
            submit={submitInvoice}
            savingDraft={draftInvoiceReq.isLoading}
          />
        </>
      )}

      {step === "SUCCESS" && (
        <CreateInvoiceSucess createdInvoice={getRequest(draftingInvoice)?.response?.data} isEditing={isEditing} />
      )}
    </>
  );
};

const validationSchema = (stepIndex: number) => {
  return Yup.object().shape({
    title: Yup.string().required("Invoice title is required"),
    customer: Yup.string().required("Customer is required"),
    date_created: Yup.date()
      .typeError("Please provide a valid invoice created date")
      .required("Invoice date is required"),
    date_due: Yup.date().typeError("Please provide a valid due date").required("Invoice due date is required"),
    currency: Yup.string()
      .required("Please select a currency")
      .oneOf(
        [
          CURRENCIES.EUR,
          CURRENCIES.USD,
          CURRENCIES.GBP,
          CURRENCIES.NGN,
          CURRENCIES.GHC,
          CURRENCIES.ZAR,
          CURRENCIES.KES,
        ],
        "Please select a valid currency"
      ),
    items: stepIndex > 1 || stepIndex < 1 ? ItemsValidationShema : undefined,
    fees: stepIndex > 2 || stepIndex < 1 ? FeesValidationSchema : undefined,
    store_logo:
      stepIndex > 3 || stepIndex < 1
        ? Yup.string().typeError("Store logo should be a string").nullable().notRequired()
        : undefined,
    store_address:
      stepIndex > 3 || stepIndex < 1
        ? Yup.string().typeError("Address should be a string").nullable().notRequired()
        : undefined,
  });
};

const ItemsValidationShema = Yup.array()
  .of(
    Yup.object().shape({
      name: Yup.string().required("Item name is required"),
      price: Yup.number().required("Item price is required").min(1, "Item price must be greater than 0"),
      quantity: Yup.number().required("Item quantity is required").min(1, "Item quantity must be greater than 0"),
    })
  )
  .min(1, "Please add at least one item");

const FeesValidationSchema = Yup.array().of(
  Yup.object().shape({
    type: Yup.string().required("Fee type is required"),
    amount: Yup.number().required("Fee amount is required").min(0.01, "Amount should be greater than 0"),
  })
);

const checkErrors = async (form: FormikProps<InvoiceFormType>) => {
  const { values, initialValues, errors: initialErrors } = form;
  let errors: FormikErrors<InvoiceFormType> = {};

  try {
    await validationSchema(4).validate(form.values, { abortEarly: false });
  } catch (err) {
    errors = transformYupErrorsIntoObject(err);
  }

  const statuses = {
    BASIC_INFO: Boolean(errors?.customer || errors?.date_due || errors?.date_created || errors?.title),
    ITEMS: values.items.length > 0 ? Boolean(errors?.items) || errors?.items?.length > 0 : true,
    FEES: values.fees.length > 0 ? Boolean(errors?.fees) || errors?.fees?.length > 0 : true,
    EXTRA_INFO:
      initialValues.store_address === values.store_address && initialValues.store_logo === values.store_logo
        ? true
        : Boolean(errors?.store_address || errors?.store_logo),
  };

  return statuses;
};

export default InvoiceForm;
