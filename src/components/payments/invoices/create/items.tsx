import classNames from "classnames";
import { Formik<PERSON><PERSON>rs, FormikProps, FormikProvider } from "formik";
import React, { useEffect, useState } from "react";
import { GetItemsParams } from "../../../../api/interfaces/items.interface";
import { GetItems } from "../../../../api/items";
import { useFetcher } from "../../../../api/utils";
import { COUNTRIES, StrippedItem } from "../../../../assets/interfaces";
import { InvoiceForm, InvoiceItem, INVOICE_FEE_TYPES } from "../../../../assets/interfaces/invoices";
import { convertAmount, getRandString, toCurrency } from "../../../../assets/js/utils/functions";
import authContext from "../../../../contexts/auth-context";
import { useModals } from "../../../hooks/useModals";
import useStoreItems from "../../../hooks/useStoreItems";
import LazyImage from "../../../lazy-image";
import Portal from "../../../portal";
import { AppBtn, RoundActionBtn } from "../../../ui/buttons";
import { ProductsSelector } from "../../../ui/form-elements";
import ProductsSelectorModal, { getProductColors } from "../../../ui/form-elements/products-selector-modal";
import { DropdownOptionInterface } from "../../../ui/form-elements/select-dropdown";
import { StepNumberInput } from "../../../ui/form-elements/step-number-input";
import AddItemModal from "./add-item-modal";
import SingleItem from "./item";
import StepAccordion from "../../../ui/step-accordion";
import { COUNTRY_VATS } from "@/assets/js/utils/constants";

interface IProps {
  isActive: boolean;
  form: FormikProps<InvoiceForm>;
  toggleStep: () => void;
  stepComplete: boolean;
  isEditing?: boolean;
  country: COUNTRIES;
}

const InvoiceItemsForm: React.FC<IProps> = (props) => {
  const { isActive, form, toggleStep, stepComplete, isEditing, country } = props;
  const { items: storeItems, fetchItemsReq, addNewItem } = useStoreItems();
  const [items, setItems] = useState<Map<string, InvoiceItem>>(new Map());
  const hasError = Boolean(form.errors.items) || form.errors?.items?.length > 0;
  const itemsErrors = Array.isArray(form.errors?.items) ? form.errors?.items : [];

  const { modals, toggleModal } = useModals(["add_item", "select_items"]);
  const [selectedItemKey, setSelectedItemKey] = useState(null);
  const submitIsEnabled = (() => {
    const nonEmptyItems = Array.from(items)
      .map((i) => i[1])
      .filter((i) => i.name !== "" && i.price !== 0);

    return nonEmptyItems.length > 0 && nonEmptyItems.every((i) => i.name !== "" && i.price !== 0 && i.quantity !== 0);
  })();

  const inputValue = Array.from(items.keys());

  useEffect(() => {
    const formItems = form.values.items;
    if (formItems.length > 0) {
      const itemsCopy = new Map();

      formItems.forEach((i) => {
        itemsCopy.set(i.id, i);
      });

      setItems(itemsCopy);
    }
  }, []);

  useEffect(() => {
    if (form.values.currency) {
      const itemsCopy = new Map(items);
      calculateVATAndSaveItems(itemsCopy);
    }
  }, [form.values.currency]);

  const updateItem = (data: InvoiceItem, key: string) => {
    const itemsCopy = new Map(items);
    itemsCopy.set(key, data);

    setItems(itemsCopy);
    calculateVATAndSaveItems(itemsCopy);
  };

  const deleteItem = (key: string) => {
    const itemsCopy = new Map(items);
    itemsCopy.delete(key);

    setItems(itemsCopy);
    calculateVATAndSaveItems(itemsCopy);
  };

  const saveItems = (selected: StrippedItem[]) => {
    const itemsCopy = new Map(items);
    //remove items that were unselected
    Array.from(items.values()).forEach((item) => {
      if (selected.findIndex((i) => i.id === item.id) === -1) {
        itemsCopy.delete(item.id);
      }
    });

    //add new items
    selected.forEach((item) => {
      if (!items.has(item.id)) {
        itemsCopy.set(item.id, {
          ...item,
          quantity: 1,
        });
      }
    });

    setItems(itemsCopy);
    calculateVATAndSaveItems(itemsCopy);
  };

  const calculateVATAndSaveItems = (items: Map<string, InvoiceItem>) => {
    const vatFeeIndex = form.values.fees.findIndex((f) => f.type === INVOICE_FEE_TYPES.VAT);

    if (vatFeeIndex > -1) {
      const subTotal = Array.from(items)
        .map((i) => i[1])
        .reduce((total, item) => total + convertAmount(item.price * item.quantity, form.values.currency), 0);
      const fees = [...form.values.fees];
      fees[vatFeeIndex].amount = subTotal * COUNTRY_VATS[country].value;

      form.setFieldValue("fees", fees);
    }

    const nonEmptyItems = Array.from(items.values())
      .filter((i) => i.name !== "" && i.price !== 0)
      .map((i) => ({
        id: i.id,
        name: i.name,
        price: i.price,
        quantity: i.quantity,
      })); //map to remove the image property

    form.setFieldValue("items", nonEmptyItems);
  };

  const itemError = (index: number) => {
    if (form.touched?.items?.length === 0) return null;
    return getError(itemsErrors[index]);
  };

  return (
    <>
      <StepAccordion {...{ isActive, isFirstStepComplete: true, toggleStep, stepComplete, hasError }} title="Items">
        <div className={isEditing ? "mb-5" : "mb-8"}>
          <div className="space-y-4">
            {items.size < 1 && (
              <ProductsSelector
                label="Select or Create Items"
                value={inputValue}
                saveItems={saveItems}
                items={storeItems}
                addItem={addNewItem}
                error={
                  form.touched?.items?.length !== 0 && typeof form.errors.items === "string" ? form.errors.items : null
                }
                currency={form.values.currency}
              />
            )}

            {Array.from(items.values()).map((item, index) => (
              <>
                <div
                  className={classNames("w-full bg-white rounded-15 border border-opacity-40 p-3 sm:p-3.75", {
                    "border-grey-border": !itemError(index),
                    "border-accent-red-500": itemError(index),
                  })}
                  key={index}
                >
                  <div className="flex items-start justify-between pb-3 border-b border-grey-border border-opacity-50">
                    <div className="flex items-center flex-1">
                      <figure className="flex-shrink-0 h-10 w-10 sm:h-11.25 sm:w-11.25 rounded-md overflow-hidden mr-3 relative">
                        {item?.image && (
                          <LazyImage
                            src={item.image}
                            className="h-full w-full object-cover rounded-md relative z-10"
                            alt={item.name}
                          />
                        )}

                        {!item?.image && (
                          <div
                            className={`w-full h-full flex items-center justify-center bg-opacity-10 ${getProductColors(
                              item.name
                            )}`}
                          >
                            {/* prettier-ignore */}
                            <svg width="60%" viewBox="0 0 24 24" fill="none">
                              <path d="M20.2083 7.82141L12.5083 12.2814C12.1983 12.4614 11.8083 12.4614 11.4883 12.2814L3.78826 7.82141C3.23826 7.50141 3.09826 6.75141 3.51826 6.28141C3.80826 5.95141 4.13826 5.68141 4.48826 5.49141L9.90826 2.49141C11.0683 1.84141 12.9483 1.84141 14.1083 2.49141L19.5283 5.49141C19.8783 5.68141 20.2083 5.96141 20.4983 6.28141C20.8983 6.75141 20.7583 7.50141 20.2083 7.82141Z" fill="currentColor"/>
                              <path d="M11.4305 14.1389V20.9589C11.4305 21.7189 10.6605 22.2189 9.98047 21.8889C7.92047 20.8789 4.45047 18.9889 4.45047 18.9889C3.23047 18.2989 2.23047 16.5589 2.23047 15.1289V9.9689C2.23047 9.1789 3.06047 8.6789 3.74047 9.0689L10.9305 13.2389C11.2305 13.4289 11.4305 13.7689 11.4305 14.1389Z" fill="currentColor"/>
                              <path d="M12.5703 14.1389V20.9589C12.5703 21.7189 13.3403 22.2189 14.0203 21.8889C16.0803 20.8789 19.5503 18.9889 19.5503 18.9889C20.7703 18.2989 21.7703 16.5589 21.7703 15.1289V9.9689C21.7703 9.1789 20.9403 8.6789 20.2603 9.0689L13.0703 13.2389C12.7703 13.4289 12.5703 13.7689 12.5703 14.1389Z" fill="currentColor"/>
                            </svg>
                          </div>
                        )}
                      </figure>
                      <div className="flex-1 overflow-hidden">
                        <span className="text-sm sm:text-1sm mt-0.75 overflow-hidden whitespace-nowrap overflow-ellipsis text-black-secondary block max-w-[200px]">
                          {item.name}
                        </span>
                        <span className="text-xs sm:text-1xs font-semibold text-black">
                          {toCurrency(item.price * item.quantity, form.values.currency, true)}
                        </span>
                      </div>
                    </div>
                    <RoundActionBtn
                      icon="delete"
                      size="sm"
                      // white={true}
                      // grey={false}
                      onClick={() => deleteItem(item.id)}
                    />
                  </div>
                  <div className="flex items-center pt-3">
                    <div className="ml-auto">
                      <StepNumberInput
                        min={1}
                        value={item.quantity ?? 1}
                        onChange={(quantity) => updateItem({ ...item, quantity }, item.id)}
                      />
                    </div>
                  </div>
                </div>
                {itemError(index) && (
                  <span className="text-1xs font-medium text-accent-red-500 text-opacity-70">{itemError(index)}</span>
                )}
              </>
            ))}
          </div>
          <button
            className="font-medium text-primary-500 text-sm flex items-center mt-3.5"
            type="button"
            onClick={() => toggleModal("select_items")}
          >
            + Add Item
          </button>
        </div>
        <div className={`flex items-center w-full ${isEditing ? "mt-3.5" : "mt-5"}`}>
          <AppBtn
            isBlock
            disabled={!submitIsEnabled}
            type="submit"
            size={isEditing ? "md" : "lg"}
            color={isEditing ? "neutral" : "primary"}
          >
            Next
          </AppBtn>
        </div>
      </StepAccordion>
      <ProductsSelectorModal
        show={modals.select_items.show}
        toggle={() => toggleModal("select_items")}
        value={inputValue}
        items={storeItems}
        saveItems={saveItems}
        addItem={addNewItem}
        currency={form.values.currency}
      />
    </>
  );
};

const getError = (errors: string | { [key: string]: string | string[] }) => {
  if (!errors) return null;

  if (typeof errors === "string") {
    return errors;
  }

  const error = Object.values(errors)[0];
  return Array.isArray(error) ? error[0] : error;
};

// const options: DropdownOptionInterface[] = [
//   {
//     text: "Macbook Pro",
//     value: "8938803989i8393",
//     meta: {
//       name: "Macbook Pro",
//       price: 700000,
//       image: "https://catlog-1.s3.eu-west-2.amazonaws.com/48m423tgx.jpeg",
//     },
//   },
//   {
//     text: "Pasta",
//     value: "8y3t7u378892872",
//     meta: {
//       name: "Pasta",
//       price: 5000,
//       image: "https://catlog-1.s3.eu-west-2.amazonaws.com/lijeebhgdl.jpeg",
//     },
//   },
//   {
//     text: "Air Force 1",
//     value: "983y2387972y728",
//     meta: {
//       name: "Air Force 1",
//       price: 24000,
//       image: "https://catlog-1.s3.eu-west-2.amazonaws.com/tzptzgy2il.jpeg",
//     },
//   },
// ];

export default InvoiceItemsForm;
