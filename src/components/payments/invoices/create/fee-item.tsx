import { FormikErrors } from "formik";
import { ChangeEvent, useState } from "react";
import { InvoiceFee, INVOICE_FEE_TYPES } from "../../../../assets/interfaces/invoices";
import { toCurrency } from "../../../../assets/js/utils/functions";
import { SelectDropdown } from "../../../ui/form-elements";
import { InputWithAddon } from "../../../ui/form-elements/input-field";
import { DropdownOptionInterface } from "../../../ui/form-elements/select-dropdown";
import { toast } from "../../../ui/toast";
import { COUNTRIES, CURRENCIES } from "@/assets/interfaces";
import authContext from "@/contexts/auth-context";
import { feeOptions } from "./fees";
import { COUNTRY_VATS } from "@/assets/js/utils/constants";

interface FeeItemProps {
  fee: InvoiceFee;
  updateFee: (fee: InvoiceFee) => void;
  deleteFee: VoidFunction;
  errors?: string | FormikErrors<InvoiceFee>;
  subTotal: number;
  fees: DropdownOptionInterface[];
  currency: CURRENCIES;
  country: COUNTRIES;
}

const FeeItem: React.FC<FeeItemProps> = (props) => {
  const { fee, updateFee, deleteFee, errors, subTotal, fees, currency, country } = props;
  const [showCollapsed, setShowCollapsed] = useState(fee.type !== null && fee.amount > 0);
  const textFromType = feeOptions(country).find((fO) => fO.value === fee.type)?.text;

  const handleInputBlur = () => {
    if (fee.type !== null && fee.amount > 0) {
      setShowCollapsed(true);
    }
  };

  const handleTypeUpdate = (e: ChangeEvent<HTMLInputElement>) => {
    const type = e.target.value as INVOICE_FEE_TYPES;

    if (type === INVOICE_FEE_TYPES.VAT) {
      if (subTotal === 0)
        toast.error({
          title: "Couldn't calculate VAT",
          message: "Please select items for this invoice to calculate VAT",
        });
      updateFee({ type, amount: subTotal * COUNTRY_VATS[country].value });

      // manually call handleblur because amount is manually set
      setTimeout(() => {
        handleInputBlur();
      }, 500);

      return;
    }

    updateFee({ ...fee, type: type });
  };

  return (
    <div>
      {!showCollapsed && (
        <div className="flex flex-col sm:flex-row items-start space-y-3 sm:space-y-0 sm:space-x-3 w-full">
          <SelectDropdown
            name="label"
            label="Select fee type"
            options={fees}
            value={fee.type}
            className="flex-1"
            onChange={handleTypeUpdate}
            onBlur={handleInputBlur}
            error={typeof errors !== "string" && errors?.type}
            selectionRemovable
          />
          <div className="flex-1 w-full">
            <InputWithAddon
              placeholder="Enter Amount"
              name="fee_amount"
              value={fee.amount}
              error={typeof errors !== "string" && errors?.amount}
              onChange={(e) =>
                updateFee({
                  ...fee,
                  amount: Number(e.target.value),
                })
              }
              onBlur={handleInputBlur}
            >
              <div className="bg-white text-dark h-full flex items-center text-xs rounded-l-10 px-3 font-medium">
                {currency}
              </div>
            </InputWithAddon>
          </div>
        </div>
      )}
      {showCollapsed && (
        <div className="bg-grey-bg-light border px-3.75 py-2 sm:py-1.75 border-grey-border border-opacity-50 w-full rounded-10 flex justify-between items-center">
          <div className="flex sm:items-center flex-col sm:flex-row sm:space-x-10  text-dark">
            <span className="inline-block text-dark text-sm my-px">{textFromType}</span>
            <span className="inline-block font-medium text-black text-1xs sm:text-sm my-px">
              {toCurrency(fee.amount, currency)}
            </span>
          </div>
          <div className="flex space-x-2 items-center">
            <button
              className="flex h-9 w-9 rounded-full bg-white border border-grey-border border-opacity-50 text-dark items-center justify-center"
              onClick={() => setShowCollapsed(false)}
            >
              {/* prettier-ignore */}
              <svg viewBox="0 0 20 20" fill="none" className="w-4">
                <path d="M4.61666 16.2666C4.10832 16.2666 3.63332 16.0916 3.29166 15.7666C2.85832 15.3583 2.64999 14.7416 2.72499 14.075L3.03332 11.375C3.09166 10.8666 3.39999 10.1916 3.75832 9.82496L10.6 2.58329C12.3083 0.774959 14.0917 0.72496 15.9 2.43329C17.7083 4.14163 17.7583 5.92496 16.05 7.73329L9.20832 14.975C8.85832 15.35 8.20832 15.7 7.69999 15.7833L5.01666 16.2416C4.87499 16.25 4.74999 16.2666 4.61666 16.2666ZM13.275 2.42496C12.6333 2.42496 12.075 2.82496 11.5083 3.42496L4.66666 10.675C4.49999 10.85 4.30832 11.2666 4.27499 11.5083L3.96666 14.2083C3.93332 14.4833 3.99999 14.7083 4.14999 14.85C4.29999 14.9916 4.52499 15.0416 4.79999 15L7.48332 14.5416C7.72499 14.5 8.12499 14.2833 8.29166 14.1083L15.1333 6.86663C16.1667 5.76663 16.5417 4.74996 15.0333 3.33329C14.3667 2.69163 13.7917 2.42496 13.275 2.42496Z" fill="currentColor"/>
                <path d="M14.45 9.12504C14.4333 9.12504 14.4083 9.12504 14.3916 9.12504C11.7916 8.8667 9.69996 6.8917 9.29996 4.30837C9.24996 3.9667 9.48329 3.65004 9.82496 3.5917C10.1666 3.5417 10.4833 3.77504 10.5416 4.1167C10.8583 6.13337 12.4916 7.68337 14.525 7.88337C14.8666 7.9167 15.1166 8.22504 15.0833 8.5667C15.0416 8.88337 14.7666 9.12504 14.45 9.12504Z" fill="currentColor"/>
                <path d="M17.5 18.958H2.5C2.15833 18.958 1.875 18.6747 1.875 18.333C1.875 17.9913 2.15833 17.708 2.5 17.708H17.5C17.8417 17.708 18.125 17.9913 18.125 18.333C18.125 18.6747 17.8417 18.958 17.5 18.958Z" fill="currentColor"/>
              </svg>
            </button>
            <button
              className="flex h-9 w-9 rounded-full bg-white border border-grey-border border-opacity-50 text-dark items-center justify-center"
              onClick={deleteFee}
            >
              {/* prettier-ignore */}
              <svg viewBox="0 0 20 20" fill="none" className="w-4">
                <path d="M17.5001 5.60839C17.4834 5.60839 17.4584 5.60839 17.4334 5.60839C13.0251 5.16673 8.62505 5.00006 4.26672 5.44173L2.56672 5.60839C2.21672 5.64173 1.90839 5.39173 1.87505 5.04173C1.84172 4.69173 2.09172 4.39173 2.43339 4.35839L4.13339 4.19173C8.56672 3.74173 13.0584 3.91673 17.5584 4.35839C17.9001 4.39173 18.1501 4.70006 18.1167 5.04173C18.0917 5.36673 17.8167 5.60839 17.5001 5.60839Z" fill="currentColor"/>
                <path d="M7.08338 4.76699C7.05005 4.76699 7.01672 4.76699 6.97505 4.75866C6.64172 4.70033 6.40838 4.37533 6.46672 4.04199L6.65005 2.95033C6.78338 2.15033 6.96671 1.04199 8.90838 1.04199H11.0917C13.0417 1.04199 13.225 2.19199 13.35 2.95866L13.5334 4.04199C13.5917 4.38366 13.3584 4.70866 13.025 4.75866C12.6834 4.81699 12.3584 4.58366 12.3084 4.25033L12.125 3.16699C12.0084 2.44199 11.9834 2.30033 11.1 2.30033H8.91672C8.03338 2.30033 8.01672 2.41699 7.89172 3.15866L7.70005 4.24199C7.65005 4.55033 7.38338 4.76699 7.08338 4.76699Z" fill="currentColor"/>
                <path d="M12.675 18.9586H7.325C4.41666 18.9586 4.3 17.3503 4.20833 16.0503L3.66666 7.65864C3.64166 7.31697 3.90833 7.01697 4.25 6.99197C4.6 6.97531 4.89166 7.23364 4.91666 7.57531L5.45833 15.967C5.55 17.2336 5.58333 17.7086 7.325 17.7086H12.675C14.425 17.7086 14.4583 17.2336 14.5417 15.967L15.0833 7.57531C15.1083 7.23364 15.4083 6.97531 15.75 6.99197C16.0917 7.01697 16.3583 7.30864 16.3333 7.65864L15.7917 16.0503C15.7 17.3503 15.5833 18.9586 12.675 18.9586Z" fill="currentColor"/>
                <path d="M11.3834 14.375H8.6084C8.26673 14.375 7.9834 14.0917 7.9834 13.75C7.9834 13.4083 8.26673 13.125 8.6084 13.125H11.3834C11.7251 13.125 12.0084 13.4083 12.0084 13.75C12.0084 14.0917 11.7251 14.375 11.3834 14.375Z" fill="currentColor"/>
                <path d="M12.0834 11.042H7.91675C7.57508 11.042 7.29175 10.7587 7.29175 10.417C7.29175 10.0753 7.57508 9.79199 7.91675 9.79199H12.0834C12.4251 9.79199 12.7084 10.0753 12.7084 10.417C12.7084 10.7587 12.4251 11.042 12.0834 11.042Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FeeItem;
