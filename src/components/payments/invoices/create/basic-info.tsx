import classNames from "classnames";
import { FormikProps } from "formik";
import React, { useEffect, useState } from "react";
import { GetCustomers, GetCustomersBasic } from "../../../../api/orders-customers";
import { useFetcher } from "../../../../api/utils";
import { getFieldvalues, removeCountryCode, resolvePhone } from "../../../../assets/js/utils/functions";
import { AppBtn } from "../../../ui/buttons";
import { DatePickerInput, InputField, SelectDropdown, SelectWithModal } from "../../../ui/form-elements";
import { DropdownOptionInterface } from "../../../ui/form-elements/select-dropdown";
import StoreLogo from "../../../ui/store-logo";
import StepAccordion from "../../../ui/step-accordion";
import { GetCustomersParams } from "../../../../api/interfaces/orders-customers.interface";
import AddCustomerModal from "../../../orders/modals/add-customer";
import { useModals } from "../../../hooks/useModals";
import { CURRENCIES, CustomerInterface } from "../../../../assets/interfaces";
import Portal from "../../../portal";
import { InvoiceForm } from "../../../../assets/interfaces/invoices";
import { CURRENCY_OPTIONS } from "../../../../assets/js/utils/constants";

interface IProps {
  isActive: boolean;
  isFirstStepComplete: boolean;
  form: FormikProps<InvoiceForm>;
  toggleStep: () => void;
  stepComplete: boolean;
  isEditing?: boolean;
  currencyOptions: CURRENCIES[];
}

const InvoiceFormBasicInfo: React.FC<IProps> = (props) => {
  const { isActive, isFirstStepComplete, form, toggleStep, stepComplete, isEditing, currencyOptions } = props;
  const submitIsEnabled = form.values.customer && form.values.date_created && form.values.title && form.values.date_due;
  const [customers, setCustomers] = useState<DropdownOptionInterface[]>([]);
  const { modals, toggleModal } = useModals(["add_customer"]);
  const { response, error, isLoading, makeRequest } = useFetcher<GetCustomersParams>(GetCustomersBasic, {
    per_page: Number.MAX_SAFE_INTEGER,
  });
  const hasError = Boolean(
    form.errors.customer || form.errors.title || form.errors.date_created || form.errors.date_due
  );

  const currencyOpts = CURRENCY_OPTIONS.filter((c) => currencyOptions.includes(c.value));

  useEffect(() => {
    if (response?.data?.data) {
      setCustomers(
        response.data.data.map((c) => ({
          text: c.name ?? "Unknown Customer",
          value: c.id,
          meta: { phone: c.phone, email: c?.email ?? "" },
        }))
      );
    }
  }, [response]);

  const addCustomer = (customer: CustomerInterface) => {
    form.setFieldValue("customer", customer.id);
    setCustomers([...customers, { text: customer.name, value: customer.id }]);
  };

  return (
    <>
      <StepAccordion {...{ isActive, isFirstStepComplete, toggleStep, stepComplete, hasError }} title="Basic Info">
        <InputField label="Invoice Title" {...getFieldvalues("title", form)} />
        <SelectDropdown
          label="Invoice Currency"
          {...getFieldvalues("currency", form)}
          options={currencyOpts}
          disabled={currencyOptions.length < 2}
        />
        <SelectWithModal
          label="Who is this for"
          options={customers}
          {...getFieldvalues("customer", form)}
          action={{ onClick: () => toggleModal("add_customer"), label: "Create new customer" }}
          hasSearch
          OptionRender={CustomerOptionRender}
          isLoadingData={isLoading}
          searchLabel="Search Customers"
        />
        {/* <SelectDropdown
          label="Default storefront currency"
          options={CURRENCY_OPTIONS}
          {...getFieldvalues("storefront_default", form)}
          className="mt-7.5"
        /> */}
        <div className="grid grid-cols-1 sm:grid-cols-2 sm:gap-3.5">
          <div className="mt-4.5">
            <DatePickerInput label="Date created" {...getFieldvalues("date_created", form)} />
          </div>
          <div className="mt-4.5">
            <DatePickerInput label="Due Date" {...getFieldvalues("date_due", form)} />
          </div>
        </div>
        <div className={`flex items-center w-full ${isEditing ? "mt-3.5" : "mt-5"}`}>
          <AppBtn
            isBlock
            disabled={!submitIsEnabled}
            type="submit"
            size={isEditing ? "md" : "lg"}
            color={isEditing ? "neutral" : "primary"}
          >
            Next
          </AppBtn>
        </div>
      </StepAccordion>
      <Portal>
        <AddCustomerModal
          show={modals.add_customer.show}
          toggle={() => toggleModal("add_customer")}
          addCustomer={addCustomer}
        />
      </Portal>
    </>
  );
};

export const CustomerOptionRender: React.FC<{ option: DropdownOptionInterface; isOption?: boolean }> = ({
  option,
  isOption,
}) => {
  return (
    <div className="flex items-center">
      <StoreLogo
        className={classNames("mr-2", { "h-6 w-6 text-sm": !isOption, "h-10 w-10 text-lg": isOption })}
        storeName={option.text}
        logo={null}
      />
      <div className={classNames({ "ml-1.5": isOption })}>
        <span className={classNames("inline-block text-sm text-black-secondary", { "font-medium": isOption })}>
          {option.text ?? "Anonymous Customer"}
        </span>
        {isOption && (
          <div className="flex items-center mt-0.5">
            <span className="text-xs text-dark inline-block">{removeCountryCode(option?.meta?.phone)}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default InvoiceFormBasicInfo;
