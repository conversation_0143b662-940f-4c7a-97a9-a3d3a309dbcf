import classNames from "classnames";
import { FormikProps } from "formik";
import React from "react";
import { InvoiceForm, INVOICE_FEE_TYPES } from "../../../../assets/interfaces/invoices";
import symbols from "../../../../assets/js/utils/currency-symbols";
import { convertAmount, millify } from "../../../../assets/js/utils/functions";
import { AppBtn } from "../../../ui/buttons";

interface IProps {
  form: FormikProps<InvoiceForm>;
  submit: (isDraft?: boolean) => void;
  showFooter: boolean;
  creatingInvoice: boolean;
  savingDraft: boolean;
}

const InvoiceFormFooter: React.FC<IProps> = ({ form, submit, showFooter, creatingInvoice, savingDraft }) => {
  const classes = classNames(
    "absolute w-full bg-white bottom-0 border-t border-grey-border border-opacity-50 transform transition-all ease-in-out duration-300 z-[99]",
    {
      "-bottom-full opacity-0": !showFooter,
      "bottom-0 opacity-100": showFooter,
    }
  );

  return (
    <div className={classes} style={{ transitionDelay: "250ms" }}>
      {showFooter && (
        <div className="py-4 sm:py-5 px-5 sm:px-6.25 lg:px-7.5 flex items-center justify-between w-full">
          <span className="text-xs sm:text-1xs text-white font-semibold py-1.5 pl-2 pr-2.5 rounded-30 bg-accent-green-500 flex items-center">
            <div className="flex items-center justify-center text-[10px] bg-white h-5 w-5 rounded-full text-accent-green-500 mr-1.5 font-bold">
              {symbols[form.values.currency]}
            </div>
            {`${form.values.currency} ${millify(calculateTotal(form.values) ?? 0, 2)}`}
          </span>
          <div className="ml-auto space-x-3 sm:space-x-4 flex items-center ">
            <AppBtn size="lg" color="neutral" onClick={() => submit(true)} disabled={savingDraft}>
              Save as draft
            </AppBtn>
            <AppBtn size="lg" onClick={() => submit()} disabled={creatingInvoice}>
              {creatingInvoice ? "Saving..." : "Create Invoice"}
            </AppBtn>
          </div>
        </div>
      )}
    </div>
  );
};

const calculateTotal = (values: InvoiceForm) => {
  const itemsTotal = values.items.reduce(
    (total, item) => total + convertAmount(item.price * item.quantity, values.currency),
    0
  );
  const feeTotal = values.fees.reduce(
    (total, fee) => (fee.type === INVOICE_FEE_TYPES.DISCOUNT ? total - fee.amount : total + fee.amount),
    0
  );

  return itemsTotal + feeTotal;
};

export default InvoiceFormFooter;
