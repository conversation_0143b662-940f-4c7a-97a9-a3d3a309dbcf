import { FormikErrors } from "formik";
import { useEffect, useState } from "react";
import { InvoiceItem } from "../../../../assets/interfaces/invoices";
import { toCurrency } from "../../../../assets/js/utils/functions";
import LazyImage from "../../../lazy-image";
import { InputField } from "../../../ui/form-elements";
import SelectDropdown, { DropdownOptionInterface } from "../../../ui/form-elements/select-dropdown";
import { CURRENCIES } from "@/assets/interfaces";

interface SingleItemProps {
  item: InvoiceItem;
  updateItem: (item: InvoiceItem) => void;
  deleteItem: VoidFunction;
  selectOptions: DropdownOptionInterface[];
  createItem: VoidFunction;
  errors?: string | FormikErrors<InvoiceItem>;
  currency: CURRENCIES;
}

const SingleItem: React.FC<SingleItemProps> = (props) => {
  const { item, updateItem, deleteItem, selectOptions, createItem, errors, currency } = props;
  const [showPreview, setShowPreview] = useState(false);
  const getItemFromOptions = (value: string) => selectOptions.find((i) => i.value === value);
  const thisError =
    (errors as FormikErrors<InvoiceItem>)?.name ||
    (errors as FormikErrors<InvoiceItem>)?.price ||
    (errors as FormikErrors<InvoiceItem>)?.quantity;

  useEffect(() => {
    if (item.name && item.price) {
      setShowPreview(true);
      return;
    }

    setShowPreview(false);
  }, [item]);

  const handleSelectChange = (e: any) => {
    const value = e.target.value;
    const option = getItemFromOptions(value);

    if (option) {
      updateItem({ ...item, name: option.meta.name, price: option.meta?.price, id: value });
      // setShowPreview(true);
    }
  };

  const swapItems = () => {
    // setShowPreview(false);
    updateItem({ ...item, name: "", price: 0, id: null });
  };

  return (
    <div>
      {!showPreview && (
        <div className="flex items-stretch w-full space-x-2.5">
          <SelectDropdown
            label="Select or create an item"
            options={selectOptions}
            name="item"
            value={item.id}
            onChange={handleSelectChange}
            action={{ onClick: createItem, label: "Create new item" }}
            hasSearch
            searchLabel="Search Items"
            OptionRender={(props: { option: DropdownOptionInterface; isOption: boolean }) => (
              <ItemRender option={props.option} isOption={props.isOption} currency={currency} />
            )}
            className="flex-1"
          />
          {/* <button
            className="h-11.25 w-11.25 bg-primary-50 border border-grey-border border-opacity-60 rounded-5 flex items-center justify-center"
            onClick={deleteItem}
          >
            {icons.trash}
          </button> */}
        </div>
      )}

      {showPreview && (
        <div>
          <div className="grid gap-4 sm:grid-cols-[1fr,minmax(100px,0.3fr)] w-full sm:gap-2.5 items-center">
            <div className="flex items-center justify-between bg-grey-bg-light rounded-xl border border-grey-border border-opacity-50 px-3.75 py-1.75 w-full">
              <span className="text-sm text-black-muted">{item.name}</span>
              <span className="text-xs font-medium text-dark">{toCurrency(item.price, currency, true)}</span>
              <div className="flex items-center space-x-2">
                <button
                  className="h-8 w-8 rounded-full bg-white border border-grey-border border-opacity-50 flex items-center justify-center transition-all ease-out duration-150 hover:bg-opacity-50 text-grey-subtext"
                  type="button"
                  onClick={swapItems}
                >
                  {icons.swap}
                </button>
                <button
                  className="h-8 w-8 rounded-full bg-white border border-grey-border border-opacity-50 flex items-center justify-center transition-all ease-out duration-150 hover:bg-opacity-50 text-grey-subtext"
                  type="button"
                  onClick={deleteItem}
                >
                  {icons.trash}
                </button>
              </div>
            </div>
            <div>
              <InputField
                label="Qty"
                name="quantity"
                type="number"
                value={item.quantity}
                onChange={(e) => updateItem({ ...item, quantity: Number(e.target.value) })}
                inputMode="numeric"
              />
            </div>
          </div>
          {thisError && (
            <div className="text-accent-red-500 text-xxs mt-1 flex items-center">
              <span className="inline-block pt-1">{thisError}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const ItemRender: React.FC<{ option: DropdownOptionInterface; isOption?: boolean; currency: CURRENCIES }> = ({
  option,
  currency,
  isOption = true,
}) => {
  return (
    <div className="flex items-center w-full justify-between">
      <div className="flex items-center">
        <figure className="w-6 h-6 relative rounded-5 mr-2 overflow-hidden">
          {isOption && <LazyImage src={option.meta?.image} alt={option.text} className="w-full h-full" showLoader />}
        </figure>
        <span className="inline-black text-sm">
          {option.meta?.name} {!isOption && ` - ${toCurrency(option.meta?.price, currency, true)}`}
        </span>
      </div>
      {isOption && (
        <span className="font-medium text-xs text-black-muted">{toCurrency(option.meta?.price, currency, true)}</span>
      )}
    </div>
  );
};

const icons = {
  trash:
    // prettier-ignore
    <svg width="18" height="18" viewBox="0 0 20 20" fill="none">
      <path d="M17.0142 5.45236C16.998 5.45236 16.9737 5.45236 16.9494 5.45236C12.6635 5.02296 8.38571 4.86093 4.14844 5.29032L2.49566 5.45236C2.15539 5.48477 1.85562 5.24171 1.82321 4.90144C1.7908 4.56116 2.03386 4.26949 2.36604 4.23708L4.01881 4.07505C8.329 3.63755 12.6959 3.80769 17.0709 4.23708C17.4031 4.26949 17.6461 4.56926 17.6137 4.90144C17.5894 5.21741 17.3221 5.45236 17.0142 5.45236Z" fill="currentColor" strokeWidth="1"/>
      <path d="M6.88754 4.63422C6.85513 4.63422 6.82272 4.63422 6.78221 4.62612C6.45814 4.56941 6.23129 4.25344 6.288 3.92936L6.46624 2.86802C6.59587 2.09024 6.77411 1.0127 8.66184 1.0127H10.7845C12.6804 1.0127 12.8586 2.13075 12.9801 2.87612L13.1584 3.92936C13.2151 4.26154 12.9882 4.57751 12.6642 4.62612C12.332 4.68283 12.016 4.45598 11.9674 4.13191L11.7892 3.07867C11.6757 2.37381 11.6514 2.23608 10.7926 2.23608H8.66994C7.81115 2.23608 7.79494 2.3495 7.67342 3.07057L7.48707 4.12381C7.43846 4.42358 7.1792 4.63422 6.88754 4.63422Z" fill="currentColor" strokeWidth="1"/>
      <path d="M12.3222 18.4319H7.12079C4.29325 18.4319 4.17982 16.8682 4.0907 15.6043L3.56408 7.44576C3.53977 7.11359 3.79903 6.82192 4.13121 6.79762C4.47149 6.78141 4.75505 7.03257 4.77936 7.36475L5.30598 15.5233C5.3951 16.7548 5.42751 17.2166 7.12079 17.2166H12.3222C14.0236 17.2166 14.056 16.7548 14.137 15.5233L14.6636 7.36475C14.6879 7.03257 14.9796 6.78141 15.3118 6.79762C15.6439 6.82192 15.9032 7.10549 15.8789 7.44576L15.3523 15.6043C15.2632 16.8682 15.1497 18.4319 12.3222 18.4319Z" fill="currentColor" strokeWidth="1"/>
      <path d="M11.0673 13.976H8.36936C8.03718 13.976 7.76172 13.7006 7.76172 13.3684C7.76172 13.0362 8.03718 12.7607 8.36936 12.7607H11.0673C11.3994 12.7607 11.6749 13.0362 11.6749 13.3684C11.6749 13.7006 11.3994 13.976 11.0673 13.976Z" fill="currentColor" strokeWidth="1"/>
      <path d="M11.7484 10.7348H7.69748C7.36531 10.7348 7.08984 10.4593 7.08984 10.1272C7.08984 9.79499 7.36531 9.51953 7.69748 9.51953H11.7484C12.0806 9.51953 12.356 9.79499 12.356 10.1272C12.356 10.4593 12.0806 10.7348 11.7484 10.7348Z" fill="currentColor" strokeWidth="1"/>
    </svg>,
  swap:
    //prettier-ignore
    <svg width="18" height="18" viewBox="0 0 19 19" fill="none">
      <path d="M5.30273 7.33105L9.49856 9.76147L13.6627 7.34689" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M9.49805 14.0685V9.75391" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M8.5178 4.97963L5.98446 6.3888C5.41446 6.70547 4.93945 7.50505 4.93945 8.16213V10.8459C4.93945 11.503 5.40654 12.3026 5.98446 12.6192L8.5178 14.0284C9.05614 14.3292 9.94279 14.3292 10.489 14.0284L13.0224 12.6192C13.5924 12.3026 14.0674 11.503 14.0674 10.8459V8.15422C14.0674 7.49713 13.6003 6.69755 13.0224 6.38088L10.489 4.97171C9.94279 4.67088 9.05614 4.67088 8.5178 4.97963Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M17.4167 11.875C17.4167 14.9387 14.9387 17.4167 11.875 17.4167L12.7062 16.0312" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M1.58398 7.12467C1.58398 4.06092 4.0619 1.58301 7.12565 1.58301L6.29441 2.96842" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
};

export default SingleItem;
