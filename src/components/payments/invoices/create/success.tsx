import classNames from "classnames";
import React, { useState } from "react";
import { DownloadInvoicePdf } from "../../../../api/invoices";
import SuccessIcon from "../../../assets/icons/statuses/success.svg";
import { InvoiceInterface } from "../../../../assets/interfaces/invoices";
import { toAppUrl } from "../../../../assets/js/utils/functions";
import useCopyClipboard from "../../../hooks/useCopyClipboard";
import { useModals } from "../../../hooks/useModals";
import usePDFDownloads from "../../../hooks/usePDFDownloads";
import Portal from "../../../portal";
import { AppBtn } from "../../../ui/buttons";
import SuccessAnimation from "../../../ui/success-animation";
import SendModal from "../modals/send";

interface IProps {
  createdInvoice: InvoiceInterface;
  isEditing?: boolean;
}

const CreateInvoiceSucess: React.FC<IProps> = ({ createdInvoice, isEditing }) => {
  const [copied, copy] = useCopyClipboard(toAppUrl(`invoices/${createdInvoice.invoice_id}`), { successDuration: 500 });
  const { modals, toggleModal } = useModals(["send"]);
  const isDraft = Boolean(createdInvoice?.is_draft);

  const { isLoadingPdf, download } = usePDFDownloads({
    request: DownloadInvoicePdf,
    data: { invoiceId: createdInvoice?.invoice_id },
    filename: `invoice-${createdInvoice?.invoice_id}`,
  });

  return (
    <div className="h-full overflow-y-auto py-12.5 sm:py-20 px-6.25 lg:px-7.5 flex items-center justify-center">
      <div className={classNames("flex flex-col items-center w-full", { "-mt-20": !isEditing, "mt-5": isEditing })}>
        <figure className="mb-5 sm:mb-7.5">
          <SuccessAnimation />
        </figure>
        {!isDraft && !isEditing && (
          <h3 className="text-black font-medium text-2xl sm:text-3lg lg:text-3xl text-center">
            Your <b className="font-bold">Invoice</b> <br />
            has been created.
          </h3>
        )}
        {isDraft && !isEditing && (
          <h3 className="text-black font-medium text-2xl sm:text-3lg lg:text-3xl text-center">
            Your <b className="font-bold">Invoice</b> <br />
            draft was saved.
          </h3>
        )}

        {isEditing && (
          <h3 className="text-black font-medium text-2xl sm:text-3lg lg:text-3xl text-center">
            Your <b className="font-bold">Invoice</b> <br />
            has been updated.
          </h3>
        )}

        {!isEditing && (
          <div className="w-full max-w-[350px] flex flex-col items-center">
            {!isDraft && (
              <ul className="w-full border border-grey-border border-opacity-50 rounded-20 py-1.25 px-3.75 sm:px-5 flex flex-col divide-y divide-grey-border divide-opacity-50 mt-5 sm:mt-7.5">
                <li className="w-full">
                  <button
                    className="flex items-center py-3 hover:bg-grey-fields-100 transition ease-out duration-150 w-full hover:bg-opacity-50"
                    onClick={() => copy()}
                  >
                    <figure className="h-7.5 w-7.5 rounded-full bg-accent-yellow-500 bg-opacity-10 flex items-center justify-center text-accent-yellow-500">
                      {icons.copy}
                    </figure>
                    <span className="inline-block text-sm text-dark font-medium ml-2.5">Copy invoice link</span>
                  </button>
                </li>
                <li className="w-full">
                  <button
                    onClick={() => {
                      toggleModal("send");
                    }}
                    className="flex items-center py-3 hover:bg-grey-fields-100 transition ease-out duration-150 w-full hover:bg-opacity-50"
                  >
                    <figure className="h-7.5 w-7.5 rounded-full bg-accent-red-500 bg-opacity-10 flex items-center justify-center text-accent-red-500">
                      {icons.send}
                    </figure>
                    <span className="inline-block text-sm text-dark font-medium ml-2.5">Send Invoice</span>
                  </button>
                </li>
                <li className="w-full">
                  <button
                    onClick={() => download()}
                    disabled={isLoadingPdf}
                    className="flex items-center py-3 hover:bg-grey-fields-100 transition ease-out duration-150 w-full hover:bg-opacity-50"
                  >
                    <figure className="h-7.5 w-7.5 rounded-full bg-accent-orange-500 bg-opacity-10 flex items-center justify-center text-accent-orange-500">
                      {icons.download}
                    </figure>
                    <span className="inline-block text-sm text-dark font-medium ml-2.5">Download Invoice as PDF</span>
                  </button>
                </li>
              </ul>
            )}

            <AppBtn
              href="/payments/invoices"
              className="mt-5 sm:mt-7.5 !px-8 !inline-flex"
              isBlock={!isDraft}
              size="lg"
            >
              Continue to invoices
            </AppBtn>
          </div>
        )}
      </div>
      <Portal>
        <SendModal invoice={createdInvoice} toggle={() => toggleModal("send")} show={modals.send.show}></SendModal>
      </Portal>
    </div>
  );
};

export const icons = {
  copy:
    // prettier-ignore
    <svg className="w-4.5" viewBox="0 0 18 18" fill="none">
      <path d="M12 9.675V12.825C12 15.45 10.95 16.5 8.325 16.5H5.175C2.55 16.5 1.5 15.45 1.5 12.825V9.675C1.5 7.05 2.55 6 5.175 6H8.325C10.95 6 12 7.05 12 9.675Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M16.5 5.175V8.325C16.5 10.95 15.45 12 12.825 12H12V9.675C12 7.05 10.95 6 8.325 6H6V5.175C6 2.55 7.05 1.5 9.675 1.5H12.825C15.45 1.5 16.5 2.55 16.5 5.175Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  send:
    // prettier-ignore
    <svg className="w-4.5" viewBox="0 0 18 18" fill="none">
      <path d="M5.54977 4.74012L11.9173 2.61762C14.7748 1.66512 16.3273 3.22512 15.3823 6.08262L13.2598 12.4501C11.8348 16.7326 9.49477 16.7326 8.06977 12.4501L7.43977 10.5601L5.54977 9.93012C1.26727 8.50512 1.26727 6.17262 5.54977 4.74012Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7.58203 10.2374L10.267 7.54492" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  download:
    // prettier-ignore
    <svg className="w-4.5" viewBox="0 0 18 18" fill="none">
      <path d="M6.75 16.5H11.25C15 16.5 16.5 15 16.5 11.25V6.75C16.5 3 15 1.5 11.25 1.5H6.75C3 1.5 1.5 3 1.5 6.75V11.25C1.5 15 3 16.5 6.75 16.5Z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M6.75 8.63281L9 10.8828L11.25 8.63281" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M9 10.8828V4.88281" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M4.5 12.3828C7.4175 13.3578 10.5825 13.3578 13.5 12.3828" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
};

export default CreateInvoiceSucess;
