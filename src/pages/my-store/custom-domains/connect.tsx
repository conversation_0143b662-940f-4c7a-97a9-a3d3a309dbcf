import ConnectDomains from "@/components/store/custom-domains/connect-domains";
import useTabs from "../../../components/hooks/useTabs";
import { StorePaymentHistory, StoreSubscriptionInfo } from "../../../components/store";
import DashboardLayout from "../../../components/ui/layouts/dashboard";
import Tabs from "../../../components/ui/tabs";
import authContext from "@/contexts/auth-context";

const ConnectDomain = () => {
  const { user } = authContext.useContainer();

  return (
    <DashboardLayout
      title="Connect Domains"
      padding={false}
      breadCrumb={{ parent: { label: "Custom Domain", path: "/my-store/configurations?tab=custom_domains" } }}
    >
      <div className="overflow-y-auto h-full flex flex-col relative px-5 sm:px-6.25 lg:px-7.5 pb-10">
        <div className="sticky top-0 bg-white pt-2.5 sm:pt-4.5">
          <div className="w-full mx-auto max-w-[450px] pt-22.5">
            <ConnectDomains />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

interface Props {
  activetab: string;
}

export default ConnectDomain;
