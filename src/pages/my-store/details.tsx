import { useFormik } from "formik";
import * as Yup from "yup";
import { UpdateStoreDetailsParams } from "../../api/interfaces/stores.interface";
import { UpdateStoreDetails } from "../../api/stores";
import { useRequest } from "../../api/utils";
import useTabs from "../../components/hooks/useTabs";
import { AppBtn } from "../../components/ui/buttons";
import DashboardLayout from "../../components/ui/layouts/dashboard";
import Tabs from "../../components/ui/tabs";
import BasicDetailsSettings from "../../components/store/basic-details";
import { StoreCategory } from "@/components/store/basic-details/category";

const StoreDetails = () => {
  const { tabs, switchTab, active, acitveKey } = useTabs(
    ["Store Basic Details", "Store Location", "Social Links", "Extra Info", "Business Category", "About Us", "FAQs"],
    0
  );
  const updateStoreReq = useRequest<UpdateStoreDetailsParams>(UpdateStoreDetails);

  const _switchTab = (index: number) => {
    updateStoreReq.clearResponse();
    updateStoreReq.error && (updateStoreReq.error.message = null);
    switchTab(index);
  };

  return (
    <DashboardLayout
      title="Store Details"
      padding={false}
      breadCrumb={{ parent: { label: "Store settings", path: "/my-store" } }}
    >
      <div
        className="overflow-y-auto max-h-full flex flex-col relative px-5 sm:px-6.25 lg:px-7.5"
        id="basic-details-form"
      >
        <div className="pt-2.5 sm:pt-4.5 sticky top-0 bg-white z-50">
          <Tabs tabs={tabs} switchTab={_switchTab} active={active} />
        </div>
        <BasicDetailsSettings activeTab={acitveKey} updateStoreReq={updateStoreReq} />
      </div>
    </DashboardLayout>
  );
};

export const StoreSettingsBreadCrumb = () => {
  return (
    <div className="border-b border-grey-outline border-opacity-40 py-3 px-4 sticky top-0 z-10">
      <AppBtn size="sm" color="neutral" href="/my-store" className="inline-flex">
        {/* prettier-ignore */}
        <svg width="16" viewBox="0 0 24 24" fill="none" className="mr-1">
        <path d="M19 12H5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M12 19L5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
        Back to my store
      </AppBtn>
    </div>
  );
};

export default StoreDetails;
