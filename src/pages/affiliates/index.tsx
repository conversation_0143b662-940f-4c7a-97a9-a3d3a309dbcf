import React from "react";
import DashboardLayout from "@/components/ui/layouts/dashboard";
import AffiliatesList from "@/components/affiliates/affiliates-list";
import AffiliatesStatistics from "@/components/affiliates/affiliates-statistics";
import authContext from "@/contexts/auth-context";
import { SCOPES } from "@/assets/js/utils/permissions";
import { actionIsAllowed } from "@/assets/js/utils/permissions";
import ErrorBox from "@/components/ui/error";
import { AppBtn } from "@/components/ui/buttons";

const Affiliates = () => {
  return (
    <DashboardLayout padding={false} title="Affiliates">
      <div className="h-full overflow-y-auto flex flex-col relative">
        <AffiliatesMain />
      </div>
    </DashboardLayout>
  );
};

const AffiliatesMain = () => {
  const { subscription } = authContext.useContainer();

  let canManageAffiliates = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES,
  });

  if (!canManageAffiliates) {
    return (
      <ErrorBox title="Upgrade Required" message="Please upgrade to business plus plan to add affiliates to your store">
        <AppBtn href="/my-store/change-plan" size="sm" className="mt-5">
          Upgrade Subscription
        </AppBtn>
      </ErrorBox>
    );
  }

  return (
    <div className="px-5 xl:px-7.5">
      <AffiliatesStatistics />
      <AffiliatesList />
    </div>
  );
};
export default Affiliates;
