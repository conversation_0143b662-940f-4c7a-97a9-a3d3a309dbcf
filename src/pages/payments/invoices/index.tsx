import React, { useState } from "react";
import { actionIsAllowed, CountryPermissions, SCOPES } from "../../../assets/js/utils/permissions";
import useFluxState from "../../../components/hooks/useFluxState";
import { InvoicesPlaceholder } from "../../../components/payments/invoices";
import InvoicesDashboard from "../../../components/payments/invoices/dashboard";
import DashboardLayout from "../../../components/ui/layouts/dashboard";
import authContext from "../../../contexts/auth-context";
import InvoiceStatistics from "@/components/payments/invoices/dashboard/invoice-statistics";
import InvoiceTable from "@/components/payments/invoices/dashboard/invoice-table";

const Invoices = ({}) => {
  return (
    <DashboardLayout title="Invoices" padding={false}>
      <div className="h-full overflow-y-auto pb-12.5 px-5 sm:px-6.25 lg:px-7.5">
        <InvoicesDashboardMain />
      </div>
    </DashboardLayout>
  );
};

const InvoicesDashboardMain = () => {
  const { userRole } = authContext.useContainer();
  const canManagePayments = actionIsAllowed({
    permission: SCOPES.WALLETS.CAN_MANAGE_WALLET,
    userRole,
  });

  return (
    <>
      {canManagePayments && <InvoiceStatistics />}
      <InvoiceTable />
    </>
  );
};

export default Invoices;
