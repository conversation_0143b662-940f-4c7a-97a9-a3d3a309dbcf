import React from "react";
import InvoiceForm from "../../../components/payments/invoices/create";
import DashboardLayout from "../../../components/ui/layouts/dashboard";

const CreateInvoice = () => {
  return (
    <DashboardLayout
      title="Create Invoice"
      breadCrumb={{ parent: { label: "Invoices", path: "/payments/invoices" } }}
      padding={false}
    >
      <InvoiceForm />
    </DashboardLayout>
  );
};

export default CreateInvoice;
