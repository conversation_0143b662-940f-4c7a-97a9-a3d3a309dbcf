import { useEffect, useState } from "react";
import { GetPaymentAnalyticsParams } from "../../api/interfaces";
import { useRequest } from "../../api/utils";
import { actionIsAllowed, SCOPES } from "../../assets/js/utils/permissions";
import Can from "../../components/ui/can";
import DashboardLayout from "../../components/ui/layouts/dashboard";
import { TimeRange } from "../../components/ui/statistics-graph";
import { getFilter } from "../dashboard";
import { GetPaymentAnalytics } from "@/api";
import { CURRENCIES, PAYMENT_STATUS } from "@/assets/interfaces";
import { PaymentChart, PaymentStatistics, PaymentsTable } from "@/components/payments/dashboard";
import authContext from "@/contexts/auth-context";
import { InvoicesPlaceholder } from "@/components/payments/invoices";
import PaymentsOnboardingProgress from "@/components/payments/invoices/dashboard/setup-progress";
import WalletContext from "@/contexts/wallet-context";

const Payments = () => {
  const { store } = authContext.useContainer();
  const canCollectPayments = actionIsAllowed({
    countryPermission: SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS,
    country: store?.country?.code,
  });

  if (!store) {
    return (
      <DashboardLayout title="Coming soon">
        <div className="py-7.5 h-full overflow-y-auto w-full flex items-center justify-center -mt-8">
          <span className="text-placeholder text-1xs">Loading...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (!canCollectPayments) {
    return (
      <DashboardLayout title="Coming soon">
        <div className="py-7.5 h-full overflow-y-auto w-full flex items-center justify-center -mt-8">
          <div className="flex flex-col items-center">
            <div className="h-20 w-20 sm:h-25 sm:w-25 bg-accent-green-pastel rounded-full flex items-center justify-center text-[32px] sm:text-4xl">
              {countryPlaceholders[store?.country?.code] ? countryPlaceholders[store?.country?.code].emoji : "💳"}
            </div>
            <h3 className="text-black-secondary font-bold text-lg sm:text-xl my-2.5 sm:my-3 text-center">
              Payments Coming <br /> Soon to{" "}
              {countryPlaceholders[store?.country?.code]
                ? countryPlaceholders[store?.country?.code].country
                : "your country"}
            </h3>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!store?.payments_enabled || !store?.kyc_approved) {
    return (
      <WalletContext.Provider>
        <InvoicesPlaceholder country={store?.country?.code} />
      </WalletContext.Provider>
    );
  }

  return (
    <DashboardLayout title="Payments" padding={false}>
      <div className="h-full overflow-y-auto flex flex-col relative">
        <PaymentsMain />
      </div>
    </DashboardLayout>
  );
};

const PaymentsMain = () => {
  const { store, userRole } = authContext.useContainer();
  const [range, setRange] = useState<TimeRange>(TimeRange.LAST_30_DAYS);
  const statsReq = useRequest<GetPaymentAnalyticsParams>(GetPaymentAnalytics);
  const { response, error, isLoading, makeRequest } = statsReq;
  const data = response?.data;

  const defaultCurrency = store?.currencies?.default ?? CURRENCIES.NGN;
  const [currency, setCurrency] = useState(defaultCurrency);

  const responseData = response?.data;
  const currencyData = data?.grouped_data[currency] ?? null;

  const canManagePayments = actionIsAllowed({
    permission: SCOPES.WALLETS.CAN_MANAGE_WALLET,
    userRole,
  });

  useEffect(() => {
    if (responseData?.currencies?.length > 0) {
      setCurrency(responseData?.currencies.includes(defaultCurrency) ? defaultCurrency : responseData?.currencies[0]);
    }
  }, [responseData, defaultCurrency]);

  useEffect(() => {
    fetchData();
  }, [range]);

  const fetchData = async () => {
    await makeRequest({ ...getFilter(range), status: PAYMENT_STATUS.SUCCESS });
  };

  return (
    <>
      <div className="px-5 xl:px-7.5">
        <Can data={{ permission: SCOPES.PAYMENTS.CAN_VIEW_ANALYTICS }}>
          <>
            <PaymentStatistics
              {...{ data: currencyData, statsReq, currency, setCurrency, currencies: data?.currencies ?? [] }}
            />
            <PaymentsOnboardingProgress canManagePayments={canManagePayments} />
            <PaymentChart {...{ range, setRange, statsReq, currency, data: currencyData?.payments }} />
          </>
        </Can>
        <PaymentsTable />
        {/* <OrdersList /> */}
      </div>
    </>
  );
};

const countryPlaceholders = {
  GH: {
    emoji: "🇬🇭",
    country: "Ghana",
  },
};

export default Payments;
