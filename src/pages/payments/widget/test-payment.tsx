import { COUNTRIES } from "@/assets/interfaces";
import { reloadPage } from "@/assets/js/utils/functions";
import { AppBtn } from "@/components/ui/buttons";
import { ModalFooter } from "@/components/ui/modal";
import MakeTestPayment, {
  CTALabels,
  MakePaymentHandle,
  TEST_PAYMENT_STEPS,
} from "@/components/wallets/test-payment/make-payment";
import authContext from "@/contexts/auth-context";
import { GetServerSideProps } from "next";
import React, { useEffect, useRef, useState } from "react";
import { MESSAGE_TYPES } from ".";

interface Props {
  authToken?: string;
  country: COUNTRIES;
}

const TestPaymentWidget = ({ authToken, country }: Props) => {
  const { isAuthenticated } = authContext.useContainer();
  const makePaymentRef = useRef<MakePaymentHandle>(null);

  const [step, setStep] = useState(TEST_PAYMENT_STEPS.INITIATING);
  const [showCTA, setShowCTA] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCTADisabled, setCTADisabled] = useState(false);

  useEffect(() => {
    if (authToken && !localStorage.getItem("token")) {
      localStorage.setItem("token", authToken);
      reloadPage();
    }
  }, [authToken]);

  const onSuccess = () => {
    const messageObject = {
      type: MESSAGE_TYPES.SUCCESS,
    };
    const message = JSON.stringify(messageObject);
    const win = window as any;
    makePaymentRef?.current?.cleanup();
    if (win.ReactNativeWebView) win.ReactNativeWebView.postMessage(message);
  };

  if (!isAuthenticated || !authToken) {
    return (
      <div className="h-full w-full flex flex-col items-center justify-center">
        <span className="text-black-placeholder">Please reload page to continue</span>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col w-full py-10 px-3.75">
      <div className="flex-1">
        <MakeTestPayment
          ref={makePaymentRef}
          {...{
            setCTADisabled,
            setIsLoading,
            setShowCTA,
            setStep,
            country,
            toggle: (state: boolean) => onSuccess(),
            fromWidget: true,
          }}
        />
      </div>
      {showCTA ? (
        <ModalFooter>
          <AppBtn size="lg" isBlock onClick={makePaymentRef.current?.handleCTAClick} disabled={isCTADisabled}>
            {isLoading ? "Please wait..." : CTALabels[step]}
          </AppBtn>
        </ModalFooter>
      ) : (
        false
      )}
    </div>
  );
};

export default TestPaymentWidget;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const queries = context.query;

  try {
    if (Object.keys(queries).length > 0) {
      return {
        props: {
          authToken: (queries?.authToken as string) ?? "",
          country: (queries?.country as string) ?? "",
        },
      };
    }
    throw Error("Invalid query");
  } catch (e) {
    return {
      props: {
        error: e.message,
      },
    };
  }
};
