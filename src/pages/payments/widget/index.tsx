import { PAYMENT_TYPES, Plan } from "@/assets/interfaces";
import { IDelivery } from "@/assets/interfaces/deliveries";
import { queryToNestedObject, reloadPage } from "@/assets/js/utils/functions";
import MakePayments from "@/components/make-payments";
import { AppBtn } from "@/components/ui/buttons";
import ContentState from "@/components/ui/content-state";
import { GetServerSideProps } from "next";
import { useEffect } from "react";

enum ADDON_TYPES {
  DELIVERY = "delivery",
}

export enum MESSAGE_TYPES {
  SUCCESS = "success",
  CLOSE = "close",
}

interface Props {
  error?: string;
  plan?: Plan;
  delivery?: IDelivery;
  tokens?: number;
  paymentType?: PAYMENT_TYPES;
  successMessage?: string;
  addon?: ADDON_TYPES;
  authToken?: string;
}

const PaymentWidget: React.FC<Props> = (props) => {
  const { paymentType, successMessage, addon, plan, delivery, error, authToken } = props;

  useEffect(() => {
    if (authToken && !localStorage.getItem("token")) {
      localStorage.setItem("token", authToken);
      reloadPage();
    }
  }, []);

  if (error) {
    return (
      <ContentState
        error={error}
        errorTitle="Something went wrong"
        errorMessage="Couldn't load payments, please retry"
        errorAction={
          <AppBtn size="md" onClick={reloadPage}>
            Reload page
          </AppBtn>
        }
      ></ContentState>
    );
  }

  const onSuccess = (ref: string) => {
    const messageObject = {
      type: MESSAGE_TYPES.SUCCESS,
      reference: ref,
    };
    const message = JSON.stringify(messageObject);
    const win = window as any;
    if (win.ReactNativeWebView) win.ReactNativeWebView.postMessage(message);
  };

  const onEdit = () => {
    const messageObject = {
      type: MESSAGE_TYPES.CLOSE,
    };
    const message = JSON.stringify(messageObject);
    const win = window as any;
    if (win.ReactNativeWebView) win.ReactNativeWebView.postMessage(message);
  };

  const addOnMap = {
    [ADDON_TYPES.DELIVERY]: (
      <AppBtn color="neutral" size="lg" isBlock className="mt-2.5 mb-1" onClick={onEdit}>
        Edit Delivery Info
      </AppBtn>
    ),
  };

  return (
    <section className="w-full flex flex-col max-w-[600px] mx-auto px-2.5 py-2.5">
      <MakePayments
        {...{ plan, delivery, paymentType, successMessage, handleSuccess: onSuccess }}
        usedInModal={true}
        addon={addOnMap[addon]}
      />
    </section>
  );
};
export default PaymentWidget;

export const getServerSideProps: GetServerSideProps<Props> = async (context) => {
  const queries = context.query;

  try {
    if (Object.keys(queries).length > 0) {
      const props = queryToNestedObject(queries);
      return {
        props,
      };
    }
    throw Error("Invalid query");
  } catch (e) {
    return {
      props: {
        error: e.message,
      },
    };
  }
};
