import { AppBtn } from "@/components/ui/buttons";
import DashboardLayout from "../../components/ui/layouts/dashboard";
import { useState } from "react";
import classNames from "classnames";
import LinkIllustration from "@/assets/icons/streamline/link.svg";
import InvoiceIllustration from "@/assets/icons/streamline/invoice.svg";
import router from "next/router";

type OptionType = "invoice" | "payment-link";

const RequestPaymentPage = () => {
  const [uploadMethod, setUploadMethod] = useState<OptionType>(null);

  const handleNext = () => {
    router.push(uploadMethod === "invoice" ? `/payments/invoices/create` : "/payments/payment-links/create");
  };

  return (
    <DashboardLayout title="Request A Payment" padding={false}>
      <div className="h-full overflow-y-auto py-12.5 sm:py-20 pb-20 px-5 sm:px-6.25 lg:px-7.5">
        <div className="w-full mx-auto max-w-[400px] pb-15">
          <div className="w-full">
            <h2 className="text-center font-bold text-black text-2lg sm:text-[26px] lg:text-3xl mx-auto mt-3.5 !leading-tight">
              How would you <br /> like to get paid?
            </h2>

            <div className="grid w-full grid-cols-2 gap-3 sm:gap-5 mt-9 sm:mt-12.5">
              {options.map((o, index) => (
                <div
                  className={classNames(
                    "w-full pt-[100%] relative border-2  transition-all ease-out duration-100 rounded-15 group",
                    {
                      "border-grey-divider hover:border-grey-border": uploadMethod !== o.key,
                      "border-primary-400": uploadMethod === o.key,
                    }
                  )}
                  key={index}
                >
                  <button
                    className="absolute top-0 left-100 w-full h-full flex flex-col items-center justify-center"
                    type="button"
                    onClick={() => setUploadMethod(o.key)}
                  >
                    <div className="p-3 sm:p-3.75 w-full flex items-center justify-center">
                      <figure
                        className={classNames("w-1/2 opacity-70 transition-all ease-out duration-100", {
                          "text-grey-subtext group-hover:text-black-secondary": uploadMethod !== o.key,
                          "text-black-secondary": uploadMethod === o.key,
                        })}
                      >
                        {o.icon()}
                      </figure>
                    </div>
                    <span
                      className={classNames(
                        "inline-block font-medium text-1xs sm:text-1sm  mt-2 sm:mt-2.5 transition-all ease-out duration-100",
                        {
                          "text-placeholder group-hover:text-black": uploadMethod !== o.key,
                          "text-black": uploadMethod === o.key,
                        }
                      )}
                    >
                      {o.label}
                    </span>
                  </button>
                </div>
              ))}
            </div>
            <AppBtn className="mt-5 sm:mt-7.5" isBlock size="lg" disabled={!uploadMethod} onClick={handleNext}>
              Continue
            </AppBtn>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

const options = [
  {
    label: "With a Payment Link",
    key: "payment-link" as OptionType,
    icon: LinkIllustration,
  },
  {
    label: "With an Invoice",
    key: "invoice" as OptionType,
    icon: InvoiceIllustration,
  },
];
export default RequestPaymentPage;
