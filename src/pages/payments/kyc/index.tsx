import Router from "next/router";
import React, { useEffect, useRef, useState } from "react";
import React<PERSON>on<PERSON>tti from "react-confetti";
import { CheckRelatedKyc, GetStoreKYC, SubmitKYC } from "../../../api/store.kyc";
import { useFetcher, useRequest } from "../../../api/utils";
import { COUNTRIES, KYCInfo, KYC_STATUSES, Tier } from "../../../assets/interfaces";
import { actionIsAllowed, SCOPES } from "../../../assets/js/utils/permissions";
import scroll from "../../../assets/js/utils/scroll";
import { useListenerState } from "../../../components/hooks/useListener";
import { useModals } from "../../../components/hooks/useModals";
import useSteps from "../../../components/hooks/useSteps";
import {
  KYCAddressInfo,
  KYCBVNInfo,
  KYCBasicInfo,
  KYCFooter,
  KYCIDInfo,
  KYCInfoSummary,
} from "../../../components/payments/invoices";
import CopyKycInfoConfirmationModal from "../../../components/payments/kyc/copy-kyc-confirmation-modal";
import KycOptions from "../../../components/payments/kyc/kyc-options";
import KYCStatus from "../../../components/payments/kyc/status";
import TierModal from "../../../components/payments/kyc/modals/tier";
import Badge, { BadgeColor } from "../../../components/ui/badge";
import { AppBtn } from "../../../components/ui/buttons";
import ErrorBox from "../../../components/ui/error";
import DashboardLayout from "../../../components/ui/layouts/dashboard";
import authContext from "../../../contexts/auth-context";
import KYCPhoneInfo from "@/components/payments/kyc/phone-info";
import ManualKYCIDInfo from "@/components/payments/kyc/manual-id-info";
import { ChowbotSetupPageComponent } from "@/pages/get-started/chowbot";

const InvoicesKYC = () => {
  return (
    <DashboardLayout title="Get Verified" padding={false} bannerConfig={{ show: false }} showSupportBtn={true}>
      <KYCMain />
    </DashboardLayout>
  );
};

export const KYCMain: ChowbotSetupPageComponent = ({ isChowbotSetup = false }) => {
  const { updateStore, store, userRole, user } = authContext.useContainer();
  const [kycInfo, setKycInfo] = useState<KYCInfo>(null);
  const [showOptions, setShowOptions] = useState(true);
  const submitKycRequest = useRequest(SubmitKYC);
  const relatedKycReq = useFetcher(CheckRelatedKyc);
  const relatedKycData: KYCInfo[] = relatedKycReq?.response?.data;
  const { response, error, isLoading } = useFetcher(GetStoreKYC);
  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = useSteps(
    kycSteps.map((s) => s.key),
    getStep(kycInfo, store?.country?.code)
  );
  const stepsWrapperRef = useRef<HTMLDivElement>(null);
  const { modals, toggleModal } = useModals(["tier", "copy_confirmation"]);
  const [tier] = useListenerState<Tier>("show-tier-modal", Tier.TIER_1, (tier) => {
    toggleModal("tier");
  });

  const canUpdateKyc = actionIsAllowed({
    userRole,
    permission: SCOPES.WALLETS.CAN_MANAGE_WALLET,
  });
  const [hasDocument, setHasDocument] = useState(false);

  const showOptionsList = !kycInfo?.bvn && relatedKycData?.length > 0 && relatedKycData[0]?.id !== kycInfo?.id;
  const isNGKYC = store.country.code === COUNTRIES.NG;

  useEffect(() => {
    setHasDocument(document !== undefined && document.body ? true : false);
  }, []);

  useEffect(() => {
    //Handles scrolling the page sections
    if (stepsWrapperRef.current) {
      const stepsWrapper = stepsWrapperRef.current;
      const wrapperScrollTop = stepsWrapper.scrollTop;

      scroll({
        e: stepsWrapper,
        time: 350,
        amount: stepsWrapper.clientHeight * stepIndex - wrapperScrollTop,
        start: stepsWrapper.scrollTop,
        pos: "top",
        shouldScroll: stepIndex < 5,
      });
    }
  }, [step, stepIndex]);

  useEffect(() => {
    const info: KYCInfo = response?.data ?? null;
    setKycInfo(info);

    if (info !== null && info.verification_method === "MANUAL") {
      Router.push(`/payments/kyc/manual?isChowbotSetup=${String(isChowbotSetup)}`);
    }

    if (info !== null) {
      const step = steps[getStep(info, store?.country?.code)];
      changeStep(step);

      // if (step === "ID" && info.country === COUNTRIES.NG) {
      //   Router.push("/payments/kyc/manual");
      // } else {
      // }
    }
  }, [response]);

  useEffect(() => {
    if (submitKycRequest.isLoading) {
      changeStep("STATUS");
    }
  }, [submitKycRequest.isLoading]);

  const canGoNext = validateData(kycInfo)[kycSteps[stepIndex].key] && canNext;

  const submitKycInfo = async () => {
    const [res, error] = await submitKycRequest.makeRequest({});

    if (res) {
      setKycInfo(res?.data?.kyc);
      const storeData = res?.data?.store;

      //update state for the rest of the dashboard
      if (storeData) {
        updateStore({
          payments_enabled: storeData?.payments_enabled,
          wallet: storeData?.wallet,
          payment_options: storeData?.payment_options,
        });
      }
    }
  };

  if (!canUpdateKyc) {
    return (
      <ErrorBox
        title="Only owners can enable payments"
        message="We need BVN & ID of the owner of this store to enable payments, Please notify the owner of this store to enable payments."
      >
        <AppBtn size="sm" href="/dashboard" className="mt-5">
          Back to Dashboard
        </AppBtn>
      </ErrorBox>
    );
  }

  if (showOptionsList && showOptions) {
    return <KycOptions {...{ setShowOptions, kycInfo, relatedKycData }} />;
  }

  return (
    <>
      {isLoading && (
        <div className="h-full relative w-full overflow-hidden px-5 sm:px-6.25 lg:px-7.5 flex flex-col items-center justify-center">
          <div className="spinner spinner--md text-primary-500"></div>
        </div>
      )}

      {!isLoading && !error && (
        <>
          <div className="h-full relative w-full overflow-hidden" ref={stepsWrapperRef}>
            <KYCBasicInfo
              isActive={isActive("BASIC")}
              next={next}
              kycInfo={kycInfo}
              setKycInfo={setKycInfo}
              user={user}
              country={store?.country?.code}
            />
            {isNGKYC ? (
              <KYCBVNInfo isActive={isActive("BVN")} kycInfo={kycInfo} setKycInfo={setKycInfo} next={next} />
            ) : (
              <KYCPhoneInfo
                isActive={isActive("BVN")}
                kycInfo={kycInfo}
                setKycInfo={setKycInfo}
                next={next}
                phone={user?.phone}
              />
            )}

            {kycInfo?.country === COUNTRIES.NG ? (
              <ManualKYCIDInfo kycInfo={kycInfo} next={next} setKycInfo={setKycInfo} isActive={isActive("ID")} />
            ) : (
              <KYCIDInfo kycInfo={kycInfo} next={next} setKycInfo={setKycInfo} isActive={isActive("ID")} />
            )}
            <KYCAddressInfo isActive={isActive("ADDRESS")} kycInfo={kycInfo} setKycInfo={setKycInfo} next={next} />
            <KYCInfoSummary kycInfo={kycInfo} submitKyc={submitKycInfo} previous={previous} />
            <KYCStatus
              kycInfo={kycInfo}
              isSubmitting={submitKycRequest.isLoading}
              error={submitKycRequest.error?.message}
              retry={submitKycInfo}
              submitResponse={submitKycRequest?.response}
              isChowbotSetup={isChowbotSetup}
            />
          </div>
          <KYCFooter {...{ previous, next, canNext: canGoNext, canPrevious, step: stepIndex, steps: steps.length }} />
          <TierModal show={modals.tier.show} toggle={() => toggleModal("tier")} tier={tier} />
        </>
      )}

      {hasDocument && (
        <ReactConfetti
          width={window.innerWidth}
          height={window.innerHeight}
          numberOfPieces={500}
          recycle={false}
          run={kycInfo?.status === KYC_STATUSES.APPROVED && step === "STATUS"}
          style={{ zIndex: 9999 }}
        />
      )}
    </>
  );
};

export const validateData = (kycInfo: KYCInfo) => {
  return {
    BASIC: kycInfo !== null && kycInfo?.first_name && kycInfo?.last_name,
    BVN: Boolean(kycInfo?.bvn) && Boolean(kycInfo?.phone) && Boolean(kycInfo?.bvn_verified_at) && Boolean(kycInfo?.dob),
    PHONE: Boolean(kycInfo?.phone_verified) && Boolean(kycInfo?.dob),
    ID: kycInfo?.identity?.number && kycInfo?.identity?.type && kycInfo?.identity?.url,
    ADDRESS:
      kycInfo?.address?.city && kycInfo?.address?.lga && kycInfo?.address?.address_line1 && kycInfo?.address?.state,
  };
};

const getStep = (kycInfo: KYCInfo, country: COUNTRIES) => {
  if (!validateData(kycInfo).BASIC) {
    return 0;
  }

  if (!validateData(kycInfo).BVN && country === COUNTRIES.NG) {
    return 1;
  }

  if (!validateData(kycInfo).PHONE && country !== COUNTRIES.NG) {
    return 1;
  }

  if (!validateData(kycInfo).ID) {
    return 2;
  }

  if (!validateData(kycInfo).ADDRESS) {
    return 3;
  }

  if (
    kycInfo?.status === KYC_STATUSES.APPROVED ||
    kycInfo?.status === KYC_STATUSES.PENDING ||
    kycInfo?.status === KYC_STATUSES.DENIED
  ) {
    return 5;
  }

  return 4;
};

const kycSteps = [
  {
    key: "BASIC",
    isSkipable: false,
  },
  {
    key: "BVN",
    isSkipable: false,
  },
  {
    key: "ID",
    isSkipable: true,
  },
  {
    key: "ADDRESS",
    isSkipable: true,
  },
  {
    key: "SUMMARY",
    isSkipable: false,
  },
  {
    key: "STATUS",
    isSkipable: false,
  },
];

export default InvoicesKYC;
