import { NextApiRequest, NextApiResponse } from "next";
import fetch from "node-fetch";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { url } = req.query; // ?key=logo.png
  const r = await fetch(url as string);
  res.setHeader("Cross-Origin-Resource-Policy", "same-site"); // or 'cross-origin'
  r.headers.forEach((v, k) => res.setHeader(k, v));
  r.body.pipe(res);
}
