import { GetStoreWrappedData, GetStoreWrappedDataPublic } from "@/api";
import { useRequest } from "@/api/utils";
import Logo from "@/assets/icons/chronicles.svg";
import { COUNTRIES, YearWrappedResponse } from "@/assets/interfaces";
import { emit, useListener } from "@/components/hooks/useListener";
import { useModals } from "@/components/hooks/useModals";
import Portal from "@/components/portal";
import { AppBtn, RoundActionBtn } from "@/components/ui/buttons";
import ErrorScreen from "@/components/ui/error-screen";
import CardControls from "@/components/wrapped/card-controls";
import CardProgress from "@/components/wrapped/card-progress";
import ShareWrappedCardsModal from "@/components/wrapped/share-cards";
import authContext from "@/contexts/auth-context";
import { GetServerSideProps } from "next";
import { FC, useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import { CURRENCY_COUNTRY_MAP, wrappedCardData } from "@/assets/js/utils/constants";
import { fromBase64 } from "@/assets/js/utils/functions";
import Preload from "preload-it";
import Bg from "@/components/wrapped/cards/big-moves-bg";
const preload = Preload();

export interface WrappedData {
  key: string;
  name: string;
  duration: number;
  sound_file?: string;
  is_private: boolean;
  shareable: boolean;
  colors?: {
    fill1: string;
    fill2: string;
    textColor: string;
  };
  component: FC<any>;
}

interface Props {
  slug: string;
  share_data: {
    cards: string[];
    // type?: "all" | "multiple";
  };
}

const StoreWrapped: React.FC<Props> = ({ slug = null, share_data }) => {
  const { isAuthenticated, stores, store } = authContext.useContainer();
  const isOwner = slug === null && isAuthenticated;

  const privateRequest = useRequest(GetStoreWrappedData);
  const publicRequest = useRequest(GetStoreWrappedDataPublic);

  const request = isOwner ? privateRequest : publicRequest;

  const [wrappedResponse, setWrappedResponse] = useState<YearWrappedResponse>();
  const [isPaused, setIsPaused] = useState(false);
  const [currentProgress, setCurrentProgress] = useState(0);
  const [currentCard, setCurrentCard] = useState(0);

  const [assetsLoaded, setAssetsLoaded] = useState(false);
  const [assetsLoadingProgress, setAssetsLoadingProgress] = useState(false);
  const [canStart, setCanStart] = useState(false);

  const controls = useRef<any>({});
  const soundRef = useRef<HTMLAudioElement>(null);
  const { modals, toggleModal } = useModals(["share"]);

  const maxVolume = 0.2;

  const filteredCards = useMemo<WrappedData[]>(() => {
    let cards = [...wrappedCardData];
    if (wrappedResponse) {
      const country = CURRENCY_COUNTRY_MAP[wrappedResponse?.store_currency];
      if (country !== COUNTRIES.NG) {
        cards = cards.filter((card) => card.key !== "total_deliveries");
      }
      if (share_data?.cards?.length > 0) {
        cards = cards.filter((card) =>
          ["intro_card", ...share_data?.cards, "big_moves", "metaphor", "outro_card"]?.includes(card.key)
        );
      }
      if (!isOwner) {
        cards = cards.filter((card) => card.is_private === false);
      }

      if (wrappedResponse.top_customer === undefined || wrappedResponse?.top_customer?.name.trim() === "") {
        cards = cards.filter((card) => card.key !== "top_customer");
      }

      if (wrappedResponse.top_product === undefined || wrappedResponse?.top_product?.name.trim() === "") {
        cards = cards.filter((card) => card.key !== "best_product");
      }
    }
    return cards;
  }, [wrappedResponse?.store_currency, isOwner]);

  useListener(
    "open_share_modal",
    () => {
      if (isOwner) toggleModal("share");
    },
    [isOwner]
  );

  useEffect(() => {
    preload.onprogress = (event) => {
      setAssetsLoadingProgress(event.progress);
      // console.log(event.progress + "%");
    };
    preload
      .fetch([
        ...wrappedCardData.map((card) => card.sound_file),
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/videos/total_store_visits.mp4",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/videos/top_products.mp4",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/videos/total_order_processed.mp4",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/videos/total_referrals.mp4",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/videos/top_order_location.mp4",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/videos/total_deliveries.mp4",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/videos/order_best_month.mp4",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/videos/top_customers.mp4",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/videos/payments_processed.mp4",

        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/illustrations/lily.png",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/illustrations/bamboo.png",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/illustrations/oak.png",
        "https://catlog-s3.s3.eu-west-2.amazonaws.com/year+wrapped/illustrations/iroko.png",
      ])
      .then((items) => {
        // console.log(items);
        setAssetsLoaded(true);
      });
  }, []);

  useEffect(() => {
    getWrapped();
  }, [isOwner]);

  const getWrapped = async () => {
    const [res, err] = await request.makeRequest({ store_slug: slug });
    if (res) {
      setWrappedResponse({ ...res?.data, is_shared: !isOwner });
    }
  };

  useLayoutEffect(() => {
    if (wrappedResponse && assetsLoaded && canStart) {
      const timeout = startCardPlay();
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [wrappedResponse, assetsLoaded, canStart]);

  const startSound = () => {
    try {
      const sound = soundRef.current;
      sound.onended = () => sound.play();

      if (sound.volume > 0) {
        controls.current.muted = true;
        sound.volume = 0;
      } else {
        controls.current.muted = false;
        sound.volume = maxVolume;
        sound.play();
      }
    } catch (e) {
      console.log(e);
    }
  };

  const startCardPlay = () => {
    const fps = 30;
    const fpsInMills = 1000.0 / fps;

    controls.current.currentCard = 0;
    controls.current.progress = 0;

    startSound();

    const id = setInterval(() => {
      requestAnimationFrame(() => {
        const card = filteredCards[controls.current.currentCard];
        if (card) {
          if (soundRef.current == null) {
            const sound = new Audio(card.sound_file);
            sound.volume = maxVolume;
            soundRef.current = sound;

            try {
              if (controls.current.muted) {
                sound.volume = 0;
              }
              sound.play();
            } catch (e) {
              console.log(e);
            }
          }
          const progressDelta = 100 / (card.duration * fps);
          if (controls.current.paused == true) return;

          controls.current.progress += progressDelta;

          if (controls.current.progress >= 100) {
            controls.current.progress = 0;
            controls.current.currentCard += 1;
            setCurrentProgress(100);

            if (controls.current.currentCard < filteredCards.length) {
              setCurrentProgress(0);
              setCurrentCard(controls.current.currentCard);
              resetSound();
            } else {
              controls.current.paused = true;
            }
          } else {
            setCurrentProgress(controls.current.progress);

            // fade sound in and out
            if (controls.current.muted) return;

            const progress = controls.current.progress;
            const fadeInStop = 10;
            const fadeOutStart = 80;

            if (progress <= fadeInStop) {
              soundRef.current.volume = (progress / fadeInStop) * maxVolume;
            } else if (progress >= fadeOutStart) {
              soundRef.current.volume = maxVolume - ((progress - fadeOutStart) / (100 - fadeOutStart)) * maxVolume;
            }
          }
        }
      });
    }, fpsInMills);

    return id;
  };

  const handleBack = () => {
    if (currentCard > 0) {
      controls.current.progress = 0;
      controls.current.paused = false;
      controls.current.currentCard = currentCard - 1;
      setCurrentCard(controls.current.currentCard);
      resetSound();
    }
  };

  const handleNext = () => {
    if (currentCard < filteredCards.length - 1) {
      controls.current.progress = 0;
      controls.current.paused = false;
      controls.current.currentCard = currentCard + 1;

      setCurrentCard(controls.current.currentCard);
      setCurrentProgress(0);
      resetSound();
    }
  };

  const resetSound = () => {
    try {
      setIsPaused(false);
      soundRef.current?.pause();
      soundRef.current?.remove();
      soundRef.current = null;
    } catch (e) {
      console.log(e);
    }
  };

  const handlePause = () => {
    setIsPaused(!isPaused);
    emit("toggle-play-gsap");
    if (controls.current.paused == true) {
      controls.current.paused = false;
      soundRef.current?.play();
      return;
    }
    soundRef.current?.pause();
    controls.current.paused = !controls.current.paused;
  };

  const getCard = (index: number) => {
    const Card = filteredCards[index]?.component;
    return <Card {...wrappedResponse} />;
  };

  if (request.error) {
    return (
      <main className="w-screen h-screen bg-[#0A0719] overflow-hidden flex flex-col items-center justify-center relative">
        <Logo className="sm:left-12.5 left-5 top-7.5 sm:top-12.5 absolute z-10" />

        <div className="flex-1 relative h-screen overflow-y-auto md:py-25 xl:py-10 md:absolute md:top-0 md:left-0 md:right-0 md:mx-auto md:h-full  ">
          <div
            style={{ aspectRatio: "9/16" }}
            className="bg-gray-700 mx-auto w-[100vw] sm:w-[fit-content] h-full flex flex-col items-center justify-center overflow-hidden sm:rounded-3xl relative"
          >
            <div className="w-full absolute left-0 top-0 h-full">{Bg("#6955D1", "#5644B3")}</div>
            <h3 className="relative z-10 text-2xl sm:text-3xl font-semibold text-center px-5 sm:px-10 text-white max-w-md">
              Oops! You did not use Catlog much in 2024. <br /> <br /> We'll be looking out for your business this year!
              🫶
            </h3>
          </div>
        </div>
      </main>
    );
  }

  if (!isOwner && !request.isLoading && (!Boolean(share_data) || (share_data && share_data?.cards === undefined))) {
    return (
      <ErrorScreen title="404: This page looks strange" message="Check the url and try again or:">
        <AppBtn href="/" className="mt-5" size="md">
          Go back home
        </AppBtn>
      </ErrorScreen>
    );
  }

  if (request.isLoading || !assetsLoaded || !canStart) {
    return (
      <main className="w-screen h-screen bg-[#0A0719] overflow-hidden flex flex-col items-center justify-center relative">
        <Logo className="sm:left-12.5 left-5 top-7.5 sm:top-12.5 absolute" />
        {request.isLoading === true ? (
          <>
            <div className="spinner text-white"></div>
            <h3 className="text-white mt-5 opacity-50">Final Touches ✨</h3>
          </>
        ) : !assetsLoaded ? (
          <>
            <h3 className="text-white mt-5 opacity-50">Getting things ready 🍳</h3>
            <div
              className="max-w-[150px] duration-200 ease-linear h-1.25 mt-3.75 bg-white opacity-40"
              style={{ width: `${assetsLoadingProgress}%` }}
            ></div>
          </>
        ) : (
          <div>
            <AppBtn
              onClick={() => setCanStart(true)}
              color="white"
              className="rounded-full bg-opacity-10 hover:bg-opacity-20 duration-300 !text-white mt-10"
            >
              <RoundActionBtn icon="play" className="bg-transparent  !text-white " />
              <span className="font-display tracking-wider">Click To Play</span>
            </AppBtn>
          </div>
        )}
      </main>
    );
  }

  const handleToggleShare = () => {
    handlePause();
    toggleModal("share");
  };

  return (
    <main className="w-screen h-screen bg-[#0A0719] overflow-hidden flex flex-col items-center">
      <div className="flex items-center justify-between p-3.75 md:p-12.5 w-full z-20">
        <div className="text-white w-40 md:w-[200px]">
          {" "}
          <Logo />{" "}
        </div>
        <div className="flex items-center gap-2.5">
          <RoundActionBtn onClick={handlePause} icon={isPaused ? "play" : "pause"} className="bg-black !text-white" />
          <RoundActionBtn
            onClick={startSound}
            icon={
              !soundRef.current || soundRef?.current?.paused || soundRef?.current?.volume === 0 ? "sound_off" : "sound"
            }
            className="bg-black !text-white"
          />
          {/* <RoundActionBtn icon="cancel_outlined" className="bg-black !text-white" /> */}
        </div>
      </div>
      {wrappedResponse && (
        <div className="flex-1 relative  overflow-y-auto md:py-25 xl:py-10 md:absolute md:top-0 md:left-0 md:right-0 md:mx-auto md:h-full  ">
          <div
            style={{ aspectRatio: "9/16" }}
            className="bg-gray-700 mx-auto w-[100vw] sm:w-[fit-content] h-[fit-content] sm:h-full flex flex-col items-center justify-center overflow-hidden sm:rounded-3xl relative"
          >
            <CardProgress
              cardTotal={filteredCards.length}
              currentCard={currentCard}
              currentProgress={currentProgress}
            />
            {getCard(currentCard)}
            <CardControls back={handleBack} pause={handlePause} next={handleNext} />
          </div>
        </div>
      )}

      {!Boolean(share_data) && isOwner && (
        <button
          onClick={() => handleToggleShare()}
          className="bg-white fixed z-[999] py-3.75 px-5 bottom-5 left-5 md:bottom-12.5  md:left-12.5 flex gap-2.5 rounded-full"
        >
          {/* prettier-ignore */}
          <svg width="22" height="22" viewBox="0 0 22 22" fill="none" >
            <path d="M3.66675 11V18.3333C3.66675 18.8196 3.8599 19.2859 4.20372 19.6297C4.54754 19.9735 5.01385 20.1667 5.50008 20.1667H16.5001C16.9863 20.1667 17.4526 19.9735 17.7964 19.6297C18.1403 19.2859 18.3334 18.8196 18.3334 18.3333V11" stroke="#03010C" strokeWidth="1.6" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M14.6666 5.50004L10.9999 1.83337L7.33325 5.50004" stroke="#03010C" strokeWidth="1.6" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M11 1.83337V13.75" stroke="#03010C" strokeWidth="1.6" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span className="text-black text-1sm md:text-base">Share</span>
        </button>
      )}
      <Portal>
        <ShareWrappedCardsModal
          store_slug={slug ?? store.slug}
          currentCard={filteredCards[currentCard].shareable ? filteredCards[currentCard].key : null}
          show={modals.share.show}
          toggle={() => handleToggleShare()}
        />
      </Portal>
    </main>
  );
};
export default StoreWrapped;

export const getServerSideProps: GetServerSideProps<Props> = async (context) => {
  const slug = context.params.slug as string;
  let share_data: any = null;
  try {
    if (context.query.share_data) share_data = JSON.parse(fromBase64(context.query.share_data));

    return {
      props: {
        slug,
        share_data,
      },
    };
  } catch (e) {
    return {
      props: {
        slug,
        share_data: null,
      },
    };
  }
};
