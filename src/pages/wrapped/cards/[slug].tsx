import { YearWrappedResponse } from "@/assets/interfaces";
import { wrappedCardData } from "@/assets/js/utils/constants";
import { fromBase64 } from "@/assets/js/utils/functions";
import BestMonthShareCard from "@/components/wrapped/cards/shareable/best-month";
import BestProductShareCard from "@/components/wrapped/cards/shareable/best-product";
import MetaphorShareCard from "@/components/wrapped/cards/shareable/metaphor";
import OrdersProcessedShareCard from "@/components/wrapped/cards/shareable/orders-processed";
import PaymentsProcessedShareCard from "@/components/wrapped/cards/shareable/payments-processed";
import ReferralsShareCard from "@/components/wrapped/cards/shareable/referrals";
import StoreVisitsShareCard from "@/components/wrapped/cards/shareable/store-visits";
import SummaryShareCard from "@/components/wrapped/cards/shareable/summary";
import TopLocationShareCard from "@/components/wrapped/cards/shareable/top-location";
import { GetServerSideProps } from "next";
import { ReactElement } from "react";

interface Props {
  slug: string;
  data?: Partial<YearWrappedResponse>;
}

type CardKey = (typeof wrappedCardData)[number]["key"];

const StoreWrappedCard: React.FC<Props> = ({ slug, data }) => {
  const cards: Partial<Record<CardKey, ReactElement>> = {
    store_visits: <StoreVisitsShareCard data={data} />,
    orders_processed: <OrdersProcessedShareCard data={data} />,
    best_month: <BestMonthShareCard data={data} />,
    best_product: <BestProductShareCard data={data} />,
    top_location: <TopLocationShareCard data={data} />,
    referrals: <ReferralsShareCard data={data} />,
    payments_processed: <PaymentsProcessedShareCard data={data} />,
    summary: <SummaryShareCard data={data} />,
    metaphor: <MetaphorShareCard data={data} />,
  };

  return (
    <main className="w-full h-full flex flex-col items-center justify-center">
      <div className="w-[1080px] h-[1920px]">{cards[slug]}</div>
    </main>
  );
};

export default StoreWrappedCard;

export const getServerSideProps: GetServerSideProps<Props> = async (context) => {
  const slug = context.params.slug as string;
  let data: any = null;

  try {
    if (context.query.data) data = JSON.parse(fromBase64(context.query.data));
    return {
      props: {
        slug,
        data,
      },
    };
  } catch (e) {
    return {
      props: {
        slug,
      },
    };
  }
};
