import { useState } from "react";
import ProductLayout, { productPageIcons } from "../../components/ui/layouts/product";
import { InfoBlocksMain } from "../../components/products";
import { useModals } from "@/components/hooks/useModals";
import authContext from "@/contexts/auth-context";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";

const InfoBlocksPage = () => {
  //   const [showModal, setShowModal] = useState(false);
  const { modals, toggleModal } = useModals(["create", "edit", "delete", "assign_items", "assigned_items"]);
  const {  subscription } = authContext.useContainer();
  let canManage = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS,
  });

  const layoutData = {
    title: "Product Info Blocks",
    featureInfo: {
      title: "Info Blocks",
      description:
        "Add extra information to your products using text or images — perfect for care instructions, how-to-use guides, and more.",
      icon: <div className="w-6 text-accent-orange-500">{productPageIcons.category}</div>,
    },
    pageIndex: 6,
    action: {
      placeholder: "Add Info Block",
      onAction: () => toggleModal("create"),
      disabled: !canManage
    },
    onSearch: (q: string) => null,
    pageKey: "info-blocks",
  };

  return (
    <ProductLayout {...layoutData}>
      <InfoBlocksMain modals={modals} toggleModal={toggleModal} />
    </ProductLayout>
  );
};

export default InfoBlocksPage;
