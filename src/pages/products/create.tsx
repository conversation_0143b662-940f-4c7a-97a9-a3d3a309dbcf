import { ProductUploadExplainerModal } from "../../components/products";
import DashboardLayout from "../../components/ui/layouts/dashboard";
import authContext from "../../contexts/auth-context";
import { PERMISSIONS } from "../../assets/js/utils/subscription-plans";
import { ProductUploadController } from "../../components/products/create-products";
import Link from "next/link";
import { Image, InfoBlockInterface, VariantItem, Video } from "../../assets/interfaces";
import { ProductCreateMethod } from "@/components/hooks/useProductController";

const AddProducts = () => {
  const { store, categories, subscription } = authContext.useContainer();
  const maxUploadable = Math.min(
    subscription?.plan ? PERMISSIONS[subscription?.plan?.type].MAXIMUM_UPLOADABLE_ITEMS - (store?.item_count || 0) : 0,
    10
  );

  const LimitBanner = () => {
    if (maxUploadable >= 10) {
      return null;
    }

    return (
      <div className="py-3">
        You can only upload <b>{maxUploadable < 0 ? 0 : maxUploadable}</b> more items.
        <Link href="/my-store/change-plan">
          <a className="ml-0.5 text-sm font-bold text-black pt-0.5 no-outline leading-none no-outline">Upgrade Plan</a>
        </Link>
      </div>
    );
  };

  return (
    <DashboardLayout title="Add Products" bannerConfig={{ show: true, content: LimitBanner() }} padding={false}>
      <div className="h-full overflow-y-auto py-12.5 sm:py-15 pb-20 px-5 sm:px-6.25 lg:px-7.5 smooth-scroll page-content">
        <ProductUploadController
          {...{ maxUploadable, store, categories, success: { label: "View all products", route: "/products" } }}
        />
      </div>
      <ProductUploadExplainerModal items={store?.item_count} />
    </DashboardLayout>
  );
};

export interface Product {
  thumbnail: number;
  thumbnail_type?: string;
  images: Image[];
  videos?: Video[];
  name: string;
  price: string;
  discount_price?: string;
  category: string;
  price_unit?: string;
  is_always_available?: boolean;
  quantity?: number;
  minimum_order_quantity?: number;
  cost_price?: number;
  description: string;
  expiry_date?: string;
  variants?: {
    type: string;
    is_template?: boolean;
    options: VariantItem[];
  };
  hasImages?: boolean;
  id?: string;
  temp_id?: string;
  upload_source?: ProductCreateMethod;
  info_blocks?: InfoBlockInterface[];
}

export interface ProductForm {
  products: Product[];
}

export default AddProducts;
