import { useState } from "react";
import ProductLayout, { productPageIcons } from "../../components/ui/layouts/product";
import { HightLightsMain } from "../../components/products";
import { useModals } from "@/components/hooks/useModals";
import authContext from "@/contexts/auth-context";
import { actionIsAllowed, SCOPES } from "@/assets/js/utils/permissions";

const HighlightsPage = () => {
  //   const [showModal, setShowModal] = useState(false);
  const { modals, toggleModal } = useModals(["create", "edit", "delete", "details"]);
  const { subscription } = authContext.useContainer();
  let canManage = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_HIGHLIGHTS,
  });

  const layoutData = {
    title: "Product Highlights",
    featureInfo: {
      title: "Product Highlights",
      description: "Group product videos to showcase products on your store",
      icon: <div className="w-6 text-accent-red-500">{productPageIcons.productHighlights}</div>,
    },
    pageIndex: 5,
    action: {
      placeholder: "Add Highlight",
      onAction: () => toggleModal("create"),
      disabled: !canManage,
    },
    onSearch: (q: string) => null,
    pageKey: "product-highlights",
  };

  return (
    <ProductLayout {...layoutData}>
      <HightLightsMain modals={modals} toggleModal={toggleModal} />
    </ProductLayout>
  );
};

export default HighlightsPage;
