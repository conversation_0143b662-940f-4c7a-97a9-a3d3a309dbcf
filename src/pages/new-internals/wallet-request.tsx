import { useFormik } from "formik";
import React, { useState, useEffect } from "react";
import { InternalLoginParams } from "../../api/interfaces/internals";
import { InternalLogin } from "../../api/internals";
import { useRequest } from "../../api/utils";
import * as Yup from "yup";
import { AppBtn } from "../../components/ui/buttons";
import { InputField, PasswordField } from "../../components/ui/form-elements";
import { getFieldvalues, getLocalStorageWithExpiry, setLocalStorageWithExpiry } from "../../assets/js/utils/functions";
import ErrorLabel from "../../components/ui/error-label";
import { InternalsData } from "../../components/internals";
import InternalsWalletRequests from "@/components/internals/wallet-request";

// Token expiration time in milliseconds (8 hours)
const TOKEN_EXPIRY_TIME = 8 * 60 * 60 * 1000;

const NewAdminInternals = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if user is authenticated using getLocalStorageWithExpiry
    const token = getLocalStorageWithExpiry("admin_token");
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  if (!isAuthenticated) {
    return <Login setIsAuthenticated={setIsAuthenticated} />;
  }

  return <InternalsWalletRequests />;
};

const Login: React.FC<{ setIsAuthenticated: (state: boolean) => void }> = ({ setIsAuthenticated }) => {
  const { isLoading, makeRequest, error, response } = useRequest<InternalLoginParams>(InternalLogin);
  const form = useFormik({
    initialValues: {
      username: "",
      password: "",
    },
    onSubmit: async (values) => {
      const [res, err] = await makeRequest(values);

      if (res) {
        // Use setLocalStorageWithExpiry instead of sessionStorage
        setLocalStorageWithExpiry("admin_token", res.token, TOKEN_EXPIRY_TIME);
        setIsAuthenticated(true);
      }
    },
    validationSchema,
  });

  return (
    <div className="h-screen w-screen bg-primary-100 flex items-center justify-center">
      <form className="w-10/12 max-w-sm" onSubmit={form.handleSubmit}>
        <h4 className="text-lg font-semibold mb-5">Log in to dashboard</h4>
        <ErrorLabel error={error?.message} />
        <InputField label="Enter login" {...getFieldvalues("username", form)} />
        <PasswordField label="Enter password" type="password" {...getFieldvalues("password", form)} />
        <AppBtn isBlock className="mt-8" type="submit">
          Log In
        </AppBtn>
      </form>
    </div>
  );
};

const validationSchema = Yup.object().shape({
  username: Yup.string().required("Username is required"),
  password: Yup.string().required("Password is required"),
});

export default NewAdminInternals;
