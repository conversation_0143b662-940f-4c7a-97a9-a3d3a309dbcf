import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { GetUserById, MarkUserAsQualified } from "../../../api/internals";
import { useRequest } from "../../../api/utils";
import { User } from "../../../assets/interfaces/users";
import { StoreInterface } from "../../../assets/interfaces/stores";
import { PlanOption, SubscriptionInterface } from "../../../assets/interfaces/subscriptions";
import { AppBtn } from "../../../components/ui/buttons";
import { InternalLogin } from "../../../api/internals";
import { InternalLoginParams } from "../../../api/interfaces/internals";
import { useFormik } from "formik";
import * as Yup from "yup";
import { InputField, PasswordField } from "../../../components/ui/form-elements";
import {
  getFieldvalues,
  getLocalStorageWithExpiry,
  setLocalStorageWithExpiry,
} from "../../../assets/js/utils/functions";
import ErrorLabel from "../../../components/ui/error-label";
import dayjs from "dayjs";

// Token expiration time in milliseconds (8 hours)
const TOKEN_EXPIRY_TIME = 8 * 60 * 60 * 1000;

const UserDetailPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { isLoading, makeRequest, error, response } = useRequest(GetUserById);
  const { makeRequest: markQualifiedRequest } = useRequest(MarkUserAsQualified);
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    // Check if user is authenticated using getLocalStorageWithExpiry
    const token = getLocalStorageWithExpiry("admin_token");
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  useEffect(() => {
    if (isAuthenticated && id) {
      fetchUserData();
    }
  }, [isAuthenticated, id]);

  const fetchUserData = async () => {
    const [res, err] = await makeRequest({ id: id as string });
    if (res && res.data) {
      setUser(res.data);
    }
  };

  const handleMarkAsQualified = async () => {
    if (user) {
      const [res, err] = await markQualifiedRequest({ id: user.id });
      if (res) {
        alert("User marked as qualified successfully");
        // Refresh user data
        fetchUserData();
      } else if (err) {
        alert(`Error: ${err.message}`);
      }
    }
  };

  // Computed variables for store and subscription
  const store =
    user?.primary_store && typeof user.primary_store === "object" ? (user.primary_store as StoreInterface) : null;

  const subscription = store?.subscription as SubscriptionInterface | null;

  // Check if user was created within the last 4 days
  const isRecentlyCreated = user?.created_at ? dayjs().diff(dayjs(user.created_at), "day") <= 4 : false;

  // Check if user has utm_campaign in source_ad
  const hasUtmCampaign = !!user?.source_ad?.utm_campaign;

  // Determine if the "Mark as Qualified" button should be shown
  const shouldShowQualifiedButton = isRecentlyCreated && hasUtmCampaign;

  if (!isAuthenticated) {
    return <Login setIsAuthenticated={setIsAuthenticated} />;
  }

  if (isLoading) {
    return <div className="p-8">Loading...</div>;
  }

  if (error) {
    return <div className="p-8 text-red-500">Error: {error.message}</div>;
  }

  if (!user) {
    return <div className="p-8">User not found</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">User Details</h1>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-gray-600">Name</p>
            <p className="font-medium">{user.name}</p>
          </div>
          <div>
            <p className="text-gray-600">Email</p>
            <p className="font-medium">{user.email}</p>
          </div>
          <div>
            <p className="text-gray-600">Phone</p>
            <p className="font-medium">{user.phone}</p>
          </div>
          <div>
            <p className="text-gray-600">User ID</p>
            <p className="font-medium">{user.id}</p>
          </div>
          <div>
            <p className="text-gray-600">Signup Date</p>
            <p className="font-medium">{user.created_at ? dayjs(user.created_at).format("MMMM D, YYYY") : "N/A"}</p>
          </div>
          <div>
            <p className="text-gray-600">User Type</p>
            <p className="font-medium">{user.type}</p>
          </div>
        </div>
      </div>

      {store && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Store Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">Store Name</p>
              <p className="font-medium">{store.name}</p>
            </div>
            <div>
              <p className="text-gray-600">Store ID</p>
              <p className="font-medium">{store.id}</p>
            </div>
            <div>
              <p className="text-gray-600">Description</p>
              <p className="font-medium">{store.description}</p>
            </div>
            <div>
              <p className="text-gray-600">Number of Products</p>
              <p className="font-medium">{store?.item_count || store?.items_count || 0}</p>
            </div>
            <div>
              <p className="text-gray-600">Store Link</p>
              <p className="font-medium">
                <a
                  href={`https://catlog.shop/${store.slug}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  {store.slug}
                </a>
              </p>
            </div>
            <div>
              <p className="text-gray-600">Social Links</p>
              <div className="flex space-x-2">
                {store.socials?.instagram && (
                  <a
                    href={`https://instagram.com/${store.socials.instagram}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    Instagram
                  </a>
                )}
                {store.socials?.facebook && (
                  <a
                    href={`https://facebook.com/${store.socials.facebook}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    Facebook
                  </a>
                )}
                {store.socials?.twitter && (
                  <a
                    href={`https://twitter.com/${store.socials.twitter}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    Twitter
                  </a>
                )}
                {!store.socials?.instagram && !store.socials?.facebook && !store.socials?.twitter && (
                  <span>No social links</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {subscription && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Subscription Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">Plan Name</p>
              <p className="font-medium">{subscription.plan.name}</p>
            </div>
            <div>
              <p className="text-gray-600">Interval</p>
              <p className="font-medium">{(subscription?.plan_option as PlanOption)?.interval_text}</p>
            </div>
            <div>
              <p className="text-gray-600">Status</p>
              <p className="font-medium">{subscription.status}</p>
            </div>
            <div>
              <p className="text-gray-600">Next Payment Date</p>
              <p className="font-medium">{new Date(subscription.next_payment_date).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      )}

      {shouldShowQualifiedButton && (
        <div className="mt-6">
          <AppBtn onClick={handleMarkAsQualified}>Mark as Qualified</AppBtn>
        </div>
      )}
    </div>
  );
};

const Login: React.FC<{ setIsAuthenticated: (state: boolean) => void }> = ({ setIsAuthenticated }) => {
  const { isLoading, makeRequest, error, response } = useRequest<InternalLoginParams>(InternalLogin);
  const form = useFormik({
    initialValues: {
      username: "",
      password: "",
    },
    onSubmit: async (values) => {
      const [res, err] = await makeRequest(values);

      if (res) {
        // Use setLocalStorageWithExpiry instead of sessionStorage
        setLocalStorageWithExpiry("admin_token", res.token, TOKEN_EXPIRY_TIME);
        setIsAuthenticated(true);
      }
    },
    validationSchema,
  });

  return (
    <div className="h-screen w-screen bg-primary-100 flex items-center justify-center">
      <form className="w-10/12 max-w-sm" onSubmit={form.handleSubmit}>
        <h4 className="text-lg font-semibold mb-5">Log in to dashboard</h4>
        <ErrorLabel error={error?.message} />
        <InputField label="Enter login" {...getFieldvalues("username", form)} />
        <PasswordField label="Enter password" type="password" {...getFieldvalues("password", form)} />
        <AppBtn isBlock className="mt-8" type="submit">
          Log In
        </AppBtn>
      </form>
    </div>
  );
};

const validationSchema = Yup.object().shape({
  username: Yup.string().required("Username is required"),
  password: Yup.string().required("Password is required"),
});

export default UserDetailPage;
