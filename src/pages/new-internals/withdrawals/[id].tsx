import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import {
  GetWithdrawalRequest,
  MarkWithdrawalRequestAsSuccessful,
  MarkWithdrawalRequestAsFailed,
} from "../../../api/internals";
import { useFetcher, useRequest } from "../../../api/utils";
import { AppBtn } from "../../../components/ui/buttons";
import { InternalLogin } from "../../../api/internals";
import { InternalLoginParams } from "../../../api/interfaces/internals";
import { useFormik } from "formik";
import * as Yup from "yup";
import { InputField, PasswordField } from "../../../components/ui/form-elements";
import {
  getFieldvalues,
  getLocalStorageWithExpiry,
  setLocalStorageWithExpiry,
  toCurrency,
} from "../../../assets/js/utils/functions";
import ErrorLabel from "../../../components/ui/error-label";
import dayjs from "dayjs";
import { toast } from "@/components/ui/toast";
import { toNaira } from "@/assets/js/utils/utils";

// Token expiration time in milliseconds (8 hours)
const TOKEN_EXPIRY_TIME = 8 * 60 * 60 * 1000;

const WithdrawalDetailPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if user is authenticated using getLocalStorageWithExpiry
    const token = getLocalStorageWithExpiry("admin_token");
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  if (!isAuthenticated) {
    return <Login setIsAuthenticated={setIsAuthenticated} />;
  }

  return <WithdrawalDetailContent id={id as string} />;
};

const WithdrawalDetailContent = ({ id }: { id: string }) => {
  const { isLoading, makeRequest, error, response } = useFetcher(GetWithdrawalRequest, { id });
  const { makeRequest: markSuccessfulRequest } = useRequest(MarkWithdrawalRequestAsSuccessful);
  const { makeRequest: markFailedRequest } = useRequest(MarkWithdrawalRequestAsFailed);
  const withdrawal = response?.data ?? null;

  const handleMarkAsSuccessful = async () => {
    if (withdrawal) {
      if (!window.confirm("Are you sure you want to mark this withdrawal as successful?")) {
        return;
      }

      const markSuccessfulPromise = async () => {
        const [res, err] = await markSuccessfulRequest({ id: withdrawal.id });
        if (err) {
          return Promise.reject(err);
        }
        makeRequest();
        return Promise.resolve(res);
      };

      toast.promise(markSuccessfulPromise, {
        loading: {
          title: "Processing",
          message: "Marking withdrawal as successful...",
        },
        success: {
          title: "Success",
          message: "Withdrawal marked as successful",
        },
        error: {
          title: "Error",
          message: "Failed to mark withdrawal as successful",
        },
      });
    }
  };

  const handleMarkAsFailed = async () => {
    if (withdrawal) {
      if (!window.confirm("Are you sure you want to mark this withdrawal as failed?")) {
        return;
      }

      const markFailedPromise = async () => {
        const [res, err] = await markFailedRequest({ id: withdrawal.id });
        if (err) {
          return Promise.reject(err);
        }
        makeRequest();
        return Promise.resolve(res);
      };

      toast.promise(markFailedPromise, {
        loading: {
          title: "Processing",
          message: "Marking withdrawal as failed...",
        },
        success: {
          title: "Success",
          message: "Withdrawal marked as failed",
        },
        error: {
          title: "Error",
          message: "Failed to mark withdrawal as failed",
        },
      });
    }
  };

  if (isLoading) {
    return <div className="p-8">Loading...</div>;
  }

  if (error) {
    return <div className="p-8 text-red-500">Error: {error.message}</div>;
  }

  if (!withdrawal) {
    return <div className="p-8">Withdrawal request not found</div>;
  }

  return (
    <div className="p-8 max-w-screen-lg mx-auto">
      <h1 className="text-2xl font-bold mb-6">Withdrawal Request Details</h1>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-gray-600">Request ID</p>
            <p className="font-medium">{withdrawal.id}</p>
          </div>
          <div>
            <p className="text-gray-600">Status</p>
            <p className="font-medium">{withdrawal.status}</p>
          </div>
          <div>
            <p className="text-gray-600">Amount</p>
            <p className="font-medium">{toCurrency(toNaira(withdrawal.amount), withdrawal.currency)}</p>
          </div>
          <div>
            <p className="text-gray-600">Currency</p>
            <p className="font-medium">{withdrawal.currency}</p>
          </div>
          <div>
            <p className="text-gray-600">Created At</p>
            <p className="font-medium">
              {withdrawal.created_at ? dayjs(withdrawal.created_at).format("MMMM D, YYYY HH:mm") : "N/A"}
            </p>
          </div>
          <div>
            <p className="text-gray-600">Updated At</p>
            <p className="font-medium">
              {withdrawal.updated_at ? dayjs(withdrawal.updated_at).format("MMMM D, YYYY HH:mm") : "N/A"}
            </p>
          </div>
        </div>
      </div>

      {withdrawal.withdrawal_account && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Withdrawal Account Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">Account Name</p>
              <p className="font-medium">{withdrawal.withdrawal_account.account_name}</p>
            </div>
            <div>
              <p className="text-gray-600">Account Number</p>
              <p className="font-medium">{withdrawal.withdrawal_account.account_number}</p>
            </div>
            <div>
              <p className="text-gray-600">Bank Name</p>
              <p className="font-medium">{withdrawal.withdrawal_account.bank_name}</p>
            </div>
            <div>
              <p className="text-gray-600">Bank Code</p>
              <p className="font-medium">{withdrawal.withdrawal_account.bank_code}</p>
            </div>
          </div>
        </div>
      )}

      {withdrawal.user && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">User Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">User ID</p>
              <p className="font-medium">{withdrawal.user.id}</p>
            </div>
            <div>
              <p className="text-gray-600">Name</p>
              <p className="font-medium">{withdrawal.user.name}</p>
            </div>
            <div>
              <p className="text-gray-600">Email</p>
              <p className="font-medium">{withdrawal.user.email}</p>
            </div>
            <div>
              <p className="text-gray-600">Phone</p>
              <p className="font-medium">{withdrawal.user.phone}</p>
            </div>
          </div>
        </div>
      )}

      {withdrawal.wallet?.store && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Store Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">Store ID</p>
              <p className="font-medium">{withdrawal.wallet.store.id}</p>
            </div>
            <div>
              <p className="text-gray-600">Store Name</p>
              <p className="font-medium">{withdrawal.wallet.store.name}</p>
            </div>
            <div>
              <p className="text-gray-600">Store Slug</p>
              <p className="font-medium">
                <a
                  href={`https://catlog.shop/${withdrawal.wallet.store.slug}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  {withdrawal.wallet.store.slug}
                </a>
              </p>
            </div>
            <div>
              <p className="text-gray-600">Description</p>
              <p className="font-medium">{withdrawal.wallet.store.description || "N/A"}</p>
            </div>
            <div>
              <p className="text-gray-600">Wallet ID</p>
              <p className="font-medium">{withdrawal.wallet.id}</p>
            </div>
            <div>
              <p className="text-gray-600">Wallet Currency</p>
              <p className="font-medium">{withdrawal.wallet.currency}</p>
            </div>
          </div>
        </div>
      )}

      {withdrawal.status === "PENDING" && (
        <div className="mt-6 flex space-x-4">
          <AppBtn onClick={handleMarkAsSuccessful} color="success">
            Mark as Successful
          </AppBtn>
          <AppBtn onClick={handleMarkAsFailed} color="danger">
            Mark as Failed
          </AppBtn>
        </div>
      )}
    </div>
  );
};

const Login: React.FC<{ setIsAuthenticated: (state: boolean) => void }> = ({ setIsAuthenticated }) => {
  const { isLoading, makeRequest, error, response } = useRequest<InternalLoginParams>(InternalLogin);
  const form = useFormik({
    initialValues: {
      username: "",
      password: "",
    },
    onSubmit: async (values) => {
      const [res, err] = await makeRequest(values);

      if (res) {
        // Use setLocalStorageWithExpiry instead of sessionStorage
        setLocalStorageWithExpiry("admin_token", res.token, TOKEN_EXPIRY_TIME);
        setIsAuthenticated(true);
      }
    },
    validationSchema,
  });

  return (
    <div className="h-screen w-screen bg-primary-100 flex items-center justify-center">
      <form className="w-10/12 max-w-sm" onSubmit={form.handleSubmit}>
        <h4 className="text-lg font-semibold mb-5">Log in to dashboard</h4>
        <ErrorLabel error={error?.message} />
        <InputField label="Enter login" {...getFieldvalues("username", form)} />
        <PasswordField label="Enter password" type="password" {...getFieldvalues("password", form)} />
        <AppBtn isBlock className="mt-8" type="submit">
          Log In
        </AppBtn>
      </form>
    </div>
  );
};

const validationSchema = Yup.object().shape({
  username: Yup.string().required("Username is required"),
  password: Yup.string().required("Password is required"),
});

export default WithdrawalDetailPage;
