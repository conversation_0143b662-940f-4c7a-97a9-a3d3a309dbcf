import { AppBtn } from "@/components/ui/buttons";
import { ChangeEvent, ChangeEventHandler, useState, useEffect } from "react";
import * as csv from "csv";
import { useRequest } from "@/api/utils";
import { ValidateWithdrawalsProps } from "@/api/interfaces/internals";
import { ValidateWithdrawals } from "@/api/internals";
import { Login } from ".";
import { CURRENCIES } from "@/assets/interfaces";
import Table, { TableHead, TableHeadItem, TableBody, TableRow, TableCell } from "@/components/ui/table";
import { toCurrency, getLocalStorageWithExpiry } from "@/assets/js/utils/functions";
import { toast } from "@/components/ui/toast";

const ValidateWithdrawalRequests = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if user is authenticated using getLocalStorageWithExpiry
    const token = getLocalStorageWithExpiry("admin_token");
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  if (!isAuthenticated) {
    return <Login setIsAuthenticated={setIsAuthenticated} />;
  }
  return <ValidateWithdrawalRequestsMain />;
};
export default ValidateWithdrawalRequests;

const ValidateWithdrawalRequestsMain = () => {
  const [withdrawals, setWithdrawals] = useState<any[]>([]);
  const [invalidWithdrawals, setInvalid] = useState<any[]>([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [props, setProps] = useState<ValidateWithdrawalsProps>(null);
  const { makeRequest, clearResponse, response, isLoading } = useRequest<ValidateWithdrawalsProps>(ValidateWithdrawals);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files[0];
    if (file && file.name.endsWith(".csv")) {
      setSelectedFile(file);
    } else {
      setSelectedFile(null);
      alert("Please select a CSV file.");
    }

    const reader = new FileReader();
    reader.onload = () => {
      csv.parse(reader.result as string, (err, data) => {
        const keys = data[0] as string[];
        var dataSlice = data.slice(1) as any[];
        var obj = {};

        const jsonData = dataSlice.map((data: any[]) => {
          const json: { [key: string]: any } = {};
          data.forEach((d, i) => (json[keys[i].toLocaleLowerCase().replaceAll(" ", "_")] = d));

          return json;
        });

        const references = jsonData.filter((d) => d.status === "success").map((d) => d.reference);
        const lastData = jsonData[jsonData.length - 1];
        setProps({
          references,
          start_date: new Date(lastData.transfer_date).toISOString(),
          currency: lastData.currency,
        });

        setWithdrawals(jsonData);
      });
    };

    reader.readAsBinaryString(file);
  };

  const validate = async () => {
    const [res, error] = await makeRequest(props);
    if (error) {
      toast.error({ message: "An error occurred", title: "Error occurred" });
      return;
    }

    const invalidWithdrawalRefs = res?.data;
    const invalids = withdrawals.filter(
      (w) => invalidWithdrawalRefs.includes(w.reference) && w.reason.includes("Withdrawal to")
    );

    setInvalid(invalids);
  };

  return (
    <div className="p-10">
      {!response && (
        <>
          <input
            className="p-5 block bg-primary-100 text-black-secondary rounded-md mx-auto mt-10"
            type="file"
            accept=".csv"
            onChange={handleFileChange}
          />
          {selectedFile && (
            <p className="mx-auto text-black-placeholder text-sm text-center mt-10">
              Selected file: {selectedFile.name}
            </p>
          )}

          <AppBtn onClick={validate} disabled={props === null || isLoading} className="mx-auto mt-10">
            {isLoading ? "Uploading..." : "Upload and Validate"}
          </AppBtn>
        </>
      )}

      {response && (
        <>
          <div className="flex items-center mb-5 gap-5">
            <h1 className="text-xl ">Flagged Withdrawals</h1>
            <AppBtn size="sm" color="neutral" onClick={clearResponse}>
              Upload new File
            </AppBtn>
          </div>
          <Table className="">
            <TableHead>
              <TableHeadItem>Reference</TableHeadItem>
              <TableHeadItem>Amount</TableHeadItem>
              <TableHeadItem>Date</TableHeadItem>
              <TableHeadItem>Account Name</TableHeadItem>
              <TableHeadItem>Account Number</TableHeadItem>
              <TableHeadItem>Bank</TableHeadItem>
              <TableHeadItem>Currency</TableHeadItem>
            </TableHead>
            <TableBody>
              {invalidWithdrawals.map((d, index) => (
                <TableRow key={index}>
                  <TableCell>{d.reference}</TableCell>
                  <TableCell>{toCurrency(d.amount)}</TableCell>
                  <TableCell>{d["transfer_date"]}</TableCell>
                  <TableCell>{d["recipient_a/c_name"]}</TableCell>
                  <TableCell>{d["recipient_a/c_no"]}</TableCell>
                  <TableCell>{d["recipient_bank"]}</TableCell>
                  <TableCell>{d.currency}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {response.data.length === 0 && <p className="text-center w-full mt-20 text-black-placeholder">No results</p>}
        </>
      )}
    </div>
  );
};
