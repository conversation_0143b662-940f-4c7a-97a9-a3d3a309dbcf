import { StoreInterface } from "@/assets/interfaces";
import { SETUP_TASK_KEYS, TASK_KEYS } from "@/assets/js/utils/onboarding-tasks";
import { AppBtn } from "@/components/ui/buttons";
import DashboardLayout from "@/components/ui/layouts/dashboard";
import authContext from "@/contexts/auth-context";
import { useMemo } from "react";

export interface ChowbotSetupPageProps {
  onComplete?: (store?: StoreInterface) => void;
  isChowbotSetup?: boolean;
}
export type ChowbotSetupPageComponent = React.FC<ChowbotSetupPageProps>;

const SetupProgress = () => {
  const { store } = authContext.useContainer();

  const { currentRoute, tasks, taskData, percentageCompleted = 0 } = useMemo(() => getSetupProgress(store), [store]);

  return (
    <DashboardLayout
      title="Finish Setup"
      sidebarLoader={{ store: false, navLinks: true, isOnboarding: true }}
      bannerConfig={{ show: false, content: false }}
    >
      <div className="flex flex-col items-center w-full h-full overflow-y-auto py-15 text-center px-5 sm:px-6.25 lg:px-7.5">
        <div className="flex flex-col items-center w-full">
          <div className="flex flex-col items-center text-center">
            <figure className="w-17.5 h-17.5 sm:h-20 sm:w-20 rounded-full bg-accent-green-500 flex items-center justify-center text-white">
              {/* prettier-ignore */}
              <svg className="w-8 sm:w-10" viewBox="0 0 24 24" fill="none">
                <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM17 17.25H7C6.59 17.25 6.25 16.91 6.25 16.5C6.25 16.09 6.59 15.75 7 15.75H17C17.41 15.75 17.75 16.09 17.75 16.5C17.75 16.91 17.41 17.25 17 17.25ZM17 12.75H7C6.59 12.75 6.25 12.41 6.25 12C6.25 11.59 6.59 11.25 7 11.25H17C17.41 11.25 17.75 11.59 17.75 12C17.75 12.41 17.41 12.75 17 12.75ZM17 8.25H7C6.59 8.25 6.25 7.91 6.25 7.5C6.25 7.09 6.59 6.75 7 6.75H17C17.41 6.75 17.75 7.09 17.75 7.5C17.75 7.91 17.41 8.25 17 8.25Z" fill="currentColor"/>
              </svg>
            </figure>
            <h2 className="text-2lg sm:text-2xl md:text-3xl text-black-500 font-bold mt-3.75">Setup in progress</h2>
            <p className="text-sm text-dark mt-1.25 max-w-[250px] mx-auto">Pick up where you left</p>
          </div>

          <div className="mt-7.5 w-full mx-auto max-w-[450px]">
            <div className="w-full border border-grey-border border-opacity-50 rounded-15 overflow-hidden">
              <div className="w-full">
                <div className="flex items-center justify-between px-3.75 sm:px-5 py-3 sm:py-3.75">
                  <h4 className="text-base sm:text-lg text-black font-bold">Setup Progress</h4>
                  <span className="text-placeholder text-1xs text-sm font-medium">
                    {Math.round(percentageCompleted)}% Complete
                  </span>
                </div>
                <div className="w-full h-1.5 bg-grey-fields-100">
                  <div
                    className="h-full rounded-r-15 bg-accent-green-500"
                    style={{ width: `${percentageCompleted}%` }}
                  ></div>
                </div>
              </div>
              <>
                <ul className="p-3.5 sm:px-5 sm:py-4 divide-y divide-grey-border divide-opacity-50">
                  {tasks?.map((s, index) => (
                    <li
                      className="flex flex-center py-3.5 sm:py-4 first:pt-1.5 last:pb-1.5 justify-between"
                      key={index}
                    >
                      <span className="text-sm sm:text-1sm font-medium text-black-secondary">{taskData[s].title}</span>
                      <figure className="h-4.5 w-4.5 sm:w-5 sm:h-5 rounded-full bg-grey-fields-100">
                        {taskData[s].done && ( // prettier-ignore
                          <svg width="100%" viewBox="0 0 18 18" fill="none">
                            <rect width="18" height="18" rx="9" fill="#39B588" />
                            <path
                              d="M7.72136 12.375C7.54127 12.375 7.37019 12.2997 7.24412 12.1678L4.69585 9.50266C4.43472 9.22955 4.43472 8.77751 4.69585 8.5044C4.95698 8.2313 5.38919 8.2313 5.65033 8.5044L7.72136 10.6704L12.3497 5.82983C12.6108 5.55672 13.043 5.55672 13.3042 5.82983C13.5653 6.10294 13.5653 6.55498 13.3042 6.82809L8.1986 12.1678C8.07254 12.2997 7.90145 12.375 7.72136 12.375Z"
                              fill="white"
                            />
                          </svg>
                        )}

                        {!taskData[s].done && ( // prettier-ignore
                          <svg width="100%" className="text-grey-border-dark" viewBox="0 0 24 24" fill="none">
                            <path
                              d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM11.25 8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V13C12.75 13.41 12.41 13.75 12 13.75C11.59 13.75 11.25 13.41 11.25 13V8ZM12.92 16.38C12.87 16.51 12.8 16.61 12.71 16.71C12.61 16.8 12.5 16.87 12.38 16.92C12.26 16.97 12.13 17 12 17C11.87 17 11.74 16.97 11.62 16.92C11.5 16.87 11.39 16.8 11.29 16.71C11.2 16.61 11.13 16.51 11.08 16.38C11.03 16.26 11 16.13 11 16C11 15.87 11.03 15.74 11.08 15.62C11.13 15.5 11.2 15.39 11.29 15.29C11.39 15.2 11.5 15.13 11.62 15.08C11.86 14.98 12.14 14.98 12.38 15.08C12.5 15.13 12.61 15.2 12.71 15.29C12.8 15.39 12.87 15.5 12.92 15.62C12.97 15.74 13 15.87 13 16C13 16.13 12.97 16.26 12.92 16.38Z"
                              fill="currentColor"
                            />
                          </svg>
                        )}
                      </figure>
                    </li>
                  ))}
                </ul>
                <div className="px-3.75 sm:px-5 py-3 sm:py-3.75 border-t border-grey-border border-opacity-50">
                  <AppBtn size="lg" href={percentageCompleted === 100 ? "/dashboard" : currentRoute}>
                    {percentageCompleted === 100 ? "Proceed to dashboard" : "Finish setting up"}
                    {/* prettier-ignore */}
                    <svg className="w-5 ml-1.25 transform transition-transform ease-out duration-150 group-hover:translate-x-0.5" viewBox="0 0 25 25" fill="none">
                      <path d="M5.59375 12.4209H19.5938" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M12.5938 5.4209L19.5938 12.4209L12.5938 19.4209" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </AppBtn>
                </div>
              </>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export const getSetupProgress = (store: StoreInterface) => {
  if (store) {
    const { item_count, subscription } = store;
    const userHasAdditionalStoreDetails = store?.business_category?.name && store?.business_category?.type;

    const tasks = [
      SETUP_TASK_KEYS.CREATE_STORE,
      SETUP_TASK_KEYS.PRODUCTS_UPLOAD,
      SETUP_TASK_KEYS.EXTRA_INFO,
      SETUP_TASK_KEYS.PICK_PLAN,
    ];

    const taskData: { [key in SETUP_TASK_KEYS]?: { done: boolean; route: string; title: string } } = {
      [SETUP_TASK_KEYS.CREATE_STORE]: {
        done: !!store,
        route: "/setup/create-store",
        title: "Create your Business",
      },
      [SETUP_TASK_KEYS.PRODUCTS_UPLOAD]: {
        done: item_count > 0,
        route: "/setup/add-products",
        title: "Upload Products",
      },
      [SETUP_TASK_KEYS.EXTRA_INFO]: {
        done: !!userHasAdditionalStoreDetails,
        route: "/setup/additional-store-details",
        title: "Store Extra Info",
      },
      [SETUP_TASK_KEYS.PICK_PLAN]: {
        done: !!subscription,
        route: "/setup/pick-plan",
        title: "Select Preference",
      },
    };

    const values = Object.values(taskData);
    const currentRoute = taskData[tasks.find((v) => !taskData[v].done)]?.route;
    const pendingTasks = values.filter((v) => v.done).length;
    const percentageCompleted = (pendingTasks / values.length) * 100;

    return { percentageCompleted, pendingTasks, currentRoute, taskData, tasks };
  }
  return {};
};
export default SetupProgress;
