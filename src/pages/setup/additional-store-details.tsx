import StoreAdditionalDetailsForm from "@/components/create-store/additional-store-details";
import DashboardLayout from "../../components/ui/layouts/dashboard";
import authContext from "../../contexts/auth-context";

const AdditionalStoreDetails = () => {
  return (
    <DashboardLayout
      title="Additional Store Details"
      sidebarLoader={{ store: false, navLinks: true, isOnboarding: true }}
      bannerConfig={{ show: false, content: false }}
      padding={false}
      showSupportBtn
    >
      <div className="pt-15 h-full overflow-y-auto pb-25">
        <AdditionalStoreDetailsMain />
      </div>
    </DashboardLayout>
  );
};

const AdditionalStoreDetailsMain = () => {
  const { user, store } = authContext.useContainer();

  return (
    <div className="px-6.25">
      <figure className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-accent-orange-500 m-auto flex items-center justify-center text-white">
        {/* prettier-ignore */}
        <svg className="w-[30px] sm:w-8" viewBox="0 0 29 30" fill="none">
          <path d="M9.36 27.6875C6.18 27.6875 3.588 25 3.588 21.675V18.125C3.588 17.2875 4.38 16.725 5.136 17C5.448 17.1125 5.76 17.1875 6.084 17.2375C6.228 17.2625 6.372 17.2875 6.516 17.2875C6.696 17.3125 6.888 17.325 7.068 17.325C8.4 17.325 9.72 16.8125 10.764 15.925C11.76 16.8125 13.044 17.325 14.412 17.325C15.78 17.325 17.052 16.8375 18.048 15.9375C19.08 16.8125 20.376 17.325 21.696 17.325C21.9 17.325 22.116 17.3125 22.308 17.2875C22.452 17.275 22.572 17.2625 22.704 17.2375C23.064 17.1875 23.388 17.0875 23.712 16.975C24.456 16.7125 25.236 17.2875 25.236 18.1V21.675C25.236 24.9875 22.656 27.6875 19.464 27.6875H9.36Z" fill="white"/>
          <path d="M26.376 10.7375L26.04 7.4125C25.56 3.7875 23.976 2.3125 20.592 2.3125H17.88H16.176H12.672H10.956H8.184C4.8 2.3125 3.228 3.7875 2.736 7.45L2.424 10.75C2.304 12.0375 2.64 13.2875 3.372 14.2625C4.248 15.45 5.592 16.125 7.092 16.125C8.544 16.125 9.936 15.3625 10.812 14.15C11.592 15.3625 12.936 16.125 14.424 16.125C15.912 16.125 17.22 15.4 18.012 14.2C18.9 15.3875 20.268 16.125 21.708 16.125C23.244 16.125 24.624 15.4125 25.488 14.1625C26.184 13.2 26.496 11.9875 26.376 10.7375ZM16.2 9.6625H15.3V10.6375C15.3 11.1625 14.892 11.575 14.4 11.575C13.908 11.575 13.5 11.1625 13.5 10.6375V9.6625H12.6C12.108 9.6625 11.7 9.25 11.7 8.725C11.7 8.2125 12.108 7.7875 12.6 7.7875H13.5V6.9C13.5 6.3875 13.908 5.9625 14.4 5.9625C14.892 5.9625 15.3 6.3875 15.3 6.9V7.7875H16.2C16.692 7.7875 17.1 8.2125 17.1 8.725C17.1 9.25 16.692 9.6625 16.2 9.6625Z" fill="white"/>
        </svg>
      </figure>
      <h2 className="text-center font-light text-black text-xl md:text-3xl mx-auto mt-4">
        You're almost done,
        <br />
        <b className="font-bold">Just a few more details</b>
      </h2>
      <StoreAdditionalDetailsForm />
    </div>
  );
};

export default AdditionalStoreDetails;
