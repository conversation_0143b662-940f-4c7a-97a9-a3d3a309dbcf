import { StoreInterface } from "@/assets/interfaces";
import { AppBtn } from "@/components/ui/buttons";
import { AppBtnColor, AppBtnSize } from "@/components/ui/buttons/app-btn";
import DashboardLayout from "@/components/ui/layouts/dashboard";
import authContext from "@/contexts/auth-context";
import classNames from "classnames";
import { useRouter } from "next/router";
import { useEffect, useMemo, useRef, useState } from "react";
import { ReactTyped } from "react-typed";

export interface ChowbotSetupPageProps {
  onComplete?: (store?: StoreInterface) => void;
  isChowbotSetup?: boolean;
}
export type ChowbotSetupPageComponent = React.FC<ChowbotSetupPageProps>;
const TYPING_SPEED = 15;

const SetupWalkthrough = () => {
  const { user } = authContext.useContainer();
  const [stepIndex, setStepIndex] = useState(0);
  const [visibleSteps, setVisibleSteps] = useState<number[]>([0]); // Steps that are fully revealed
  const [showButton, setShowButton] = useState<number[]>([]); // Steps where button should be visible
  const containerRef = useRef<HTMLDivElement>(null);
  const activeBlockRef = useRef<HTMLDivElement>(null);

  const router = useRouter();

  const contentBlocks = useMemo(() => getWalkthroughSections(user?.name.split(" ")[0] || ""), [user?.name]);

  const [translateY, setTranslateY] = useState(0);

  useEffect(() => {
    if (activeBlockRef.current && containerRef.current) {
      const block = activeBlockRef.current;
      const container = containerRef.current;

      const blockBottom = block.getBoundingClientRect().bottom;
      const blockTop = block.getBoundingClientRect().top;
      const viewportHeight = window.innerHeight;
      const threshold = viewportHeight * 0.5; // 60% of viewport height

      let newTranslateY = translateY; // Copy current translateY state

      if (blockBottom > threshold) {
        // Move container up when going forward
        const divisor = 1.5 * stepIndex;
        newTranslateY -= container.getBoundingClientRect().height / (divisor < 2.5 ? 2.5 : divisor);
      } else if (blockTop < viewportHeight * 0.2 && newTranslateY < 0) {
        // Move container down when going backward
        newTranslateY += container.getBoundingClientRect().height / 2;
      }

      setTranslateY(newTranslateY);
      container.style.transform = `translateY(${newTranslateY}px)`;
      container.style.transition = "transform 0.3s ease-in-out";
    }
  }, [stepIndex, visibleSteps]);

  const handleTypingComplete = (index: number) => {
    setTimeout(() => {
      setShowButton((prev) => [...prev, index]);
    }, 300); // Slight delay after typing for smoother transition
  };

  const handleNext = () => {
    if (stepIndex < contentBlocks.length - 1) {
      const sIdx = stepIndex + 1;
      setStepIndex(sIdx);
      setVisibleSteps((prev) => [...prev, sIdx]);
    } else {
      router.push("/setup/add-products");
    }
  };

  const handlePrev = () => {
    if (stepIndex > 0) {
      setVisibleSteps((prev) => prev.filter((step) => step !== stepIndex));
      setShowButton((prev) => prev.filter((step) => step !== stepIndex));
      setStepIndex(stepIndex - 1);
    }
  };

  return (
    <DashboardLayout
      title="Let's Get Started"
      sidebarLoader={{ store: false, navLinks: true, isOnboarding: true }}
      bannerConfig={{ show: false, content: false }}
      padding={false}
    >
      <div className="flex flex-col items-center w-full h-full overflow-y-auto py-15 text-center px-5 sm:px-6.25 lg:px-7.5">
        <div
          className="flex flex-col items-center w-full transform transition-transform ease-out duration-300"
          ref={containerRef}
        >
          <div className="mt-7.5 w-full mx-auto max-w-[450px] text-left">
            {contentBlocks.slice(0, stepIndex + 1).map((step, index) => (
              <div
                key={index}
                ref={index === stepIndex ? activeBlockRef : null}
                className={index === 0 ? "" : "mt-4 pt-4 border-t border-grey-divider"}
              >
                {/* Title */}
                <h1
                  className={classNames(
                    "text-2lg md:text-2xl font-bold text-black transform transition-opacity ease-out duration-300",
                    {
                      "opacity-30": index !== stepIndex,
                    }
                  )}
                >
                  {step.title}
                </h1>

                {/* Body with typewriter effect */}
                {visibleSteps.includes(index) && (
                  <p
                    className={classNames(
                      "text-dark text-1sm mt-2 transform transition-opacity ease-out duration-300",
                      { "opacity-50": index !== stepIndex }
                    )}
                  >
                    <ReactTyped
                      strings={[step.body]}
                      typeSpeed={TYPING_SPEED}
                      onComplete={() => handleTypingComplete(index)}
                      showCursor={false}
                    />
                  </p>
                )}

                {/* CTA Button */}
                {showButton.includes(index) && (
                  <div className="flex items-center mt-5 space-x-3.75 w-full">
                    {stepIndex === index && stepIndex > 0 && (
                      <AppBtn
                        className="inline-flex"
                        disabled={stepIndex !== index}
                        onClick={handlePrev}
                        {...step.buttonConfigs}
                        color="neutral"
                      >
                        Back
                      </AppBtn>
                    )}
                    <AppBtn
                      className="inline-flex"
                      disabled={stepIndex !== index}
                      onClick={handleNext}
                      {...step.buttonConfigs}
                    >
                      {step.buttonCTA}
                    </AppBtn>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

const getWalkthroughSections = (
  firstName: string
): {
  title: string;
  body: string;
  buttonCTA: string;
  buttonConfigs: { size: AppBtnSize; isBlock: boolean; color?: AppBtnColor; className?: string };
}[] => [
  {
    title: `Hi ${firstName}, Welcome 👋🏾`,
    body: `Running a business is hard, but you don't have to do it alone. Catlog gives you the tools to run your business easily.`,
    buttonCTA: "Show Me",
    buttonConfigs: {
      size: "sm",
      isBlock: false,
    },
  },
  {
    title: "Your Online Store 🏪",
    body: `You&apos;ll get an online store you can customize to match your brand. Add your store link to your social media bio so customers can shop 24/7 without always messaging you first.`,
    buttonCTA: "Next",
    buttonConfigs: {
      size: "sm",
      isBlock: false,
    },
  },
  {
    title: "Get Paid, Your Way 💸",
    body: `You can accept payments via bank transfer, cards, and even international currencies like USD and GBP. You can also create payment links and professional invoices. Every payment goes into your wallet, and you can withdraw anytime.`,
    buttonCTA: "Next",
    buttonConfigs: {
      size: "sm",
      isBlock: false,
    },
  },
  {
    title: "Stay Organized 📝",
    body: `Catlog automatically tracks all your orders, customers, and payments. You can also add records manually. Plus, if a customer adds items to their cart but doesn't buy, you&apos;ll know—so you can remind them to complete their order.`,
    buttonCTA: "Next",
    buttonConfigs: {
      size: "sm",
      isBlock: false,
    },
  },
  {
    title: "Your Free Trial & Subscription 💳",
    body: `You can use Catlog freely for 14 days, but you'll need to pick a plan today - <b className="font-medium text-black-secondary">no card needed</b>. The Business+ plan unlocks powerful features like international payments, Facebook Pixel, multiple stores, and adding staff.`,
    buttonCTA: "Finish Setting Up",
    buttonConfigs: {
      size: "md",
      isBlock: true,
      className: "inline-flex flex-1",
    },
  },
];

export default SetupWalkthrough;
