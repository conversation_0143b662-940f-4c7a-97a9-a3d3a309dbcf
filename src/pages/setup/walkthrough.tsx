import { UpdateSetupVideoWatched } from "@/api";
import { useRequest } from "@/api/utils";
import {
  COUNTRY_CURRENCY_MAP,
  CURRENCY_COUNTRY_MAP,
  ONBOARDING_CREDITS,
  ONBOARDING_STEPS_WITH_REWARDS,
} from "@/assets/js/utils/constants";
import useSteps from "@/components/hooks/useSteps";
import WalkthroughVideoWithReward from "@/components/setup/walthrough-video-with-reward";
import { AppBtn } from "@/components/ui/buttons";
import DashboardLayout from "@/components/ui/layouts/dashboard";
import SuccessAnimation from "@/components/ui/success-animation";
import authContext from "@/contexts/auth-context";
import React, { useState } from "react";
import ReactConfetti from "react-confetti";

const SetupWalkthrough = () => {
  const { user, store } = authContext.useContainer();
  const { steps, step, changeStep, stepIndex } = useSteps(["walkthrough", "success"], 0);
  const updateSetupVideoWatchedReq = useRequest(UpdateSetupVideoWatched);

  const storeCurrency = store?.currencies?.default;
  const completionBonus = `${storeCurrency} ${
    ONBOARDING_CREDITS[ONBOARDING_STEPS_WITH_REWARDS.WATCH_SETUP_VIDEO][storeCurrency] / 100
  }`;

  const handleRewardEarned = async () => {
    const [res, err] = await updateSetupVideoWatchedReq.makeRequest({ userId: user.id });
    changeStep("success");
  };

  return (
    <DashboardLayout
      title="Start Setup"
      sidebarLoader={{ store: false, navLinks: true, isOnboarding: true }}
      bannerConfig={{ show: false, content: false }}
      padding={false}
      HeaderAddon={
        <AppBtn color="neutral" size="sm" href="/setup/add-products">
          Skip
        </AppBtn>
      }
    >
      <div className="flex flex-col items-center w-full h-full overflow-y-auto py-15 text-center px-5 sm:px-6.25 lg:px-7.5">
        <div className="container mx-auto p-4">
          {step === "walkthrough" && (
            <div className="w-full max-w-xl mx-auto">
              <div className="max-w-[450px] mx-auto">
                <h1 className="text-2xl sm:text-3xl font-bold text-black">Watch to see how Catlog works</h1>
                <p className="text-dark text-sm mt-2">
                  If you watch this video fully today, you'll earn{" "}
                  <b className="font-medium text-black-secondary">{completionBonus}</b> as a discount towards your first
                  subscription!
                </p>
              </div>

              <WalkthroughVideoWithReward
                videoSrc="https://res.cloudinary.com/catlog/video/upload/v1742617861/Onboarding_1.mp4"
                onRewardEarned={handleRewardEarned}
              />
            </div>
          )}

          {step === "success" && (
            <div className="w-full max-w-md mx-auto">
              <div className="max-w-[350px] mx-auto flex flex-col items-center">
                <SuccessAnimation />
                <h1 className="text-2xl sm:text-3xl font-bold text-black mt-3.75">
                  Well done, you watched the full video 🎉
                </h1>
                <p className="text-dark text-sm mt-2">
                  We've added <b className="font-medium text-black-secondary">{completionBonus}</b> to your catlog
                  credits, now let's continue your setup.
                </p>
              </div>
              <div className="flex items-center space-x-2.5 mt-3.75">
                <AppBtn className="flex-1" color="neutral" size="md" onClick={() => changeStep("walkthrough")}>
                  Watch Again
                </AppBtn>
                <AppBtn className="flex-1" size="lg" href="/setup/add-products">
                  Continue Setup
                </AppBtn>
              </div>
            </div>
          )}
        </div>
        {step === "success" && (
          <ReactConfetti
            width={window.innerWidth}
            height={window.innerHeight}
            numberOfPieces={500}
            recycle={false}
            style={{ zIndex: 1000 }}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default SetupWalkthrough;
