FROM node:14.18.0-alpine AS BUILD_IMAGE

WORKDIR /usr/src/app

COPY package.json yarn.lock ./

COPY . .

ENV PATH=/usr/src/app/node_modules/.bin:$PATH
ENV NEXT_PUBLIC_APP_URL=https://app.stx.catlog.shop
ENV NEXT_PUBLIC_PUBLIC_URL=https://stx.catlog.shop
ENV NEXT_PUBLIC_API_URL=https://api.stx.catlog.shop
ENV NEXT_PUBLIC_SERVER_IP=*************
ENV INTERNAL_API_KEY=AcrpElBoJrFdH1khQLndrAZkYSaQuIrm
ENV NEXT_PUBLIC_WEB_PUSH_PUBLIC_KEY=BGeoIrVOX4erEsCg0zqfQ6AhsrUM_-_t20kJNbs-naOvcZq9SKoOwy6frSr13Qvnaq8gcq89GLF21uurrMwoIDA
ENV WEB_PUSH_PRIVATE_KEY=B0djDSSHmbcnx4yAMeyJ1OK37V53tppbljVKBb1AxE8
ENV WEB_PUSH_EMAIL=<EMAIL>
ENV NEXT_PUBLIC_GOOGLE_API_KEY=AIzaSyBL_fl7b9ozmJbuMhZi1klcstUgw4qaB1s
ENV NEXT_PUBLIC_RECAPTCHA_SITE_KEY=6Lc9lQspAAAAAK9erY7hLfzn5gWUDnVM5AokdMlX
ENV NEXT_PUBLIC_INSTAGRAM_APP_KEY=1078947597341793
ENV NEXT_PUBLIC_CHOWBOT_URL=https://stx.chow.bot


RUN yarn install
RUN yarn build
RUN rm -rf node_modules
RUN yarn install --production

FROM node:14.18.0-alpine

WORKDIR /usr/src/app

COPY --from=BUILD_IMAGE /usr/src/app .

EXPOSE 4000

CMD [ "yarn", "start" ]
