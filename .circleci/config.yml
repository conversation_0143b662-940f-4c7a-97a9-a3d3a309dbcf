version: 2.1

# Define the jobs we want to run for this project
jobs:
  run-tests:
    docker:
      - image: circleci/node:14
    steps:
      - checkout
      - run: yarn install --frozen-lock-file
      - run: yarn build

  deploy-beta-dashboard:
    docker:
      - image: cimg/base:2021.04
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: false
      - run: docker build -t myshopcatlog/catlog-dashboard:beta -f deployments/prod.Dockerfile .
      - run: docker login -u  $DOCKERHUB_USERNAME -p $DOCKERHUB_PASSWORD
      - run: docker push myshopcatlog/catlog-dashboard:beta
      - run: ssh -oStrictHostKeyChecking=no -v $USER@$BETA_IP "bash ~/deployments/dashboard-deploy.sh"

  deploy-staging-dashboard:
    docker:
      - image: cimg/base:2021.04
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: false
      - run: docker build -t myshopcatlog/catlog-dashboard:staging -f deployments/staging.Dockerfile .
      - run: docker login -u  $DOCKERHUB_USERNAME -p $DOCKERHUB_PASSWORD
      - run: docker push myshopcatlog/catlog-dashboard:staging
      - run: ssh -oStrictHostKeyChecking=no -v $USER@$IP "bash ~/deployments/dashboard-deploy.sh"

  deploy-experiment-dashboard:
    docker:
      - image: cimg/base:2021.04
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: false
      - run: docker build -t myshopcatlog/catlog-dashboard:experiment -f deployments/experiments.Dockerfile .
      - run: docker login -u  $DOCKERHUB_USERNAME -p $DOCKERHUB_PASSWORD
      - run: docker push myshopcatlog/catlog-dashboard:experiment
      - run: sudo apt-get update && sudo apt-get install sshpass -y
      - run: sshpass -p "circleci" ssh -oStrictHostKeyChecking=no -v circleci@$EXPERIMENTS_IP "bash ~/deployments/dashboard-deploy.sh"


# Orchestrate our job run sequence
workflows:
  build-project:
    jobs:
      - run-tests
      - deploy-beta-dashboard:
          requires:
            - run-tests
          filters:
            branches:
              only:
                - beta
      - deploy-staging-dashboard:
          requires:
            - run-tests
          filters:
            branches:
              only:
                - develop

      - deploy-experiment-dashboard:
          requires:
            - run-tests
          filters:
            branches:
              only:
                - experiments
# VS Code Extension Version: 1.5.1
