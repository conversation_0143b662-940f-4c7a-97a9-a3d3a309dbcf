const withPWA = require("next-pwa")({
  dest: "public",
  scope: "/",
  dynamicStartUrlRedirect: "/login",
  cacheOnFrontEndNav: true,
  sw: "clgsw.js",
});
const { withSentryConfig } = require("@sentry/nextjs");

const webpack = (config) => {
  config.module.rules.push({
    test: /\.svg$/,
    use: ["@svgr/webpack"],
  });
  return config;
};

// Get the WebSocket URL based on the NEXT_PUBLIC_API_URL
const apiUrl = process.env.NEXT_PUBLIC_API_URL || ""; // Add a fallback empty string
const websocketURL = apiUrl ? apiUrl.replace(/^http/, "ws") : "";

// Security Headers
const securityHeaders = [
  {
    key: "Strict-Transport-Security",
    value: "max-age=63072000; includeSubDomains; preload",
  },
  {
    key: "Content-Security-Policy",
    value: `
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.paystack.co https://www.google-analytics.com https://www.googletagmanager.com https://www.clarity.ms https://*.clarity.ms http://static.ads-twitter.com https://static.ads-twitter.com https://analytics.twitter.com https://connect.facebook.net https://www.facebook.com https://*.amazonaws.com https://res.cloudinary.com https://fonts.googleapis.com https://fonts.gstatic.com https://t.co https://*.googleapis.com https://*.cdninstagram.com http://www.youtube.com https://play.google.com https://youtube.com https://cdn.jsdelivr.net https://www.instagram.com;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      img-src 'self' data: blob: https://catlog-1.s3.eu-west-2.amazonaws.com https://catlog-dev.s3.eu-west-2.amazonaws.com https://*.amazonaws.com https://analytics.twitter.com https://www.facebook.com https://t.co https://res.cloudinary.com https://*.clarity.ms https://*.cdninstagram.com https://cdn.jsdelivr.net;
      connect-src 'self' data: blob: ${process.env.NEXT_PUBLIC_API_URL} ${websocketURL} https://unpkg.com/@rive-app/canvas@2.25.4/rive.wasm https://js.paystack.co https://www.google-analytics.com https://www.googletagmanager.com https://www.clarity.ms https://*.clarity.ms https://*.sentry.io http://static.ads-twitter.com https://analytics.twitter.com https://static.ads-twitter.com https://connect.facebook.net https://www.facebook.com https://*.amazonaws.com https://res.cloudinary.com https://fonts.googleapis.com https://fonts.gstatic.com https://t.co https://maps.googleapis.com https://*.cdninstagram.com https://*.googleapis.com http://www.youtube.com https://play.google.com https://youtube.com https://api.iplocation.net https://catlog-dev.s3.eu-west-2.amazonaws.com;
      font-src 'self' https://fonts.gstatic.com;
      media-src 'self' data: blob: https://res.cloudinary.com http://www.youtube.com https://youtube.com https://cdn.jsdelivr.net https://catlog-dev.s3.eu-west-2.amazonaws.com https://catlog-1.s3.eu-west-2.amazonaws.com https://catlog-s3.s3.eu-west-2.amazonaws.com;
      frame-src *;
      object-src *;
      base-uri 'self';
      form-action *;
  `.replace(/\n/g, ""),
  },
  {
    key: "X-Frame-Options",
    value: "DENY",
  },
  {
    key: "X-Content-Type-Options",
    value: "nosniff",
  },
  {
    key: "Referrer-Policy",
    value: "no-referrer",
  },
  {
    key: "Permissions-Policy",
    value: "geolocation=(self)",
  },
];

const SharedArrayBufferHeaders = [
  { key: "Cross-Origin-Embedder-Policy", value: "require-corp" },
  { key: "Cross-Origin-Opener-Policy", value: "same-origin" },
  { key: "Cross-Origin-Resource-Policy", value: "cross-origin" },
  ...securityHeaders,
];

const headers = async () => [
  {
    source: "/(.*)",
    headers: SharedArrayBufferHeaders,
  },
  {
    source: "/static/:path*",
    headers: [{ key: "Cross-Origin-Resource-Policy", value: "same-origin" }],
  },
];

const configWithPWA = withPWA({
  reactStrictMode: true,
  webpack,
  workbox: {
    // Disable logging
    dev: false,
  },
  headers,
});

const sentryWebpackPluginOptions = {
  // Additional config options for the Sentry Webpack plugin. Keep in mind that
  // the following options are set automatically, and overriding them is not
  // recommended:
  //   release, url, org, project, authToken, configFile, stripPrefix,
  //   urlPrefix, include, ignore

  silent: true, // Suppresses all logs
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options.
};

module.exports =
  process.env.NODE_ENV === "development"
    ? { reactStrictMode: true, webpack, headers }
    : withSentryConfig(configWithPWA, sentryWebpackPluginOptions);
