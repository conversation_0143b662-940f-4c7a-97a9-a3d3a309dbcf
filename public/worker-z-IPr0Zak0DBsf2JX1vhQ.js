(()=>{"use strict";self.addEventListener("push",(function(t){const n=JSON.parse(t.data.text());t.waitUntil(registration.showNotification(n.title,{body:n.message,data:{path:null==n?void 0:n.path},icon:"/app-icons/192x192.png"}))})),self.addEventListener("notificationclick",(function(t){var n,i;const o=null==t||null===(n=t.notification)||void 0===n||null===(i=n.data)||void 0===i?void 0:i.path;t.notification.close(),t.waitUntil(clients.matchAll({type:"window",includeUncontrolled:!0}).then((function(t){if(clients.openWindow)return clients.openWindow(null!=o?o:"/dashboard");if(t.length>0){let n=t[0];for(let i=0;i<t.length;i++)t[i].focused&&(n=t[i]);return n.focus()}})))}))})();